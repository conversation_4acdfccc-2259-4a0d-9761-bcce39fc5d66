import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import {
  ActionState,
  Action,
  CreateActionRequest,
  ActionPageMode,
  ActionAction,
} from "./types";
import {
  fetchActions,
  fetchAction,
  createActionApi,
  updateActionApi,
  deleteAction<PERSON>pi,
} from "./api";
import { toast } from "sonner";

const initialState: ActionState = {
  data: [],
  currentAction: null,
  pageMode: "list",
  actionType: "view",
  searchTerm: "",
  loading: false,
  error: null,
};

// Async thunks
export const fetchActionsThunk = createAsyncThunk(
  "action/fetchActions",
  async () => {
    const response = await fetchActions();
    if (!response.data.data) {
      throw new Error("No data");
    }
    return response.data.data;
  }
);

export const fetchActionByIdThunk = createAsyncThunk(
  "action/fetchById",
  async (id: number) => {
    const response = await fetchAction(id);
    if (!response.data.data) {
      throw new Error("No data");
    }
    return response.data.data;
  }
);

export const createActionThunk = createAsyncThunk(
  "actions/create",
  async (data: CreateActionRequest) => {
    const response = await createActionApi(data);
    if (!response.data.data) {
      throw new Error("Failed to create action");
    }
    toast.success("Tạo quyền thành công");
    return response.data.data;
  }
);

export const updateActionThunk = createAsyncThunk(
  "actions/update",
  async ({ id, payload }: { id: number; payload: Partial<Action> }) => {
    const response = await updateActionApi(id, payload);
    if (!response.data.data) {
      throw new Error("Failed to update action");
    }
    toast.success("Cập nhật quyền thành công");
    return response.data.data;
  }
);

export const deleteActionThunk = createAsyncThunk(
  "actions/delete",
  async (id: number) => {
    await deleteActionApi(id);
    toast.success("Xóa quyền thành công");
    return id;
  }
);

const actionSlice = createSlice({
  name: "action",
  initialState,
  reducers: {
    setCurrentAction: (state, action: PayloadAction<Action | null>) => {
      state.currentAction = action.payload;
    },
    setPathParams: (
      state,
      action: PayloadAction<{
        mode?: ActionPageMode;
        actionType?: ActionAction;
      }>
    ) => {
      const { mode, actionType } = action.payload;
      if (mode) state.pageMode = mode;
      if (actionType) state.actionType = actionType;
    },
    setSearchTerm: (state, action: PayloadAction<string>) => {
      state.searchTerm = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch actions
      .addCase(fetchActionsThunk.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchActionsThunk.fulfilled, (state, action) => {
        state.loading = false;
        state.data = action.payload;
      })
      .addCase(fetchActionsThunk.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || "Failed to fetch actions";
      })

      // Fetch action by id
      .addCase(fetchActionByIdThunk.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchActionByIdThunk.fulfilled, (state, action) => {
        state.loading = false;
        state.currentAction = action.payload;
      })
      .addCase(fetchActionByIdThunk.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || "Failed to fetch action";
      })

      // Create action
      .addCase(createActionThunk.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createActionThunk.fulfilled, (state, action) => {
        state.loading = false;
        state.data.push(action.payload);
        state.currentAction = action.payload;
      })
      .addCase(createActionThunk.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || "Failed to create action";
      })

      // Update action
      .addCase(updateActionThunk.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateActionThunk.fulfilled, (state, action) => {
        state.loading = false;
        const index = state.data.findIndex(
          (item) => item.id === action.payload.id
        );
        if (index !== -1) {
          state.data[index] = action.payload;
        }
        if (state.currentAction?.id === action.payload.id) {
          state.currentAction = action.payload;
        }
      })
      .addCase(updateActionThunk.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || "Failed to update action";
      })

      // Delete action
      .addCase(deleteActionThunk.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteActionThunk.fulfilled, (state, action) => {
        state.loading = false;
        state.data = state.data.filter((item) => item.id !== action.payload);
        if (state.currentAction?.id === action.payload) {
          state.currentAction = null;
        }
      })
      .addCase(deleteActionThunk.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || "Failed to delete action";
      });
  },
});

export const { setCurrentAction, setPathParams, setSearchTerm, clearError } =
  actionSlice.actions;

export default actionSlice.reducer;
