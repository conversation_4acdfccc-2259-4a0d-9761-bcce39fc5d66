export type AlbumStatus =
  | "TRASH"
  | "PUBLISHED"
  | "UNPUBLISHED"
  | "DRAFT"
  | "REVIEW"
  | "REJECTED";

export type AlbumType = "image" | "video";

interface Description {
  text: string;
}

export interface Album {
  id: number;
  createdAt: number;
  updatedAt: number;
  publishedAt: number | null;

  name: string;
  description: {
    text: string;
  };
  coverImage?: string;

  type: AlbumType;
  status: AlbumStatus;
}

export interface CreateAlbum {
  name: string;
  description: Description;
  coverImage: string;
  type: AlbumType;
}

export interface AlbumQueryParams {
  type?: AlbumType;
  status?: AlbumStatus;
  page: number;
  size: number;
  search?: string;
}

// Response types based on actual API
export interface Pagination {
  page: number;
  size: number;
  total: number; // Always 0, not reliable
  hasMore?: boolean; // We'll calculate this based on returned data
}

// Removed: AlbumResponse, AlbumDetailResponse - use BaseResponse<T> instead

// Media types for album media
export interface Media {
  id: number;
  createdAt: number;
  updatedAt: number;

  name: string;
  description?: {
    text: string;
  };
  src: string;
  type: AlbumType;
  priority?: number;

  albumId?: number;
}

// Removed: MediaResponse - use BaseResponse<T> instead

export interface MediaQueryParams {
  page?: number;
  size?: number;
  search?: string;
}

// Media creation and update types
export interface CreateMediaRequest {
  type: AlbumType;
  name: string;
  src: string;
  description?: {
    text: string;
  };
  albumId: number;
}

export interface UpdateMediaRequest {
  type: string; // Required according to API docs
  name: string; // Required according to API docs  
  src: string; // Required according to API docs
  description: {
    text: string;
  };
}

export interface MediaPriorityRequest {
  priority: number;
}

export interface CreateMediaResponse {
  data: Media;
}

export interface UpdateMediaResponse {
  data: Media;
}
