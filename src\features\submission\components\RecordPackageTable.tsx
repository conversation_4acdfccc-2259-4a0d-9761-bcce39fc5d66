import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Eye, Edit, Trash2, Download, Upload, Plus } from "lucide-react";
import {
  RecordPackage,
  RecordPackageFilters,
  recordPackageStatusLabels,
  recordPackageStatusColors,
} from "../data/mockData";

interface RecordPackageTableProps {
  packages: RecordPackage[];
  filters: RecordPackageFilters;
  onFiltersChange: (filters: RecordPackageFilters) => void;
  onView?: (pkg: RecordPackage) => void;
  onEdit?: (pkg: RecordPackage) => void;
  onDelete?: (pkg: RecordPackage) => void;
  onAdd?: () => void;
  onImport?: () => void;
  onExport?: () => void;
  selectedIds?: number[];
  onSelectionChange?: (ids: number[]) => void;
}

export const RecordPackageTable = ({
  packages,
  filters,
  onFiltersChange,
  onView,
  onEdit,
  onDelete,
  onAdd,
  onImport,
  onExport,
  selectedIds = [],
  onSelectionChange,
}: RecordPackageTableProps) => {
  const [localFilters, setLocalFilters] =
    useState<RecordPackageFilters>(filters);

  const handleFilterChange = (
    key: keyof RecordPackageFilters,
    value: string
  ) => {
    // Convert "all" to empty string for filter logic
    const filterValue = value === "all" ? "" : value;
    const newFilters = { ...localFilters, [key]: filterValue };
    setLocalFilters(newFilters);
    onFiltersChange(newFilters);
  };

  const handleSelectAll = (checked: boolean) => {
    if (onSelectionChange) {
      onSelectionChange(checked ? packages.map((pkg) => pkg.id) : []);
    }
  };

  const handleSelectItem = (id: number, checked: boolean) => {
    if (onSelectionChange) {
      if (checked) {
        onSelectionChange([...selectedIds, id]);
      } else {
        onSelectionChange(
          selectedIds.filter((selectedId) => selectedId !== id)
        );
      }
    }
  };

  const isAllSelected =
    packages.length > 0 && selectedIds.length === packages.length;
  const isIndeterminate =
    selectedIds.length > 0 && selectedIds.length < packages.length;

  return (
    <Card className="w-full">
      <CardHeader className="bg-gray-50 border-b">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold text-gray-900">
            DANH SÁCH GÓI HỒ SƠ
          </CardTitle>
          <div className="flex items-center space-x-2">
            <Select
              value={localFilters.keyword === "" ? "all" : localFilters.keyword}
              onValueChange={(value) => handleFilterChange("keyword", value)}
            >
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Tìm theo từ khóa" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả</SelectItem>
                <SelectItem value="G09">Mã hồ sơ G09</SelectItem>
                <SelectItem value="quyết định">Quyết định</SelectItem>
                <SelectItem value="báo cáo">Báo cáo</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        {/* Action Buttons */}
        <div className="p-4 border-b bg-white">
          <div className="flex justify-end space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={onImport}
              className="flex items-center space-x-1"
            >
              <Upload className="h-4 w-4" />
              <span>Import</span>
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={onExport}
              className="flex items-center space-x-1"
            >
              <Download className="h-4 w-4" />
              <span>Export</span>
            </Button>
            <Button
              size="sm"
              onClick={onAdd}
              className="flex items-center space-x-1 bg-blue-600 hover:bg-blue-700"
            >
              <Plus className="h-4 w-4" />
              <span>Thêm</span>
            </Button>
          </div>
        </div>

        {/* Table */}
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50">
                <TableHead className="w-12">
                  <Checkbox
                    checked={isAllSelected}
                    onCheckedChange={handleSelectAll}
                    ref={(ref) => {
                      if (ref) {
                        const input = ref.querySelector(
                          'input[type="checkbox"]'
                        ) as HTMLInputElement;
                        if (input) input.indeterminate = isIndeterminate;
                      }
                    }}
                  />
                </TableHead>
                <TableHead className="text-center font-medium">STT</TableHead>
                <TableHead className="font-medium">Mã hồ sơ</TableHead>
                <TableHead className="font-medium">Nội dung</TableHead>
                <TableHead className="text-center font-medium">
                  Kích thước
                </TableHead>
                <TableHead className="text-center font-medium">
                  Số thứ tự hồ sơ
                </TableHead>
                <TableHead className="text-center font-medium">
                  Trạng thái
                </TableHead>
                <TableHead className="text-center font-medium">
                  Số lượng tài liệu
                </TableHead>
                <TableHead className="text-center font-medium">
                  Thao tác
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {packages.length === 0 ? (
                <TableRow>
                  <TableCell
                    colSpan={9}
                    className="text-center py-8 text-gray-500"
                  >
                    Không có dữ liệu
                  </TableCell>
                </TableRow>
              ) : (
                packages.map((pkg, index) => (
                  <TableRow key={pkg.id} className="hover:bg-gray-50">
                    <TableCell>
                      <Checkbox
                        checked={selectedIds.includes(pkg.id)}
                        onCheckedChange={(checked) =>
                          handleSelectItem(pkg.id, !!checked)
                        }
                      />
                    </TableCell>
                    <TableCell className="text-center font-medium">
                      {index + 1}
                    </TableCell>
                    <TableCell className="font-mono text-sm bg-yellow-50">
                      {pkg.recordCode}
                    </TableCell>
                    <TableCell className="max-w-xs">
                      <div className="truncate" title={pkg.content}>
                        {pkg.content}
                      </div>
                    </TableCell>
                    <TableCell className="text-center">{pkg.size}</TableCell>
                    <TableCell className="text-center">
                      {pkg.recordOrder}
                    </TableCell>
                    <TableCell className="text-center">
                      <span
                        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          recordPackageStatusColors[pkg.status]
                        }`}
                      >
                        {recordPackageStatusLabels[pkg.status]}
                      </span>
                    </TableCell>
                    <TableCell className="text-center">
                      {pkg.documentCount}
                    </TableCell>
                    <TableCell className="text-center">
                      <div className="flex items-center justify-center space-x-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onView?.(pkg)}
                          className="h-8 w-8 p-0"
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onEdit?.(pkg)}
                          className="h-8 w-8 p-0"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0"
                        >
                          <Download className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onDelete?.(pkg)}
                          className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>

        {/* Pagination */}
        <div className="p-4 border-t bg-gray-50">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Select defaultValue="10">
                <SelectTrigger className="w-20">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="20">20</SelectItem>
                  <SelectItem value="50">50</SelectItem>
                </SelectContent>
              </Select>
              <span className="text-sm text-gray-600">1-10/100</span>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" disabled>
                ‹‹
              </Button>
              <Button variant="outline" size="sm" disabled>
                ‹
              </Button>
              <Button variant="outline" size="sm">
                ›
              </Button>
              <Button variant="outline" size="sm">
                ››
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
