export interface Group {
  id: number;
  createdAt: string;
  updatedAt: string;
  parentId: number | null;
  code: string;
  name: string;
  metadata: Record<string, string | number | boolean> | null;
}

export interface GroupTree extends Group {
  children: GroupTree[];
}

export interface GroupRole {
  id: number;
  name: string;
  description: string;
  permissions: string[];
}

export interface CreateGroupRequest {
  parentId: number | null;
  code: string;
  name: string;
}

export type GroupTab = "basic" | "metadata" | "users" | "roles";
export type GroupAction = "view" | "edit" | "create";

export interface GroupState {
  data: Group[];
  selectedId: number;
  // Add tab and action state
  currentTab: GroupTab;
  currentAction: GroupAction;
  loading: boolean;
  error: string | null;
  roles: Record<number, GroupRole[]>;
  // Add system roles assigned to groups
  assignedRoleIds: Record<number, number[]>; // groupId -> roleId[]
  assignedRolesLoading: Record<number, boolean>;
}

export interface UpdateGroupRequest {
  id: number;
  payload: Partial<Group>;
}
