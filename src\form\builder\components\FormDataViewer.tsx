/**
 * Form Data Viewer
 * JSON viewer for form submission data in preview mode
 */

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Copy, Check, Trash2, Eye, EyeOff } from 'lucide-react';
import { toast } from 'sonner';

interface FormSubmission {
  id: string;
  timestamp: number;
  data: Record<string, any>;
  isValid: boolean;
}

interface FormDataViewerProps {
  submissions: FormSubmission[];
  onClear: () => void;
}

export const FormDataViewer: React.FC<FormDataViewerProps> = ({
  submissions,
  onClear
}) => {
  const [copiedId, setCopiedId] = useState<string | null>(null);
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());

  const handleCopy = async (data: any, id: string) => {
    try {
      const jsonString = JSON.stringify(data, null, 2);
      await navigator.clipboard.writeText(jsonString);
      setCopiedId(id);
      toast.success('JSO<PERSON> copied to clipboard!');
      
      // Reset copy state after 2 seconds
      setTimeout(() => setCopiedId(null), 2000);
    } catch (error) {
      toast.error('Failed to copy to clipboard');
    }
  };

  const toggleExpanded = (id: string) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(id)) {
      newExpanded.delete(id);
    } else {
      newExpanded.add(id);
    }
    setExpandedItems(newExpanded);
  };

  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  const renderJsonValue = (value: any, key?: string): React.ReactNode => {
    if (value === null) return <span className="text-gray-400">null</span>;
    if (value === undefined) return <span className="text-gray-400">undefined</span>;
    if (typeof value === 'string') return <span className="text-green-600">"{value}"</span>;
    if (typeof value === 'number') return <span className="text-blue-600">{value}</span>;
    if (typeof value === 'boolean') return <span className="text-purple-600">{value.toString()}</span>;
    if (Array.isArray(value)) return <span className="text-gray-600">[{value.length} items]</span>;
    if (typeof value === 'object') return <span className="text-gray-600">{"{...}"}</span>;
    return <span>{String(value)}</span>;
  };

  if (submissions.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-full text-center p-6">
        <div className="text-3xl mb-3">📋</div>
        <h3 className="text-sm font-semibold text-gray-900 mb-2">
          Form Data Viewer
        </h3>
        <p className="text-xs text-gray-600 mb-3">
          Submit the form to see JSON data here
        </p>
        <div className="text-xs text-gray-500">
          All submissions will be captured and displayed for testing
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-sm font-semibold text-gray-900">
            Form Submissions
          </h3>
          <Button
            variant="outline"
            size="sm"
            onClick={onClear}
            className="h-7 px-2"
          >
            <Trash2 className="h-3 w-3 mr-1" />
            Clear
          </Button>
        </div>
        <p className="text-xs text-gray-500">
          {submissions.length} submission{submissions.length !== 1 ? 's' : ''} captured
        </p>
      </div>

      {/* Submissions List */}
      <div className="flex-1 overflow-auto p-4 space-y-3">
        {submissions.map((submission) => {
          const isExpanded = expandedItems.has(submission.id);
          const isCopied = copiedId === submission.id;
          
          return (
            <div
              key={submission.id}
              className="bg-white border border-gray-200 rounded-lg overflow-hidden"
            >
              {/* Submission Header */}
              <div className="p-3 bg-gray-50 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Badge 
                      variant={submission.isValid ? "default" : "destructive"}
                      className="text-xs"
                    >
                      {submission.isValid ? 'Valid' : 'Invalid'}
                    </Badge>
                    <span className="text-xs text-gray-600">
                      {formatTimestamp(submission.timestamp)}
                    </span>
                  </div>
                  
                  <div className="flex items-center gap-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => toggleExpanded(submission.id)}
                      className="h-6 w-6 p-0"
                    >
                      {isExpanded ? (
                        <EyeOff className="h-3 w-3" />
                      ) : (
                        <Eye className="h-3 w-3" />
                      )}
                    </Button>
                    
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleCopy(submission.data, submission.id)}
                      className="h-6 w-6 p-0"
                    >
                      {isCopied ? (
                        <Check className="h-3 w-3 text-green-600" />
                      ) : (
                        <Copy className="h-3 w-3" />
                      )}
                    </Button>
                  </div>
                </div>
              </div>

              {/* JSON Content */}
              {isExpanded ? (
                <div className="p-3">
                  <pre className="text-xs font-mono bg-gray-900 text-gray-100 p-3 rounded overflow-auto max-h-64">
                    {JSON.stringify(submission.data, null, 2)}
                  </pre>
                </div>
              ) : (
                <div className="p-3">
                  <div className="space-y-1">
                    {Object.entries(submission.data).slice(0, 3).map(([key, value]) => (
                      <div key={key} className="flex items-center gap-2 text-xs">
                        <span className="font-mono text-gray-600 min-w-0 flex-shrink-0">
                          {key}:
                        </span>
                        <span className="min-w-0 flex-1 truncate">
                          {renderJsonValue(value)}
                        </span>
                      </div>
                    ))}
                    {Object.keys(submission.data).length > 3 && (
                      <div className="text-xs text-gray-400">
                        ... {Object.keys(submission.data).length - 3} more fields
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};