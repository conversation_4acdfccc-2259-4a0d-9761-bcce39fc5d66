# AutoTable Component Guide

## Overview

AutoTable is a flexible, type-safe table component built on top of TanStack Table (React Table v8). It provides a declarative way to display tabular data with features like pagination, sorting, custom cell renderers, and actions. The component uses a registry pattern for cell components, making it highly extensible and reusable across different data types.

## Core Concepts

### 1. Component Registry Pattern

AutoTable uses a registry system to map cell types to React components. This allows for:
- Consistent cell rendering across the application
- Easy addition of new cell types
- Type-safe component props
- Reusable cell components

### 2. Column Configuration

Columns are defined using a declarative configuration:

```typescript
interface ColumnConfig<T> {
  accessorKey: string;      // Path to data field (supports nested: "user.name")
  header: string;           // Column header text
  visible?: boolean;        // Show/hide column (default: true)
  cell?: CellConfig;        // Custom cell renderer configuration
}

interface CellConfig {
  type: string;             // Cell component type from registry
  props?: Record<string, any>;  // Props passed to cell component
}
```

### 3. Built-in Cell Types

- `TextCell` - Default text display
- `DateTimeCell` - Date/time formatting
- `Badge` - Status badges with variants
- `ImageCell` - Image display with fallback
- `Avatar` - User avatars
- `IconCell` - Icon display
- `ActionCell` - Action buttons
- `SimpleCheckbox` - Checkbox display
- `ActionCheckbox` - Checkbox with actions

## Basic Usage

### Simple Table Example

```typescript
import { AutoTable } from '@/components/table/AutoTable';

interface User {
  id: string;
  name: string;
  email: string;
  status: 'active' | 'inactive';
  createdAt: string;
}

const columns: ColumnConfig<User>[] = [
  {
    accessorKey: 'name',
    header: 'Name'
  },
  {
    accessorKey: 'email',
    header: 'Email'
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: {
      type: 'Badge',
      props: {
        variant: (value: string) => value === 'active' ? 'success' : 'secondary'
      }
    }
  },
  {
    accessorKey: 'createdAt',
    header: 'Created Date',
    cell: {
      type: 'DateTimeCell',
      props: {
        format: 'DD/MM/YYYY'
      }
    }
  }
];

function UserList() {
  const { data, loading, error } = useUserData();

  return (
    <AutoTable<User>
      columns={columns}
      data={data || []}
      loading={loading}
      error={error}
    />
  );
}
```

### Table with Actions

```typescript
const columnsWithActions: ColumnConfig<User>[] = [
  ...columns,
  {
    accessorKey: 'actions',
    header: 'Actions',
    cell: {
      type: 'ActionCell',
      props: {
        actions: [
          {
            id: 'edit',
            label: 'Edit',
            icon: Edit,
            onClick: (row: User) => handleEdit(row.id)
          },
          {
            id: 'delete',
            label: 'Delete',
            icon: Trash,
            variant: 'destructive',
            onClick: (row: User) => handleDelete(row.id)
          }
        ]
      }
    }
  }
];
```

### Table with Pagination

```typescript
import { PaginationState } from '@tanstack/react-table';

function PaginatedTable() {
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10
  });

  const { data, totalPages } = usePagedData(pagination);

  return (
    <AutoTable<User>
      columns={columns}
      data={data}
      pagination={{
        pageIndex: pagination.pageIndex,
        pageSize: pagination.pageSize,
        totalPages,
        onPageChange: (page) => setPagination(prev => ({ ...prev, pageIndex: page })),
        onPageSizeChange: (size) => setPagination({ pageIndex: 0, pageSize: size })
      }}
    />
  );
}
```

## AutoTable Props

```typescript
interface AutoTableProps<T> {
  columns: ColumnConfig<T>[];        // Column definitions
  data: T[];                         // Table data
  pagination?: PaginationConfig;     // Pagination settings
  loading?: boolean;                 // Loading state
  error?: Error | null;              // Error state
  emptyMessage?: string;             // Empty state message
  onAction?: (action: string, row: T) => void;  // Generic action handler
  onChange?: (row: T) => void;       // Row change handler
  className?: string;                // Additional CSS classes
}

interface PaginationConfig {
  pageIndex: number;
  pageSize: number;
  totalPages: number;
  pageSizeOptions?: number[];
  onPageChange: (page: number) => void;
  onPageSizeChange: (size: number) => void;
}
```

## Advanced Features

### Nested Data Access

AutoTable supports accessing nested properties using dot notation:

```typescript
const columns: ColumnConfig<Order>[] = [
  {
    accessorKey: 'id',
    header: 'Order ID'
  },
  {
    accessorKey: 'customer.name',    // Nested property
    header: 'Customer Name'
  },
  {
    accessorKey: 'customer.email',   // Nested property
    header: 'Customer Email'
  },
  {
    accessorKey: 'items.length',     // Array property
    header: 'Item Count'
  }
];
```

### Custom Cell Components

Register custom cell components for specific use cases:

```typescript
// 1. Create custom cell component
const StatusCell: React.FC<{ value: string; row: any }> = ({ value, row }) => {
  const getColor = () => {
    switch (value) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'approved': return 'bg-green-100 text-green-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getColor()}`}>
      {value}
    </span>
  );
};

// 2. Register the component
import { registerCellComponent } from '@/components/table/registry';

registerCellComponent('StatusCell', StatusCell);

// 3. Use in column config
const columns: ColumnConfig<Item>[] = [
  {
    accessorKey: 'status',
    header: 'Status',
    cell: {
      type: 'StatusCell'
    }
  }
];
```

### Dynamic Column Configuration

```typescript
function DynamicTable() {
  const [visibleColumns, setVisibleColumns] = useState(['name', 'email']);
  
  const columns: ColumnConfig<User>[] = [
    {
      accessorKey: 'name',
      header: 'Name',
      visible: visibleColumns.includes('name')
    },
    {
      accessorKey: 'email', 
      header: 'Email',
      visible: visibleColumns.includes('email')
    },
    {
      accessorKey: 'phone',
      header: 'Phone',
      visible: visibleColumns.includes('phone')
    }
  ];

  return (
    <>
      <ColumnSelector 
        columns={['name', 'email', 'phone']}
        selected={visibleColumns}
        onChange={setVisibleColumns}
      />
      <AutoTable columns={columns} data={data} />
    </>
  );
}
```

## Common Patterns

### Table with Selection

```typescript
const columnsWithSelection: ColumnConfig<User>[] = [
  {
    accessorKey: 'select',
    header: '',
    cell: {
      type: 'ActionCheckbox',
      props: {
        onCheckedChange: (checked: boolean, row: User) => {
          handleRowSelection(row.id, checked);
        }
      }
    }
  },
  ...baseColumns
];
```

### Table with Expandable Rows

```typescript
const expandableColumns: ColumnConfig<Order>[] = [
  {
    accessorKey: 'expand',
    header: '',
    cell: {
      type: 'ActionCell',
      props: {
        actions: [{
          id: 'expand',
          icon: ChevronDown,
          onClick: (row) => toggleExpand(row.id)
        }]
      }
    }
  },
  ...orderColumns
];
```

### Table with Custom Empty State

```typescript
<AutoTable
  columns={columns}
  data={data}
  emptyMessage="No users found. Click 'Add User' to create one."
  loading={loading}
  error={error}
/>
```

## Performance Optimization

1. **Memoize Column Configurations**: Use `useMemo` for column definitions
```typescript
const columns = useMemo(() => [
  { accessorKey: 'name', header: 'Name' },
  // ... more columns
], []);
```

2. **Optimize Data Transformations**: Transform data before passing to table
```typescript
const tableData = useMemo(() => 
  rawData.map(item => ({
    ...item,
    fullName: `${item.firstName} ${item.lastName}`
  })),
  [rawData]
);
```

3. **Use Pagination**: For large datasets, always implement pagination
4. **Lazy Load Cell Components**: For complex cells, consider lazy loading

## Best Practices

1. **Type Safety**: Always provide generic type for AutoTable
```typescript
<AutoTable<YourDataType>
  columns={columns}
  data={typedData}
/>
```

2. **Consistent Cell Types**: Use predefined cell types when possible
3. **Action Handlers**: Keep action handlers outside render functions
4. **Error Handling**: Always provide error prop for better UX
5. **Loading States**: Show loading state during data fetching
6. **Accessible Headers**: Use descriptive header text
7. **Mobile Responsiveness**: Consider mobile view for tables

## Integration with API

```typescript
function UserTable() {
  const [pagination, setPagination] = useState({ pageIndex: 0, pageSize: 10 });
  const { data, loading, error } = useQuery({
    queryKey: ['users', pagination],
    queryFn: () => fetchUsers(pagination)
  });

  const handleEdit = useCallback((userId: string) => {
    navigate(`/users/${userId}/edit`);
  }, [navigate]);

  const handleDelete = useMutation({
    mutationFn: deleteUser,
    onSuccess: () => {
      queryClient.invalidateQueries(['users']);
      toast.success('User deleted');
    }
  });

  const columns: ColumnConfig<User>[] = [
    { accessorKey: 'name', header: 'Name' },
    { accessorKey: 'email', header: 'Email' },
    {
      accessorKey: 'status',
      header: 'Status',
      cell: { type: 'Badge' }
    },
    {
      accessorKey: 'actions',
      header: '',
      cell: {
        type: 'ActionCell',
        props: {
          actions: [
            { id: 'edit', label: 'Edit', onClick: (row) => handleEdit(row.id) },
            { id: 'delete', label: 'Delete', onClick: (row) => handleDelete.mutate(row.id) }
          ]
        }
      }
    }
  ];

  return (
    <AutoTable<User>
      columns={columns}
      data={data?.items || []}
      loading={loading}
      error={error}
      pagination={{
        ...pagination,
        totalPages: data?.totalPages || 0,
        onPageChange: (page) => setPagination(prev => ({ ...prev, pageIndex: page })),
        onPageSizeChange: (size) => setPagination({ pageIndex: 0, pageSize: size })
      }}
    />
  );
}
```

## Troubleshooting

1. **Missing Cell Type**: Ensure cell type is registered in registry
2. **Nested Data Not Showing**: Check accessor key path is correct
3. **Actions Not Working**: Verify action handlers are properly bound
4. **Performance Issues**: Implement pagination and memoization
5. **Type Errors**: Ensure generic type matches data structure

Remember: AutoTable is designed to be a flexible, reusable component that can handle various data display needs while maintaining consistency across the application. Use its registry pattern and type safety features to create maintainable table implementations.