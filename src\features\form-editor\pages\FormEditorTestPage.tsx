/**
 * FormEditor Test Page - Orchestrate Form Editor and Form Preview
 */

import React, { useState, useMemo } from "react";
import { registerFormMock } from "../states/mock";
import { FormNode, FormData } from "../../../form/types";
import { FormUser } from "../types/FormUser";
import { unflattenObjectKeys } from "../../../form/utils/objectKeyHelper";
import { FormPreview } from "../components/FormPreview";

const FormEditorTestPage: React.FC = () => {
  // Initialize with mock data
  const [formData] = useState<Record<string, unknown>>(registerFormMock);
  const [submittedUser, setSubmittedUser] = useState<FormUser | null>(null);
  const [submitError, setSubmitError] = useState<string>("");

  // Get root node from new structure
  const rootNode: FormNode | null = useMemo(() => {
    return (formData?.node as FormNode) || null;
  }, [formData]);

  // Handle form submission - Convert form data to FormUser
  const handleFormSubmit = (data: FormData) => {
    try {
      // Use objectKeyHelper to unflatten if needed
      const processedData = unflattenObjectKeys(data);

      // Cast to FormUser (AutoForm đã validate với Zod)
      const user: FormUser = processedData as unknown as FormUser;

      setSubmittedUser(user);
      setSubmitError("");

      console.log("🎉 Form submitted successfully!");
      console.log("📝 Form Data (raw):", data);
      console.log("👤 FormUser (typed):", user);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      setSubmitError(`Failed to process form data: ${errorMessage}`);
      console.error("❌ Form submission error:", error);
    }
  };

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 py-4">
          <h1 className="text-2xl font-bold text-gray-800">
            🛠️ Form Editor & Test
          </h1>
        </div>
      </div>

      {/* Content */}
      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col w-full">
          <FormPreview
            rootNode={rootNode}
            submittedUser={submittedUser}
            submitError={submitError}
            onFormSubmit={handleFormSubmit}
          />
          {submittedUser && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4 mt-8">
              <h4 className="font-medium text-green-800 mb-2">
                ✅ FormUser Submitted:
              </h4>
              <pre className="text-sm text-green-700 bg-green-100 p-2 rounded overflow-auto">
                {JSON.stringify(submittedUser, null, 2)}
              </pre>
            </div>
          )}
          {submitError && (
            <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
              <h4 className="font-medium text-red-800 mb-2">
                ❌ Submit Error:
              </h4>
              <div className="text-sm text-red-700">{submitError}</div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default FormEditorTestPage;
