import React from "react";
import { LogOut } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { useNavigate } from "react-router-dom";
import { useAppDispatch, useAppSelector } from "@/store/rootReducer";
import { selectAuthState } from "@/features/auth/states/selector";
import { logoutAsync } from "@/features/auth/states/slices";

export const UserInfoMobile: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { is_authenticated, account } = useAppSelector(selectAuthState);

  const handleLogin = () => {
    navigate("/auth/login");
  };

  const handleRegister = () => {
    navigate("/auth/register");
  };

  const handleProfile = () => {
    navigate("/me");
  };

  const handleLogout = async () => {
    try {
      await dispatch(logoutAsync()).unwrap();
      navigate("/", { replace: true });
    } catch (error) {
      console.error("Logout failed:", error);
      navigate("/", { replace: true });
    }
  };

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((word) => word[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  if (!is_authenticated) {
    return (
      <div className="flex flex-col gap-2">
        <Button size="sm" onClick={handleLogin} className="w-full">
          Đăng nhập
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={handleRegister}
          className="w-full"
        >
          Đăng ký
        </Button>
      </div>
    );
  }

  return (
    <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
      <div
        onClick={handleProfile}
        className="flex items-center gap-3 flex-1 cursor-pointer hover:bg-gray-100 rounded-lg p-2 transition-colors"
      >
        <Avatar className="h-12 w-12">
          <AvatarFallback className="bg-primary text-primary-foreground">
            {account?.fullName ? getInitials(account.fullName) : "U"}
          </AvatarFallback>
        </Avatar>
        <div className="flex-1">
          <div className="font-medium text-base">
            {account?.fullName || "Người dùng"}
          </div>
          <div className="text-sm text-gray-600">{account?.email || ""}</div>
        </div>
      </div>

      <button
        onClick={handleLogout}
        className="p-2 rounded-lg hover:bg-red-50 text-red-600 transition-colors"
        aria-label="Đăng xuất"
      >
        <LogOut className="w-5 h-5" />
      </button>
    </div>
  );
};
