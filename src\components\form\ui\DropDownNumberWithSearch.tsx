import React from "react";
import { BasicInputProps } from "../registry";
import { DropDownNumberWithSearch as BasicDropDownNumberWithSearch } from "@/form/field/components/basic/DropDownNumberWithSearch";

type DropdownNumberWithSearchProps = BasicInputProps<"number">;

export const DropDownNumberWithSearch = React.forwardRef<
  HTMLDivElement,
  DropdownNumberWithSearchProps
>(
  (
    {
      isViewMode,
      value,
      onChange,
      options = [],
      labels,
      placeholder = "Chọn giá trị",
      disabled,
      id,
      ...rest
    },
    ref
  ) => {
    return (
      <div ref={ref} {...rest}>
        <BasicDropDownNumberWithSearch
          value={value}
          onChange={(newValue) => onChange(newValue as number)}
          options={options}
          labels={labels || []}
          placeholder={placeholder}
          disabled={disabled || (isViewMode && !disabled)}
          id={id}
        />
      </div>
    );
  }
);

DropDownNumberWithSearch.displayName = "DropDownNumberWithSearch";
