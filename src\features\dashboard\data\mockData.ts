export interface DashboardStats {
  totalUsers: number;
  totalPosts: number;
  totalQuestions: number;
  totalMedia: number;
}

export interface ChartData {
  name: string;
  value: number;
  color?: string;
}

export interface RecentActivity {
  id: number;
  type: "user" | "post" | "question" | "media";
  title: string;
  description: string;
  timestamp: string;
  user: string;
}

// New interfaces for Data Warehouse
export interface RecordStats {
  newRecordsToday: number;
  newRecordsThisWeek: number;
  newRecordsThisMonth: number;
  processingRecords: number;
  averageProcessingTime: number; // in hours
  completedRecordsWeek: number;
  completedRecordsMonth: number;
  rejectedRecords: number;
  rejectedPercentage: number;
  storageUsed: number; // in GB
  storageTotal: number; // in GB
  storagePercentage: number;
}

export interface ProcessingTrendData extends ChartData {
  date: string;
}

export interface RecordStatusData extends ChartData {
  status: "new" | "processing" | "completed" | "rejected";
}

export interface RecordTypeData extends ChartData {
  type: string;
}

export interface AlertNotification {
  id: number;
  type: "overdue" | "error" | "warning" | "info";
  title: string;
  message: string;
  timestamp: string;
  isUrgent: boolean;
}

export interface CalendarEvent {
  id: number;
  date: string;
  title: string;
  type: "deadline" | "meeting" | "reminder";
  description?: string;
}

export interface DataWarehouseData {
  recordStats: RecordStats;
  processingTrend: ProcessingTrendData[];
  recordStatus: RecordStatusData[];
  recordTypes: RecordTypeData[];
  alerts: AlertNotification[];
  calendarEvents: CalendarEvent[];
}

export interface DashboardData {
  stats: DashboardStats;
  userGrowth: ChartData[];
  contentDistribution: ChartData[];
  recentActivities: RecentActivity[];
  dataWarehouse: DataWarehouseData;
}

// Mock data generator
export const generateMockDashboardData = (): DashboardData => {
  return {
    stats: {
      totalUsers: 1248,
      totalPosts: 342,
      totalQuestions: 89,
      totalMedia: 567,
    },
    userGrowth: [
      { name: "T1", value: 120 },
      { name: "T2", value: 98 },
      { name: "T3", value: 156 },
      { name: "T4", value: 203 },
      { name: "T5", value: 189 },
      { name: "T6", value: 234 },
      { name: "T7", value: 278 },
    ],
    contentDistribution: [
      { name: "Bài viết", value: 342, color: "#3B82F6" },
      { name: "Hỏi đáp", value: 89, color: "#10B981" },
      { name: "Hình ảnh", value: 567, color: "#F59E0B" },
      { name: "Khác", value: 45, color: "#EF4444" },
    ],
    recentActivities: [
      {
        id: 1,
        type: "user",
        title: "Người dùng mới đăng ký",
        description: "Nguyễn Văn A đã tạo tài khoản mới",
        timestamp: "2 phút trước",
        user: "Nguyễn Văn A",
      },
      {
        id: 2,
        type: "post",
        title: "Bài viết mới được đăng",
        description: "Hướng dẫn đầu tư cryptocurrency cho người mới",
        timestamp: "15 phút trước",
        user: "Admin",
      },
      {
        id: 3,
        type: "question",
        title: "Câu hỏi mới",
        description: "Làm thế nào để bắt đầu đầu tư DeFi?",
        timestamp: "1 giờ trước",
        user: "Trần Thị B",
      },
      {
        id: 4,
        type: "media",
        title: "Hình ảnh được tải lên",
        description: "Biểu đồ phân tích thị trường Q4",
        timestamp: "2 giờ trước",
        user: "Editor",
      },
      {
        id: 5,
        type: "user",
        title: "Cập nhật hồ sơ",
        description: "Lê Văn C đã cập nhật thông tin cá nhân",
        timestamp: "3 giờ trước",
        user: "Lê Văn C",
      },
    ],
    dataWarehouse: {
      recordStats: {
        newRecordsToday: 23,
        newRecordsThisWeek: 156,
        newRecordsThisMonth: 678,
        processingRecords: 45,
        averageProcessingTime: 2.5,
        completedRecordsWeek: 234,
        completedRecordsMonth: 1024,
        rejectedRecords: 12,
        rejectedPercentage: 1.2,
        storageUsed: 2.8,
        storageTotal: 10.0,
        storagePercentage: 28,
      },
      processingTrend: [
        { name: "1/6", value: 34, date: "2024-06-01" },
        { name: "2/6", value: 28, date: "2024-06-02" },
        { name: "3/6", value: 45, date: "2024-06-03" },
        { name: "4/6", value: 38, date: "2024-06-04" },
        { name: "5/6", value: 52, date: "2024-06-05" },
        { name: "6/6", value: 41, date: "2024-06-06" },
        { name: "7/6", value: 48, date: "2024-06-07" },
      ],
      recordStatus: [
        { name: "Mới", value: 89, color: "#3B82F6", status: "new" },
        {
          name: "Đang xử lý",
          value: 45,
          color: "#F59E0B",
          status: "processing",
        },
        { name: "Đã xử lý", value: 234, color: "#10B981", status: "completed" },
        { name: "Bị từ chối", value: 12, color: "#EF4444", status: "rejected" },
      ],
      recordTypes: [
        {
          name: "Đăng ký mới",
          value: 156,
          color: "#3B82F6",
          type: "registration",
        },
        { name: "Cập nhật hồ sơ", value: 89, color: "#10B981", type: "update" },
        { name: "Đổi thông tin", value: 67, color: "#F59E0B", type: "change" },
        { name: "Hủy bỏ", value: 23, color: "#EF4444", type: "cancellation" },
        { name: "Khác", value: 45, color: "#8B5CF6", type: "other" },
      ],
      alerts: [
        {
          id: 1,
          type: "overdue",
          title: "Hồ sơ quá hạn xử lý",
          message: "5 hồ sơ đã quá hạn xử lý hơn 24 giờ",
          timestamp: "30 phút trước",
          isUrgent: true,
        },
        {
          id: 2,
          type: "error",
          title: "Lỗi dữ liệu",
          message: "3 hồ sơ có dữ liệu thiếu hoặc không hợp lệ",
          timestamp: "1 giờ trước",
          isUrgent: true,
        },
        {
          id: 3,
          type: "warning",
          title: "Cảnh báo dung lượng",
          message: "Dung lượng kho dữ liệu đã sử dụng 28%",
          timestamp: "2 giờ trước",
          isUrgent: false,
        },
        {
          id: 4,
          type: "info",
          title: "Cập nhật hệ thống",
          message: "Hệ thống sẽ được bảo trì vào 2h sáng ngày mai",
          timestamp: "3 giờ trước",
          isUrgent: false,
        },
      ],
      calendarEvents: [
        {
          id: 1,
          date: "2024-06-10",
          title: "Deadline xử lý batch #1023",
          type: "deadline",
          description: "Hoàn thành xử lý 50 hồ sơ đăng ký mới",
        },
        {
          id: 2,
          date: "2024-06-12",
          title: "Họp đánh giá tiến độ",
          type: "meeting",
          description: "Họp team review tiến độ xử lý hồ sơ tháng 6",
        },
        {
          id: 3,
          date: "2024-06-15",
          title: "Báo cáo định kỳ",
          type: "reminder",
          description: "Gửi báo cáo thống kê hàng tháng",
        },
        {
          id: 4,
          date: "2024-06-18",
          title: "Deadline xử lý batch #1045",
          type: "deadline",
          description: "Hoàn thành xử lý 75 hồ sơ cập nhật thông tin",
        },
      ],
    },
  };
};
