import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>Title,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Loader2, Upload, Search } from "lucide-react";
import { ImageUploader } from "@/components/form/upload/ImageUploader";
import { AlbumSelectionGrid } from "./AlbumSelectionGrid";
import { AlbumMediaGrid } from "./AlbumMediaGrid";
import { Album, AlbumType, CreateMediaRequest } from "../states/types";

interface AddMediaModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmitNewMedia: (
    data: Omit<CreateMediaRequest, "albumId">
  ) => Promise<void>;
  isLoading?: boolean;
  albumType: AlbumType;
  currentAlbumId?: number;
}

export function AddMediaModal({
  isOpen,
  onClose,
  onSubmitNewMedia,
  isLoading = false,
  albumType,
  currentAlbumId,
}: AddMediaModalProps) {
  const [activeTab, setActiveTab] = useState<"upload" | "browse">("upload");

  // Upload tab state
  const [uploadData, setUploadData] = useState({
    name: "",
    src: "",
    description: "",
  });

  // Browse tab state
  const [selectedAlbum, setSelectedAlbum] = useState<Album | null>(null);
  const [browseView, setBrowseView] = useState<"albums" | "media">("albums");

  const handleUploadSuccess = (fileUrl: string) => {
    setUploadData((prev) => ({ ...prev, src: fileUrl }));
  };

  const handleSubmitUpload = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validation
    if (
      !uploadData.src ||
      !uploadData.name.trim() ||
      !uploadData.description.trim()
    ) {
      return;
    }

    try {
      const submitData = {
        type: albumType,
        name: uploadData.name.trim(),
        src: uploadData.src,
        description: { text: uploadData.description.trim() },
      };

      await onSubmitNewMedia(submitData);
      handleClose();
    } catch {
      // Error will be handled by the parent component
    }
  };



  const handleSelectAlbum = (album: Album) => {
    setSelectedAlbum(album);
    setBrowseView("media");
  };

  const handleBackToAlbums = () => {
    setBrowseView("albums");
    setSelectedAlbum(null);
  };

  const handleMediaAdded = () => {
    // Optionally refresh the parent component or show success feedback
    // The toast notification is already handled in AlbumMediaGrid
  };

  const handleClose = () => {
    // Reset all states
    setActiveTab("upload");
    setUploadData({ name: "", src: "", description: "" });
    setSelectedAlbum(null);
    setBrowseView("albums");
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[800px] max-h-[80vh] overflow-hidden bg-white">
        <DialogHeader>
          <DialogTitle>Thêm ảnh mới</DialogTitle>
        </DialogHeader>

        <Tabs
          value={activeTab}
          onValueChange={(value) => setActiveTab(value as "upload" | "browse")}
        >
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="upload" className="flex items-center gap-2">
              <Upload className="w-4 h-4" />
              Tải lên từ máy
            </TabsTrigger>
            <TabsTrigger
              value="browse"
              className="flex items-center gap-2"
            >
              <Search className="w-4 h-4" />
              Chọn từ thư viện khác
            </TabsTrigger>
          </TabsList>

          {/* Upload Tab */}
          <TabsContent value="upload" className="space-y-4">
            <form onSubmit={handleSubmitUpload} className="space-y-4">
              {/* Image Uploader */}
              <div className="space-y-2">
                <Label>Chọn ảnh từ máy tính *</Label>
                <div className="flex justify-center">
                  <ImageUploader
                    onUploadSuccess={handleUploadSuccess}
                    onUploadError={() => { }}
                    placeholder="Click để chọn và tải ảnh lên"
                    className="w-full max-w-xs"
                  />
                </div>
              </div>

              {/* Name field - only show when image is uploaded */}
              {uploadData.src && (
                <>
                  <div className="space-y-2">
                    <Label htmlFor="upload-name">Tên ảnh *</Label>
                    <Input
                      id="upload-name"
                      value={uploadData.name}
                      onChange={(e) =>
                        setUploadData((prev) => ({
                          ...prev,
                          name: e.target.value,
                        }))
                      }
                      placeholder="Nhập tên ảnh"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="upload-description">Mô tả *</Label>
                    <Textarea
                      id="upload-description"
                      value={uploadData.description}
                      onChange={(e) =>
                        setUploadData((prev) => ({
                          ...prev,
                          description: e.target.value,
                        }))
                      }
                      placeholder="Nhập mô tả cho ảnh (bắt buộc)"
                      rows={3}
                      required
                    />
                  </div>

                  <div className="flex justify-end space-x-2 pt-4">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleClose}
                    >
                      Hủy
                    </Button>
                    <Button
                      type="submit"
                      disabled={
                        isLoading ||
                        !uploadData.src ||
                        !uploadData.name.trim() ||
                        !uploadData.description.trim()
                      }
                    >
                      {isLoading && (
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      )}
                      Thêm vào thư viện
                    </Button>
                  </div>
                </>
              )}
            </form>
          </TabsContent>

          {/* Browse Tab */}
          <TabsContent value="browse" className="space-y-4">
            <div className="h-[60vh] overflow-hidden">
              {browseView === "albums" ? (
                <div className="h-full overflow-y-auto">
                  <AlbumSelectionGrid
                    albumType={albumType}
                    currentAlbumId={currentAlbumId}
                    onSelectAlbum={handleSelectAlbum}
                  />
                </div>
              ) : selectedAlbum && currentAlbumId ? (
                <div className="h-full overflow-y-auto">
                  <AlbumMediaGrid
                    album={selectedAlbum}
                    currentAlbumId={currentAlbumId}
                    onBack={handleBackToAlbums}
                    onMediaAdded={handleMediaAdded}
                  />
                </div>
              ) : null}
            </div>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
