import { useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Plus, ArrowLeft } from "lucide-react";
import { useAppSelector, useAppDispatch } from "@/store/rootReducer";
import { selectCurrentResource } from "../states/selectors";
import {
  setCurrentResource,
  fetchResourceByIdThunk,
  clearError,
} from "../states/slices";
import { ResourceTable } from "../components/ResourceTable";
import { ResourceForm } from "../components/ResourceForm";
import { useNavigate, useSearchParams } from "react-router-dom";
import { ResourcePageMode, ResourceAction } from "../states/types";

const ResourceAdminPage = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  const currentResource = useAppSelector(selectCurrentResource);
  const deletedIds = useAppSelector((state) => state.resourceState.deletedIds);

  // Derive all state from URL - Single Source of Truth
  const mode = (searchParams.get("mode") as ResourcePageMode) || "list";
  const actionType =
    (searchParams.get("actionType") as ResourceAction) || "view";
  const resourceId = searchParams.get("resource");

  // Helper function to update URL
  const updateURL = (
    newMode: ResourcePageMode,
    newResourceId?: string,
    newActionType?: ResourceAction
  ) => {
    const params = new URLSearchParams();
    params.set("mode", newMode);

    if (newResourceId && newResourceId !== "0") {
      params.set("resource", newResourceId);
    }

    if (newActionType && newActionType !== "view") {
      params.set("actionType", newActionType);
    }

    navigate(`?${params.toString()}`, { replace: true });
  };

  // Data fetching logic
  useEffect(() => {
    if (mode === "detail" && resourceId && resourceId !== "0") {
      const id = parseInt(resourceId);
      if (!isNaN(id)) {
        // Check if resource was deleted
        if (deletedIds.includes(id)) {
          updateURL("list");
          return;
        }

        // Fetch if we don't have current resource or it's different ID
        if (!currentResource || currentResource.id !== id) {
          dispatch(fetchResourceByIdThunk(id))
            .unwrap()
            .catch(() => {
              dispatch(setCurrentResource(null));
              updateURL("list");
            });
        }
      } else {
        updateURL("list");
      }
    } else if (mode === "list") {
      if (currentResource) {
        dispatch(setCurrentResource(null));
      }
    }
  }, [mode, resourceId, dispatch, currentResource, deletedIds]);

  useEffect(() => {
    return () => {
      dispatch(clearError());
    };
  }, [dispatch]);

  const handleCreateNew = () => {
    dispatch(
      setCurrentResource({
        id: 0,
        code: "",
        name: "",
        metadata: {},
        actions: [],
        createdAt: 0,
        updatedAt: 0,
      })
    );
    updateURL("detail", "0", "create");
  };

  const handleBackToList = () => {
    dispatch(setCurrentResource(null));
    updateURL("list");
  };

  const handleActionChange = (action: ResourceAction) => {
    if (currentResource) {
      updateURL("detail", String(currentResource.id), action);
    }
  };

  if (mode === "detail") {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold">
            {resourceId === "0" ? "Tạo nhóm quyền mới" : "Chi tiết nhóm quyền"}
          </h1>
          <Button className="w-30" onClick={handleBackToList}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Quay lại
          </Button>
        </div>

        <ResourceForm
          currentAction={actionType}
          onActionChange={handleActionChange}
        />
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Quản lý nhóm quyền</h1>
        <Button className="w-30" onClick={handleCreateNew}>
          <Plus className="w-4 h-4 mr-2" />
          Tạo mới
        </Button>
      </div>

      <ResourceTable />
    </div>
  );
};

export default ResourceAdminPage;
