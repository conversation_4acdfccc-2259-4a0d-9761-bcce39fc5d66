import React, { useState, useEffect } from "react";
import { useAppDispatch, useAppSelector } from "@/store/rootReducer";
import { selectSelectedGroup, selectGroupLoading } from "../states/selectors";
import { updateGroupThunk } from "../states/slices";
import { Button } from "@/components/ui/button";
import { Save, Pencil, X, Plus, Trash2 } from "lucide-react";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";

interface GroupMetadataProps {
  currentAction: "view" | "edit" | "create";
  onActionChange: (action: "view" | "edit" | "create") => void;
}

export const GroupMetadata: React.FC<GroupMetadataProps> = ({
  currentAction,
  onActionChange,
}) => {
  const dispatch = useAppDispatch();
  const selectedGroup = useAppSelector(selectSelectedGroup);
  const loading = useAppSelector(selectGroupLoading);

  const [metadata, setMetadata] = useState<
    Record<string, string | number | boolean>
  >({});
  const [newField, setNewField] = useState({
    key: "",
    value: "",
    type: "string" as "string" | "number" | "boolean",
  });

  useEffect(() => {
    if (selectedGroup && selectedGroup.metadata) {
      setMetadata(selectedGroup.metadata);
    } else {
      setMetadata({});
    }
  }, [selectedGroup]);

  const isEditMode = currentAction === "edit";
  const isViewMode = currentAction === "view";

  const handleSave = async () => {
    if (!selectedGroup) return;

    await dispatch(
      updateGroupThunk({
        id: selectedGroup.id,
        payload: {
          metadata: metadata,
        },
      })
    );

    onActionChange("view");
  };

  const handleAddField = () => {
    if (!newField.key) return;

    let value: string | number | boolean = newField.value;

    // Convert value based on type
    if (newField.type === "number") {
      value = parseFloat(newField.value) || 0;
    } else if (newField.type === "boolean") {
      value = newField.value.toLowerCase() === "true";
    }

    setMetadata((prev) => ({
      ...prev,
      [newField.key]: value,
    }));

    setNewField({ key: "", value: "", type: "string" });
  };

  const handleRemoveField = (key: string) => {
    setMetadata((prev) => {
      const newMetadata = { ...prev };
      delete newMetadata[key];
      return newMetadata;
    });
  };

  const handleUpdateField = (
    key: string,
    value: string,
    type: "string" | "number" | "boolean"
  ) => {
    let convertedValue: string | number | boolean = value;

    if (type === "number") {
      convertedValue = parseFloat(value) || 0;
    } else if (type === "boolean") {
      convertedValue = value.toLowerCase() === "true";
    }

    setMetadata((prev) => ({
      ...prev,
      [key]: convertedValue,
    }));
  };

  if (!selectedGroup) {
    return (
      <div className="p-6 text-center text-gray-500">
        Chọn một nhóm để quản lý metadata
      </div>
    );
  }

  const metadataEntries = Object.entries(metadata);

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold">Metadata</h3>

        <div className="flex space-x-2">
          {isViewMode && (
            <Button onClick={() => onActionChange("edit")} disabled={loading}>
              <Pencil className="h-4 w-4 mr-2" />
              Chỉnh sửa
            </Button>
          )}

          {isEditMode && (
            <>
              <Button
                variant="outline"
                onClick={() => onActionChange("view")}
                disabled={loading}
              >
                <X className="h-4 w-4 mr-2" />
                Hủy
              </Button>
              <Button onClick={handleSave} disabled={loading}>
                <Save className="h-4 w-4 mr-2" />
                Lưu
              </Button>
            </>
          )}
        </div>
      </div>

      {/* Metadata Table */}
      {metadataEntries.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          {isEditMode
            ? "Chưa có metadata nào. Thêm metadata mới bên dưới."
            : "Chưa có metadata nào"}
        </div>
      ) : (
        <div className="border rounded-lg mb-6">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Khóa</TableHead>
                <TableHead>Giá trị</TableHead>
                <TableHead>Kiểu</TableHead>
                {isEditMode && (
                  <TableHead className="text-right">Hành động</TableHead>
                )}
              </TableRow>
            </TableHeader>
            <TableBody>
              {metadataEntries.map(([key, value]) => {
                const valueType = typeof value;
                return (
                  <TableRow key={key}>
                    <TableCell className="font-medium">{key}</TableCell>
                    <TableCell>
                      {isEditMode ? (
                        <Input
                          value={value.toString()}
                          onChange={(e) =>
                            handleUpdateField(
                              key,
                              e.target.value,
                              valueType as "string" | "number" | "boolean"
                            )
                          }
                          disabled={loading}
                        />
                      ) : (
                        value.toString()
                      )}
                    </TableCell>
                    <TableCell>
                      <span className="px-2 py-1 bg-gray-100 rounded text-sm">
                        {valueType}
                      </span>
                    </TableCell>
                    {isEditMode && (
                      <TableCell className="text-right">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleRemoveField(key)}
                          disabled={loading}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    )}
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </div>
      )}

      {/* Add New Field */}
      {isEditMode && (
        <div className="border rounded-lg p-4">
          <h4 className="font-medium mb-3">Thêm metadata mới</h4>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-3 items-end">
            <div>
              <Label htmlFor="fieldKey">Khóa</Label>
              <Input
                id="fieldKey"
                placeholder="Tên khóa"
                value={newField.key}
                onChange={(e) =>
                  setNewField((prev) => ({ ...prev, key: e.target.value }))
                }
                disabled={loading}
              />
            </div>

            <div>
              <Label htmlFor="fieldValue">Giá trị</Label>
              <Input
                id="fieldValue"
                placeholder="Giá trị"
                value={newField.value}
                onChange={(e) =>
                  setNewField((prev) => ({ ...prev, value: e.target.value }))
                }
                disabled={loading}
              />
            </div>

            <div>
              <Label htmlFor="fieldType">Kiểu</Label>
              <Select
                value={newField.type}
                onValueChange={(value: "string" | "number" | "boolean") =>
                  setNewField((prev) => ({ ...prev, type: value }))
                }
                disabled={loading}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="string">String</SelectItem>
                  <SelectItem value="number">Number</SelectItem>
                  <SelectItem value="boolean">Boolean</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Button
              onClick={handleAddField}
              disabled={!newField.key || loading}
            >
              <Plus className="h-4 w-4 mr-2" />
              Thêm
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};
