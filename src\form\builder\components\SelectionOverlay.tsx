/**
 * Selection Overlay System - Figma Style with Portal
 * Uses React Portal for accurate viewport positioning
 */

import React, { useEffect, useState, useCallback } from "react";
import { createPortal } from "react-dom";
import { useFormBuilder } from "../context/FormBuilderContext";

interface OverlayBounds {
  top: number;
  left: number;
  width: number;
  height: number;
}

/**
 * Portal-based Selection Overlay for accurate positioning
 */
export const SelectionOverlay: React.FC<{ 
  canvasRef: React.RefObject<HTMLDivElement> 
}> = ({ canvasRef }) => {
  const { state, selectNode } = useFormBuilder();
  const [overlayBounds, setOverlayBounds] = useState<OverlayBounds | null>(null);

  // Update overlay position using viewport coordinates
  const updateOverlayPosition = useCallback(() => {
    // Only show overlay in design mode
    if (state.editor.mode !== 'design' || !state.editor.selectedNodeId || !canvasRef.current) {
      setOverlayBounds(null);
      return;
    }

    // Find target element with data-node-id
    const targetElement = canvasRef.current.querySelector(
      `[data-node-id="${state.editor.selectedNodeId}"]`
    ) as HTMLElement;

    if (!targetElement) {
      setOverlayBounds(null);
      return;
    }

    // Use getBoundingClientRect for accurate viewport coordinates
    const targetRect = targetElement.getBoundingClientRect();

    // Add 2px offset to not cover borders
    const offset = 2;
    const bounds: OverlayBounds = {
      top: targetRect.top + window.scrollY - offset,
      left: targetRect.left + window.scrollX - offset,
      width: targetRect.width + (offset * 2),
      height: targetRect.height + (offset * 2),
    };

    setOverlayBounds(bounds);
  }, [state.editor.selectedNodeId, canvasRef]);

  // Update overlay on selection change or layout changes
  useEffect(() => {
    updateOverlayPosition();

    // Re-calculate on window resize
    const handleResize = () => updateOverlayPosition();
    window.addEventListener('resize', handleResize);

    // Re-calculate on window scroll
    const handleScroll = () => updateOverlayPosition();
    window.addEventListener('scroll', handleScroll, true); // Capture phase for all scrolls
    
    // Re-calculate on canvas scroll
    const canvas = canvasRef.current;
    if (canvas) {
      canvas.addEventListener('scroll', handleScroll);
    }

    // Re-calculate when canvas content changes (using ResizeObserver)
    let resizeObserver: ResizeObserver | null = null;
    if (canvasRef.current) {
      resizeObserver = new ResizeObserver(() => updateOverlayPosition());
      resizeObserver.observe(canvasRef.current);
      
      // Also observe all child elements for content changes
      const selectedElement = canvasRef.current.querySelector(
        `[data-node-id="${state.editor.selectedNodeId}"]`
      );
      if (selectedElement) {
        resizeObserver.observe(selectedElement);
      }
    }
    
    // MutationObserver for DOM changes
    let mutationObserver: MutationObserver | null = null;
    if (canvasRef.current) {
      mutationObserver = new MutationObserver(() => {
        // Debounce updates
        requestAnimationFrame(() => updateOverlayPosition());
      });
      mutationObserver.observe(canvasRef.current, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['style', 'class']
      });
    }

    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('scroll', handleScroll, true);
      canvas?.removeEventListener('scroll', handleScroll);
      resizeObserver?.disconnect();
      mutationObserver?.disconnect();
    };
  }, [updateOverlayPosition, state.editor.selectedNodeId, state.editor.mode]);

  // Canvas click delegation system - only in design mode
  useEffect(() => {
    const handleCanvasClick = (e: MouseEvent) => {
      // Only handle clicks in design mode and within our canvas
      if (state.editor.mode !== 'design' || !canvasRef.current?.contains(e.target as Node)) return;

      // Walk up DOM tree to find data-node-id
      let currentElement = e.target as HTMLElement;
      let foundNodeId: string | null = null;

      while (currentElement && currentElement !== canvasRef.current) {
        const nodeId = currentElement.getAttribute('data-node-id');
        if (nodeId) {
          foundNodeId = nodeId;
          break;
        }
        currentElement = currentElement.parentElement!;
      }

      // Delegate selection
      if (foundNodeId) {
        selectNode(foundNodeId);
        e.stopPropagation();
      } else {
        // Clicked on canvas background - select root
        selectNode('form-root');
      }
    };

    // Attach to document to catch all clicks
    document.addEventListener('click', handleCanvasClick);
    return () => document.removeEventListener('click', handleCanvasClick);
  }, [canvasRef, selectNode, state.editor.mode]);

  // Don't render if not in design mode, no selection or bounds
  if (state.editor.mode !== 'design' || !overlayBounds || !state.editor.selectedNodeId) {
    return null;
  }

  // Portal the overlay to document.body for accurate positioning
  const overlayElement = (
    <div
      className="fixed pointer-events-none z-[10]"  // Lower than dialogs/popovers/forms
      style={{
        top: overlayBounds.top,
        left: overlayBounds.left,
        width: overlayBounds.width,
        height: overlayBounds.height,
      }}
    >
      {/* Simple Figma-style blue outline */}
      <div 
        className="absolute inset-0 border-2 border-blue-500 rounded-sm"
        style={{
          boxShadow: '0 0 0 1px rgba(59, 130, 246, 0.2)'
        }}
      />
      
      {/* Corner handles for visual feedback - adjusted for offset */}
      <div className="absolute top-0 left-0 w-2 h-2 bg-blue-500 rounded-full border border-white -translate-x-1 -translate-y-1" />
      <div className="absolute top-0 right-0 w-2 h-2 bg-blue-500 rounded-full border border-white translate-x-1 -translate-y-1" />
      <div className="absolute bottom-0 left-0 w-2 h-2 bg-blue-500 rounded-full border border-white -translate-x-1 translate-y-1" />
      <div className="absolute bottom-0 right-0 w-2 h-2 bg-blue-500 rounded-full border border-white translate-x-1 translate-y-1" />
    </div>
  );

  // Render via Portal to document.body
  return createPortal(overlayElement, document.body);
};