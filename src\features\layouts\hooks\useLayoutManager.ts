import { useEffect } from "react";
import { useAppDispatch, useAppSelector } from "@/store/rootReducer";
import { setScreenSizes } from "../states/slices";
import {
  selectSidebarVisible,
  selectSidebarLocked,
  selectMobileMenuOpen,
  selectUserMenuOpen,
  selectShowPublicMenu,
  selectHeaderHeight,
  selectIsMobile,
  selectIsDesktop,
  selectIsVeryWideScreen,
  selectCanToggleSidebar,
  selectShowSidebarToggle,
} from "../states/selector";

export const useLayoutManager = () => {
  const dispatch = useAppDispatch();

  // Selectors
  const sidebarVisible = useAppSelector(selectSidebarVisible);
  const sidebarLocked = useAppSelector(selectSidebarLocked);
  const mobileMenuOpen = useAppSelector(selectMobileMenuOpen);
  const userMenuOpen = useAppSelector(selectUserMenuOpen);
  const showPublicMenu = useAppSelector(selectShowPublicMenu);
  const headerHeight = useAppSelector(selectHeaderHeight);
  const isMobile = useAppSelector(selectIsMobile);
  const isDesktop = useAppSelector(selectIsDesktop);
  const isVeryWideScreen = useAppSelector(selectIsVeryWideScreen);
  const canToggleSidebar = useAppSelector(selectCanToggleSidebar);
  const showSidebarToggle = useAppSelector(selectShowSidebarToggle);

  // Screen size detection
  useEffect(() => {
    const updateScreenSizes = () => {
      const width = window.innerWidth;

      const screenSizes = {
        isMobile: width <= 768, // iPad mini and below
        isTablet: width > 768 && width < 1024,
        isDesktop: width >= 1024,
        isVeryWideScreen: width >= 1440,
      };

      dispatch(setScreenSizes(screenSizes));
    };

    // Initial check
    updateScreenSizes();

    // Listen for resize
    window.addEventListener("resize", updateScreenSizes);

    return () => {
      window.removeEventListener("resize", updateScreenSizes);
    };
  }, [dispatch]);

  return {
    // State
    sidebarVisible,
    sidebarLocked,
    mobileMenuOpen,
    userMenuOpen,
    showPublicMenu,
    headerHeight,
    isMobile,
    isDesktop,
    isVeryWideScreen,
    canToggleSidebar,
    showSidebarToggle,
  };
};
