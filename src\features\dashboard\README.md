# Dashboard Feature

## 📊 Overview

Dashboard feature cung cấp trang tổng quan hệ thống với các thống kê, biểu đồ và hoạt động gần đây. Feature này sử dụng dữ liệu mock để demonstate chức năng.

## 🏗️ Structure

```
src/features/dashboard/
├── components/          # Dashboard components
│   ├── StatsCard.tsx   # Card hiển thị thống kê
│   ├── RecentActivities.tsx # Danh sách hoạt động gần đây
│   └── SimpleChart.tsx # Component biểu đồ đơn giản
├── data/               # Mock data
│   └── mockData.ts     # Dữ liệu mô phỏng
├── pages/              # Dashboard pages
│   ├── DashboardPage.tsx # Trang chính dashboard
│   └── loadable.tsx    # Lazy loading components
└── index.ts            # Feature exports
```

## 🎯 Features

### 📈 Statistics Cards

- Tổng số người dùng
- Số lượng bài viết
- Số câu hỏi từ người dùng
- Số lượng media
- Hiển thị trend (tăng/giảm)

### 📊 Charts

- **User Growth Chart**: Biểu đồ tăng trưởng người dùng theo tuần
- **Content Distribution**: Phân bố nội dung theo loại (pie chart)

### 🔔 Recent Activities

- Hoạt động đăng ký người dùng mới
- Bài viết mới được đăng
- Câu hỏi mới từ người dùng
- Media được tải lên
- Cập nhật hồ sơ

## 🔧 Components

### StatsCard

```tsx
<StatsCard
  title="Tổng người dùng"
  value={1248}
  icon={Users}
  description="Người dùng đã đăng ký"
  trend={{ value: 12.5, isPositive: true }}
/>
```

### SimpleChart

```tsx
<SimpleChart
  title="Người dùng theo tuần"
  data={chartData}
  type="bar" // hoặc "pie"
/>
```

### RecentActivities

```tsx
<RecentActivities activities={recentActivities} />
```

## 📊 Mock Data

Dashboard sử dụng `generateMockDashboardData()` để tạo dữ liệu mô phỏng:

```typescript
interface DashboardData {
  stats: DashboardStats;
  userGrowth: ChartData[];
  contentDistribution: ChartData[];
  recentActivities: RecentActivity[];
}
```

## 🎨 UI/UX Features

- **Responsive Design**: Tương thích với mobile và desktop
- **Loading States**: Skeleton loading khi tải dữ liệu
- **Error Handling**: Xử lý lỗi khi không tải được dữ liệu
- **Modern UI**: Sử dụng Tailwind CSS và Radix UI components

## 🚀 Usage

Dashboard được đăng ký trong ComponentRegistry và có thể được sử dụng trong:

1. **Component Picker**: Chọn "Dashboard" trong danh sách components
2. **Direct Import**: Import trực tiếp từ feature
3. **Route Configuration**: Cấu hình route cho dashboard

## 🔄 Future Enhancements

- [ ] Kết nối với API thực tế
- [ ] Thêm filter theo thời gian
- [ ] Export dữ liệu dashboard
- [ ] Customizable dashboard widgets
- [ ] Real-time updates với WebSocket
- [ ] Advanced charting với thư viện chuyên dụng

## 📝 Notes

- Dashboard hiện tại sử dụng mock data
- Không có API endpoints thực tế
- Dữ liệu được refresh mỗi khi load trang
- Tất cả components đều responsive và accessible
