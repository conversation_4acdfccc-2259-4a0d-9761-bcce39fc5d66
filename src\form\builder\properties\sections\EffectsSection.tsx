/**
 * Effects Section Component
 * For shadow and other visual effects
 */

import React from 'react';
import { FormNode } from '@/form/types';
import { PropertiesFormProps } from '../types';
import { Label } from '@/components/ui/label';
import { ShadowPopover } from '../input/ShadowPopover';
import { SelectPopover } from '../input/SelectPopover';
import { ColorPicker } from '../input/ColorPicker';

export const EffectsSection: React.FC<PropertiesFormProps> = ({
  node,
  onChange,
  disabled = false
}) => {
  const currentClasses = node.styles?.container || '';

  // Shadow type options
  const shadowTypeOptions = [
    { value: 'box', label: 'Box Shadow', description: 'Shadow bên ngoài' },
    { value: 'drop', label: 'Drop Shadow', description: 'Shadow theo hình dạng' }
  ];

  // Shadow size options
  const shadowSizeOptions = [
    { value: 'none', label: 'Không có' },
    { value: 'sm', label: 'Small' },
    { value: 'default', label: 'Default' },
    { value: 'md', label: 'Medium' },
    { value: 'lg', label: 'Large' },
    { value: 'xl', label: 'Extra Large' },
    { value: '2xl', label: '2X Large' },
    { value: 'inner', label: 'Inner Shadow' }
  ];

  // Extract current shadow type - more precise logic
  const getCurrentShadowType = () => {
    const classes = currentClasses.split(' ');
    
    // Check if any class starts with 'drop-shadow'
    for (const cls of classes) {
      if (cls.startsWith('drop-shadow')) {
        return 'drop';
      }
    }
    
    // Default to box shadow
    return 'box';
  };

  // Extract current shadow size - more precise logic
  const getCurrentShadow = () => {
    const classes = currentClasses.split(' ');
    
    // Check for drop-shadow first (more specific)
    for (const cls of classes) {
      if (cls === 'drop-shadow') return 'default';
      const dropMatch = cls.match(/^drop-shadow-(.+)$/);
      if (dropMatch) {
        return dropMatch[1]; // sm, md, lg, xl, 2xl, none
      }
    }
    
    // Check for box shadow
    for (const cls of classes) {
      if (cls === 'shadow') return 'default';
      const boxMatch = cls.match(/^shadow-(.+)$/);
      if (boxMatch) {
        const sizePart = boxMatch[1];
        
        // Only return if it's a valid size (not a color)
        if (['none', 'sm', 'md', 'lg', 'xl', '2xl', 'inner'].includes(sizePart)) {
          return sizePart;
        }
      }
    }
    
    return 'none';
  };

  // Extract current shadow color - more precise logic
  const getCurrentShadowColor = () => {
    // Look for shadow color patterns, excluding size-only patterns
    const classes = currentClasses.split(' ');
    
    for (const cls of classes) {
      // Match shadow-[color] but exclude shadow size classes
      const colorMatch = cls.match(/^shadow-(.+)$/);
      if (colorMatch) {
        const colorPart = colorMatch[1];
        
        // Exclude size-only classes
        if (['none', 'sm', 'md', 'lg', 'xl', '2xl', 'inner'].includes(colorPart)) {
          continue;
        }
        
        // Exclude 'shadow' without suffix (this is default size)
        if (colorPart === '') {
          continue;
        }
        
        // This should be a color (e.g., red-500, blue-600, etc.)
        return colorPart;
      }
    }
    
    return 'gray-500';
  };

  // Update shadow type
  const updateShadowType = (shadowType: string) => {
    let classArray = currentClasses.split(' ').filter(cls => cls.trim() !== '');
    const currentSize = getCurrentShadow();
    const currentColor = getCurrentShadowColor();

    // Remove existing shadow classes
    classArray = classArray.filter(cls => 
      !cls.match(/^(shadow|shadow-\w+|drop-shadow|drop-shadow-\w+)$/)
    );

    // Add new shadow classes based on type
    if (currentSize !== 'none') {
      if (shadowType === 'drop') {
        if (currentSize === 'default') {
          classArray.push('drop-shadow');
        } else if (currentSize !== 'inner') { // Drop shadow doesn't have inner
          classArray.push(`drop-shadow-${currentSize}`);
        }
      } else {
        // Box shadow
        if (currentSize === 'default') {
          classArray.push('shadow');
        } else {
          classArray.push(`shadow-${currentSize}`);
        }
        // Add color for box shadow
        if (currentColor !== 'gray-500') {
          classArray.push(`shadow-${currentColor}`);
        }
      }
    }

    const newClasses = classArray.join(' ').trim();

    onChange({
      styles: {
        ...node.styles,
        container: newClasses
      }
    });
  };

  // Update shadow size
  const updateShadow = (shadow: string) => {
    let classArray = currentClasses.split(' ').filter(cls => cls.trim() !== '');
    const currentType = getCurrentShadowType();
    const currentColor = getCurrentShadowColor();

    // Remove existing shadow classes
    classArray = classArray.filter(cls => 
      !cls.match(/^(shadow|shadow-\w+|drop-shadow|drop-shadow-\w+)$/)
    );

    // Add new shadow classes
    if (shadow && shadow !== 'none') {
      if (currentType === 'drop') {
        if (shadow === 'default') {
          classArray.push('drop-shadow');
        } else if (shadow !== 'inner') { // Drop shadow doesn't have inner
          classArray.push(`drop-shadow-${shadow}`);
        }
      } else {
        // Box shadow
        if (shadow === 'default') {
          classArray.push('shadow');
        } else {
          classArray.push(`shadow-${shadow}`);
        }
        // Add color for box shadow
        if (currentColor !== 'gray-500') {
          classArray.push(`shadow-${currentColor}`);
        }
      }
    }

    const newClasses = classArray.join(' ').trim();

    onChange({
      styles: {
        ...node.styles,
        container: newClasses
      }
    });
  };

  // Update shadow color (only for box shadow) - fixed logic
  const updateShadowColor = (color: string) => {
    let classArray = currentClasses.split(' ').filter(cls => cls.trim() !== '');
    const currentType = getCurrentShadowType();
    const currentSize = getCurrentShadow();

    // Only update color for box shadow
    if (currentType !== 'box' || currentSize === 'none') return;

    // Remove existing shadow color classes (keep size classes)
    classArray = classArray.filter(cls => {
      // Keep non-shadow classes
      if (!cls.startsWith('shadow-')) return true;
      
      // Keep shadow size classes
      const match = cls.match(/^shadow-(.+)$/);
      if (match) {
        const part = match[1];
        return ['none', 'sm', 'md', 'lg', 'xl', '2xl', 'inner'].includes(part);
      }
      
      // Keep base 'shadow' class
      return cls === 'shadow';
    });

    // Add new shadow color class
    if (color && color !== 'gray-500') {
      classArray.push(`shadow-${color}`);
    }

    const newClasses = classArray.join(' ').trim();

    onChange({
      styles: {
        ...node.styles,
        container: newClasses
      }
    });
  };


  return (
    <div className="space-y-2">
      {/* Shadow Type */}
      <div className="space-y-1">
        <Label className="text-xs">Loại bóng</Label>
        <SelectPopover
          value={getCurrentShadowType()}
          onValueChange={updateShadowType}
          disabled={disabled}
          options={shadowTypeOptions}
          placeholder="Chọn loại bóng"
        />
      </div>

      {/* Shadow Size */}
      <div className="space-y-1">
        <Label className="text-xs">Kích thước</Label>
        <ShadowPopover
          value={getCurrentShadow()}
          onValueChange={updateShadow}
          disabled={disabled}
          placeholder="Choose shadow"
        />
      </div>

      {/* Shadow Color - only for box shadow */}
      {getCurrentShadowType() === 'box' && getCurrentShadow() !== 'none' && (
        <div className="space-y-1">
          <Label className="text-xs">Màu bóng</Label>
          <ColorPicker
            value={getCurrentShadowColor()}
            onChange={updateShadowColor}
            disabled={disabled}
            placeholder="Chọn màu bóng"
            className="w-full text-xs"
          />
        </div>
      )}
    </div>
  );
};