import { RootState } from "@/store/rootReducer";
import { Post } from "@/features/post/states/types";
import { createSelector } from "@reduxjs/toolkit";

// Basic selectors
export const selectPageState = (state: RootState) => state.pageState;

export const selectAllPages = (state: RootState) => state.pageState.pages;

export const selectPagesAsArray = createSelector([selectAllPages], (pages) =>
  Object.values(pages)
);

export const selectPageById = (
  state: RootState,
  id: number
): Post | undefined => state.pageState.pages[id];

export const selectSelectedCategoryId = (state: RootState) =>
  state.pageState.selectedCategoryId;

export const selectSelectedPageId = (state: RootState) =>
  state.pageState.selectedPageId;

export const selectSelectedPage = (state: RootState): Post | undefined => {
  const pageId = state.pageState.selectedPageId;
  return pageId ? state.pageState.pages[pageId] : undefined;
};

export const selectPageMode = (state: RootState) => state.pageState.mode;

export const selectPageLoading = (state: RootState) => state.pageState.loading;

export const selectPageError = (state: RootState) => state.pageState.error;

export const selectCategoryPageMapping = (state: RootState) =>
  state.pageState.categoryPageMapping;

// Get linked page for a specific category
export const selectLinkedPageForCategory = (
  state: RootState,
  categoryId: number
): Post | undefined => {
  const pageId = state.pageState.categoryPageMapping[categoryId];
  return pageId ? state.pageState.pages[pageId] : undefined;
};

// Check if a category has a linked page
export const selectCategoryHasLinkedPage = (
  state: RootState,
  categoryId: number
): boolean => {
  return categoryId in state.pageState.categoryPageMapping;
};

// Get all categories that have linked pages - memoized
export const selectCategoriesWithLinkedPages = createSelector(
  [selectCategoryPageMapping],
  (mapping) => Object.keys(mapping).map(Number)
);

// Get pages that are not linked to any category - memoized
export const selectUnlinkedPages = createSelector(
  [selectCategoryPageMapping, selectAllPages],
  (mapping, pages) => {
    const linkedPageIds = new Set(Object.values(mapping));
    // Ensure we always return a transformed result, not the original input
    const pageValues = Object.values(pages);
    return pageValues.filter((page) => !linkedPageIds.has(page.id));
  }
);

// Get page summaries for lists
export const selectPageSummaries = (state: RootState) =>
  state.pageState.pageSummaries;

// Get search pagination state
export const selectSearchPagination = (state: RootState) =>
  state.pageState.searchPagination;

// Get all available pages for linking - now using lightweight summaries
export const selectAvailablePagesForLinking = createSelector(
  [selectPageSummaries],
  (summaries) => {
    // Guard against undefined summaries
    if (!summaries || !Array.isArray(summaries)) {
      return [];
    }
    return summaries;
  }
);

// Search pages by title - API already filters, so just return summaries
export const selectSearchPages = (state: RootState) => {
  const summaries = state.pageState.pageSummaries;

  // Guard against undefined summaries
  if (!summaries || !Array.isArray(summaries)) {
    return [];
  }

  // API already filtered results, so just return them directly
  return summaries;
};

// Helper selector to find category by slug (simplified)
const selectCategoryBySlugSimple = createSelector(
  [
    (state: RootState) => state.categoryState.types,
    (_: RootState, slug: string) => slug,
  ],
  (categoryTypes, slug) => {
    // Simple slug matching - find category with matching slug across all types
    for (const typeState of Object.values(categoryTypes)) {
      const found = typeState.data.find((cat) => cat.slug === slug);
      if (found) return found;
    }
    return null;
  }
);

// Get page content by category slug - simplified logic
export const selectPageBySlug = createSelector(
  [
    selectCategoryBySlugSimple,
    (state: RootState) => state.pageState.pages,
    selectPageLoading,
    selectPageError,
  ],
  (category, pages, loading, error) => {
    if (!category) {
      return {
        category: null,
        page: null,
        loading,
        error,
        found: false,
      };
    }

    if (!category.postId) {
      return {
        category,
        page: null,
        loading,
        error: `Category "${category.name}" has no linked page (postId is ${category.postId})`,
        found: true,
      };
    }

    // Check if page exists in cache
    const page = pages[category.postId] || null;

    return {
      category,
      page,
      loading,
      error,
      found: true,
    };
  }
);
