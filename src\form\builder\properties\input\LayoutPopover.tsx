/**
 * Layout Popover Component
 * Popover with visual layout options
 */

import React, { useState } from 'react';
import { cn } from '@/lib/utils';
import { ChevronDown, Check } from 'lucide-react';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Button } from '@/components/ui/button';

interface LayoutOption {
  value: string;
  label: string;
  description: string;
  icon: React.ReactNode;
}

interface LayoutPopoverProps {
  value?: string;
  onValueChange?: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
}

// Layout options with visual representations
const LAYOUT_OPTIONS: LayoutOption[] = [
  {
    value: 'default',
    label: 'Mặc định',
    description: 'Block',
    icon: (
      <div className="flex flex-col gap-0.5">
        <div className="w-6 h-1.5 bg-gray-400 rounded"></div>
        <div className="w-6 h-1.5 bg-gray-400 rounded"></div>
        <div className="w-6 h-1.5 bg-gray-400 rounded"></div>
      </div>
    )
  },
  {
    value: 'horizontal',
    label: 'Ngang',
    description: 'Flex row',
    icon: (
      <div className="flex gap-0.5">
        <div className="w-1.5 h-5 bg-blue-400 rounded"></div>
        <div className="w-1.5 h-5 bg-blue-400 rounded"></div>
        <div className="w-1.5 h-5 bg-blue-400 rounded"></div>
      </div>
    )
  },
  {
    value: 'vertical',
    label: 'Dọc',
    description: 'Flex col',
    icon: (
      <div className="flex flex-col gap-0.5">
        <div className="w-5 h-1.5 bg-green-400 rounded"></div>
        <div className="w-5 h-1.5 bg-green-400 rounded"></div>
        <div className="w-5 h-1.5 bg-green-400 rounded"></div>
      </div>
    )
  },
  {
    value: 'grid',
    label: 'Lưới',
    description: 'Grid',
    icon: (
      <div className="grid grid-cols-2 gap-0.5">
        <div className="w-2.5 h-2.5 bg-purple-400 rounded"></div>
        <div className="w-2.5 h-2.5 bg-purple-400 rounded"></div>
        <div className="w-2.5 h-2.5 bg-purple-400 rounded"></div>
        <div className="w-2.5 h-2.5 bg-purple-400 rounded"></div>
      </div>
    )
  }
];

export const LayoutPopover: React.FC<LayoutPopoverProps> = ({
  value = 'default',
  onValueChange,
  placeholder = 'Chọn layout',
  disabled = false
}) => {
  const [open, setOpen] = useState(false);

  // Find current option to display
  const currentOption = LAYOUT_OPTIONS.find(opt => opt.value === value) || LAYOUT_OPTIONS[0];

  const handleValueSelect = (newValue: string) => {
    onValueChange?.(newValue);
    setOpen(false);
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            "h-7 w-full justify-between text-xs",
            disabled && "opacity-50 cursor-not-allowed"
          )}
          disabled={disabled}
        >
          <span className="truncate">{currentOption.label}</span>
          <ChevronDown className="ml-2 h-3 w-3 shrink-0 opacity-50 flex-shrink-0" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-64 p-2" align="start">
        <div className="grid grid-cols-2 gap-2">
          {LAYOUT_OPTIONS.map((option) => (
            <button
              key={option.value}
              onClick={() => handleValueSelect(option.value)}
              className={cn(
                "flex flex-col items-center p-3 text-xs rounded-lg hover:bg-gray-100 transition-colors relative border border-gray-200",
                value === option.value && "bg-blue-100 text-blue-900 ring-1 ring-blue-500 border-blue-200"
              )}
            >
              <div className="flex items-center justify-center w-8 h-8 mb-2">
                {option.icon}
              </div>
              <span className="font-medium text-center">{option.label}</span>
              <span className="text-gray-500 text-xs">{option.description}</span>
              
              {value === option.value && (
                <Check className="h-3 w-3 absolute top-1 right-1 text-blue-600" />
              )}
            </button>
          ))}
        </div>
      </PopoverContent>
    </Popover>
  );
};