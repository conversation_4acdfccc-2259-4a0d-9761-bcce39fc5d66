/**
 * NumberInput - Number input component for AutoForm
 */

import React from "react";
import { FieldComponentProps } from "../../FieldFactory";
import { Input } from "@/components/ui/input";

export const NumberInput: React.FC<FieldComponentProps> = ({
  value,
  onChange,
  onBlur,
  disabled = false,
  placeholder = "",
  id,
  className = "",
}) => {
  const handleChange = disabled
    ? undefined
    : (e: React.ChangeEvent<HTMLInputElement>) => {
        const numValue = Number(e.target.value);
        onChange(numValue);
      };

  return (
    <Input
      id={id}
      type="number"
      value={value as number}
      onChange={handleChange}
      onBlur={onBlur}
      disabled={disabled}
      placeholder={placeholder}
      className={className}
    />
  );
};
