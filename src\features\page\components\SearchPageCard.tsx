import React, { useState, useEffect, useMemo, useCallback } from "react";
import debounce from "lodash.debounce";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Search, Link } from "lucide-react";
import { searchPages, PageSummary } from "../states/api";

interface SearchPageCardProps {
  hasLinkedPage: boolean;
  onLinkPage: (pageId: number) => void;
}

export const SearchPageCard: React.FC<SearchPageCardProps> = ({
  hasLinkedPage,
  onLinkPage,
}) => {
  // Local search state - completely isolated
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState<PageSummary[]>([]);
  const [searchPagination, setSearchPagination] = useState({
    currentPage: 0,
    hasMore: false,
    loading: false,
  });

  // Direct API search function
  const performSearch = useCallback(
    async (keyword: string, page: number = 0, isLoadMore: boolean = false) => {
      if (!keyword.trim()) {
        setSearchResults([]);
        setSearchPagination({ currentPage: 0, hasMore: false, loading: false });
        return;
      }

      console.log("🔍 SearchPageCard - Starting search:", {
        keyword,
        page,
        isLoadMore,
      });

      try {
        if (isLoadMore) {
          setSearchPagination((prev) => ({ ...prev, loading: true }));
        } else {
          setSearchPagination({
            currentPage: 0,
            hasMore: false,
            loading: true,
          });
        }

        // Don't use status filter - API doesn't support multiple status filtering
        const response = await searchPages(keyword, page, 20);
        console.log("📡 SearchPageCard - Raw API response:", response);

        // Follow project pattern: response.data.data contains the actual data
        const data = response.data.data || [];
        const pagination = response.data.pagination; // Use existing Pagination from restApi
        console.log("📊 SearchPageCard - Extracted data:", {
          data,
          pagination,
        });
        console.log("🔢 SearchPageCard - Data length:", data.length);

        if (isLoadMore) {
          // Append results for load more
          setSearchResults((prev) => {
            const newResults = [...prev, ...data];
            console.log("➕ SearchPageCard - Load more results:", newResults);
            return newResults;
          });
          setSearchPagination({
            currentPage: pagination?.page || page,
            hasMore: data.length === (pagination?.size || 20),
            loading: false,
          });
        } else {
          // Replace results for new search
          console.log("🔄 SearchPageCard - New search results:", data);
          setSearchResults(data);
          setSearchPagination({
            currentPage: pagination?.page || page,
            hasMore: data.length === (pagination?.size || 20),
            loading: false,
          });
        }
      } catch (error) {
        console.error("❌ SearchPageCard - Search failed:", error);
        setSearchResults([]);
        setSearchPagination({ currentPage: 0, hasMore: false, loading: false });
      }
    },
    []
  );

  // Debounced search function
  const debouncedSearch = useMemo(
    () =>
      debounce((keyword: string) => {
        performSearch(keyword, 0, false);
      }, 500),
    [performSearch]
  );

  // Search when query changes (debounced)
  useEffect(() => {
    debouncedSearch(searchQuery);

    // Cleanup on unmount
    return () => {
      debouncedSearch.cancel();
    };
  }, [searchQuery, debouncedSearch]);

  // Load more function
  const handleLoadMore = useCallback(() => {
    if (
      searchQuery.trim() &&
      searchPagination.hasMore &&
      !searchPagination.loading
    ) {
      performSearch(searchQuery.trim(), searchPagination.currentPage + 1, true);
    }
  }, [searchQuery, searchPagination, performSearch]);

  // Handle page linking - Let parent component handle restoration
  const handleLinkPage = useCallback(
    (pageId: number) => {
      // Call the original onLinkPage (parent will handle TRASH restoration)
      onLinkPage(pageId);
    },
    [onLinkPage]
  );

  // Debug: Monitor search state
  useEffect(() => {
    console.log("🔄 SearchPageCard - State changed:", {
      searchQuery,
      searchResults: searchResults.length,
      searchPagination,
    });
  }, [searchResults, searchQuery, searchPagination]);

  // Status badge styling
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "DRAFT":
        return (
          <Badge variant="secondary" className="text-xs">
            Bản nháp
          </Badge>
        );
      case "PUBLISHED":
        return (
          <Badge variant="default" className="text-xs">
            Đã xuất bản
          </Badge>
        );
      case "UNPUBLISHED":
        return (
          <Badge variant="outline" className="text-xs">
            Chưa xuất bản
          </Badge>
        );
      case "TRASH":
        return (
          <Badge variant="destructive" className="text-xs">
            Đã xóa
          </Badge>
        );
      default:
        return (
          <Badge variant="secondary" className="text-xs">
            {status}
          </Badge>
        );
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>
            {hasLinkedPage
              ? "Liên kết thêm trang khác"
              : "Liên kết trang có sẵn"}
          </span>
          <Badge variant="outline" className="text-xs">
            {searchResults.length} trang
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {/* Search Input */}
        <div className="mb-4">
          <div className="relative">
            <Search
              className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
              size={16}
            />
            <Input
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Tìm kiếm theo tiêu đề hoặc slug..."
              className="pl-10"
            />
          </div>
        </div>

        {/* Pages List */}
        <div className="space-y-2 max-h-96 overflow-y-auto">
          {searchResults.length > 0 ? (
            <>
              {searchResults.map((page) => (
                <div
                  key={page.id}
                  className="flex items-center justify-between p-3 border rounded hover:bg-gray-50"
                >
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <p className="font-medium">{page.title}</p>
                      {getStatusBadge(page.status)}
                    </div>
                    <p className="text-sm text-gray-500">
                      ID: {page.id} | {page.slug}
                    </p>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleLinkPage(page.id)}
                  >
                    <Link size={16} className="mr-1" />
                    {page.status === "TRASH"
                      ? "Khôi phục & Liên kết"
                      : "Liên kết"}
                  </Button>
                </div>
              ))}

              {/* Load More Button */}
              {searchPagination.hasMore && (
                <div className="pt-3 border-t">
                  <Button
                    variant="outline"
                    onClick={handleLoadMore}
                    disabled={searchPagination.loading}
                    className="w-full"
                  >
                    {searchPagination.loading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2" />
                        Đang tải...
                      </>
                    ) : (
                      "Xem thêm"
                    )}
                  </Button>
                </div>
              )}
            </>
          ) : (
            <div className="text-center py-8 text-gray-500">
              {searchQuery
                ? `Không tìm thấy trang nào với từ khóa "${searchQuery}"`
                : "Nhập từ khóa để tìm kiếm trang"}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
