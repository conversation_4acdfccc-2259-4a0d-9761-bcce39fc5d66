import React, { useEffect, useState, useRef } from "react";
import { useAppDispatch, useAppSelector } from "@/store/rootReducer";

import { ConfigPageLayout } from "../../shared/components/layouts";
import { ContactTable } from "../components/ContactTable";
import { ContactPreview } from "../components/ContactPreview";
import type { ContactMetadata, ContactConfig } from "../states/type";

import {
  fetchContactAsync,
  updateContactAsync,
  setContactData,
  clearError,
} from "../states/slices";
import {
  selectContactData,
  selectContactSavedData,
  selectContactLoading,
  selectContactSaving,
  selectContactError,
  selectContactIsDirty,
} from "../states/selector";
import { toast } from "sonner";

// ============================================================================
// Configuration
// ============================================================================

const CONTACT_METADATA: ContactMetadata = {
  title: 'Quản lý liên hệ',
  description: 'Thông tin liên hệ của công ty',
  type: 'array',
  arrayKey: 'contacts',
  fields: [
    {
      key: 'name',
      label: 'Họ và Tên',
      type: 'text',
      required: true,
      placeholder: 'Nhập họ và tên'
    },
    {
      key: 'phone',
      label: 'Số điện thoại',
      type: 'text',
      required: true,
      placeholder: '024-1234-5678'
    },
    {
      key: 'email',
      label: 'Email',
      type: 'email',
      required: true,
      placeholder: '<EMAIL>'
    },
    {
      key: 'address',
      label: 'Địa chỉ',
      type: 'text',
      required: true,
      placeholder: 'Nhập địa chỉ đầy đủ'
    },
  ],
};

// ============================================================================
// Component
// ============================================================================

export const ContactManagementPage: React.FC = () => {
  const dispatch = useAppDispatch();
  const data = useAppSelector(selectContactData);
  const savedData = useAppSelector(selectContactSavedData);
  const loading = useAppSelector(selectContactLoading);
  const saving = useAppSelector(selectContactSaving);
  const error = useAppSelector(selectContactError);
  const isDirty = useAppSelector(selectContactIsDirty);

  const [isValid, setIsValid] = useState(true);
  const [showPreview, setShowPreview] = useState(false);
  const [showValidationWarning, setShowValidationWarning] = useState(false);
  const validateTableRef = useRef<(() => boolean) | undefined>(undefined);

  useEffect(() => {
    if (!data && !loading) {
      dispatch(fetchContactAsync());
    }
  }, [dispatch, data, loading]);

  // ============================================================================
  // Event Handlers
  // ============================================================================

  const handleSave = async () => {
    // Validate data before saving
    const isDataValid = validateTableRef.current ? validateTableRef.current() : true;

    if (!isDataValid) {
      setShowValidationWarning(true);
      return;
    }

    setShowValidationWarning(false);

    if (data && isDirty) {
      try {
        await dispatch(updateContactAsync(data)).unwrap();
        toast.success("Lưu cấu hình liên hệ thành công");
      } catch (error) {
        toast.error(`Lỗi khi lưu cấu hình liên hệ: ${String(error)}`);
      }
    }
  };

  const handleRefresh = () => {
    dispatch(fetchContactAsync());
  };

  const handleClearError = () => {
    dispatch(clearError());
  };

  const handleDataChange = (newData: ContactConfig) => {
    dispatch(setContactData(newData));
  };

  const handleValidationChange = (valid: boolean) => {
    setIsValid(valid);
  };

  const handlePreview = () => {
    setShowPreview(!showPreview);
  };

  return (
    <ConfigPageLayout
      metadata={CONTACT_METADATA}
      loading={loading}
      saving={saving}
      error={error}
      isDirty={isDirty && isValid}
      hasValidationErrors={showValidationWarning}
      onSave={handleSave}
      onRefresh={handleRefresh}
      onClearError={handleClearError}
      onPreview={handlePreview}
      showPreview={showPreview}
    >
      <div className="flex flex-col h-full space-y-6">
        <div className="flex-shrink-0">
          <ContactTable
            metadata={CONTACT_METADATA}
            data={data}
            loading={loading}
            onDataChange={handleDataChange}
            onValidationChange={handleValidationChange}
            onValidationRequest={validateTableRef}
          />
        </div>

        {showPreview && (
          <ContactPreview
            data={savedData}
            visible={showPreview}
          />
        )}
      </div>
    </ConfigPageLayout>
  );
};

export default ContactManagementPage;
