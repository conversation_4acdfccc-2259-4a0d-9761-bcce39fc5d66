// File: src/components/CategoryTree.tsx
import React, { useState, useMemo, useEffect } from "react";
import { useAppSelector } from "@/store/rootReducer";
import { selectCategoryByType } from "../states/selector";
import { CategoryDTO, CategoryType } from "../states/types";
// import { setSelectedId, setCategoryMode } from "../states/slices";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ChevronDown, ChevronRight, Plus } from "lucide-react";
import { cn } from "@/lib/utils";
import { DynamicIcon } from "@/components/other/DynamicIcon";

export type CategoryNode = CategoryDTO & { children: CategoryNode[] };

interface CategoryTreeBaseProps {
  allCategories: CategoryDTO[];
  selectedId: number | null;
  onSelected: (id: number) => void;
}

export const CategoryTreeBase: React.FC<CategoryTreeBaseProps> = ({
  allCategories,
  selectedId,
  onSelected,
}) => {
  // build a virtual root node
  const tree = useMemo<CategoryNode>(() => {
    const root: CategoryNode = {
      id: 0,
      name: "Tất cả",
      slug: "",
      type: "public-menu",
      description: { icon: "Folder", component: "" },
      priority: 0,
      parentId: null,
      postId: null,
      createdAt: 0,
      updatedAt: 0,
      children: [],
      status: "ACTIVE",
    };
    const map = new Map<number, CategoryNode>();

    allCategories.forEach((c) => map.set(c.id, { ...c, children: [] }));

    map.forEach((node) => {
      if (node.parentId == null || !map.has(node.parentId)) {
        root.children.push(node);
      } else {
        map.get(node.parentId)!.children.push(node);
      }
    });

    // Hàm đệ quy sắp xếp theo priority giảm dần (priority lớn lên trên)
    const sortChildren = (node: CategoryNode) => {
      node.children.sort((a, b) => b.priority - a.priority);
      node.children.forEach(sortChildren);
    };
    sortChildren(root);

    return root;
  }, [allCategories]);

  // parent map for BFS expand
  const parentMap = useMemo(() => {
    const m = new Map<number, number | null>();
    const traverse = (n: CategoryNode, p: number | null) => {
      m.set(n.id, p);
      n.children.forEach((ch) => traverse(ch, n.id));
    };
    traverse(tree, null);
    return m;
  }, [tree]);

  // expanded path from root to open nodes
  const [expandedPath, setExpandedPath] = useState<number[]>([tree.id]);

  useEffect(() => {
    if (selectedId == null) return;

    // Check if selectedId exists in parentMap (tree)
    if (!parentMap.has(selectedId)) {
      return;
    }

    // Tự động mở path từ root đến selectedId
    const path: number[] = [];
    let cur: number | null = selectedId;
    while (cur != null) {
      path.unshift(cur);
      cur = parentMap.get(cur) ?? null;
    }

    setExpandedPath(path);
  }, [selectedId, parentMap, allCategories.length]);

  const toggle = (id: number) => {
    const idx = expandedPath.indexOf(id);
    if (idx >= 0) {
      // closing: drop after parent
      const p = parentMap.get(id) ?? tree.id;
      const pi = expandedPath.indexOf(p);
      setExpandedPath(expandedPath.slice(0, pi + 1));
    } else {
      // opening: build path from root
      const path: number[] = [];
      let cur: number | null = id;
      while (cur != null) {
        path.unshift(cur);
        cur = parentMap.get(cur) ?? null;
      }
      setExpandedPath(path);
    }
  };

  const handleSelect = (id: number) => {
    if (selectedId === id) return;
    onSelected(id);
  };

  const renderNode = (node: CategoryNode, depth = 0): React.ReactNode => {
    const isOpen = expandedPath.includes(node.id);
    const hasChildren = node.children.length > 0;
    const isSelected = selectedId === node.id;
    return (
      <div key={node.id} className="mb-1">
        <div
          className={cn(
            "flex items-center p-1 space-x-2 rounded cursor-pointer",
            isSelected ? "bg-primary text-white" : "hover:bg-muted"
          )}
          style={{ paddingLeft: depth * 16 + 4 }}
          onClick={() => handleSelect(node.id)}
        >
          {hasChildren ? (
            <Button
              variant="ghost"
              size="icon"
              className="w-5 h-5"
              onClick={(e) => {
                e.stopPropagation();
                toggle(node.id);
              }}
            >
              {isOpen ? (
                <ChevronDown className="w-4 h-4" />
              ) : (
                <ChevronRight className="w-4 h-4" />
              )}
            </Button>
          ) : (
            <div className="w-5 h-5 mr-1 flex items-center justify-center">
              <DynamicIcon icon={node.description.icon} className="w-4 h-4" />
            </div>
          )}
          <span>{node.name}</span>
        </div>
        {hasChildren && isOpen && (
          <div>{node.children.map((ch) => renderNode(ch, depth + 1))}</div>
        )}
      </div>
    );
  };

  return <div className="p-0 pt-3">{renderNode(tree)}</div>;
};

interface CategoryTreeProps {
  selectedType: CategoryType;
  selectedId: number;
  onIdChange: (id: number) => void;
  onCreateNew: (parentId: number) => void;
}

export const CategoryTree: React.FC<CategoryTreeProps> = ({
  selectedType,
  selectedId,
  onIdChange,
  onCreateNew,
}) => {
  const allCategories = useAppSelector((state) =>
    selectCategoryByType(state, selectedType)
  );

  const handleSelected = (id: number) => {
    onIdChange(id);
  };

  const handleCreateNew = () => {
    onCreateNew(selectedId);
  };

  return (
    <Card className="h-fit">
      <CardHeader className="pb-0">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-medium">Cây danh mục</CardTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleCreateNew}
            className="h-8 w-8 p-0"
            title="Tạo danh mục mới"
          >
            <Plus className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="pt-0 pb-6">
        <CategoryTreeBase
          allCategories={allCategories}
          selectedId={selectedId}
          onSelected={handleSelected}
        />
      </CardContent>
    </Card>
  );
};
