import { z } from "zod";

// ============================================================================
// Validation Schemas
// ============================================================================

/**
 * Validation schema for individual contact item
 * Enforces proper phone format (10 digits), email format, and required fields
 */
export const contactItemSchema = z.object({
  name: z.string().min(1, "Vui lòng nhập Họ và tên"),
  phone: z
    .string()
    .min(1, "Vui lòng nhập Số điện thoại")
    .regex(/^\d{10}$/, "Số điện thoại phải có đúng 10 chữ số"),
  email: z
    .string()
    .min(1, "Vui lòng nhập Email")
    .email("Email không đúng định dạng"),
  address: z.string().min(1, "Vui lòng nhập Địa chỉ"),
});

/**
 * Validation schema for the complete contact configuration
 * Validates array of contact items
 */
export const contactConfigSchema = z.object({
  contacts: z.array(contactItemSchema).min(1, "Phải có ít nhất một liên hệ"),
});

// ============================================================================
// Type Exports
// ============================================================================

/**
 * TypeScript type for contact item validation
 */
export type ContactItemValidation = z.infer<typeof contactItemSchema>;

/**
 * TypeScript type for contact configuration validation
 */
export type ContactConfigValidation = z.infer<typeof contactConfigSchema>;
