import React, { useState, useRef } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Title,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Edit } from "lucide-react";
import { AutoForm, AutoFormRef } from "@/form/context";
import { editAlbumFormNode } from "../states/form";
import { Album } from "../states/types";
import { updateAlbum } from "../states/api";
import { toast } from "sonner";

interface EditAlbumDialogProps {
  album: Album;
  onAlbumUpdated?: (albumId: number) => void;
  trigger?: React.ReactNode;
}

export const EditAlbumDialog: React.FC<EditAlbumDialogProps> = ({
  album,
  onAlbumUpdated,
  trigger,
}) => {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  // Define the album form data type
  interface AlbumFormData {
    name: string;
    description: {
      text: string;
    };
  }

  const formRef = useRef<AutoFormRef<AlbumFormData>>(null);

  // Prepare initial data - AutoForm now handles both flat and nested input
  const initialData: AlbumFormData = {
    name: album.name,
    description: { text: album.description?.text || "" },
  };

  // No need for form submission handler - AutoForm handles it internally

  const handleSubmit = async (data: AlbumFormData) => {
    try {
      setLoading(true);

      // Transform form data to match API requirements
      // AutoForm already unflattens the data, so access nested structure
      const descriptionText = String(data.description?.text || "").trim();
      const updatedData = {
        name: String(data.name || "").trim(),
        type: album.type, // Keep original type
        description: {
          text: descriptionText,
        },
      };

      const response = await updateAlbum(album.id, updatedData);

      // Handle BaseResponse structure
      if (response.data.data) {
        toast.success(response.data.message || "Cập nhật thư viện ảnh thành công");
        setOpen(false);

        // Refresh current album data if in detail view
        if (onAlbumUpdated) {
          onAlbumUpdated(album.id);
        }
      } else if (response.data.errors) {
        // Handle validation errors from BaseResponse
        const errorMessages = Object.values(response.data.errors).join(", ");
        toast.error(errorMessages);
      } else {
        toast.error(response.data.message || "Có lỗi xảy ra khi cập nhật thư viện ảnh");
      }
    } catch (error) {
      console.error("Error updating album:", error);
      toast.error("Có lỗi xảy ra khi cập nhật thư viện ảnh");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline" size="sm">
            <Edit className="w-4 h-4 mr-2" />
            Sửa album
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-lg">
        <DialogHeader>
          <DialogTitle>Sửa album</DialogTitle>
        </DialogHeader>
        <div className="py-4">
          <AutoForm<AlbumFormData>
            ref={formRef}
            node={editAlbumFormNode}
            onSubmit={handleSubmit}
            initialData={initialData}
            validationMode="onChange"
            viewOnly={loading}
          />
          <div className="flex gap-3 justify-end mt-6">
            <Button
              type="button"
              variant="outline"
              onClick={() => setOpen(false)}
              disabled={loading}
            >
              Hủy
            </Button>
            <Button
              type="button"
              onClick={() => formRef.current?.submitForm()}
              disabled={loading}
            >
              {loading ? "Đang cập nhật..." : "Cập nhật"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
