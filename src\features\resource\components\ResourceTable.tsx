import React, { useEffect } from "react";
import { useAppSelector, useAppDispatch } from "@/store/rootReducer";
import {
  selectFilteredResources,
  selectLoading,
  selectSearchTerm,
} from "../states/selectors";
import {
  fetchResourcesThunk,
  setCurrentResource,
  setSearchTerm,
} from "../states/slices";
import { useSwitchMode } from "../states/hooks";
import { AutoTable } from "@/components/table/AutoTable";
import { Resource } from "../states/types";
import { Input } from "@/components/ui/input";
import { Search } from "lucide-react";
import { RESOURCE_COLUMNS } from "../states/table";

export const ResourceTable = () => {
  const dispatch = useAppDispatch();
  const switchMode = useSwitchMode();

  const resources = useAppSelector(selectFilteredResources);
  const loading = useAppSelector(selectLoading);
  const searchTerm = useAppSelector(selectSearchTerm);

  // Simple fetch on mount
  useEffect(() => {
    if (resources.length === 0 && !loading) {
      dispatch(fetchResourcesThunk());
    }
  }, [dispatch, resources.length, loading]);

  const handleAction = (action: string, row: Resource) => {
    if (action === "edit") {
      dispatch(setCurrentResource(row));
      switchMode("detail", row.id.toString(), "view");
    }
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setSearchTerm(event.target.value));
  };

  return (
    <div className="space-y-4">
      {/* Search */}
      <div className="flex items-center space-x-2">
        <div className="relative">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Tìm kiếm nhóm quyền..."
            value={searchTerm}
            onChange={handleSearchChange}
            className="pl-8 max-w-sm"
          />
        </div>
      </div>

      {/* Table */}
      <AutoTable<Resource>
        columns={RESOURCE_COLUMNS}
        data={resources}
        loading={loading}
        onAction={handleAction}
      />
    </div>
  );
};
