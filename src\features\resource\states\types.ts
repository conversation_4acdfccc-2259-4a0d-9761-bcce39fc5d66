import { FieldValue } from "@/components/form/registry";

export interface Resource {
  id: number;
  code: string;
  name: string;
  metadata: Record<string, FieldValue>;
  actions: number[];
  createdAt: number;
  updatedAt: number;
}

export interface ResourceRequest {
  code: string;
  name: string;
  metadata?: Record<string, FieldValue>;
  actions?: number[];
}

export type ResourcePageMode = "list" | "detail";
export type ResourceAction = "view" | "edit" | "create";

export interface ResourceState {
  data: Resource[];
  currentResource: Resource | null;
  // URL sync states
  pageMode: ResourcePageMode;
  actionType: ResourceAction;
  // Search/filter states
  searchTerm: string;
  loading: boolean;
  error: string | null;
  // Simple delete tracking
  deletedIds: number[];
}

export interface ResourceQueryParams {
  mode?: ResourcePageMode;
  resource?: string; // resource id
  actionType?: ResourceAction;
  search?: string;
}
