import { BaseResponse, restApi } from "@/api/restApi";
import { API_ENDPOINTS } from "@/api/endpoints";
import { Action, CreateActionRequest } from "./types";
import { AxiosResponse } from "axios";

export async function fetchActions(): Promise<
  AxiosResponse<BaseResponse<Action[]>>
> {
  const res = await restApi.get<BaseResponse<Action[]>>(
    API_ENDPOINTS.AUTH.ADMIN.ACTIONS
  );
  return res;
}

export async function fetchAction(
  id: number
): Promise<AxiosResponse<BaseResponse<Action>>> {
  const res = await restApi.get<BaseResponse<Action>>(
    `${API_ENDPOINTS.AUTH.ADMIN.ACTIONS}/${id}`
  );
  return res;
}

export async function createActionApi(
  action: CreateActionRequest
): Promise<AxiosResponse<BaseResponse<Action>>> {
  const res = await restApi.post<BaseResponse<Action>>(
    API_ENDPOINTS.AUTH.ADMIN.ACTIONS,
    action
  );
  return res;
}

export async function updateActionApi(
  id: number,
  action: Partial<Action>
): Promise<AxiosResponse<BaseResponse<Action>>> {
  const res = await restApi.put<BaseResponse<Action>>(
    `${API_ENDPOINTS.AUTH.ADMIN.ACTIONS}/${id}`,
    action
  );
  return res;
}

export async function deleteActionApi(
  id: number
): Promise<AxiosResponse<BaseResponse<null>>> {
  const res = await restApi.delete<BaseResponse<null>>(
    `${API_ENDPOINTS.AUTH.ADMIN.ACTIONS}/${id}`
  );
  return res;
}
