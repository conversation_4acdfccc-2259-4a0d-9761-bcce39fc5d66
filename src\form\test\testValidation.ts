import { buildFormZodSchema, extractDefaultValues } from "../utils/zodHelper";
import { registerFormMock } from "../../features/form-editor/states/mock";
import { FormNode } from "../types";

/**
 * Test password matching validation
 */
export function testPasswordMatchValidation() {
  const formNode = registerFormMock.node as unknown as FormNode;

  // Generate Zod schema
  const schema = buildFormZodSchema(formNode);

  // Extract default values
  const defaultValues = extractDefaultValues(formNode);

  console.log("🔧 Register Form Schema Generated");
  console.log("Default Values:", defaultValues);

  // Test valid data
  const validData = {
    email: "<EMAIL>",
    password: "mypassword123",
    confirmPassword: "mypassword123",
  };

  console.log("\n✅ Testing valid data:", validData);
  const validResult = schema.safeParse(validData);
  console.log(
    "Valid Result:",
    validResult.success ? "PASS" : validResult.error.issues
  );

  // Test invalid data (password mismatch)
  const invalidData = {
    email: "<EMAIL>",
    password: "mypassword123",
    confirmPassword: "differentpassword",
  };

  console.log("\n❌ Testing invalid data (password mismatch):", invalidData);
  const invalidResult = schema.safeParse(invalidData);
  console.log(
    "Invalid Result:",
    invalidResult.success ? "UNEXPECTED PASS" : invalidResult.error.issues
  );

  // Test invalid email format
  const invalidEmailData = {
    email: "invalid-email",
    password: "mypassword123",
    confirmPassword: "mypassword123",
  };

  console.log("\n❌ Testing invalid email format:", invalidEmailData);
  const invalidEmailResult = schema.safeParse(invalidEmailData);
  console.log(
    "Invalid Email Result:",
    invalidEmailResult.success
      ? "UNEXPECTED PASS"
      : invalidEmailResult.error.issues
  );

  return {
    schema,
    defaultValues,
    tests: {
      valid: validResult,
      passwordMismatch: invalidResult,
      invalidEmail: invalidEmailResult,
    },
  };
}
