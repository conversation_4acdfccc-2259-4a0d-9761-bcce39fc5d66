import { BaseResponse, restApi } from "@/api/restApi";
import { API_ENDPOINTS } from "@/api/endpoints";
import { Resource, ResourceRequest } from "./types";
import { AxiosResponse } from "axios";

export async function fetchResources(): Promise<
  AxiosResponse<BaseResponse<Resource[]>>
> {
  const res = await restApi.get<BaseResponse<Resource[]>>(
    API_ENDPOINTS.AUTH.ADMIN.RESOURCES
  );
  return res;
}

export async function fetchResource(
  id: number
): Promise<AxiosResponse<BaseResponse<Resource>>> {
  const res = await restApi.get<BaseResponse<Resource>>(
    `${API_ENDPOINTS.AUTH.ADMIN.RESOURCES}/${id}`
  );
  return res;
}

export async function createResourceApi(
  resource: ResourceRequest
): Promise<AxiosResponse<BaseResponse<Resource>>> {
  const res = await restApi.post<BaseResponse<Resource>>(
    API_ENDPOINTS.AUTH.ADMIN.RESOURCES,
    resource
  );
  return res;
}

export async function updateResourceApi(
  id: number,
  resource: ResourceRequest
): Promise<AxiosResponse<BaseResponse<Resource>>> {
  const res = await restApi.put<BaseResponse<Resource>>(
    `${API_ENDPOINTS.AUTH.ADMIN.RESOURCES}/${id}`,
    resource
  );
  return res;
}

export async function deleteResourceApi(
  id: number
): Promise<AxiosResponse<BaseResponse<null>>> {
  const res = await restApi.delete<BaseResponse<null>>(
    `${API_ENDPOINTS.AUTH.ADMIN.RESOURCES}/${id}`
  );
  return res;
}
