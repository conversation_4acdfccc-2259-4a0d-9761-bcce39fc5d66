import { useState, useMemo } from "react";
import { Button } from "@/components/ui/button";
import { Save, Send, ArrowLeft } from "lucide-react";
import { SubmissionInfoForm } from "../components/SubmissionInfoForm";
import { RecordPackageTable } from "../components/RecordPackageTable";
import {
  SubmissionInfo,
  RecordPackage,
  RecordPackageFilters,
  mockSubmissionInfo,
  mockRecordPackages,
  filterRecordPackages,
} from "../data/mockData";

const CreateSubmissionPage = () => {
  const [submissionInfo, setSubmissionInfo] =
    useState<SubmissionInfo>(mockSubmissionInfo);
  const [recordPackages] = useState<RecordPackage[]>(mockRecordPackages);
  const [selectedPackageIds, setSelectedPackageIds] = useState<number[]>([]);
  const [packageFilters, setPackageFilters] = useState<RecordPackageFilters>({
    keyword: "",
    status: "",
  });

  // Filter packages based on current filters
  const filteredPackages = useMemo(() => {
    return filterRecordPackages(recordPackages, packageFilters);
  }, [recordPackages, packageFilters]);

  const handleSubmissionInfoChange = (info: SubmissionInfo) => {
    setSubmissionInfo(info);
  };

  const handlePackageFiltersChange = (filters: RecordPackageFilters) => {
    setPackageFilters(filters);
  };

  const handleViewPackage = (pkg: RecordPackage) => {
    console.log("View package:", pkg);
    // TODO: Implement view package modal/page
  };

  const handleEditPackage = (pkg: RecordPackage) => {
    console.log("Edit package:", pkg);
    // TODO: Implement edit package modal/page
  };

  const handleDeletePackage = (pkg: RecordPackage) => {
    console.log("Delete package:", pkg);
    // TODO: Implement delete package confirmation
  };

  const handleAddPackage = () => {
    console.log("Add new package");
    // TODO: Implement add new package modal/page
  };

  const handleImportPackages = () => {
    console.log("Import packages");
    // TODO: Implement import packages functionality
  };

  const handleExportPackages = () => {
    console.log("Export packages");
    // TODO: Implement export packages functionality
  };

  const handlePackageSelectionChange = (ids: number[]) => {
    setSelectedPackageIds(ids);
  };

  const handleSaveDraft = () => {
    console.log("Save as draft:", { submissionInfo, selectedPackageIds });
    // TODO: Implement save as draft functionality
  };

  const handleSubmit = () => {
    console.log("Submit submission:", { submissionInfo, selectedPackageIds });
    // TODO: Implement submit functionality
  };

  const handleGoBack = () => {
    console.log("Go back");
    // TODO: Implement navigation back to list
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-6">
        {/* Header */}
        <div className="mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="outline"
                onClick={handleGoBack}
                className="flex items-center space-x-2"
              >
                <ArrowLeft className="h-4 w-4" />
                <span>Quay lại</span>
              </Button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  Tạo gói tin nộp lưu
                </h1>
                <p className="text-gray-600 mt-1">
                  Tạo và quản lý gói tin nộp lưu hồ sơ tài liệu
                </p>
              </div>
            </div>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                onClick={handleSaveDraft}
                className="flex items-center space-x-2"
              >
                <Save className="h-4 w-4" />
                <span>Lưu nháp</span>
              </Button>
              <Button
                onClick={handleSubmit}
                className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700"
              >
                <Send className="h-4 w-4" />
                <span>Gửi nộp lưu</span>
              </Button>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="space-y-6">
          {/* Submission Info Form */}
          <SubmissionInfoForm
            submissionInfo={submissionInfo}
            onSubmissionInfoChange={handleSubmissionInfoChange}
          />

          {/* Record Packages Table */}
          <RecordPackageTable
            packages={filteredPackages}
            filters={packageFilters}
            onFiltersChange={handlePackageFiltersChange}
            onView={handleViewPackage}
            onEdit={handleEditPackage}
            onDelete={handleDeletePackage}
            onAdd={handleAddPackage}
            onImport={handleImportPackages}
            onExport={handleExportPackages}
            selectedIds={selectedPackageIds}
            onSelectionChange={handlePackageSelectionChange}
          />
        </div>

        {/* Footer Actions */}
        <div className="mt-8 flex justify-end space-x-4 p-4 bg-white rounded-lg border">
          <Button variant="outline" onClick={handleGoBack} className="px-8">
            Hủy
          </Button>
          <Button
            variant="outline"
            onClick={handleSaveDraft}
            className="px-8 flex items-center space-x-2"
          >
            <Save className="h-4 w-4" />
            <span>Lưu nháp</span>
          </Button>
          <Button
            onClick={handleSubmit}
            className="px-8 flex items-center space-x-2 bg-blue-600 hover:bg-blue-700"
          >
            <Send className="h-4 w-4" />
            <span>Gửi nộp lưu</span>
          </Button>
        </div>
      </div>
    </div>
  );
};

export default CreateSubmissionPage;
