import { FieldConfig } from "./registry";

export interface TreeNode extends FieldConfig {
  /**
   * Mảng chứa các node con
   */
  children?: TreeNode[];

  /**
   * Trạng thái mở rộng của node (true: đang mở rộng, false: đang thu gọn)
   */
  expanded?: boolean;

  /**
   * Cấp độ của node trong cây (root = 0)
   */
  level?: number;

  /**
   * ID của node cha
   */
  parentId?: string;

  /**
   * Vị trí của node trong mảng children của node cha
   */
  index?: number;
}

export interface TreeNodeProps {
  /**
   * Dữ liệu của node
   */
  node: TreeNode;

  /**
   * Callback khi node được chọn
   */
  onSelect?: (node: TreeNode) => void;

  /**
   * Callback khi trạng thái mở rộng của node thay đổi
   */
  onExpand?: (node: TreeNode) => void;
}
