// Question Status theo Java enum
export type QuestionStatus = "TRASH" | "NEW" | "DRAFT" | "PUBLISHED";

// Question Status transition rules (theo Java enum logic)
export const QUESTION_STATUS_TRANSITIONS: Record<
  QuestionStatus,
  QuestionStatus[]
> = {
  NEW: ["TRASH", "DRAFT"],
  DRAFT: ["TRASH", "PUBLISHED"],
  PUBLISHED: ["DRAFT"],
  TRASH: ["DRAFT"],
};

// Asker Info (theo API thật)
export interface AskerInfo {
  fullName: string;
  phoneNumber: string;
  email: string;
  address: string;
}

// Question Content (theo API thật)
export interface QuestionContent {
  title: string;
  question: string; // "question" thay vì "description"
}

// Question theo API structure thật
export interface Question {
  id: number;
  asker: AskerInfo; // "asker" thay vì "info"
  content: QuestionContent;
  status: QuestionStatus;
  topic: string;
  createdAt: number; // timestamp
  updatedAt: number; // timestamp
}

// Answer Content
export interface AnswerContent {
  answer: string;
  fileList: string[]; // array of file URLs
}

// Answer theo API structure
export interface Answer {
  id: number;
  questionId: number;
  content: AnswerContent;
}

// API Response pagination
export interface Pagination {
  page: number;
  size: number;
  totalElements: number;
}

// API Response wrapper (deprecated - use BaseResponse from restApi)
export interface ApiResponse<T> {
  data: T;
  pagination?: Pagination;
}

// API Error Response
export interface ApiError {
  success: false;
  message: string;
  errorCode?: string;
}

// Request DTOs (theo API thật)
export interface CreateQuestionRequest {
  asker: AskerInfo;
  content: QuestionContent;
  topic: string;
}

export interface UpdateQuestionRequest {
  asker: AskerInfo;
  content: QuestionContent;
  topic: string;
}

export interface CreateAnswerRequest {
  questionId: number;
  answerContent: AnswerContent;
}

export interface UpdateAnswerRequest {
  questionId: number;
  answerContent: AnswerContent;
}

// Query parameters
export interface QuestionQueryParams {
  keyword?: string;
  status?: QuestionStatus;
  page?: number;
  size?: number;
}

export interface AnswerQueryParams {
  keyword?: string;
  page?: number;
  size?: number;
}

// Q&A Configuration
export interface QAConfig {
  active: boolean;
  topics: string[];
}

export interface QAConfigRequest {
  active: boolean;
  topics: string[];
}

// Question Filter State (following post pattern)
export interface QuestionFilterState {
  params: QuestionQueryParams;
  loading: boolean;
  error: string | null;
  refetch: boolean;
}

// Redux State
export interface QuestionState {
  // Questions
  questions: Question[];
  questionsLoading: boolean;
  questionsError: string | null;
  questionsPagination: Pagination | null;
  selectedQuestion: Question | null;

  // Answers
  answers: Answer[];
  answersLoading: boolean;
  answersError: string | null;
  answersPagination: Pagination | null;
  selectedAnswer: Answer | null;

  // Q&A Configuration
  qaConfig: QAConfig | null;
  qaConfigLoading: boolean;
  qaConfigError: string | null;
}
