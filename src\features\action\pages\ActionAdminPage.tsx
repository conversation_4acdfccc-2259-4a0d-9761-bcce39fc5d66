import { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Plus, ArrowLeft } from "lucide-react";
import { useAppSelector, useAppDispatch } from "@/store/rootReducer";
import { selectCurrentAction, selectPageMode } from "../states/selectors";
import {
  setCurrentAction,
  setPathParams,
  fetchActionByIdThunk,
} from "../states/slices";
import { useSwitchMode } from "../states/hooks";
import { ActionTable } from "../components/ActionTable";
import { ActionCreateForm } from "../components/ActionCreateForm";
import { useLocation } from "react-router-dom";
import { ActionPageMode, ActionAction } from "../states/types";

const ActionAdminPage = () => {
  const dispatch = useAppDispatch();
  const location = useLocation();
  const switchMode = useSwitchMode();

  const currentActionEntity = useAppSelector(selectCurrentAction); // Rename để tránh confusing
  const pageMode = useAppSelector(selectPageMode);

  const [currentActionType, setCurrentActionType] =
    useState<ActionAction>("view");

  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const mode = searchParams.get("mode") as ActionPageMode;
    const actionType = searchParams.get("actionType") as ActionAction;
    const actionId = searchParams.get("action");

    if (!mode) {
      dispatch(setPathParams({ mode: "list" }));
    } else {
      const finalActionType = actionType || "view";
      dispatch(
        setPathParams({
          mode,
          actionType: finalActionType,
        })
      );
      setCurrentActionType(finalActionType);

      // If we have an action ID and we're in detail mode, load the action
      if (mode === "detail" && actionId && actionId !== "0") {
        const id = parseInt(actionId);
        if (
          !isNaN(id) &&
          (!currentActionEntity || currentActionEntity.id !== id)
        ) {
          dispatch(fetchActionByIdThunk(id));
        }
      }
      
      // Reset to view if in detail mode but no actionType specified
      if (mode === "detail" && !actionType) {
        setCurrentActionType("view");
      }
    }
  }, [location.search, dispatch, currentActionEntity]);

  const handleCreateNew = () => {
    dispatch(
      setCurrentAction({
        id: 0,
        code: "",
        name: "",
        metadata: {},
        createdAt: 0,
        updatedAt: 0,
      })
    );
    setCurrentActionType("create");
    switchMode("detail", "0", "create");
  };

  const handleBackToList = () => {
    dispatch(setCurrentAction(null));
    setCurrentActionType("view");
    switchMode("list");
  };

  const handleEditAction = () => {
    setCurrentActionType("edit");
    if (currentActionEntity) {
      switchMode("detail", String(currentActionEntity.id), "edit");
    }
  };

  if (pageMode === "detail") {
    return (
      <div className="space-y-4">
        {/* Header với nút quay lại */}
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold">
            {currentActionEntity?.id === 0 ? "Tạo quyền mới" : "Chi tiết quyền"}
          </h1>
          <Button className="w-30" onClick={handleBackToList}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Quay lại
          </Button>
        </div>

        {/* Form */}
        <ActionCreateForm
          mode={currentActionType}
          onActionSaved={(action) => {
            setCurrentActionType("view");
            switchMode("detail", String(action.id), "view");
          }}
          onActionChange={(actionType) => {
            setCurrentActionType(actionType);
            if (currentActionEntity) {
              switchMode("detail", String(currentActionEntity.id), actionType);
            }
          }}
          onDeleted={() => {
            // After delete, always go back to list
            switchMode("list");
          }}
        />
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Quản lý quyền</h1>
        <Button className="w-30" onClick={handleCreateNew}>
          <Plus className="w-4 h-4 mr-2" />
          Tạo mới
        </Button>
      </div>

      {/* Table */}
      <ActionTable />
    </div>
  );
};

export default ActionAdminPage;
