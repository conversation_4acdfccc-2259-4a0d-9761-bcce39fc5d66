import React, { useState, use<PERSON><PERSON>back, KeyboardEvent } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { X, Plus } from "lucide-react";

/**
 * Props for the TagInput component
 */
interface TagInputProps {
  /** Array of current tags */
  tags: string[];
  /** Callback fired when tags change */
  onChange: (tags: string[]) => void;
  /** Placeholder text for input */
  placeholder?: string;
  /** Whether the input is disabled */
  disabled?: boolean;
  /** Maximum number of tags allowed (optional) */
  maxTags?: number;
  /** Maximum length for each tag */
  maxTagLength?: number;
  /** Custom validation function for tags */
  validateTag?: (tag: string) => string | null;
  /** CSS class name for styling */
  className?: string;
}

/**
 * TagInput component for adding and removing string tags
 * Provides keyboard navigation and validation support
 */
export const TagInput: React.FC<TagInputProps> = ({
  tags,
  onChange,
  placeholder = "Nhập và nhấn Enter để thêm...",
  disabled = false,
  maxTags,
  maxTagLength = 50,
  validateTag,
  className = "",
}) => {
  // ============================================================================
  // State Management
  // ============================================================================
  const [inputValue, setInputValue] = useState("");
  const [error, setError] = useState<string | null>(null);

  // ============================================================================
  // Validation Functions
  // ============================================================================

  /**
   * Validates a tag before adding it
   */
  const isValidTag = useCallback((tag: string): string | null => {
    const trimmedTag = tag.trim();

    if (!trimmedTag) {
      return "Thẻ không được để trống";
    }

    if (trimmedTag.length > maxTagLength) {
      return `Thẻ không được vượt quá ${maxTagLength} ký tự`;
    }

    if (tags.includes(trimmedTag)) {
      return "Thẻ này đã tồn tại";
    }

    if (maxTags && tags.length >= maxTags) {
      return `Không được vượt quá ${maxTags} thẻ`;
    }

    // Custom validation if provided
    if (validateTag) {
      return validateTag(trimmedTag);
    }

    return null;
  }, [tags, maxTags, maxTagLength, validateTag]);

  // ============================================================================
  // Event Handlers
  // ============================================================================

  /**
   * Adds a new tag
   */
  const addTag = useCallback((tagValue: string) => {
    const validationError = isValidTag(tagValue);

    if (validationError) {
      setError(validationError);
      return;
    }

    const trimmedTag = tagValue.trim();
    onChange([...tags, trimmedTag]);
    setInputValue("");
    setError(null);
  }, [tags, onChange, isValidTag]);

  /**
   * Removes a tag by index
   */
  const removeTag = useCallback((indexToRemove: number) => {
    const newTags = tags.filter((_, index) => index !== indexToRemove);
    onChange(newTags);
    setError(null);
  }, [tags, onChange]);

  /**
   * Handles input change
   */
  const handleInputChange = useCallback((value: string) => {
    setInputValue(value);
    if (error) {
      setError(null);
    }
  }, [error]);

  /**
   * Handles keyboard events
   */
  const handleKeyDown = useCallback((e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && inputValue.trim()) {
      e.preventDefault();
      addTag(inputValue);
    } else if (e.key === 'Backspace' && !inputValue && tags.length > 0) {
      // Remove last tag when backspace is pressed on empty input
      removeTag(tags.length - 1);
    }
  }, [inputValue, tags, addTag, removeTag]);

  /**
   * Handles add button click
   */
  const handleAddClick = useCallback(() => {
    if (inputValue.trim()) {
      addTag(inputValue);
    }
  }, [inputValue, addTag]);

  // ============================================================================
  // Render
  // ============================================================================

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Input Section */}
      <div className="flex gap-2">
        <div className="flex-1">
          <Input
            value={inputValue}
            onChange={(e) => handleInputChange(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            disabled={disabled || (maxTags ? tags.length >= maxTags : false)}
            className={error ? 'border-red-500 focus:border-red-500' : ''}
          />
          {error && (
            <div className="text-sm text-red-500 mt-1">{error}</div>
          )}
        </div>
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={handleAddClick}
          disabled={disabled || !inputValue.trim() || (maxTags ? tags.length >= maxTags : false)}
          className="px-3"
        >
          <Plus className="h-4 w-4" />
        </Button>
      </div>

      {/* Tags Display */}
      {tags && tags.length > 0 && (
        <div className="flex flex-wrap gap-2">
          {tags.map((tag, index) => (
            <Badge
              key={`${tag}-${index}`}
              variant="secondary"
              className="flex items-center gap-1 px-2 py-1"
            >
              <span>{tag}</span>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => removeTag(index)}
                disabled={disabled}
                className="h-4 w-4 p-0 hover:bg-transparent"
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          ))}
        </div>
      )}

      {/* Info Text */}
      <div className="text-xs text-muted-foreground">
        {maxTags ? `${tags.length}/${maxTags} thẻ` : `${tags.length} thẻ`}
        {tags.length > 0 && (
          <span className="ml-2">
            • Nhấn Backspace để xóa thẻ cuối
          </span>
        )}
      </div>
    </div>
  );
};
