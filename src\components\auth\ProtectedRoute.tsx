import React from "react";
import { Navigate, useLocation } from "react-router-dom";
import { useAppSelector } from "@/store/rootReducer";
import { selectAuthState } from "@/features/auth/states/selector";
import { LoadingPage } from "@/components/loading/LoadingPage";

interface ProtectedRouteProps {
  children: React.ReactNode;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const location = useLocation();
  const { is_authenticated, loading } = useAppSelector(selectAuthState);

  // Hi<PERSON>n thị loading khi đang kiểm tra authentication
  if (loading) {
    return <LoadingPage />;
  }

  // Nếu chưa đăng nhập, redirect về login với returnUrl
  if (!is_authenticated) {
    return (
      <Navigate
        to="/auth/login"
        state={{ returnUrl: location.pathname + location.search }}
        replace
      />
    );
  }

  // Đ<PERSON> đăng nhập, render children
  return <>{children}</>;
};
