# Loop Fix Documentation

## Vấn đề: Infinite Render Loop

Ứng dụng bị loop vô hạn khi load QuestionConfigAdminPage, gây ra:

- API calls liên tục
- Component re-render không ngừng
- Performance issues

## Nguyên nhân phát hiện:

### 1. **useEffect Dependencies Issue**

```typescript
// ❌ BEFORE: Gây loop vì fetchQAConfig được tạo mới mỗi lần render
useEffect(() => {
  fetchQAConfig();
}, [fetchQAConfig]);

// ✅ AFTER: Sử dụng dispatch trực tiếp
useEffect(() => {
  if (!qaConfig && !qaConfigLoading) {
    dispatch(fetchQAConfigAction());
  }
}, []); // Empty dependency array
```

### 2. **Function Re-creation trong useQuestion Hook**

```typescript
// ❌ BEFORE: Functions được tạo mới mỗi render
const handleFetchQAConfig = () => {
  dispatch(fetchQAConfig());
};

// ✅ AFTER: Sử dụng useCallback để memoize
const handleFetchQAConfig = useCallback(() => {
  dispatch(fetchQAConfig());
}, [dispatch]);
```

### 3. **Auto-fetch trong Test Connection**

```typescript
// ❌ BEFORE: Test success trigger fetch lại
if (result.success) {
  setTestStatus("success");
  fetchQAConfig(); // Gây loop!
}

// ✅ AFTER: Không auto-fetch
if (result.success) {
  setTestStatus("success");
  // User có thể manual refresh nếu cần
}
```

### 4. **Conditional Fetch**

```typescript
// ✅ Chỉ fetch khi chưa có data và không đang loading
useEffect(() => {
  if (!qaConfig && !qaConfigLoading) {
    dispatch(fetchQAConfigAction());
  }
}, []);
```

## Các Fix đã áp dụng:

### ✅ 1. QuestionConfigAdminPage.tsx

- Sử dụng `dispatch` trực tiếp thay vì function từ hook
- Empty dependency array cho useEffect
- Conditional fetch để tránh fetch khi đã có data
- Disable auto-fetch trong test connection
- Thêm debug logs để monitor renders

### ✅ 2. useQuestion.ts

- Thêm `useCallback` cho QA Config functions
- Memoize `handleFetchQAConfig`, `handleUpdateQAConfig`, `handleClearQAConfigError`
- Prevent function re-creation gây dependency changes

### ✅ 3. slices.ts

- Giữ nguyên logic nhưng đảm bảo không có side effects
- Mock data fallback không gây re-fetch

### ✅ 4. API Endpoints Fix

- GET QA Config: `/portal/v1/public/config/qa_config` (Public access)
- POST QA Config: `/portal/v1/admin/config/qa_config` (Admin access)
- Updated QAConfigAPI to use correct endpoints

## Testing:

### Debug Console Logs:

```
🔄 QuestionConfigAdminPage render: {
  qaConfig: 'loaded' | 'null',
  qaConfigLoading: true | false,
  qaConfigError: 'has error' | 'no error'
}
```

### Expected Behavior:

1. **First render**: qaConfig: null, qaConfigLoading: false → Trigger fetch
2. **Loading**: qaConfig: null, qaConfigLoading: true
3. **Success**: qaConfig: loaded, qaConfigLoading: false → Stop
4. **No more renders** unless user interaction

### Red Flags:

- Console log lặp lại liên tục
- qaConfigLoading flip-flop giữa true/false
- API calls không ngừng trong Network tab

## Prevention Guidelines:

### ✅ DO:

- Sử dụng `useCallback` cho functions trong custom hooks
- Empty dependency arrays cho one-time effects
- Conditional logic để tránh unnecessary API calls
- Direct dispatch thay vì wrapped functions trong useEffect

### ❌ DON'T:

- Đặt functions từ hooks vào dependency arrays
- Auto-trigger fetch trong success callbacks
- Fetch khi đã có data mà không cần thiết
- Tạo functions mới mỗi render

## Monitoring:

Để phát hiện loops trong tương lai:

1. Check console logs có lặp lại không
2. Monitor Network tab cho repeated API calls
3. Use React DevTools Profiler
4. Add render counters trong development mode
