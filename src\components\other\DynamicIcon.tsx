import React from "react";
import {
  Home,
  Folder,
  Dot,
  User,
  Users,
  Settings,
  List,
  Bell,
  FilePlus,
  Settings2,
  UserCog,
  ShieldPlus,
  FolderCog,
  BriefcaseBusiness,
  FileText,
  ArrowUpFromLine,
} from "lucide-react";
import { InputOption } from "../form/types";

const iconMap = {
  Home,
  Folder,
  Dot,
  User,
  Users,
  Settings,
  List,
  Bell,
  FilePlus,
  Settings2,
  UserCog,
  ShieldPlus,
  FolderCog,
  BriefcaseBusiness,
  FileText,
  ArrowUpFromLine,
};

export const IconOptions: InputOption[] = [
  {
    value: "Home",
    label: <Home />,
  },
  {
    value: "Folder",
    label: <Folder />,
  },
  {
    value: "Dot",
    label: <Dot />,
  },
  {
    value: "User",
    label: <User />,
  },
  {
    value: "Users",
    label: <Users />,
  },
  {
    value: "Settings",
    label: <Settings />,
  },
  {
    value: "List",
    label: <List />,
  },
  {
    value: "Bell",
    label: <Bell />,
  },
  {
    value: "FilePlus",
    label: <FilePlus />,
  },
  {
    value: "Settings2",
    label: <Settings2 />,
  },
  {
    value: "UserCog",
    label: <UserCog />,
  },
  {
    value: "ShieldPlus",
    label: <ShieldPlus />,
  },
  {
    value: "FolderCog",
    label: <FolderCog />,
  },
  {
    value: "BriefcaseBusiness",
    label: <BriefcaseBusiness />,
  },
  {
    value: "FileText",
    label: <FileText />,
  },
];

interface DynamicIconProps extends React.SVGProps<SVGSVGElement> {
  icon: string;
}

export const DynamicIcon: React.FC<DynamicIconProps> = ({ icon, ...props }) => {
  const IconComponent = iconMap[icon as keyof typeof iconMap];
  return IconComponent ? <IconComponent {...props} /> : null;
};
