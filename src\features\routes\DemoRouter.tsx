import { createBrowserRouter, RouterProvider } from "react-router-dom";
import { ErrorBoundary } from "./ErrorBoudary";
import { DemoPage } from "../demo/pages/loadable";

export const DemoRouter: React.FC = () => {
  const router = createBrowserRouter([
    {
      path: "/",
      errorElement: <ErrorBoundary />,
      element: <DemoPage />,
    },
  ]);
  return <RouterProvider router={router} />;
};
