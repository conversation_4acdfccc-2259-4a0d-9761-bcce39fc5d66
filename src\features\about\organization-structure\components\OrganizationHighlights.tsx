import React from "react";

// ============================================================================
// Types
// ============================================================================

interface HighlightItem {
  /** Highlight number (01, 02, 03) */
  number: string;
  /** Highlight title */
  title: string;
  /** Highlight description */
  description: string;
}

interface OrganizationHighlightsProps {
  /** Optional custom className */
  className?: string;
}

// ============================================================================
// Mock Data
// ============================================================================

const highlightItems: HighlightItem[] = [
  {
    number: "01",
    title: "Lãnh đạo Trung tâm",
    description: "Có Giám đốc và không quá 02 Phó giám đốc"
  },
  {
    number: "02", 
    title: "Không tổ chức các phòng thuộc Trung tâm",
    description: "Tổ chức tinh gọn, hiệu quả"
  },
  {
    number: "03",
    title: "Trung tâm Lưu trữ lịch sử có số lượng người làm việc",
    description: "Của Sở Nội vụ đã được Ủy ban nhân dân tỉnh giao"
  }
];

// ============================================================================
// Highlight Item Component
// ============================================================================

interface HighlightItemComponentProps {
  /** Highlight item data */
  item: HighlightItem;
  /** Optional custom className */
  className?: string;
}

const HighlightItemComponent: React.FC<HighlightItemComponentProps> = ({
  item,
  className = ""
}) => {
  return (
    <div className={`text-center p-6 ${className}`}>
      {/* Number Circle */}
      <div className="mb-4">
        <span className="inline-flex items-center justify-center w-12 h-12 rounded-full bg-blue-100 text-blue-600 text-xl font-bold">
          {item.number}
        </span>
      </div>
      
      {/* Title */}
      <h4 className="font-semibold text-base text-gray-900 mb-3 leading-tight">
        {item.title}
      </h4>
      
      {/* Description */}
      <p className="text-sm text-gray-600 leading-relaxed">
        {item.description}
      </p>
    </div>
  );
};

// ============================================================================
// Main Organization Highlights Component
// ============================================================================

/**
 * Organization Highlights Component
 * 
 * Displays three numbered highlight cards showing key organizational features
 * Uses a horizontal layout with responsive design
 * 
 * @param props - Component props
 * @returns JSX element for organization highlights
 */
export const OrganizationHighlights: React.FC<OrganizationHighlightsProps> = ({
  className = ""
}) => {
  return (
    <div className={`w-full ${className}`}>
      {/* Highlights Grid */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8">
        {highlightItems.map((item, index) => (
          <HighlightItemComponent
            key={`highlight-${item.number}`}
            item={item}
            className={`
              ${index < highlightItems.length - 1 ? 'md:border-r md:border-gray-200' : ''}
            `}
          />
        ))}
      </div>
    </div>
  );
};

export default OrganizationHighlights;
