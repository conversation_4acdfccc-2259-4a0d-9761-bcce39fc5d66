import React, { useState } from "react";
import { CategoryTreeBase } from "@/features/category/components/CategoryTree";
import { useAppSelector } from "@/store/rootReducer";
import { selectCategoryByType } from "@/features/category/states/selector";

interface PublicCategoryTreeProps {
  onCategorySelect?: (categoryId: number) => void;
  className?: string;
}

export const PublicCategoryTree: React.FC<PublicCategoryTreeProps> = ({
  onCategorySelect,
  className,
}) => {
  const [selectedId, setSelectedId] = useState<number | null>(null);

  // Lấy danh sách category thuộc loại public-menu
  const publicCategories = useAppSelector((state) =>
    selectCategoryByType(state, "public-menu")
  );

  const handleSelected = (id: number) => {
    setSelectedId(id);
    onCategorySelect?.(id);
  };

  return (
    <div className={className}>
      <CategoryTreeBase
        allCategories={publicCategories}
        selectedId={selectedId}
        onSelected={handleSelected}
      />
    </div>
  );
};

export default PublicCategoryTree;
