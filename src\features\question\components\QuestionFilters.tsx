import React from "react";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import type { QuestionStatus } from "../states/types";

interface QuestionFiltersProps {
  currentStatus: QuestionStatus | "all";
  onStatusChange: (status: QuestionStatus | "all") => void;
}

const statusOptions = [
  { value: "all", label: "Tất cả", color: "" },
  { value: "NEW", label: "Chưa trả lời", color: "text-blue-600" },
  { value: "DRAFT", label: "Đã trả lời", color: "text-yellow-600" },
  { value: "PUBLISHED", label: "Đã xuất bản", color: "text-green-600" },
  { value: "TRASH", label: "Thùng rác", color: "text-red-600" },
] as const;

export const QuestionFilters: React.FC<QuestionFiltersProps> = ({
  currentStatus,
  onStatusChange,
}) => {
  return (
    <div className="w-full">
      <Tabs
        value={currentStatus}
        onValueChange={(value) =>
          onStatusChange(value as QuestionStatus | "all")
        }
      >
        <TabsList className="grid w-full grid-cols-5">
          {statusOptions.map((option) => (
            <TabsTrigger
              key={option.value}
              value={option.value}
              className={`${option.color} data-[state=active]:bg-primary data-[state=active]:text-primary-foreground`}
            >
              {option.label}
            </TabsTrigger>
          ))}
        </TabsList>
      </Tabs>
    </div>
  );
};
