import { useCallback } from "react";
import { useAppDispatch, useAppSelector } from "@/store/rootReducer";
import type { RootState } from "@/store/rootReducer";
import {
  setQuestionFilters,
  setQuestionLoading,
  setQuestionError,
  triggerQuestionRefetch,
  resetQuestionFilters,
} from "../states/questionFilterSlice";
import type { QuestionQueryParams } from "../states/types";

// Selectors for question filter state
export const selectQuestionFilterParams = (state: RootState) =>
  state.questionFilter?.params;
export const selectQuestionFilterRefetch = (state: RootState) =>
  state.questionFilter?.refetch;
export const selectQuestionFilterLoading = (state: RootState) =>
  state.questionFilter?.loading;
export const selectQuestionFilterError = (state: RootState) =>
  state.questionFilter?.error;

export const useQuestionFilter = () => {
  const dispatch = useAppDispatch();

  // Selectors
  const params = useAppSelector(selectQuestionFilterParams);
  const refetch = useAppSelector(selectQuestionFilterRefetch);
  const loading = useAppSelector(selectQuestionFilterLoading);
  const error = useAppSelector(selectQuestionFilterError);

  // Actions
  const setFilters = useCallback(
    (newParams: Partial<QuestionQueryParams>) => {
      console.log("🔄 setQuestionFilters:", newParams);
      dispatch(setQuestionFilters(newParams));
    },
    [dispatch]
  );

  const setLoading = useCallback(
    (loading: boolean) => {
      dispatch(setQuestionLoading(loading));
    },
    [dispatch]
  );

  const setError = useCallback(
    (error: string | null) => {
      dispatch(setQuestionError(error));
    },
    [dispatch]
  );

  const triggerRefetch = useCallback(() => {
    console.log("🔄 triggerQuestionRefetch called");
    dispatch(triggerQuestionRefetch());
  }, [dispatch]);

  const resetFilters = useCallback(() => {
    dispatch(resetQuestionFilters());
  }, [dispatch]);

  return {
    // State
    params,
    refetch,
    loading,
    error,

    // Actions
    setFilters,
    setLoading,
    setError,
    triggerRefetch,
    resetFilters,
  };
};
