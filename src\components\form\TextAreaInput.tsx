import React from "react";
import { BasicInputProps } from "./registry";

export const TextAreaInput = React.forwardRef<
  HTMLTextAreaElement,
  BasicInputProps<"text">
>(({ value, onChange, isViewMode, ...rest }, ref) => {
  const handleChange = isViewMode
    ? undefined
    : (e: React.ChangeEvent<HTMLTextAreaElement>) => onChange(e.target.value);

  return (
    <textarea
      ref={ref}
      value={value}
      onChange={handleChange}
      readOnly={isViewMode}
      className="border rounded p-2 w-full"
      {...rest}
    />
  );
});
