import React from "react";
import { useTranslation } from "react-i18next";
import { CircleX } from "lucide-react";
import { Button } from "@/components/ui/button";

interface DialogProps {
  title?: React.ReactNode;
  message: React.ReactNode;
  confirm_label?: React.ReactNode;
  cancel_label?: React.ReactNode;
  onConfirm?: () => void;
  onCancel?: () => void;
  onClose?: () => void;
}

const Dialog: React.FC<DialogProps> = ({
  title,
  message,
  confirm_label,
  cancel_label,
  onConfirm,
  onCancel,
  onClose,
}) => {
  const { t } = useTranslation();

  return (
    <div className="relative p-6 border rounded-lg bg-white w-full min-w-sm max-w-md">
      {onClose && (
        <button
          title="Close"
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-600 hover:text-gray-500 transition"
        >
          <CircleX />
        </button>
      )}

      <h2 className="text-xl font-semibold text-text-900">
        {title ?? t("notification.title")}
      </h2>
      <p className="text-text-700 mt-3">{message}</p>

      <div className="mt-6 flex justify-end space-x-3">
        {onCancel && (
          <Button onClick={onCancel} variant="outline">
            <div className="px-4 text-xl font-semibold py-2">
              {cancel_label ?? t("notification.action.cancel")}
            </div>
          </Button>
        )}
        {onConfirm && (
          <Button onClick={onConfirm} variant="default">
            <div className="px-4 text-xl font-semibold py-2">
              {confirm_label ?? t("notification.action.confirm")}
            </div>
          </Button>
        )}
      </div>
    </div>
  );
};

export default Dialog;
