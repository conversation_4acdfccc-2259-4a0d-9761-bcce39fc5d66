// import { FormNode } from "@/features/builder/autoform/states/types";
import { FieldConfig, FormConfig, FormNode, FieldValue } from "./registry";

// Overloaded function to handle different input types
export function flattenObject(
  obj: Record<string, unknown>,
  prefix?: string
): Record<string, unknown>;
export function flattenObject(
  obj: Record<string, any>,
  prefix?: string
): Record<string, FieldValue>;
export function flattenObject(obj: any, prefix = ""): Record<string, any> {
  const flattened: Record<string, any> = {};

  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      const value = obj[key];
      const newKey = prefix ? `${prefix}-${key}` : key;

      if (
        typeof value === "object" &&
        value !== null &&
        !Array.isArray(value)
      ) {
        Object.assign(flattened, flattenObject(value, newKey));
      } else {
        flattened[newKey] = value;
      }
    }
  }

  return flattened;
}

export function unflattenObject<T = Record<string, unknown>>(
  obj: Record<string, unknown>
): T {
  const result: Record<string, unknown> = {};

  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      const keys = key.split("-");
      let current = result;

      for (let i = 0; i < keys.length - 1; i++) {
        const k = keys[i];
        if (!(k in current)) {
          current[k] = {};
        }
        current = current[k] as Record<string, unknown>;
      }

      current[keys[keys.length - 1]] = obj[key];
    }
  }

  return result as T;
}

export function extractFieldConfigs(config: FormConfig): FieldConfig[] {
  const fields: FieldConfig[] = [];

  function traverse(node: FormNode) {
    if (node.type === "field" && node.fieldConfig) {
      fields.push(node.fieldConfig);
    } else if (node.children && Array.isArray(node.children)) {
      node.children.forEach((child: FormNode) => traverse(child));
    }
  }

  traverse(config.config);
  return fields;
}
