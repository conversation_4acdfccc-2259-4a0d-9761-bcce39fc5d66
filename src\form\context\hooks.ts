/**
 * FormContext Hooks - Custom hooks for form context
 */

import { useContext } from "react";
import {
  FormContext as FormContextInstance,
  FormContext as FormContextType,
} from "./FormContext";

/**
 * Hook to access FormContext
 */
export const useFormContext = (): FormContextType => {
  const context = useContext(FormContextInstance);
  return context;
};

/**
 * Hook to check if we're in view-only mode
 */
export const useViewOnly = (): boolean => {
  const { viewOnly } = useFormContext();
  return viewOnly;
};

/**
 * Hook to access form instance
 */
export const useFormInstance = () => {
  const { form } = useFormContext();
  return form;
};
