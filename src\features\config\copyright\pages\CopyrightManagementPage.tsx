import React, { useEffect, useState, useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import type { AppDispatch } from "@/store/store";

import { ConfigPageLayout } from "../../shared/components/layouts";
import { CopyrightForm } from "../components/CopyrightForm";
import { CopyrightPreview } from "../components/CopyrightPreview";
import type { CopyrightConfig } from "../states/type";
import {
  fetchCopyrightAsync,
  updateCopyrightAsync,
  setDirty,
  clearError,
} from "../states/slices";
import {
  selectCopyrightData,
  selectCopyrightSavedData,
  selectCopyrightLoading,
  selectCopyrightSaving,
  selectCopyrightError,
  selectCopyrightIsDirty,
  selectCopyrightIsOperating,
} from "../states/selector";
import { toast } from "sonner";

// ============================================================================
// Component
// ============================================================================

export const CopyrightManagementPage: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const data = useSelector(selectCopyrightData);
  const savedData = useSelector(selectCopyrightSavedData);
  const loading = useSelector(selectCopyrightLoading);
  const saving = useSelector(selectCopyrightSaving);
  const error = useSelector(selectCopyrightError);
  const isDirty = useSelector(selectCopyrightIsDirty);
  const isOperating = useSelector(selectCopyrightIsOperating);

  const [isValidationValid, setIsValidationValid] = useState(true);
  const [currentData, setCurrentData] = useState<CopyrightConfig | null>(null);
  const [showPreview, setShowPreview] = useState(false);

  // ============================================================================
  // Effects
  // ============================================================================

  /**
   * Load copyright data on component mount
   */
  useEffect(() => {
    dispatch(fetchCopyrightAsync());

    // Clear any existing errors when component mounts
    return () => {
      dispatch(clearError());
    };
  }, [dispatch]);

  /**
   * Update current data when Redux data changes
   */
  useEffect(() => {
    if (data) {
      setCurrentData(data);
    }
  }, [data]);

  // ============================================================================
  // Event Handlers
  // ============================================================================

  /**
   * Handles data changes from the form component
   */
  const handleDataChange = useCallback((newData: CopyrightConfig) => {
    setCurrentData(newData);
    dispatch(setDirty(true));
  }, [dispatch]);

  /**
   * Handles validation state changes
   */
  const handleValidationChange = useCallback((isValid: boolean) => {
    setIsValidationValid(isValid);
  }, []);

  /**
   * Handles save operation
   */
  const handleSave = useCallback(async () => {
    if (!currentData || !isValidationValid) {
      return;
    }

    try {
      await dispatch(updateCopyrightAsync(currentData)).unwrap();
    } catch (error) {
      // Error is handled by the async thunk
      toast.error(`Lỗi khi lưu dữ liệu bản quyền: ${String(error)}`);
    }
  }, [dispatch, currentData, isValidationValid]);

  /**
   * Handles refresh operation
   */
  const handleRefresh = useCallback(() => {
    dispatch(fetchCopyrightAsync());
  }, [dispatch]);

  /**
   * Handles preview toggle
   */
  const handlePreview = useCallback(() => {
    setShowPreview(!showPreview);
  }, [showPreview]);

  // ============================================================================
  // Computed Values
  // ============================================================================

  const saveDisabled = !isValidationValid || isOperating || !currentData;

  // ============================================================================
  // Render
  // ============================================================================

  return (
    <ConfigPageLayout
      title="Quản lý bản quyền"
      description="Cấu hình nội dung bản quyền hiển thị trên website"
      loading={saving}
      error={error}
      onSave={handleSave}
      onRefresh={handleRefresh}
      onPreview={handlePreview}
      showPreview={showPreview}
      saveDisabled={saveDisabled}
      isDirty={isDirty}
      showDirtyAlert={true}
    >
      <div className="flex flex-col h-full space-y-6">
        <div className="flex-shrink-0 ">
          <CopyrightForm
            data={currentData}
            loading={loading}
            onDataChange={handleDataChange}
            onValidationChange={handleValidationChange}
          />
        </div>

        {showPreview && (
          <CopyrightPreview
            data={savedData}
            visible={showPreview}
          />
        )}
      </div>
    </ConfigPageLayout>
  );
};

export default CopyrightManagementPage;
