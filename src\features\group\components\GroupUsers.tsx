import React, { useEffect, useState, useCallback } from "react";
import { useAppDispatch, useAppSelector } from "@/store/rootReducer";
import { selectSelectedGroup } from "../states/selectors";
import {
  addUsersToGroupThunk,
  removeMemberFromGroupThunk,
} from "../states/slices";
import { fetchGroupMembers } from "../states/api";
import { UserData } from "@/features/users/states/type";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { GroupAction } from "../states/types";
import { useNavigate } from "react-router-dom";
import { GroupUsersTable } from "./GroupUsersTable";
import { AddUsersDialog } from "./AddUsersDialog";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";

interface GroupUsersProps {
  currentAction: GroupAction;
  onActionChange: (action: GroupAction) => void;
}

export const GroupUsers: React.FC<GroupUsersProps> = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const selectedGroup = useAppSelector(selectSelectedGroup);

  // Local state for users data
  const [users, setUsers] = useState<UserData[]>([]);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(0);
  const [pageSize, setPageSize] = useState(10);

  // Dialog states
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [isModifying, setIsModifying] = useState(false);
  const [confirmRemove, setConfirmRemove] = useState<{
    show: boolean;
    user: UserData | null;
  }>({ show: false, user: null });

  // Load group members directly from API
  const loadGroupMembers = useCallback(
    async (page = 0, size = 10) => {
      if (!selectedGroup) return;

      setLoading(true);
      try {
        const response = await fetchGroupMembers(selectedGroup.id, {
          page,
          size,
        });

        if (response.data) {
          setUsers(response.data);
        } else {
          setUsers([]);
        }
      } catch (error) {
        console.error("Failed to load group members:", error);
        setUsers([]);
      } finally {
        setLoading(false);
      }
    },
    [selectedGroup]
  );

  // Load members when group changes
  useEffect(() => {
    if (selectedGroup?.id) {
      loadGroupMembers(0, pageSize);
      setCurrentPage(0);
    }
  }, [selectedGroup?.id, pageSize, loadGroupMembers]);

  // Handle page change
  const handlePageChange = (pageIndex: number) => {
    setCurrentPage(pageIndex);
    loadGroupMembers(pageIndex, pageSize);
  };

  // Handle page size change
  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setCurrentPage(0);
    loadGroupMembers(0, newPageSize);
  };

  // Add users to group using batch API
  const handleAddUsers = async (userIds: number[]) => {
    if (!selectedGroup || userIds.length === 0) return;

    setIsModifying(true);
    try {
      // Use new batch add users API
      await dispatch(
        addUsersToGroupThunk({ groupId: selectedGroup.id, userIds })
      );

      // Reload members from API
      await loadGroupMembers(currentPage, pageSize);
    } catch (error) {
      console.error("Error adding users:", error);
      throw error; // Re-throw to let dialog handle it
    } finally {
      setIsModifying(false);
    }
  };

  // Remove user from group with confirmation
  const handleRemoveUser = async () => {
    if (!selectedGroup || !confirmRemove.user) return;

    setIsModifying(true);
    try {
      await dispatch(
        removeMemberFromGroupThunk({
          groupId: selectedGroup.id,
          userId: confirmRemove.user.id,
        })
      );

      // Reload members from API
      await loadGroupMembers(currentPage, pageSize);
      setConfirmRemove({ show: false, user: null });
    } catch (error) {
      console.error("Error removing user:", error);
    } finally {
      setIsModifying(false);
    }
  };

  // Handle table actions
  const handleAction = (action: string, user: UserData) => {
    if (action === "view") {
      navigate(`/admin/quan-ly-nguoi-dung?mode=detail&user=${user.id}`);
    } else if (action === "remove") {
      setConfirmRemove({ show: true, user });
    }
  };

  if (!selectedGroup) {
    return (
      <div className="p-6 text-center text-gray-500">
        Chọn một nhóm để quản lý người dùng
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-lg font-semibold">
          Danh sách người dùng ({users.length})
        </h3>

        <Button
          onClick={() => setShowAddDialog(true)}
          disabled={isModifying || loading}
        >
          <Plus className="h-4 w-4 mr-2" />
          Thêm người dùng
        </Button>
      </div>

      {/* Users Table */}
      <GroupUsersTable
        users={users}
        loading={loading}
        currentPage={currentPage}
        pageSize={pageSize}
        onPageChange={handlePageChange}
        onPageSizeChange={handlePageSizeChange}
        onAction={handleAction}
      />

      {/* Add Users Dialog */}
      <AddUsersDialog
        open={showAddDialog}
        onOpenChange={setShowAddDialog}
        currentUsers={users}
        onAddUsers={handleAddUsers}
        isLoading={isModifying}
      />

      {/* Remove User Confirmation Dialog */}
      <Dialog
        open={confirmRemove.show}
        onOpenChange={(open) => setConfirmRemove({ show: open, user: null })}
      >
        <DialogContent className="bg-white border border-gray-200 shadow-lg">
          <DialogHeader className="bg-white border-b border-gray-100">
            <DialogTitle className="text-gray-900">
              Xác nhận xóa người dùng
            </DialogTitle>
          </DialogHeader>

          <div className="bg-white py-4">
            <p className="text-gray-700">
              Bạn có chắc chắn muốn xóa người dùng{" "}
              <strong className="text-gray-900">
                {confirmRemove.user?.fullName || confirmRemove.user?.userName}
              </strong>{" "}
              khỏi nhóm{" "}
              <strong className="text-gray-900">{selectedGroup?.name}</strong>?
            </p>
          </div>

          <DialogFooter className="bg-white border-t border-gray-100">
            <Button
              variant="outline"
              onClick={() => setConfirmRemove({ show: false, user: null })}
              disabled={isModifying}
              className="bg-white border-gray-200 text-gray-700 hover:bg-gray-50"
            >
              Hủy
            </Button>
            <Button
              variant="destructive"
              onClick={handleRemoveUser}
              disabled={isModifying}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              {isModifying ? "Đang xóa..." : "Xóa người dùng"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};
