import { RootState } from "@/store/rootReducer";

export const selectQuestionState = (state: RootState) => state.question;

// Question selectors
export const selectQuestions = (state: RootState) =>
  selectQuestionState(state)?.questions || [];

export const selectQuestionsLoading = (state: RootState) =>
  selectQuestionState(state)?.questionsLoading || false;

export const selectQuestionsError = (state: RootState) =>
  selectQuestionState(state)?.questionsError || null;

export const selectQuestionsPagination = (state: RootState) =>
  selectQuestionState(state)?.questionsPagination || null;

export const selectSelectedQuestion = (state: RootState) =>
  selectQuestionState(state)?.selectedQuestion || null;

// Answer selectors
export const selectAnswers = (state: RootState) =>
  selectQuestionState(state)?.answers || [];

export const selectAnswersLoading = (state: RootState) =>
  selectQuestionState(state)?.answersLoading || false;

export const selectAnswersError = (state: RootState) =>
  selectQuestionState(state)?.answersError || null;

export const selectAnswersPagination = (state: RootState) =>
  selectQuestionState(state)?.answersPagination || null;

export const selectSelectedAnswer = (state: RootState) =>
  selectQuestionState(state)?.selectedAnswer || null;

// QA Config selectors
export const selectQAConfig = (state: RootState) =>
  selectQuestionState(state)?.qaConfig || null;

export const selectQAConfigLoading = (state: RootState) =>
  selectQuestionState(state)?.qaConfigLoading || false;

export const selectQAConfigError = (state: RootState) =>
  selectQuestionState(state)?.qaConfigError || null;

// Legacy selectors for backward compatibility
export const selectQuestionItems = selectQuestions;
export const selectQuestionLoading = selectQuestionsLoading;
export const selectQuestionGlobalError = selectQuestionsError;
