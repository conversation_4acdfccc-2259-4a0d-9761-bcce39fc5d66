import React, { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { useAppDispatch, useAppSelector } from "@/store/rootReducer";
import { selectAuthState } from "@/features/auth/states/selector";
import { selectRoles } from "@/features/role/states/selectors";
import { setCurrentRole } from "@/features/auth/states/slices";
import { fetchRolesThunk } from "@/features/role/states/slices";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Shield } from "lucide-react";

interface RoleSelectorProps {
  onSelect?: () => void;
}

export const RoleSelector: React.FC<RoleSelectorProps> = ({ onSelect }) => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { account } = useAppSelector(selectAuthState);
  const roles = useAppSelector(selectRoles);

  // Fetch roles when component mounts
  useEffect(() => {
    if (account?.roles?.length) {
      dispatch(fetchRolesThunk());
    }
  }, [dispatch, account?.roles]);

  // Lọc các vai trò mà người dùng có
  const userRoles = roles.filter((role) => account?.roles?.includes(role.id));

  if (!userRoles.length) {
    return null;
  }

  const handleRoleSelect = (roleId: number) => {
    dispatch(setCurrentRole(roleId));
    navigate("/admin");
    onSelect?.();
  };

  if (userRoles.length === 1) {
    return (
      <Button
        variant="default"
        size="sm"
        className="flex items-center gap-2"
        onClick={() => handleRoleSelect(userRoles[0].id)}
      >
        <Shield className="w-4 h-4" />
        <span>Trang Quản Trị</span>
      </Button>
    );
  }

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="default" size="sm" className="flex items-center gap-2">
          <Shield className="w-4 h-4" />
          <span>Trang Quản Trị</span>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-56 p-2 z-[1000]">
        <div className="flex flex-col gap-1">
          {userRoles.map((role) => (
            <Button
              key={role.id}
              variant="ghost"
              className="w-full justify-start"
              onClick={() => handleRoleSelect(role.id)}
            >
              {role.name}
            </Button>
          ))}
        </div>
      </PopoverContent>
    </Popover>
  );
};
