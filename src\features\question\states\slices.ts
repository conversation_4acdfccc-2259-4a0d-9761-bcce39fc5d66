import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import {
  Question,
  Answer,
  QuestionState,
  CreateQuestionRequest,
  UpdateQuestionRequest,
  CreateAnswerRequest,
  UpdateAnswerRequest,
  QuestionQueryParams,
  AnswerQueryParams,
  QuestionStatus,
  Pagination,
  QAConfigRequest,
} from "./types";
import { QuestionAPI, AnswerAPI, QAConfigAPI } from "./api";
import { mockQAConfig } from "../data/mockData";

const initialState: QuestionState = {
  // Questions
  questions: [],
  questionsLoading: false,
  questionsError: null,
  questionsPagination: null,
  selectedQuestion: null,

  // Answers
  answers: [],
  answersLoading: false,
  answersError: null,
  answersPagination: null,
  selectedAnswer: null,

  // Q&A Configuration
  qaConfig: null,
  qaConfigLoading: false,
  qaConfigError: null,
};

// ==================== QUESTION ASYNC THUNKS ====================

export const fetchQuestions = createAsyncThunk(
  "question/fetchQuestions",
  async (_params?: QuestionQueryParams) => {
    try {
      // TODO: Replace with real API call
      const { mockQuestions } = await import("../data/mockData");
      // Simulate API delay
      await new Promise((resolve) => setTimeout(resolve, 1000));
      return {
        data: mockQuestions,
        pagination: { page: 0, size: 10, totalElements: mockQuestions.length },
      };
    } catch (error) {
      throw error;
    }
  }
);

export const fetchPublishedQuestions = createAsyncThunk(
  "question/fetchPublishedQuestions",
  async (params?: QuestionQueryParams) => {
    try {
      const response = await QuestionAPI.getPublishedQuestions(params);
      return response.data;
    } catch (error) {
      throw error;
    }
  }
);

export const fetchQuestionById = createAsyncThunk(
  "question/fetchQuestionById",
  async (id: number) => {
    try {
      const response = await QuestionAPI.getPublishedQuestionById(id);
      return response.data;
    } catch (error) {
      throw error;
    }
  }
);

export const createQuestion = createAsyncThunk(
  "question/createQuestion",
  async (questionData: CreateQuestionRequest) => {
    try {
      const response = await QuestionAPI.createQuestion(questionData);
      return response.data;
    } catch (error) {
      throw error;
    }
  }
);

export const updateQuestion = createAsyncThunk(
  "question/updateQuestion",
  async ({ id, data }: { id: number; data: UpdateQuestionRequest }) => {
    try {
      const response = await QuestionAPI.updateQuestion(id, data);
      return response.data;
    } catch (error) {
      throw error;
    }
  }
);

export const deleteQuestion = createAsyncThunk(
  "question/deleteQuestion",
  async (questionId: number) => {
    try {
      const response = await QuestionAPI.deleteQuestion(questionId);
      return { id: questionId, ...response };
    } catch (error) {
      throw error;
    }
  }
);

export const publishQuestion = createAsyncThunk(
  "question/publishQuestion",
  async (questionId: number) => {
    try {
      const response = await QuestionAPI.publishQuestion(questionId);
      return { id: questionId, ...response };
    } catch (error) {
      throw error;
    }
  }
);

export const updateQuestionStatus = createAsyncThunk(
  "question/updateQuestionStatus",
  async ({ id, status }: { id: number; status: QuestionStatus }) => {
    try {
      const response = await QuestionAPI.updateQuestionStatus(id, status);
      return { id, status, ...response };
    } catch (error) {
      throw error;
    }
  }
);

// ==================== QA CONFIG ASYNC THUNKS ====================

export const fetchQAConfig = createAsyncThunk(
  "question/fetchQAConfig",
  async () => {
    try {
      const response = await QAConfigAPI.getQAConfig();
      // Access data from AxiosResponse<BaseResponse<QAConfig>>
      return response.data.data;
    } catch (error: any) {
      console.warn("QA Config API failed, using mock data:", error.message);
      // Return mock data but mark it as fallback
      return {
        ...mockQAConfig,
        _isMockData: true,
      };
    }
  }
);

export const updateQAConfig = createAsyncThunk(
  "question/updateQAConfig",
  async (configData: QAConfigRequest, { rejectWithValue }) => {
    try {
      const response = await QAConfigAPI.updateQAConfig(configData);
      // Access data from AxiosResponse<BaseResponse<QAConfig>>
      return response.data.data;
    } catch (error: any) {
      console.error("Failed to update QA Config:", error);
      return rejectWithValue(
        error.response?.data?.message ||
          error.message ||
          "Failed to update QA configuration"
      );
    }
  }
);

// ==================== ANSWER ASYNC THUNKS ====================

export const fetchAnswers = createAsyncThunk(
  "question/fetchAnswers",
  async (params?: AnswerQueryParams) => {
    try {
      const response = await AnswerAPI.getAllAnswers(params);
      return response.data;
    } catch (error) {
      throw error;
    }
  }
);

export const fetchAnswersByQuestionId = createAsyncThunk(
  "question/fetchAnswersByQuestionId",
  async ({
    questionId,
    params,
  }: {
    questionId: number;
    params?: AnswerQueryParams;
  }) => {
    try {
      const response = await AnswerAPI.getAnswersByQuestionId(
        questionId,
        params
      );
      return response.data;
    } catch (error) {
      throw error;
    }
  }
);

export const createAnswer = createAsyncThunk(
  "question/createAnswer",
  async (answerData: CreateAnswerRequest) => {
    try {
      const response = await AnswerAPI.createAnswer(answerData);
      return response.data;
    } catch (error) {
      throw error;
    }
  }
);

export const updateAnswer = createAsyncThunk(
  "question/updateAnswer",
  async ({ id, data }: { id: number; data: UpdateAnswerRequest }) => {
    try {
      const response = await AnswerAPI.updateAnswer(id, data);
      return response.data;
    } catch (error) {
      throw error;
    }
  }
);

// ==================== SLICE ====================

const questionSlice = createSlice({
  name: "question",
  initialState,
  reducers: {
    setSelectedQuestion: (state, action: PayloadAction<Question | null>) => {
      state.selectedQuestion = action.payload;
    },
    setSelectedAnswer: (state, action: PayloadAction<Answer | null>) => {
      state.selectedAnswer = action.payload;
    },
    clearQuestionsError: (state) => {
      state.questionsError = null;
    },
    clearAnswersError: (state) => {
      state.answersError = null;
    },
    setQuestionsPagination: (state, action: PayloadAction<Pagination>) => {
      state.questionsPagination = action.payload;
    },
    setAnswersPagination: (state, action: PayloadAction<Pagination>) => {
      state.answersPagination = action.payload;
    },
    clearQAConfigError: (state) => {
      state.qaConfigError = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // ==================== FETCH QUESTIONS ====================
      .addCase(fetchQuestions.pending, (state) => {
        state.questionsLoading = true;
        state.questionsError = null;
      })
      .addCase(fetchQuestions.fulfilled, (state, action) => {
        state.questionsLoading = false;
        if (Array.isArray(action.payload)) {
          state.questions = action.payload;
        } else {
          state.questions = (action.payload as any)?.data || [];
          state.questionsPagination =
            (action.payload as any)?.pagination || null;
        }
      })
      .addCase(fetchQuestions.rejected, (state, action) => {
        state.questionsLoading = false;
        state.questionsError =
          action.error.message || "Failed to fetch questions";
      })

      // ==================== FETCH PUBLISHED QUESTIONS ====================
      .addCase(fetchPublishedQuestions.pending, (state) => {
        state.questionsLoading = true;
        state.questionsError = null;
      })
      .addCase(fetchPublishedQuestions.fulfilled, (state, action) => {
        state.questionsLoading = false;
        if (Array.isArray(action.payload)) {
          state.questions = action.payload;
        } else {
          state.questions = (action.payload as any)?.data || [];
          state.questionsPagination =
            (action.payload as any)?.pagination || null;
        }
      })
      .addCase(fetchPublishedQuestions.rejected, (state, action) => {
        state.questionsLoading = false;
        state.questionsError =
          action.error.message || "Failed to fetch published questions";
      })

      // ==================== FETCH QUESTION BY ID ====================
      .addCase(fetchQuestionById.pending, (state) => {
        state.questionsLoading = true;
        state.questionsError = null;
      })
      .addCase(fetchQuestionById.fulfilled, (state, action) => {
        state.questionsLoading = false;
        state.selectedQuestion = action.payload;
      })
      .addCase(fetchQuestionById.rejected, (state, action) => {
        state.questionsLoading = false;
        state.questionsError =
          action.error.message || "Failed to fetch question";
      })

      // ==================== CREATE QUESTION ====================
      .addCase(createQuestion.pending, (state) => {
        state.questionsLoading = true;
        state.questionsError = null;
      })
      .addCase(createQuestion.fulfilled, (state, action) => {
        state.questionsLoading = false;
        state.questions.unshift(action.payload);
      })
      .addCase(createQuestion.rejected, (state, action) => {
        state.questionsLoading = false;
        state.questionsError =
          action.error.message || "Failed to create question";
      })

      // ==================== UPDATE QUESTION ====================
      .addCase(updateQuestion.pending, (state) => {
        state.questionsLoading = true;
        state.questionsError = null;
      })
      .addCase(updateQuestion.fulfilled, (state, action) => {
        state.questionsLoading = false;
        const index = state.questions.findIndex(
          (q) => q.id === action.payload.id
        );
        if (index !== -1) {
          state.questions[index] = action.payload;
        }
        if (state.selectedQuestion?.id === action.payload.id) {
          state.selectedQuestion = action.payload;
        }
      })
      .addCase(updateQuestion.rejected, (state, action) => {
        state.questionsLoading = false;
        state.questionsError =
          action.error.message || "Failed to update question";
      })

      // ==================== DELETE QUESTION ====================
      .addCase(deleteQuestion.pending, (state) => {
        state.questionsLoading = true;
        state.questionsError = null;
      })
      .addCase(deleteQuestion.fulfilled, (state, action) => {
        state.questionsLoading = false;
        state.questions = state.questions.filter(
          (q) => q.id !== action.payload.id
        );
        if (state.selectedQuestion?.id === action.payload.id) {
          state.selectedQuestion = null;
        }
      })
      .addCase(deleteQuestion.rejected, (state, action) => {
        state.questionsLoading = false;
        state.questionsError =
          action.error.message || "Failed to delete question";
      })

      // ==================== PUBLISH QUESTION ====================
      .addCase(publishQuestion.pending, (state) => {
        state.questionsLoading = true;
        state.questionsError = null;
      })
      .addCase(publishQuestion.fulfilled, (state, action) => {
        state.questionsLoading = false;
        const index = state.questions.findIndex(
          (q) => q.id === action.payload.id
        );
        if (index !== -1) {
          state.questions[index].status = "PUBLISHED";
        }
      })
      .addCase(publishQuestion.rejected, (state, action) => {
        state.questionsLoading = false;
        state.questionsError =
          action.error.message || "Failed to publish question";
      })

      // ==================== UPDATE QUESTION STATUS ====================
      .addCase(updateQuestionStatus.pending, (state) => {
        state.questionsLoading = true;
        state.questionsError = null;
      })
      .addCase(updateQuestionStatus.fulfilled, (state, action) => {
        state.questionsLoading = false;
        const index = state.questions.findIndex(
          (q) => q.id === action.payload.id
        );
        if (index !== -1) {
          state.questions[index].status = action.payload.status;
        }
      })
      .addCase(updateQuestionStatus.rejected, (state, action) => {
        state.questionsLoading = false;
        state.questionsError =
          action.error.message || "Failed to update question status";
      })

      // ==================== FETCH ANSWERS ====================
      .addCase(fetchAnswers.pending, (state) => {
        state.answersLoading = true;
        state.answersError = null;
      })
      .addCase(fetchAnswers.fulfilled, (state, action) => {
        state.answersLoading = false;
        if (Array.isArray(action.payload)) {
          state.answers = action.payload;
        } else {
          state.answers = (action.payload as any)?.data || [];
          state.answersPagination = (action.payload as any)?.pagination || null;
        }
      })
      .addCase(fetchAnswers.rejected, (state, action) => {
        state.answersLoading = false;
        state.answersError = action.error.message || "Failed to fetch answers";
      })

      // ==================== FETCH ANSWERS BY QUESTION ID ====================
      .addCase(fetchAnswersByQuestionId.pending, (state) => {
        state.answersLoading = true;
        state.answersError = null;
      })
      .addCase(fetchAnswersByQuestionId.fulfilled, (state, action) => {
        state.answersLoading = false;
        if (Array.isArray(action.payload)) {
          state.answers = action.payload;
        } else {
          state.answers = (action.payload as any)?.data || [];
          state.answersPagination = (action.payload as any)?.pagination || null;
        }
      })
      .addCase(fetchAnswersByQuestionId.rejected, (state, action) => {
        state.answersLoading = false;
        state.answersError = action.error.message || "Failed to fetch answers";
      })

      // ==================== CREATE ANSWER ====================
      .addCase(createAnswer.pending, (state) => {
        state.answersLoading = true;
        state.answersError = null;
      })
      .addCase(createAnswer.fulfilled, (state, action) => {
        state.answersLoading = false;
        state.answers.unshift(action.payload);
      })
      .addCase(createAnswer.rejected, (state, action) => {
        state.answersLoading = false;
        state.answersError = action.error.message || "Failed to create answer";
      })

      // ==================== UPDATE ANSWER ====================
      .addCase(updateAnswer.pending, (state) => {
        state.answersLoading = true;
        state.answersError = null;
      })
      .addCase(updateAnswer.fulfilled, (state, action) => {
        state.answersLoading = false;
        const index = state.answers.findIndex(
          (a) => a.id === action.payload.id
        );
        if (index !== -1) {
          state.answers[index] = action.payload;
        }
        if (state.selectedAnswer?.id === action.payload.id) {
          state.selectedAnswer = action.payload;
        }
      })
      .addCase(updateAnswer.rejected, (state, action) => {
        state.answersLoading = false;
        state.answersError = action.error.message || "Failed to update answer";
      })

      // ==================== FETCH QA CONFIG ====================
      .addCase(fetchQAConfig.pending, (state) => {
        state.qaConfigLoading = true;
        state.qaConfigError = null;
      })
      .addCase(fetchQAConfig.fulfilled, (state, action) => {
        state.qaConfigLoading = false;
        state.qaConfig = action.payload || null;
      })
      .addCase(fetchQAConfig.rejected, (state, action) => {
        state.qaConfigLoading = false;
        state.qaConfigError =
          action.error.message || "Failed to fetch QA config";
      })

      // ==================== UPDATE QA CONFIG ====================
      .addCase(updateQAConfig.pending, (state) => {
        state.qaConfigLoading = true;
        state.qaConfigError = null;
      })
      .addCase(updateQAConfig.fulfilled, (state, action) => {
        state.qaConfigLoading = false;
        state.qaConfig = action.payload || null;
      })
      .addCase(updateQAConfig.rejected, (state, action) => {
        state.qaConfigLoading = false;
        state.qaConfigError =
          (action.payload as string) ||
          action.error.message ||
          "Failed to update QA config";
      });
  },
});

export const {
  setSelectedQuestion,
  setSelectedAnswer,
  clearQuestionsError,
  clearAnswersError,
  setQuestionsPagination,
  setAnswersPagination,
  clearQAConfigError,
} = questionSlice.actions;

export default questionSlice.reducer;
