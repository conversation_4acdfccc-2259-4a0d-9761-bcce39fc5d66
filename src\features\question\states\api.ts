import { restApi, BaseResponse } from "@/api/restApi";
import { API_ENDPOINTS } from "@/api/endpoints";
import { AxiosResponse } from "axios";
import {
  Question,
  Answer,
  ApiResponse,
  CreateQuestionRequest,
  UpdateQuestionRequest,
  CreateAnswerRequest,
  UpdateAnswerRequest,
  QuestionQueryParams,
  AnswerQueryParams,
  QuestionStatus,
  QAConfig,
  QAConfigRequest,
} from "./types";

/**
 * Question API Service
 */
export class QuestionAPI {
  // ==================== PUBLIC QUESTION APIs ====================

  /**
   * Get all published questions
   */
  static async getPublishedQuestions(
    params?: QuestionQueryParams
  ): Promise<ApiResponse<Question[]>> {
    const queryParams = new URLSearchParams();
    if (params?.keyword) queryParams.append("keyword", params.keyword);
    if (params?.page !== undefined)
      queryParams.append("page", params.page.toString());
    if (params?.size !== undefined)
      queryParams.append("size", params.size.toString());

    const url = `${API_ENDPOINTS.PORTAL.PUBLIC.QUESTIONS}${
      queryParams.toString() ? `?${queryParams.toString()}` : ""
    }`;
    return restApi.get(url);
  }

  /**
   * Get published question by ID
   */
  static async getPublishedQuestionById(
    id: number
  ): Promise<ApiResponse<Question>> {
    return restApi.get(`${API_ENDPOINTS.PORTAL.PUBLIC.QUESTIONS}/${id}`);
  }

  // ==================== PRIVATE QUESTION APIs ====================

  /**
   * Create new question
   */
  static async createQuestion(
    data: CreateQuestionRequest
  ): Promise<ApiResponse<Question>> {
    return restApi.post(API_ENDPOINTS.PORTAL.PRIVATE.QUESTIONS, data);
  }

  /**
   * Update question
   */
  static async updateQuestion(
    id: number,
    data: UpdateQuestionRequest
  ): Promise<ApiResponse<Question>> {
    return restApi.put(`${API_ENDPOINTS.PORTAL.PRIVATE.QUESTIONS}/${id}`, data);
  }

  // ==================== ADMIN QUESTION APIs ====================

  /**
   * Get all questions (admin)
   */
  static async getAllQuestions(
    params?: QuestionQueryParams
  ): Promise<ApiResponse<Question[]>> {
    const queryParams = new URLSearchParams();
    if (params?.keyword) queryParams.append("keyword", params.keyword);
    if (params?.status) queryParams.append("status", params.status);
    if (params?.page !== undefined)
      queryParams.append("page", params.page.toString());
    if (params?.size !== undefined)
      queryParams.append("size", params.size.toString());

    const url = `${API_ENDPOINTS.PORTAL.ADMIN.QUESTIONS}${
      queryParams.toString() ? `?${queryParams.toString()}` : ""
    }`;
    return restApi.get(url);
  }

  /**
   * Publish question
   */
  static async publishQuestion(
    id: number
  ): Promise<{ success: boolean; message: string }> {
    return restApi.put(
      `${API_ENDPOINTS.PORTAL.ADMIN.QUESTIONS}/${id}/published`
    );
  }

  /**
   * Update question status
   */
  static async updateQuestionStatus(
    id: number,
    status: QuestionStatus
  ): Promise<{ success: boolean; message: string }> {
    return restApi.put(
      `${API_ENDPOINTS.PORTAL.ADMIN.QUESTIONS}/${id}/status/${status}`
    );
  }

  /**
   * Delete question
   */
  static async deleteQuestion(
    id: number
  ): Promise<{ success: boolean; message: string }> {
    return restApi.delete(`${API_ENDPOINTS.PORTAL.ADMIN.QUESTIONS}/${id}`);
  }
}

/**
 * QA Config API Service
 */
export class QAConfigAPI {
  /**
   * Get QA Configuration (Public endpoint)
   */
  static async getQAConfig(): Promise<AxiosResponse<BaseResponse<QAConfig>>> {
    return restApi.get<BaseResponse<QAConfig>>(
      API_ENDPOINTS.PORTAL.PUBLIC.QA_CONFIG
    );
  }

  /**
   * Update QA Configuration (Admin endpoint)
   */
  static async updateQAConfig(
    data: QAConfigRequest
  ): Promise<AxiosResponse<BaseResponse<QAConfig>>> {
    return restApi.post<BaseResponse<QAConfig>>(
      API_ENDPOINTS.PORTAL.ADMIN.QA_CONFIG_UPDATE,
      data
    );
  }
}

/**
 * Answer API Service
 */
export class AnswerAPI {
  // ==================== PUBLIC ANSWER APIs ====================

  /**
   * Get answers by question ID
   */
  static async getAnswersByQuestionId(
    questionId: number,
    params?: AnswerQueryParams
  ): Promise<ApiResponse<Answer[]>> {
    const queryParams = new URLSearchParams();
    if (params?.page !== undefined)
      queryParams.append("page", params.page.toString());
    if (params?.size !== undefined)
      queryParams.append("size", params.size.toString());

    const url = `${API_ENDPOINTS.PORTAL.PUBLIC.ANSWERS}/question/${questionId}${
      queryParams.toString() ? `?${queryParams.toString()}` : ""
    }`;
    return restApi.get(url);
  }

  /**
   * Get answer by ID
   */
  static async getAnswerById(id: number): Promise<ApiResponse<Answer>> {
    return restApi.get(`${API_ENDPOINTS.PORTAL.PUBLIC.ANSWERS}/${id}`);
  }

  // ==================== ADMIN ANSWER APIs ====================

  /**
   * Get all answers (admin)
   */
  static async getAllAnswers(
    params?: AnswerQueryParams
  ): Promise<ApiResponse<Answer[]>> {
    const queryParams = new URLSearchParams();
    if (params?.keyword) queryParams.append("keyword", params.keyword);
    if (params?.page !== undefined)
      queryParams.append("page", params.page.toString());
    if (params?.size !== undefined)
      queryParams.append("size", params.size.toString());

    const url = `${API_ENDPOINTS.PORTAL.ADMIN.ANSWERS}${
      queryParams.toString() ? `?${queryParams.toString()}` : ""
    }`;
    return restApi.get(url);
  }

  /**
   * Create answer
   */
  static async createAnswer(
    data: CreateAnswerRequest
  ): Promise<ApiResponse<Answer>> {
    return restApi.post(API_ENDPOINTS.PORTAL.ADMIN.ANSWERS, data);
  }

  /**
   * Update answer
   */
  static async updateAnswer(
    id: number,
    data: UpdateAnswerRequest
  ): Promise<ApiResponse<Answer>> {
    return restApi.put(`${API_ENDPOINTS.PORTAL.ADMIN.ANSWERS}/${id}`, data);
  }
}

// Export default cho convenience
export default {
  Question: QuestionAPI,
  Answer: AnswerAPI,
  QAConfig: QAConfigAPI,
};
