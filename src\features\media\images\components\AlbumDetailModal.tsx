import { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { X, Upload, Trash2, Edit, Plus } from "lucide-react";
import { LoadingPage } from "@/components/loading/LoadingPage";
import { fetchAlbum } from "../states/api";
import { Album } from "../states/types";
import { AlbumBadge } from "./AlbumBadge";
import { toast } from "sonner";

interface AlbumDetailModalProps {
  albumId: number;
  isOpen: boolean;
  onClose: () => void;
}

export function AlbumDetailModal({
  albumId,
  isOpen,
  onClose,
}: AlbumDetailModalProps) {
  const [album, setAlbum] = useState<Album | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!isOpen || !albumId) return;

    const loadAlbum = async () => {
      try {
        setLoading(true);
        const response = await fetchAlbum(albumId);
        setAlbum(response.data);
      } catch (error) {
        toast.error("Không thể tải thông tin thư viện ảnh: " + (error as Error).message);
      } finally {
        setLoading(false);
      }
    };

    loadAlbum();
  }, [albumId, isOpen]);

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center bg-black/50"
      style={{ animation: "fadeIn 0.2s" }}
      tabIndex={-1}
    >
      <div
        className="relative bg-white rounded-lg shadow-lg max-w-6xl w-full mx-4 max-h-[90vh] overflow-hidden animate-in zoom-in-95"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b bg-white sticky top-0">
          <div className="flex-1">
            <h2 className="text-xl font-semibold">
              {loading ? "Đang tải..." : album?.name || "Chi tiết thư viện ảnh"}
            </h2>
            <div className="text-muted-foreground text-sm mt-1">
              Quản lý hình ảnh trong thư viện
            </div>
          </div>
          <Button
            type="button"
            variant="ghost"
            size="icon"
            className="rounded-full"
            onClick={onClose}
          >
            <X className="w-5 h-5" />
          </Button>
        </div>

        {/* Content */}
        <div className="overflow-auto max-h-[calc(90vh-80px)]">
          {loading ? (
            <div className="flex items-center justify-center h-64">
              <LoadingPage />
            </div>
          ) : album ? (
            <div className="p-6 space-y-6">
              {/* Album Info */}
              <Card>
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <CardTitle className="text-lg mb-3">
                        {album.name}
                      </CardTitle>
                      <div className="flex items-center gap-4 mb-4">
                        <AlbumBadge value={album.status} />
                        <Badge variant="outline">{album.type}</Badge>
                      </div>
                      <div className="text-sm text-muted-foreground">
                        <p>
                          <strong>Mô tả:</strong>{" "}
                          {album.description?.text || "Chưa có mô tả"}
                        </p>
                        <div className="grid grid-cols-2 gap-4 mt-3">
                          <p>
                            <strong>ID:</strong> {album.id}
                          </p>
                          <p>
                            <strong>Loại:</strong> {album.type}
                          </p>
                          <p>
                            <strong>Tạo lúc:</strong>{" "}
                            {album.createdAt
                              ? new Date(album.createdAt).toLocaleString(
                                "vi-VN"
                              )
                              : "N/A"}
                          </p>
                          <p>
                            <strong>Cập nhật:</strong>{" "}
                            {album.updatedAt
                              ? new Date(album.updatedAt).toLocaleString(
                                "vi-VN"
                              )
                              : "N/A"}
                          </p>
                        </div>
                      </div>
                    </div>
                    {album.coverImage && (
                      <div className="ml-4">
                        <img
                          src={album.coverImage}
                          alt={album.name}
                          className="w-24 h-24 object-cover rounded-lg border"
                        />
                      </div>
                    )}
                  </div>
                </CardHeader>
              </Card>

              {/* Gallery Section */}
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">
                      Hình ảnh trong thư viện
                    </CardTitle>
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm">
                        <Upload className="w-4 h-4 mr-2" />
                        Tải lên ảnh
                      </Button>
                      <Button variant="outline" size="sm">
                        <Edit className="w-4 h-4 mr-2" />
                        Chỉnh sửa thư viện
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  {/* Gallery Grid - Placeholder */}
                  <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                    {/* Add Photo Placeholder */}
                    <div className="aspect-square border-2 border-dashed border-muted-foreground/25 rounded-lg flex items-center justify-center hover:border-primary/50 cursor-pointer transition-colors">
                      <div className="text-center">
                        <Plus className="w-8 h-8 mx-auto text-muted-foreground mb-2" />
                        <p className="text-xs text-muted-foreground">
                          Thêm ảnh
                        </p>
                      </div>
                    </div>

                    {/* Sample Images - This will be replaced with actual gallery */}
                    {[1, 2, 3, 4, 5].map((index) => (
                      <div key={index} className="aspect-square relative group">
                        <div className="w-full h-full bg-muted rounded-lg flex items-center justify-center">
                          <span className="text-muted-foreground text-sm">
                            Ảnh {index}
                          </span>
                        </div>
                        <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center">
                          <div className="flex gap-1">
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8 text-white hover:bg-white/20"
                            >
                              <Edit className="w-4 h-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8 text-white hover:bg-red-500/20"
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  <div className="text-center text-muted-foreground text-sm mt-6 p-4 bg-muted/30 rounded-lg">
                    💡 <strong>Chức năng đang phát triển:</strong> Gallery quản
                    lý hình ảnh sẽ được hoàn thiện trong phiên bản tiếp theo
                  </div>
                </CardContent>
              </Card>
            </div>
          ) : (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <h3 className="text-lg font-semibold mb-2">
                  Không tìm thấy thư viện ảnh
                </h3>
                <p className="text-muted-foreground">
                  Thư viện ảnh này có thể đã bị xóa hoặc không tồn tại.
                </p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Backdrop */}
      <div
        className="fixed inset-0"
        tabIndex={-1}
        aria-hidden="true"
        onClick={onClose}
      />
    </div>
  );
}
