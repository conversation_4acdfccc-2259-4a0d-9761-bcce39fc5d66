import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { AlertNotification } from "../data/mockData";
import { AlertTriangle, XCircle, AlertCircle, Info } from "lucide-react";

interface AlertsPanelProps {
  alerts: AlertNotification[];
}

const getAlertIcon = (type: AlertNotification["type"]) => {
  switch (type) {
    case "overdue":
      return AlertTriangle;
    case "error":
      return XCircle;
    case "warning":
      return AlertCircle;
    case "info":
      return Info;
    default:
      return AlertCircle;
  }
};

const getAlertColor = (type: AlertNotification["type"], isUrgent: boolean) => {
  if (isUrgent) {
    return "text-red-600 bg-red-50 border-red-200";
  }

  switch (type) {
    case "overdue":
      return "text-red-600 bg-red-50 border-red-200";
    case "error":
      return "text-red-600 bg-red-50 border-red-200";
    case "warning":
      return "text-yellow-600 bg-yellow-50 border-yellow-200";
    case "info":
      return "text-blue-600 bg-blue-50 border-blue-200";
    default:
      return "text-gray-600 bg-gray-50 border-gray-200";
  }
};

const getBadgeVariant = (
  type: AlertNotification["type"],
  isUrgent: boolean
) => {
  if (isUrgent) return "destructive";

  switch (type) {
    case "overdue":
    case "error":
      return "destructive";
    case "warning":
      return "default"; // Using default as warning variant might not exist
    case "info":
      return "secondary";
    default:
      return "secondary";
  }
};

export const AlertsPanel = ({ alerts }: AlertsPanelProps) => {
  const urgentAlerts = alerts.filter((alert) => alert.isUrgent);
  const regularAlerts = alerts.filter((alert) => !alert.isUrgent);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Thông báo & Cảnh báo</span>
          {urgentAlerts.length > 0 && (
            <Badge variant="destructive" className="text-xs">
              {urgentAlerts.length} Khẩn cấp
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3 max-h-80 overflow-y-auto">
          {/* Urgent alerts first */}
          {urgentAlerts.map((alert) => {
            const Icon = getAlertIcon(alert.type);
            const colorClass = getAlertColor(alert.type, alert.isUrgent);
            const badgeVariant = getBadgeVariant(alert.type, alert.isUrgent);

            return (
              <div
                key={alert.id}
                className={`p-3 rounded-lg border ${colorClass}`}
              >
                <div className="flex items-start space-x-3">
                  <Icon className="h-5 w-5 mt-0.5 flex-shrink-0" />
                  <div className="flex-1 space-y-1">
                    <div className="flex items-center justify-between">
                      <p className="text-sm font-medium">{alert.title}</p>
                      <Badge variant={badgeVariant} className="text-xs">
                        {alert.isUrgent ? "Khẩn cấp" : alert.type.toUpperCase()}
                      </Badge>
                    </div>
                    <p className="text-sm opacity-90">{alert.message}</p>
                    <p className="text-xs opacity-70">{alert.timestamp}</p>
                  </div>
                </div>
              </div>
            );
          })}

          {/* Regular alerts */}
          {regularAlerts.map((alert) => {
            const Icon = getAlertIcon(alert.type);
            const colorClass = getAlertColor(alert.type, alert.isUrgent);
            const badgeVariant = getBadgeVariant(alert.type, alert.isUrgent);

            return (
              <div
                key={alert.id}
                className={`p-3 rounded-lg border ${colorClass}`}
              >
                <div className="flex items-start space-x-3">
                  <Icon className="h-4 w-4 mt-0.5 flex-shrink-0" />
                  <div className="flex-1 space-y-1">
                    <div className="flex items-center justify-between">
                      <p className="text-sm font-medium">{alert.title}</p>
                      <Badge variant={badgeVariant} className="text-xs">
                        {alert.type.toUpperCase()}
                      </Badge>
                    </div>
                    <p className="text-sm opacity-90">{alert.message}</p>
                    <p className="text-xs opacity-70">{alert.timestamp}</p>
                  </div>
                </div>
              </div>
            );
          })}

          {alerts.length === 0 && (
            <div className="text-center py-6 text-muted-foreground">
              <Info className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">Không có thông báo nào</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
