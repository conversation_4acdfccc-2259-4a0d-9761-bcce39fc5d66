import React, { useEffect, useState } from "react";
import { useAppDispatch, useAppSelector } from "@/store/rootReducer";
import { selectSelectedGroup } from "../states/selectors";
import {
  fetchGroupRoleIdsThunk,
  assignRolesToGroupThunk,
  removeRoleFromGroupThunk,
} from "../states/slices";
import {
  selectAssignedRoleIdsByGroupId,
  selectAssignedRolesLoadingByGroupId,
} from "../states/selectors";
import { fetchRoles } from "@/features/role/states/api";
import { RoleData } from "@/features/role/states/type";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Plus, Search, Shield, X } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Checkbox } from "@/components/ui/checkbox";

export const GroupSystemRoles: React.FC = () => {
  const dispatch = useAppDispatch();
  const selectedGroup = useAppSelector(selectSelectedGroup);
  const loading = useAppSelector((state) =>
    selectedGroup
      ? selectAssignedRolesLoadingByGroupId(state, selectedGroup.id)
      : false
  );

  const assignedRoleIds = useAppSelector((state) =>
    selectedGroup ? selectAssignedRoleIdsByGroupId(state, selectedGroup.id) : []
  );

  const [allRoles, setAllRoles] = useState<RoleData[]>([]);
  const [filteredRoles, setFilteredRoles] = useState<RoleData[]>([]);
  const [searchKeyword, setSearchKeyword] = useState("");
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [selectedRoleIds, setSelectedRoleIds] = useState<number[]>([]);
  const [confirmRemove, setConfirmRemove] = useState<{
    show: boolean;
    role: RoleData | null;
  }>({ show: false, role: null });

  // Load group roles and all system roles
  useEffect(() => {
    if (selectedGroup) {
      dispatch(fetchGroupRoleIdsThunk(selectedGroup.id));
    }
  }, [dispatch, selectedGroup]);

  useEffect(() => {
    // Load all system roles
    const loadRoles = async () => {
      try {
        const response = await fetchRoles({ page: 1, size: 100, keyword: "" });
        if (response.data.data) {
          setAllRoles(response.data.data);
        }
      } catch (error) {
        console.error("Failed to load roles:", error);
      }
    };
    loadRoles();
  }, []);

  useEffect(() => {
    // Filter roles based on search
    const filtered = allRoles.filter(
      (role) =>
        role.name.toLowerCase().includes(searchKeyword.toLowerCase()) ||
        role.code.toLowerCase().includes(searchKeyword.toLowerCase())
    );
    setFilteredRoles(filtered);
  }, [allRoles, searchKeyword]);

  const assignedRoles = allRoles.filter((role) =>
    assignedRoleIds.includes(role.id)
  );
  const unassignedRoles = filteredRoles.filter(
    (role) => !assignedRoleIds.includes(role.id)
  );

  const handleRemoveRole = async () => {
    if (!selectedGroup || !confirmRemove.role) return;

    await dispatch(
      removeRoleFromGroupThunk({
        groupId: selectedGroup.id,
        roleId: confirmRemove.role.id,
      })
    );

    // Refresh the assigned roles list
    dispatch(fetchGroupRoleIdsThunk(selectedGroup.id));

    setConfirmRemove({ show: false, role: null });
  };

  const handleAssignRoles = async () => {
    if (!selectedGroup || selectedRoleIds.length === 0) return;

    // Use new batch assign roles API
    await dispatch(
      assignRolesToGroupThunk({
        groupId: selectedGroup.id,
        roleIds: selectedRoleIds,
      })
    );

    // Refresh the assigned roles list
    dispatch(fetchGroupRoleIdsThunk(selectedGroup.id));

    setShowAddDialog(false);
    setSelectedRoleIds([]);
    setSearchKeyword("");
  };

  const handleRoleToggle = (roleId: number) => {
    setSelectedRoleIds((prev) =>
      prev.includes(roleId)
        ? prev.filter((id) => id !== roleId)
        : [...prev, roleId]
    );
  };

  if (!selectedGroup) {
    return (
      <div className="p-6 text-center text-gray-500">
        Chọn một nhóm để quản lý vai trò
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-lg font-semibold">
          Vai trò hệ thống ({assignedRoles.length})
        </h3>

        <Button onClick={() => setShowAddDialog(true)} disabled={loading}>
          <Plus className="h-4 w-4 mr-2" />
          Gán vai trò
        </Button>
      </div>

      {/* Assigned Roles Table */}
      {assignedRoles.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          Chưa gán vai trò nào cho nhóm này
        </div>
      ) : (
        <div className="border rounded-lg">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Mã vai trò</TableHead>
                <TableHead>Tên vai trò</TableHead>
                <TableHead className="text-right">Hành động</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {assignedRoles.map((role) => (
                <TableRow key={role.id}>
                  <TableCell className="font-medium">
                    <div className="flex items-center">
                      <Shield className="h-4 w-4 mr-2 text-blue-500" />
                      {role.code}
                    </div>
                  </TableCell>
                  <TableCell>{role.name}</TableCell>
                  <TableCell className="text-right">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const roleToRemove = allRoles.find(
                          (r) => r.id === role.id
                        );
                        setConfirmRemove({
                          show: true,
                          role: roleToRemove || null,
                        });
                      }}
                      disabled={loading}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}

      {/* Add Roles Dialog */}
      <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
        <DialogContent className="max-w-2xl bg-white border border-gray-200 shadow-lg">
          <DialogHeader className="bg-white border-b border-gray-100">
            <DialogTitle className="text-gray-900">
              Gán vai trò cho nhóm
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-4 bg-white">
            {/* Search */}
            <div className="flex items-center space-x-2 px-1">
              <Search className="h-4 w-4 text-gray-400" />
              <Input
                placeholder="Tìm kiếm vai trò..."
                value={searchKeyword}
                onChange={(e) => setSearchKeyword(e.target.value)}
                className="bg-white border-gray-200"
              />
            </div>

            {/* Roles List */}
            <div className="max-h-64 overflow-y-auto border rounded bg-white border-gray-200">
              {unassignedRoles.length === 0 ? (
                <div className="p-4 text-center text-gray-600">
                  Không có vai trò nào khả dụng
                </div>
              ) : (
                <div className="space-y-1 p-2 bg-white">
                  {unassignedRoles.map((role) => (
                    <div
                      key={role.id}
                      className="flex items-center space-x-3 p-2 hover:bg-gray-50 rounded cursor-pointer border border-transparent hover:border-gray-200"
                      onClick={() => handleRoleToggle(role.id)}
                    >
                      <Checkbox
                        checked={selectedRoleIds.includes(role.id)}
                        onCheckedChange={() => handleRoleToggle(role.id)}
                      />
                      <div className="flex-1">
                        <div className="font-medium text-gray-900">
                          {role.name}
                        </div>
                        <div className="text-sm text-gray-600">{role.code}</div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Selected count */}
            {selectedRoleIds.length > 0 && (
              <div className="text-sm text-blue-700 bg-blue-50 p-3 rounded border border-blue-200">
                Đã chọn {selectedRoleIds.length} vai trò
              </div>
            )}
          </div>

          <DialogFooter className="bg-white border-t border-gray-100">
            <Button
              variant="outline"
              onClick={() => {
                setShowAddDialog(false);
                setSelectedRoleIds([]);
                setSearchKeyword("");
              }}
              className="bg-white border-gray-200 text-gray-700 hover:bg-gray-50"
            >
              Hủy
            </Button>
            <Button
              onClick={handleAssignRoles}
              disabled={selectedRoleIds.length === 0}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              Gán vai trò
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Remove Role Confirmation Dialog */}
      <Dialog
        open={confirmRemove.show}
        onOpenChange={(open) => setConfirmRemove({ show: open, role: null })}
      >
        <DialogContent className="bg-white border border-gray-200 shadow-lg">
          <DialogHeader className="bg-white border-b border-gray-100">
            <DialogTitle className="text-gray-900">
              Xác nhận gỡ vai trò
            </DialogTitle>
          </DialogHeader>

          <div className="bg-white py-4">
            <p className="text-gray-700">
              Bạn có chắc chắn muốn gỡ vai trò{" "}
              <strong className="text-gray-900">
                {confirmRemove.role?.name}
              </strong>{" "}
              khỏi nhóm{" "}
              <strong className="text-gray-900">{selectedGroup?.name}</strong>?
            </p>
          </div>

          <DialogFooter className="bg-white border-t border-gray-100">
            <Button
              variant="outline"
              onClick={() => setConfirmRemove({ show: false, role: null })}
              disabled={loading}
              className="bg-white border-gray-200 text-gray-700 hover:bg-gray-50"
            >
              Hủy
            </Button>
            <Button
              variant="destructive"
              onClick={handleRemoveRole}
              disabled={loading}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              Gỡ vai trò
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};
