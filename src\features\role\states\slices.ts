import { createSlice, PayloadAction, createAsyncThunk } from "@reduxjs/toolkit";
import { RoleData } from "./type";
import { fetchRoles, fetchRole } from "./api";

export type RolePageMode = "list" | "detail";
export type RoleAction = "view" | "edit" | "create";

interface RoleState {
  data: RoleData[];
  currentRole: RoleData | null;
  pageMode: RolePageMode;
  actionType: RoleAction;
  searchTerm: string;
  loading: boolean;
  error: string | null;
}

// Async thunk to fetch all roles
export const fetchRolesThunk = createAsyncThunk("role/fetchRoles", async () => {
  const response = await fetchRoles({
    page: 1,
    size: 100,
    keyword: "",
  });
  if (!response.data.data) {
    throw new Error("No data");
  }
  return response.data.data;
});

// Async thunk to fetch role by ID
export const fetchRoleByIdThunk = createAsyncThunk(
  "role/fetchById",
  async (roleId: number) => {
    const response = await fetchRole(roleId.toString());
    if (!response.data.data) {
      throw new Error("No data");
    }
    return response.data.data;
  }
);

const initialState: RoleState = {
  data: [],
  currentRole: null,
  pageMode: "list",
  actionType: "view",
  searchTerm: "",
  loading: false,
  error: null,
};

const roleSlice = createSlice({
  name: "role",
  initialState,
  reducers: {
    setCurrentRole: (state, action: PayloadAction<RoleData | null>) => {
      state.currentRole = action.payload;
    },
    setPathParams: (
      state,
      action: PayloadAction<{
        mode?: RolePageMode;
        actionType?: RoleAction;
      }>
    ) => {
      const { mode, actionType } = action.payload;
      if (mode) state.pageMode = mode;
      if (actionType) state.actionType = actionType;
    },
    setSearchTerm: (state, action: PayloadAction<string>) => {
      state.searchTerm = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch roles
      .addCase(fetchRolesThunk.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchRolesThunk.fulfilled, (state, action) => {
        state.loading = false;
        state.data = action.payload;
      })
      .addCase(fetchRolesThunk.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || "Failed to fetch roles";
      })

      // Fetch role by id
      .addCase(fetchRoleByIdThunk.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchRoleByIdThunk.fulfilled, (state, action) => {
        state.loading = false;
        state.currentRole = action.payload;
      })
      .addCase(fetchRoleByIdThunk.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || "Failed to fetch role";
      });
  },
});

export const { setCurrentRole, setPathParams, setSearchTerm, clearError } =
  roleSlice.actions;

export default roleSlice.reducer;
