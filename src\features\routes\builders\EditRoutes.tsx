/**
 * Edit Routes - Fullscreen routes for editing interfaces
 */

import { RouteObject } from "react-router-dom";
import { componentRegistry } from "../registry/ComponentRegistry";
import EditLayout from "@/features/layouts/EditLayout";

/**
 * Edit routes configuration - Fullscreen editing interfaces
 */
export const EditRoutes: RouteObject[] = [
  {
    path: "/edit",
    element: <EditLayout />,
    children: [
      {
        path: "form",
        element: componentRegistry.FormBuilderPage(),
      },
      {
        path: "form/:id",
        element: componentRegistry.FormBuilderPage(),
      },
    ],
  },
];

export default EditRoutes;