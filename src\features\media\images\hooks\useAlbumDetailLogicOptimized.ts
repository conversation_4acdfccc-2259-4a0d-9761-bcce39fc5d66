import { useState, useEffect, useCallback, useMemo } from "react";
import { useAppSelector } from "@/store/rootReducer";
import { useSwitchMode, useAlbumMedia } from "../states/hooks";
import {
  selectCurrentAlbumMedia,
  selectMediaLoading,
  selectMediaError,
  selectMediaHasMore,
  selectMediaCurrentPage,
} from "../states/selector";
import { Album, Media, CreateMediaRequest } from "../states/types";

interface UseAlbumDetailLogicProps {
  album: Album;
  onAlbumUpdated?: () => void | Promise<void>;
}

/**
 * 🚀 Optimized Album Detail Logic Hook
 *
 * ✅ Performance Optimizations:
 * 1. Stable album.id reference with useMemo
 * 2. Memoized selector results
 * 3. Optimized useCallback dependencies
 * 4. Memoized return object
 */
export function useAlbumDetailLogicOptimized({
  album,
  onAlbumUpdated,
}: UseAlbumDetailLogicProps) {
  // ✅ Stable album ID reference
  const albumId = useMemo(() => album?.id, [album?.id]);

  const { switchMode } = useSwitchMode();
  const albumMediaHooks = useAlbumMedia(onAlbumUpdated);

  // ✅ Memoized destructuring to prevent re-creates
  const {
    loadAlbumMedia,
    loadMoreAlbumMedia,
    addMediaToAlbum,
    addExistingMediaToAlbumHook,
    updateMediaDetails,
    removeMedia,
    updatePriority,
    setCoverImageHook,
    deleteCoverImageHook,
  } = useMemo(() => albumMediaHooks, [albumMediaHooks]);

  // ✅ Batch selectors to minimize re-renders
  const mediaState = useAppSelector((state) => ({
    albumMedia: selectCurrentAlbumMedia(state),
    mediaLoading: selectMediaLoading(state),
    mediaError: selectMediaError(state),
    mediaHasMore: selectMediaHasMore(state),
    mediaCurrentPage: selectMediaCurrentPage(state),
  }));

  // Local state (no optimization needed - useState is already optimized)
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [previewMedia, setPreviewMedia] = useState<Media | null>(null);
  const [deleteTarget, setDeleteTarget] = useState<Media | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  // ✅ Effect with stable dependency
  useEffect(() => {
    if (albumId) {
      loadAlbumMedia(albumId);
    }
  }, [albumId, loadAlbumMedia]);

  // ✅ Optimized handlers with minimal dependencies
  const handleBackToList = useCallback(() => {
    switchMode("list");
  }, [switchMode]);

  const handleAddMedia = useCallback(
    async (mediaData: Omit<CreateMediaRequest, "albumId">) => {
      if (albumId) {
        await addMediaToAlbum(albumId, mediaData);
      }
    },
    [albumId, addMediaToAlbum]
  );

  const handleAddExistingMedia = useCallback(
    async (mediaId: number) => {
      if (albumId) {
        await addExistingMediaToAlbumHook(albumId, mediaId);
      }
    },
    [albumId, addExistingMediaToAlbumHook]
  );

  const handleDeleteMedia = useCallback(
    async (mediaId: number) => {
      if (albumId) {
        await removeMedia(albumId, mediaId);
        setShowDeleteConfirm(false);
        setDeleteTarget(null);
      }
    },
    [albumId, removeMedia]
  );

  // ✅ No dependencies needed (only calls setState)
  const handleDeleteClick = useCallback((media: Media) => {
    setDeleteTarget(media);
    setShowDeleteConfirm(true);
  }, []);

  const handleSetCoverImage = useCallback(
    async (imageUrl: string) => {
      if (albumId) {
        await setCoverImageHook(albumId, imageUrl);
      }
    },
    [albumId, setCoverImageHook]
  );

  const handleDeleteCoverImage = useCallback(async () => {
    if (albumId) {
      await deleteCoverImageHook(albumId);
    }
  }, [albumId, deleteCoverImageHook]);

  // ✅ Extract complex dependencies
  const canLoadMore = mediaState.mediaHasMore && !mediaState.mediaLoading;
  const handleLoadMore = useCallback(async () => {
    if (albumId && canLoadMore) {
      await loadMoreAlbumMedia(albumId, mediaState.mediaCurrentPage);
    }
  }, [albumId, canLoadMore, loadMoreAlbumMedia, mediaState.mediaCurrentPage]);

  // ✅ Memoized return object to prevent prop drilling re-renders
  return useMemo(
    () => ({
      // Data
      ...mediaState,

      // Modal state
      isAddModalOpen,
      setIsAddModalOpen,
      previewMedia,
      setPreviewMedia,
      deleteTarget,
      showDeleteConfirm,

      // Handlers
      handleBackToList,
      handleAddMedia,
      handleAddExistingMedia,
      handleDeleteMedia,
      handleDeleteClick,
      handleSetCoverImage,
      handleDeleteCoverImage,
      handleLoadMore,

      // Additional actions
      updateMediaDetails,
      updatePriority,
    }),
    [
      mediaState,
      isAddModalOpen,
      previewMedia,
      deleteTarget,
      showDeleteConfirm,
      handleBackToList,
      handleAddMedia,
      handleAddExistingMedia,
      handleDeleteMedia,
      handleDeleteClick,
      handleSetCoverImage,
      handleDeleteCoverImage,
      handleLoadMore,
      updateMediaDetails,
      updatePriority,
    ]
  );
}
