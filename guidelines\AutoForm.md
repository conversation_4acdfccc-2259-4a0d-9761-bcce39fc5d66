# AutoForm Component Guide

## Overview

AutoForm is a powerful, node-based form builder that provides a declarative way to create complex forms with validation, styling, and type safety. It's built on top of TanStack Form (React Form) and uses a tree structure to define form layouts.

## Core Concepts

### 1. Node-Based Architecture

Forms are defined as a tree of `FormNode` objects. Each node has a type that determines its behavior:

- **frame**: Container node that holds child nodes
- **field**: Input field node that renders form inputs
- **title**: Header/title node for sections
- **control**: Button node for actions (submit, cancel, etc.)
- **redirect**: Link node for navigation

### 2. FormNode Structure

```typescript
interface FormNode {
  id: string;                    // Unique identifier
  type: NodeType;                // 'frame' | 'field' | 'title' | 'control' | 'redirect'
  children?: FormNode[];         // Child nodes (for frame nodes)
  field?: FieldConfig;           // Field configuration (for field nodes)
  validation?: ValidationConfig; // Validation rules
  styles?: StyleConfig;          // Custom styling
  hidden?: boolean;              // Conditionally hide node
}
```

### 3. Field Configuration

```typescript
interface FieldConfig {
  objectKey: string;      // Data binding key (use "->" for nested objects)
  component: string;      // Component type from registry
  label?: string;         // Field label
  placeholder?: string;   // Input placeholder
  defaultValue?: any;     // Default value
  dataType: string;       // 'string' | 'number' | 'boolean' | 'array' | 'object'
  options?: Option[];     // For select/dropdown fields
}
```

## Basic Usage

### Simple Form Example

```typescript
import { AutoForm } from '@/form/context/AutoForm';

const loginFormConfig: FormNode = {
  id: 'login-form',
  type: 'frame',
  children: [
    {
      id: 'title',
      type: 'title',
      label: 'Login',
      element: 'h2'
    },
    {
      id: 'username',
      type: 'field',
      field: {
        objectKey: 'username',
        component: 'TextInput',
        label: 'Username',
        placeholder: 'Enter username',
        defaultValue: '',
        dataType: 'string'
      },
      validation: {
        required: { value: true, error: 'Username is required' },
        minLength: { value: 3, error: 'Username must be at least 3 characters' }
      }
    },
    {
      id: 'password',
      type: 'field',
      field: {
        objectKey: 'password',
        component: 'PasswordInput',
        label: 'Password',
        placeholder: 'Enter password',
        defaultValue: '',
        dataType: 'string'
      },
      validation: {
        required: { value: true, error: 'Password is required' },
        minLength: { value: 6, error: 'Password must be at least 6 characters' }
      }
    },
    {
      id: 'submit',
      type: 'control',
      label: 'Login',
      action: 'submit',
      variant: 'default'
    }
  ]
};

function LoginPage() {
  const handleSubmit = (data: any) => {
    console.log('Form data:', data);
    // Handle login logic
  };

  return (
    <AutoForm
      node={loginFormConfig}
      onSubmit={handleSubmit}
      validationMode="onChange"
    />
  );
}
```

### Complex Form with Nested Data

```typescript
const userFormConfig: FormNode = {
  id: 'user-form',
  type: 'frame',
  children: [
    {
      id: 'personal-info',
      type: 'frame',
      label: 'Personal Information',
      children: [
        {
          id: 'name',
          type: 'field',
          field: {
            objectKey: 'personalInfo->name',  // Nested data
            component: 'TextInput',
            label: 'Full Name',
            dataType: 'string'
          }
        },
        {
          id: 'avatar',
          type: 'field',
          field: {
            objectKey: 'personalInfo->avatar',
            component: 'ImagePickerInput',
            label: 'Profile Picture',
            dataType: 'string'
          }
        }
      ]
    },
    {
      id: 'contact',
      type: 'frame',
      label: 'Contact Details',
      children: [
        {
          id: 'email',
          type: 'field',
          field: {
            objectKey: 'contact->email',
            component: 'TextInput',
            label: 'Email',
            dataType: 'string'
          },
          validation: {
            pattern: {
              value: '^[A-Z0-9._%+-]+@[A-Z0-9.-]+\\.[A-Z]{2,}$',
              error: 'Invalid email format'
            }
          }
        }
      ]
    }
  ]
};
```

## Available Field Components

### Basic Components
- `TextInput` - Standard text input
- `PasswordInput` - Password field with visibility toggle
- `NumberInput` - Numeric input
- `TextAreaInput` - Multi-line text
- `CheckboxInput` - Single checkbox
- `ToggleInput` - Toggle switch
- `SelectInput` - Basic dropdown
- `DropdownInput` - Enhanced dropdown with search

### Advanced Components
- `ImagePickerInput` - Image selection/upload
- `PickIconInput` - Icon picker
- `SimpleUploadInput` - File upload
- `MultipleUploadInput` - Multiple file upload
- `PickCategoryInput` - Category tree selector
- `TabInput` - Tab-based selection
- `DropDownTextWithSearch` - Searchable text dropdown
- `DropDownNumberWithSearch` - Searchable number dropdown

## Validation Rules

```typescript
interface ValidationConfig {
  required?: { value: boolean; error: string };
  min?: { value: number; error: string };
  max?: { value: number; error: string };
  minLength?: { value: number; error: string };
  maxLength?: { value: number; error: string };
  pattern?: { value: string; error: string };
  matchField?: { value: string; error: string };  // Match another field
}
```

## Styling

Each node can have custom styles:

```typescript
interface StyleConfig {
  container?: string;   // Wrapper styles
  label?: string;       // Label styles
  content?: string;     // Content area styles
  field?: string;       // Input field styles
  error?: string;       // Error message styles
}
```

## AutoForm Props

```typescript
interface AutoFormProps {
  node: FormNode;                    // Form configuration
  onSubmit?: (data: any) => void;   // Submit handler
  onChange?: (data: any) => void;   // Change handler
  initialData?: any;                 // Initial form data
  viewOnly?: boolean;                // Read-only mode
  validationMode?: 'onChange' | 'onSubmit';  // Validation timing
  className?: string;                // Additional CSS classes
  ref?: React.Ref<AutoFormRef>;     // Form ref for methods
}
```

## AutoForm Ref Methods

```typescript
interface AutoFormRef {
  submitForm: () => void;           // Trigger form submission
  getFormData: () => any;           // Get current form data
  resetForm: () => void;            // Reset to initial state
}
```

## Best Practices

1. **Use Descriptive IDs**: Node IDs should be meaningful and unique
2. **Group Related Fields**: Use frame nodes to organize related fields
3. **Consistent Validation**: Apply validation rules consistently across similar fields
4. **Reusable Configurations**: Create reusable form configs for common patterns
5. **Type Safety**: Define TypeScript interfaces for your form data
6. **Error Handling**: Always provide clear error messages for validation
7. **Nested Data**: Use "->" separator for nested object keys

## Common Patterns

### Conditional Fields

```typescript
{
  id: 'subscription-type',
  type: 'field',
  field: {
    objectKey: 'subscriptionType',
    component: 'SelectInput',
    options: [
      { value: 'free', label: 'Free' },
      { value: 'premium', label: 'Premium' }
    ]
  }
},
{
  id: 'payment-method',
  type: 'field',
  hidden: formData.subscriptionType !== 'premium',  // Conditional visibility
  field: {
    objectKey: 'paymentMethod',
    component: 'SelectInput',
    label: 'Payment Method'
  }
}
```

### Form with Actions

```typescript
{
  id: 'actions',
  type: 'frame',
  styles: { container: 'flex gap-4 justify-end' },
  children: [
    {
      id: 'cancel',
      type: 'control',
      label: 'Cancel',
      action: 'cancel',
      variant: 'outline'
    },
    {
      id: 'save-draft',
      type: 'control',
      label: 'Save Draft',
      action: 'custom',
      onClick: handleSaveDraft,
      variant: 'secondary'
    },
    {
      id: 'submit',
      type: 'control',
      label: 'Submit',
      action: 'submit',
      variant: 'default'
    }
  ]
}
```

## Integration Example

```typescript
import { useRef } from 'react';
import { AutoForm, AutoFormRef } from '@/form/context/AutoForm';
import { formConfig } from './formConfig';

function MyFormPage() {
  const formRef = useRef<AutoFormRef>(null);

  const handleSubmit = async (data: any) => {
    try {
      // Process form data
      await api.submitData(data);
      toast.success('Form submitted successfully');
    } catch (error) {
      toast.error('Submission failed');
    }
  };

  const handleSaveDraft = () => {
    const data = formRef.current?.getFormData();
    localStorage.setItem('draft', JSON.stringify(data));
  };

  return (
    <div>
      <AutoForm
        ref={formRef}
        node={formConfig}
        onSubmit={handleSubmit}
        initialData={savedDraft}
        validationMode="onChange"
      />
      <button onClick={handleSaveDraft}>Save Draft</button>
    </div>
  );
}
```

## Tips & Tricks

1. **Dynamic Forms**: Modify node configurations based on runtime conditions
2. **Custom Components**: Register new field components in the field registry
3. **Form Arrays**: Use array dataType for repeating field groups
4. **Async Validation**: Implement custom validation in submit handler
5. **Form State**: Access form state via context hooks when needed
6. **Performance**: Use memoization for large form configurations

Remember: AutoForm is designed to handle complex form scenarios while maintaining clean, declarative code. Use its node-based structure to create maintainable and reusable form configurations.