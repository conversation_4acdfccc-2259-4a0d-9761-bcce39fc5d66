import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger, <PERSON><PERSON>Content } from "@/components/ui/tabs";
import { UserBasicInfo } from "./UserBasicInfo";

export const UserCard: React.FC = () => {
  return (
    <Tabs defaultValue="basic" className="w-full">
      <TabsList className="mb-4">
        <TabsTrigger value="basic">Thông tin cơ bản</TabsTrigger>
        <TabsTrigger value="meta">Thông tin mở rộng</TabsTrigger>
      </TabsList>
      <TabsContent value="basic">
        <UserBasicInfo />
      </TabsContent>
      <TabsContent value="meta">
        <div className="text-muted-foreground italic">Đang phát triển...</div>
      </TabsContent>
    </Tabs>
  );
};
