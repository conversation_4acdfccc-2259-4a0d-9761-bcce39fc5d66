import React, { useState, useEffect, useMemo } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useAppSelector } from "@/store/rootReducer";
import { selectCategoryByType } from "@/features/category/states/selector";
import {
  buildCategoryTree,
  CategoryTree,
} from "@/features/category/states/types";
import { MenuTree } from "../menu/MenuTree";

// Helper function to build full path
const buildCategoryFullPath = (
  category: CategoryTree,
  menuMap: Record<number, CategoryTree>
): string => {
  const pathSegments: string[] = [];
  let current: CategoryTree | undefined = category;

  while (current) {
    if (current.slug) {
      pathSegments.unshift(current.slug);
    }
    if (current.parentId === null) {
      break;
    }
    current = menuMap[current.parentId];
  }

  return pathSegments.join("/");
};

interface PublicMenuVerticalProps {
  onMenuClick?: () => void;
}

export const PublicMenuVertical: React.FC<PublicMenuVerticalProps> = ({
  onMenuClick,
}) => {
  const [selectedId, setSelectedId] = useState<number | null>(null);
  const navigate = useNavigate();
  const location = useLocation();

  const list = useAppSelector((state) =>
    selectCategoryByType(state, "public-menu")
  );

  // Memoize để tránh re-render không cần thiết
  const { menuList, menuMap } = useMemo(() => {
    const menuList = buildCategoryTree(list);
    const menuMap: Record<number, CategoryTree> = {};
    const traverse = (node: CategoryTree) => {
      menuMap[node.id] = node;
      node.children.forEach(traverse);
    };
    menuList.forEach(traverse);
    return { menuList, menuMap };
  }, [list]);

  const onClick = (id: number) => {
    const node = menuMap[id];
    if (!node) return;

    setSelectedId(id);

    // Chỉ đóng menu khi click vào item có component
    if (node.description.component && onMenuClick) {
      onMenuClick();
    }

    // Navigate với full path
    if (node.description.component) {
      const fullPath = buildCategoryFullPath(node, menuMap);
      navigate(`/${fullPath}`);
    }
  };

  // Auto-select based on current path
  useEffect(() => {
    const candidates = Object.values(menuMap).filter((node) => {
      if (!node.slug) return false;
      const nodePath = buildCategoryFullPath(node, menuMap);
      return location.pathname.startsWith(`/${nodePath}`);
    });

    if (candidates.length > 0) {
      const current = candidates.reduce((a, b) =>
        (a.slug?.length ?? 0) > (b.slug?.length ?? 0) ? a : b
      );
      setSelectedId(current.id);
    }
  }, [location.pathname, menuMap]);

  return (
    <div className="w-full">
      <MenuTree items={menuList} selectedId={selectedId} onClick={onClick} />
    </div>
  );
};
