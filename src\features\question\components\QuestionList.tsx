import React from "react";
import { Question } from "../states/types";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Edit, Trash2, CheckCircle, XCircle, Clock } from "lucide-react";
import { useQuestion } from "../hooks/useQuestion";

interface QuestionListProps {
  questions: Question[];
}

export const QuestionList: React.FC<QuestionListProps> = ({ questions }) => {
  const {
    selectQuestion,
    deleteQuestion,
    updateQuestionStatus,
    publishQuestion,
  } = useQuestion();

  const handleEdit = (question: Question) => {
    selectQuestion(question);
  };

  const handleDelete = (questionId: number) => {
    if (confirm("Bạn có chắc chắn muốn xóa câu hỏi này?")) {
      deleteQuestion(questionId);
    }
  };

  const handlePublish = (questionId: number) => {
    publishQuestion(questionId);
  };

  const handleTrash = (questionId: number) => {
    updateQuestionStatus(questionId, "TRASH");
  };

  const handleDraft = (questionId: number) => {
    updateQuestionStatus(questionId, "DRAFT");
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "PUBLISHED":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "REJECTED":
        return <XCircle className="h-4 w-4 text-red-500" />;
      case "PENDING":
        return <Clock className="h-4 w-4 text-yellow-500" />;
      default:
        return null;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "PUBLISHED":
        return "Đã xuất bản";
      case "REJECTED":
        return "Từ chối";
      case "PENDING":
        return "Chờ duyệt";
      default:
        return status;
    }
  };

  const getStatusVariant = (
    status: string
  ): "default" | "secondary" | "destructive" | "outline" => {
    switch (status) {
      case "PUBLISHED":
        return "default";
      case "REJECTED":
        return "destructive";
      case "PENDING":
        return "secondary";
      default:
        return "outline";
    }
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString("vi-VN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  if (!questions || questions.length === 0) {
    return (
      <Card>
        <CardContent className="p-6">
          <p className="text-center text-gray-500">Chưa có câu hỏi nào</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <h2 className="text-lg font-semibold">
        Danh sách câu hỏi ({questions.length})
      </h2>
      {questions.map((question) => (
        <Card key={question.id}>
          <CardHeader className="pb-2">
            <div className="flex justify-between items-start">
              <div className="flex-1">
                <CardTitle className="text-sm mb-2">
                  {question.content.title}
                </CardTitle>
                <div className="text-xs text-gray-500 space-y-1">
                  <div>Người hỏi: {question.asker.fullName}</div>
                  <div>Email: {question.asker.email}</div>
                  <div>Ngày tạo: {formatDate(question.createdAt)}</div>
                </div>
              </div>
              <div className="flex gap-2 ml-4">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleEdit(question)}
                >
                  <Edit className="h-4 w-4" />
                </Button>
                {question.status === "NEW" && (
                  <>
                    <Button
                      size="sm"
                      variant="default"
                      onClick={() => handleDraft(question.id)}
                    >
                      <CheckCircle className="h-4 w-4" />
                    </Button>
                    <Button
                      size="sm"
                      variant="destructive"
                      onClick={() => handleTrash(question.id)}
                    >
                      <XCircle className="h-4 w-4" />
                    </Button>
                  </>
                )}
                {question.status === "DRAFT" && (
                  <>
                    <Button
                      size="sm"
                      variant="default"
                      onClick={() => handlePublish(question.id)}
                    >
                      <CheckCircle className="h-4 w-4" />
                    </Button>
                    <Button
                      size="sm"
                      variant="destructive"
                      onClick={() => handleTrash(question.id)}
                    >
                      <XCircle className="h-4 w-4" />
                    </Button>
                  </>
                )}
                {question.status === "PUBLISHED" && (
                  <Button
                    size="sm"
                    variant="secondary"
                    onClick={() => handleDraft(question.id)}
                  >
                    <Clock className="h-4 w-4" />
                  </Button>
                )}
                {question.status === "TRASH" && (
                  <Button
                    size="sm"
                    variant="default"
                    onClick={() => handleDraft(question.id)}
                  >
                    <Clock className="h-4 w-4" />
                  </Button>
                )}
                <Button
                  size="sm"
                  variant="destructive"
                  onClick={() => handleDelete(question.id)}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-600 mb-3 line-clamp-2">
              {question.content.question}
            </p>
            <div className="flex justify-between items-center">
              <div className="flex gap-2">
                <Badge
                  variant={getStatusVariant(question.status)}
                  className="flex items-center gap-1"
                >
                  {getStatusIcon(question.status)}
                  {getStatusText(question.status)}
                </Badge>
                <Badge variant="outline">{question.topic}</Badge>
              </div>
              <div className="text-xs text-gray-400">ID: {question.id}</div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};
