import { useAppDispatch } from "@/store/rootReducer";
import { registerAsync } from "../states/slices";
import { useNavigate } from "react-router-dom";
import { AutoForm } from "@/form/context";
import { FormData } from "@/form/types";
import { RegisterRequest } from "../states/type";
import { RegisterFormConfig } from "../states/mock";

export const AutoFormRegister: React.FC = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();

  const initialData = {
    userName: "",
    fullName: "",
    email: "",
    phone: "",
    password: "",
    repassword: "",
  };

  const handleSubmit = async (data: FormData) => {
    if (data.password !== data.repassword) {
      alert("Mật khẩu không khớp");
      return;
    }

    const registerRequest: RegisterRequest = {
      userName: String(data.userName ?? ""),
      fullName: String(data.fullName ?? ""),
      email: String(data.email ?? ""),
      phone: String(data.phone ?? ""),
      password: String(data.password ?? ""),
    };

    const resultAction = await dispatch(registerAsync(registerRequest));
    if (registerAsync.fulfilled.match(resultAction)) {
      navigate("/me");
    }
  };

  return (
    <AutoForm
      node={RegisterFormConfig.config}
      viewOnly={false}
      initialData={initialData}
      onSubmit={handleSubmit}
      validationMode="onSubmit"
      className="space-y-4"
    />
  );
};
