import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import { Post } from "@/features/post/states/types";
import { CategoryDTO } from "@/features/category/states/types";
import { RootState } from "@/store/rootReducer";
import {
  PageState,
  PageMode,
  CreatePageRequest,
  LinkPageToCategoryRequest,
} from "./types";
import {
  fetchPages,
  fetchPagesSummary,
  searchPages,
  fetchPageById,
  createPage,
  updatePage,
  deletePage,
  updatePageStatus,
  linkPageToCategory,
  unlinkPageFromCategory,
} from "./api";
import { toast } from "sonner";

const initialState: PageState = {
  pages: {},
  pageSummaries: [], // Lightweight page data for lists
  searchPagination: {
    currentPage: 0,
    hasMore: true,
    loading: false,
  },
  selectedCategoryId: null,
  selectedPageId: null,
  mode: "view",
  loading: false,
  error: null,
  categoryPageMapping: {},
};

// Async thunks
export const fetchAllPages = createAsyncThunk("page/fetchAll", async () => {
  const response = await fetchPages();
  if (!response.data) {
    throw new Error("No data received");
  }
  return response.data;
});

// New lightweight thunk for page summaries (for management lists)
export const fetchPagesSummaryAsync = createAsyncThunk(
  "page/fetchSummary",
  async () => {
    const response = await fetchPagesSummary();
    if (!response.data) {
      throw new Error("No data received");
    }
    return response.data;
  }
);

// Search pages thunk
export const searchPagesAsync = createAsyncThunk(
  "page/search",
  async ({
    keyword,
    page = 0,
    size = 20,
    isLoadMore = false,
  }: {
    keyword: string;
    page?: number;
    size?: number;
    isLoadMore?: boolean;
  }) => {
    const response = await searchPages(keyword, page, size);
    if (!response.data) {
      throw new Error("No data received");
    }
    return { ...response.data, isLoadMore }; // Add isLoadMore flag to response
  }
);

// Load more pages thunk
export const loadMorePagesAsync = createAsyncThunk(
  "page/loadMore",
  async ({
    keyword,
    currentPage,
    size = 20,
  }: {
    keyword: string;
    currentPage: number;
    size?: number;
  }) => {
    const nextPage = currentPage + 1;
    const response = await searchPages(keyword, nextPage, size);
    if (!response.data) {
      throw new Error("No data received");
    }
    return { ...response.data, page: nextPage };
  }
);

export const fetchPageByIdAsync = createAsyncThunk(
  "page/fetchById",
  async (id: number) => {
    const response = await fetchPageById(id);
    if (!response.data) {
      throw new Error("Page not found");
    }
    return response.data;
  }
);

export const createPageAsync = createAsyncThunk(
  "page/create",
  async (payload: CreatePageRequest) => {
    const response = await createPage(payload);
    if (!response.data) {
      throw new Error("Failed to create page");
    }

    // After creating page, link it to category
    await linkPageToCategory({
      categoryId: payload.categoryId,
      pageId: response.data.id,
    });

    return { page: response.data, categoryId: payload.categoryId };
  }
);

export const updatePageAsync = createAsyncThunk(
  "page/update",
  async ({
    id,
    data,
  }: {
    id: number;
    data: Omit<
      Post,
      | "id"
      | "createdAt"
      | "updatedAt"
      | "publishedAt"
      | "postType"
      | "status"
      | "authorId"
    >;
  }) => {
    const response = await updatePage(id, data);
    if (!response.data) {
      throw new Error("Failed to update page");
    }
    return response.data;
  }
);

export const deletePageAsync = createAsyncThunk(
  "page/delete",
  async (id: number) => {
    await deletePage(id);
    return id;
  }
);

export const restorePageAsync = createAsyncThunk(
  "page/restore",
  async (id: number) => {
    await updatePageStatus(id, "DRAFT");
    return id;
  }
);

export const linkPageToCategoryAsync = createAsyncThunk(
  "page/linkToCategory",
  async (payload: LinkPageToCategoryRequest) => {
    await linkPageToCategory(payload);
    return payload;
  }
);

export const unlinkPageFromCategoryAsync = createAsyncThunk(
  "page/unlinkFromCategory",
  async (categoryId: number) => {
    await unlinkPageFromCategory(categoryId);
    return categoryId;
  }
);

// Fetch page content by category full path - optimized for Page component
export const fetchPageBySlugAsync = createAsyncThunk(
  "page/fetchBySlug",
  async (fullPath: string, { getState, dispatch }) => {
    const state = getState() as RootState;

    // Helper function to build full path for a category
    const buildCategoryFullPath = (
      category: CategoryDTO,
      allCategories: CategoryDTO[]
    ): string => {
      const categoryMap = new Map<number, CategoryDTO>();
      allCategories.forEach((cat) => categoryMap.set(cat.id, cat));

      const pathSegments: string[] = [];
      let current: CategoryDTO | undefined = category;

      while (current) {
        pathSegments.unshift(current.slug);
        if (current.parentId === null) {
          break;
        }
        current = categoryMap.get(current.parentId);
      }

      return pathSegments.join("/");
    };

    // Find category by full path in current Redux state
    let category: CategoryDTO | null = null;
    const categoryTypes = state.categoryState.types;

    // First try to find category by comparing with built full paths
    for (const typeState of Object.values(categoryTypes)) {
      const categories = typeState.data;
      for (const cat of categories) {
        const builtPath = buildCategoryFullPath(cat, categories);
        if (builtPath === fullPath) {
          category = cat;
          break;
        }
      }
      if (category) break;
    }

    // Fallback: try to find by just the last segment (for backwards compatibility)
    if (!category) {
      const lastSegment = fullPath.split("/").pop() || "";
      if (lastSegment) {
        for (const typeState of Object.values(categoryTypes)) {
          const found = typeState.data.find(
            (cat: CategoryDTO) => cat.slug === lastSegment
          );
          if (found) {
            category = found;
            break;
          }
        }
      }
    }

    // If category not found in cache, try to fetch all public categories first
    if (!category) {
      const { fetchCategoryByType } = await import(
        "@/features/category/states/slices"
      );
      await dispatch(fetchCategoryByType({ type: "public-menu" }));

      // Try again after fetching with full path matching
      const updatedState = getState() as RootState;
      const updatedCategoryTypes = updatedState.categoryState.types;

      for (const typeState of Object.values(updatedCategoryTypes)) {
        const categories = typeState.data;
        for (const cat of categories) {
          const builtPath = buildCategoryFullPath(cat, categories);
          if (builtPath === fullPath) {
            category = cat;
            break;
          }
        }
        if (category) break;
      }

      // Final fallback after fetching
      if (!category) {
        const lastSegment = fullPath.split("/").pop() || "";
        if (lastSegment) {
          for (const typeState of Object.values(updatedCategoryTypes)) {
            const found = typeState.data.find(
              (cat: CategoryDTO) => cat.slug === lastSegment
            );
            if (found) {
              category = found;
              break;
            }
          }
        }
      }
    }

    if (!category) {
      throw new Error(`Category with path "${fullPath}" not found`);
    }

    if (!category.postId) {
      throw new Error(
        `Category "${fullPath}" does not have a linked page (postId: ${category.postId})`
      );
    }

    // Check if page is already in cache
    const pageInCache = state.pageState.pages[category.postId];
    if (pageInCache) {
      return { category, page: pageInCache, fromCache: true };
    }

    // Fetch page content if not in cache
    const response = await fetchPageById(category.postId);
    if (!response.data) {
      throw new Error(
        `Failed to fetch page content for category "${fullPath}" (postId: ${category.postId})`
      );
    }

    return { category, page: response.data, fromCache: false };
  }
);

const pageSlice = createSlice({
  name: "page",
  initialState,
  reducers: {
    setSelectedCategory: (state, action: PayloadAction<number | null>) => {
      state.selectedCategoryId = action.payload;

      // Find linked page for this category
      if (action.payload && state.categoryPageMapping[action.payload]) {
        state.selectedPageId = state.categoryPageMapping[action.payload];
      } else {
        state.selectedPageId = null;
      }
    },

    setSelectedPage: (state, action: PayloadAction<number | null>) => {
      state.selectedPageId = action.payload;
    },

    setMode: (state, action: PayloadAction<PageMode>) => {
      state.mode = action.payload;
    },

    clearError: (state) => {
      state.error = null;
    },

    // Update category-page mapping from category data
    updateCategoryPageMapping: (
      state,
      action: PayloadAction<Record<number, number>>
    ) => {
      state.categoryPageMapping = {
        ...state.categoryPageMapping,
        ...action.payload,
      };
    },
  },

  extraReducers: (builder) => {
    builder
      // Fetch all pages
      .addCase(fetchAllPages.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAllPages.fulfilled, (state, action) => {
        state.loading = false;
        // Convert array to record mapping id -> Post
        state.pages = action.payload.reduce((acc, page) => {
          acc[page.id] = page;
          return acc;
        }, {} as Record<number, Post>);
      })
      .addCase(fetchAllPages.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || "Failed to fetch pages";
        toast.error("Không thể tải danh sách trang");
      })

      // Fetch pages summary (lightweight)
      .addCase(fetchPagesSummaryAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchPagesSummaryAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.pageSummaries = action.payload;
      })
      .addCase(fetchPagesSummaryAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || "Failed to fetch page summaries";
        toast.error("Không thể tải danh sách trang");
      })

      // Search pages (lightweight)
      .addCase(searchPagesAsync.pending, (state) => {
        state.searchPagination.loading = true;
        state.error = null;
      })
      .addCase(searchPagesAsync.fulfilled, (state, action) => {
        const { data, pagination, isLoadMore } = action.payload;

        if (isLoadMore && data) {
          // Append to existing results for "load more"
          state.pageSummaries = [...state.pageSummaries, ...data];
        } else if (data) {
          // Replace results for new search
          state.pageSummaries = data;
          if (pagination) {
            state.searchPagination.currentPage = pagination.page;
          }
        }

        // Determine if there are more pages to load
        // Since total is always 0, check if we got the full page size
        if (data && pagination) {
          state.searchPagination.hasMore = data.length === pagination.size;
        }
        state.searchPagination.loading = false;
      })
      .addCase(searchPagesAsync.rejected, (state, action) => {
        state.searchPagination.loading = false;
        state.error = action.error.message || "Failed to search pages";
        toast.error("Không thể tìm kiếm trang");
      })

      // Load more pages
      .addCase(loadMorePagesAsync.pending, (state) => {
        state.searchPagination.loading = true;
      })
      .addCase(loadMorePagesAsync.fulfilled, (state, action) => {
        const { data, pagination } = action.payload;

        // Append new results
        if (data) {
          state.pageSummaries = [...state.pageSummaries, ...data];
        }
        if (pagination) {
          state.searchPagination.currentPage = pagination.page;
          // Check if there are more pages
          if (data) {
            state.searchPagination.hasMore = data.length === pagination.size;
          }
        }
        state.searchPagination.loading = false;
      })
      .addCase(loadMorePagesAsync.rejected, (state, action) => {
        state.searchPagination.loading = false;
        state.error = action.error.message || "Failed to load more pages";
        toast.error("Không thể tải thêm trang");
      })

      // Fetch page by ID
      .addCase(fetchPageByIdAsync.fulfilled, (state, action) => {
        state.pages[action.payload.id] = action.payload;
      })
      .addCase(fetchPageByIdAsync.rejected, (state, action) => {
        state.error = action.error.message || "Failed to fetch page";
        toast.error("Không thể tải trang");
      })

      // Create page
      .addCase(createPageAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createPageAsync.fulfilled, (state, action) => {
        state.loading = false;
        const { page, categoryId } = action.payload;
        state.pages[page.id] = page;
        state.categoryPageMapping[categoryId] = page.id;
        state.selectedPageId = page.id;
        state.mode = "view";
        toast.success("Tạo trang thành công");
      })
      .addCase(createPageAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || "Failed to create page";
        toast.error("Không thể tạo trang");
      })

      // Update page
      .addCase(updatePageAsync.fulfilled, (state, action) => {
        state.pages[action.payload.id] = action.payload;
        toast.success("Cập nhật trang thành công");
      })
      .addCase(updatePageAsync.rejected, (state, action) => {
        state.error = action.error.message || "Failed to update page";
        toast.error("Không thể cập nhật trang");
      })

      // Delete page (move to TRASH)
      .addCase(deletePageAsync.fulfilled, (state, action) => {
        const pageId = action.payload;

        // Update page status to TRASH in memory (we'll fetch updated data if needed)
        if (state.pages[pageId]) {
          state.pages[pageId].status = "TRASH";
        }

        // Remove from category mapping since TRASH pages shouldn't be linked
        Object.keys(state.categoryPageMapping).forEach((categoryId) => {
          if (state.categoryPageMapping[Number(categoryId)] === pageId) {
            delete state.categoryPageMapping[Number(categoryId)];
          }
        });

        if (state.selectedPageId === pageId) {
          state.selectedPageId = null;
        }
        toast.success("Đã chuyển trang vào thùng rác");
      })
      .addCase(deletePageAsync.rejected, (state, action) => {
        state.error = action.error.message || "Failed to delete page";
        toast.error("Không thể xóa trang");
      })

      // Restore page from TRASH
      .addCase(restorePageAsync.fulfilled, (state, action) => {
        const pageId = action.payload;
        // Update page status to DRAFT in memory
        if (state.pages[pageId]) {
          state.pages[pageId].status = "DRAFT";
        }
        toast.success("Đã khôi phục trang từ thùng rác");
      })
      .addCase(restorePageAsync.rejected, (state, action) => {
        state.error = action.error.message || "Failed to restore page";
        toast.error("Không thể khôi phục trang");
      })

      // Link page to category
      .addCase(linkPageToCategoryAsync.fulfilled, (state, action) => {
        const { categoryId, pageId } = action.payload;
        state.categoryPageMapping[categoryId] = pageId;
        toast.success("Liên kết trang với chuyên mục thành công");
      })
      .addCase(linkPageToCategoryAsync.rejected, (state, action) => {
        state.error = action.error.message || "Failed to link page";
        toast.error("Không thể liên kết trang");
      })

      // Unlink page from category
      .addCase(unlinkPageFromCategoryAsync.fulfilled, (state, action) => {
        const categoryId = action.meta.arg;
        delete state.categoryPageMapping[categoryId];
        state.loading = false;
        toast.success("Đã hủy liên kết trang thành công");
      })
      .addCase(fetchPageBySlugAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchPageBySlugAsync.fulfilled, (state, action) => {
        const { page, category } = action.payload;

        // Store page in cache
        state.pages[page.id] = page;

        // Update category-page mapping
        state.categoryPageMapping[category.id] = page.id;

        state.loading = false;
        state.error = null;
      })
      .addCase(fetchPageBySlugAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || "Failed to fetch page";
      });
  },
});

export const {
  setSelectedCategory,
  setSelectedPage,
  setMode,
  clearError,
  updateCategoryPageMapping,
} = pageSlice.actions;

export default pageSlice.reducer;
