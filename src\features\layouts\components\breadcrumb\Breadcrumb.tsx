import { Link } from "react-router-dom";
import { ChevronRight, Home } from "lucide-react";
import clsx from "clsx";

export interface BreadcrumbItem {
  label: string;
  path?: string;
}

interface BreadcrumbProps {
  items: BreadcrumbItem[];
}

export const Breadcrumb: React.FC<BreadcrumbProps> = ({ items }) => {
  return (
    <nav className="text-sm text-muted-foreground">
      <ol className="flex items-center gap-2">
        {/* Trang chủ luôn hiển thị trước */}
        <li className="inline-flex items-center gap-1">
          <Link to="/" className="hover:text-primary text-foreground">
            <Home className="w-4 h-4" />
          </Link>
        </li>

        {items.map((item, index) => (
          <li key={index} className="inline-flex items-center gap-1">
            <span>
              <ChevronRight />
            </span>
            {item.path ? (
              <Link to={item.path} className={clsx("text-primary")}>
                {item.label}
              </Link>
            ) : (
              <span className="text-foreground">{item.label}</span>
            )}
          </li>
        ))}
      </ol>
    </nav>
  );
};
