import React, { useRef, useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Upload,
  CheckCircle,
  AlertCircle,
  Loader2,
  ImagePlus,
  X,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { uploadFile, getFileViewUrl } from "@/features/demo/states/api";
import { ImageLoader } from "@/components/image/ImageLoader";

type UploadStatus = "idle" | "uploading" | "success" | "error";

interface ImageUploaderProps {
  onUploadSuccess?: (fileUrl: string) => void;
  onUploadError?: (error: string) => void;
  className?: string;
  placeholder?: string;
}

export const ImageUploader = ({
  onUploadSuccess,
  onUploadError,
  className,
  placeholder = "Click để tải ảnh lên",
}: ImageUploaderProps) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [uploadStatus, setUploadStatus] = useState<UploadStatus>("idle");
  const [errorMessage, setErrorMessage] = useState<string>("");
  const [previewUrl, setPreviewUrl] = useState<string>("");
  const [serverImageUrl, setServerImageUrl] = useState<string>("");
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && file.type.startsWith("image/")) {
      setSelectedFile(file);
      setUploadStatus("idle");
      setErrorMessage("");
      setServerImageUrl("");

      // Create local preview
      const url = URL.createObjectURL(file);
      setPreviewUrl(url);
    }
  };

  const handleChooseFile = () => {
    if (uploadStatus === "uploading") return;
    fileInputRef.current?.click();
  };

  const handleRemoveImage = () => {
    if (uploadStatus === "uploading") return;

    setSelectedFile(null);
    setUploadStatus("idle");
    setErrorMessage("");
    setServerImageUrl("");

    // Clean up preview URL
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
      setPreviewUrl("");
    }

    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const handleUpload = async () => {
    if (!selectedFile || uploadStatus === "uploading") return;

    setUploadStatus("uploading");
    setErrorMessage("");

    try {
      const response = await uploadFile(selectedFile);

      if (response.code === "SUCCESS") {
        const fileUrl = getFileViewUrl(response.data.name);
        setServerImageUrl(fileUrl);
        setUploadStatus("success");

        // Clean up local preview since we now have server image
        if (previewUrl) {
          URL.revokeObjectURL(previewUrl);
          setPreviewUrl("");
        }

        onUploadSuccess?.(fileUrl);
      } else {
        throw new Error(response.message || "Upload failed");
      }
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : "Upload failed";
      setErrorMessage(errorMsg);
      setUploadStatus("error");
      onUploadError?.(errorMsg);
    }
  };

  const isDisabled = uploadStatus === "uploading";
  const hasImage = selectedFile || serverImageUrl;
  const displayImageUrl = serverImageUrl || previewUrl;

  return (
    <div className={cn("w-full max-w-sm", className)}>
      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        onChange={handleFileSelect}
        className="hidden"
        accept="image/*"
        disabled={isDisabled}
      />

      {!hasImage ? (
        // Empty placeholder state
        <div
          onClick={handleChooseFile}
          className={cn(
            "relative w-full aspect-square border-2 border-dashed border-gray-300 rounded-lg",
            "bg-gray-50 hover:bg-gray-100 cursor-pointer transition-colors",
            "flex flex-col items-center justify-center space-y-2",
            "focus:border-primary focus:ring-2 focus:ring-primary/20",
            isDisabled && "opacity-50 cursor-not-allowed hover:bg-gray-50"
          )}
        >
          <ImagePlus className="h-8 w-8 text-gray-400" />
          <span className="text-sm text-gray-600 text-center px-4">
            {placeholder}
          </span>
          {isDisabled && (
            <div className="absolute inset-0 flex items-center justify-center bg-white/80 rounded-lg">
              <Loader2 className="h-6 w-6 animate-spin text-primary" />
            </div>
          )}
        </div>
      ) : (
        // Image preview/display state
        <div className="relative w-full aspect-square rounded-lg overflow-hidden border border-gray-300">
          {displayImageUrl && (
            <ImageLoader
              src={displayImageUrl}
              alt="Uploaded image"
              className="w-full h-full object-contain bg-gray-50"
            />
          )}

          {/* Overlay controls */}
          <div className="absolute top-2 right-2 flex items-center space-x-2">
            {/* Upload status indicator */}
            {uploadStatus === "success" && (
              <div className="bg-green-100 p-1.5 rounded-full">
                <CheckCircle className="h-4 w-4 text-green-600" />
              </div>
            )}
            {uploadStatus === "error" && (
              <div className="bg-red-100 p-1.5 rounded-full">
                <AlertCircle className="h-4 w-4 text-red-600" />
              </div>
            )}

            {/* Remove button */}
            <Button
              onClick={handleRemoveImage}
              variant="destructive"
              size="sm"
              className="h-8 w-8 p-0 rounded-full"
              disabled={isDisabled}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          {/* Upload loading overlay */}
          {uploadStatus === "uploading" && (
            <div className="absolute inset-0 flex items-center justify-center bg-black/50 rounded-lg">
              <div className="bg-white rounded-lg p-4 flex items-center space-x-3">
                <Loader2 className="h-5 w-5 animate-spin text-primary" />
                <span className="text-sm font-medium">Đang tải lên...</span>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Action buttons */}
      {hasImage && uploadStatus !== "success" && (
        <div className="mt-4 flex justify-center">
          <Button
            onClick={handleUpload}
            variant="default"
            disabled={isDisabled}
            className="px-6"
          >
            {uploadStatus === "uploading" ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Đang tải...
              </>
            ) : (
              <>
                <Upload className="h-4 w-4 mr-2" />
                Tải lên
              </>
            )}
          </Button>
        </div>
      )}

      {/* Error message */}
      {uploadStatus === "error" && errorMessage && (
        <div className="mt-3 text-sm text-red-600 flex items-center space-x-1">
          <AlertCircle className="h-4 w-4" />
          <span>{errorMessage}</span>
        </div>
      )}
    </div>
  );
};

export default ImageUploader;
