/**
 * Properties System Exports
 * Main export file for the properties form system
 */

// Main factory component
export { PropertiesFormFactory } from './PropertiesFormFactory';

// Types
export type { 
  PropertyTab, 
  DisplayMode, 
  PropertyValue, 
  PropertyGroup, 
  SectionState, 
  PropertiesFormProps,
  LayoutType,
  GridConfig,
  StyleUpdate,
  PropertySection 
} from './types';

// Components
export { PropertyTabs } from './components/PropertyTabs';
export { AttributesTab } from './components/AttributesTab';
export { DisplayTab } from './components/DisplayTab';
export { CollapsibleSection } from './components/CollapsibleSection';

// Sections
export { LayoutSection } from './sections/LayoutSection';
export { SpacingSection } from './sections/SpacingSection';
export { BorderSection } from './sections/BorderSection';
export { AppearanceSection } from './sections/AppearanceSection';
export { EffectsSection } from './sections/EffectsSection';

// Input Components
export { ColorPicker } from './input/ColorPicker';
export { SpacingPopover } from './input/SpacingPopover';
export { BorderPopover } from './input/BorderPopover';
export { LayoutPopover } from './input/LayoutPopover';
export { GapPopover } from './input/GapPopover';