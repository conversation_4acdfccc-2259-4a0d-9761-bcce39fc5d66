import React from "react";
import { ActionAutoForm } from "./ActionAutoForm";
import { useAppSelector } from "@/store/rootReducer";
import { selectCurrentAction } from "../states/selectors";

export const ActionDetailView: React.FC = () => {
  const currentAction = useAppSelector(selectCurrentAction);

  if (!currentAction) {
    return (
      <div className="p-4 text-center text-gray-500">
        Chọn một quyền để xem chi tiết
      </div>
    );
  }

  const initialData = {
    code: currentAction.code,
    name: currentAction.name,
    metadata: currentAction.metadata,
  };

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">Chi tiết quyền</h3>
      <ActionAutoForm
        initialData={initialData}
        onSubmit={() => {}} // View only mode
        viewOnly={true}
      />

      {/* Additional readonly info */}
      <div className="mt-6 grid grid-cols-2 gap-4 p-4 bg-gray-50 rounded-lg">
        <div>
          <label className="text-sm font-medium text-gray-600">ID</label>
          <p className="text-sm text-gray-900">{currentAction.id}</p>
        </div>
        <div>
          <label className="text-sm font-medium text-gray-600">Ngày tạo</label>
          <p className="text-sm text-gray-900">
            {new Date(currentAction.createdAt).toLocaleString()}
          </p>
        </div>
        <div>
          <label className="text-sm font-medium text-gray-600">
            Ngày cập nhật
          </label>
          <p className="text-sm text-gray-900">
            {new Date(currentAction.updatedAt).toLocaleString()}
          </p>
        </div>
        {currentAction.metadata &&
          Object.keys(currentAction.metadata).length > 0 && (
            <div>
              <label className="text-sm font-medium text-gray-600">
                Metadata
              </label>
              <pre className="text-sm text-gray-900 bg-white p-2 rounded border">
                {JSON.stringify(currentAction.metadata, null, 2)}
              </pre>
            </div>
          )}
      </div>
    </div>
  );
};
