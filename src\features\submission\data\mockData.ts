export interface SubmissionInfo {
  id: string;
  registrationCode: string;
  organizationCode: string;
  organizationName: string;
  totalRegisteredRecords: number;
  totalSubmittedRecords: number;
  totalUploadedRecords: number;
  submissionCount: number;
  sendDate: string;
  note: string;
  contactPerson: {
    fullName: string;
    position: string;
    phone: string;
    email: string;
  };
  status: "draft" | "submitted" | "processing" | "approved" | "rejected";
  createdAt: string;
  updatedAt: string;
}

export interface RecordPackage {
  id: number;
  recordCode: string;
  content: string;
  size: string; // in MB/KB/GB
  recordOrder: string;
  status: "new" | "expanded" | "replaced";
  documentCount: number;
  createdAt: string;
  updatedAt: string;
}

export interface RecordPackageFilters {
  keyword: string;
  status: string;
}

// Status labels for display
export const submissionStatusLabels: Record<SubmissionInfo["status"], string> =
  {
    draft: "Bản nháp",
    submitted: "Đã gửi",
    processing: "Đang xử lý",
    approved: "Đã duyệt",
    rejected: "Từ chối",
  };

export const submissionStatusColors: Record<SubmissionInfo["status"], string> =
  {
    draft: "bg-gray-100 text-gray-800",
    submitted: "bg-blue-100 text-blue-800",
    processing: "bg-yellow-100 text-yellow-800",
    approved: "bg-green-100 text-green-800",
    rejected: "bg-red-100 text-red-800",
  };

export const recordPackageStatusLabels: Record<
  RecordPackage["status"],
  string
> = {
  new: "Tạo mới",
  expanded: "Mở rộng",
  replaced: "Thay thế",
};

export const recordPackageStatusColors: Record<
  RecordPackage["status"],
  string
> = {
  new: "bg-blue-100 text-blue-800",
  expanded: "bg-green-100 text-green-800",
  replaced: "bg-yellow-100 text-yellow-800",
};

// Mock submission data
export const mockSubmissionInfo: SubmissionInfo = {
  id: "1",
  registrationCode: "NLLS01",
  organizationCode: "H32.03",
  organizationName: "Sở Công Thương Khánh Hòa",
  totalRegisteredRecords: 1,
  totalSubmittedRecords: 100,
  totalUploadedRecords: 100,
  submissionCount: 2,
  sendDate: "01/03/2025",
  note: "Nộp lưu lần 2",
  contactPerson: {
    fullName: "Nguyễn Văn An",
    position: "Nhân viên",
    phone: "0988888888",
    email: "<EMAIL>",
  },
  status: "draft",
  createdAt: "2025-03-01T00:00:00Z",
  updatedAt: "2025-03-01T00:00:00Z",
};

// Generate mock record packages
export const generateMockRecordPackages = (
  count: number = 5
): RecordPackage[] => {
  const contents = [
    "Hồ sơ G09.2021.01.TCCB về tập quyết định nhân sự năm 2021",
    "Hồ sơ G10.2021.01.TCCB về báo cáo thống kê năm 2021",
    "Hồ sơ G11.2021.01.TCCB về kế hoạch hoạt động năm 2022",
    "Hồ sơ G12.2021.01.TCCB về báo cáo tài chính năm 2021",
    "Hồ sơ G13.2021.01.TCCB về nghị quyết hội đồng quản trị",
  ];

  const statuses: RecordPackage["status"][] = ["new", "expanded", "replaced"];
  const sizes = ["2.5 MB", "1.8 MB", "3.2 MB", "1.1 MB", "4.5 MB"];

  return Array.from({ length: count }, (_, index) => ({
    id: index + 1,
    recordCode: `G09.16.04.26.01.6466`,
    content: contents[index % contents.length],
    size: sizes[index % sizes.length],
    recordOrder: "01",
    status: statuses[index % statuses.length],
    documentCount: Math.floor(Math.random() * 10) + 1,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  }));
};

export const mockRecordPackages = generateMockRecordPackages(5);

// Filter function for record packages
export const filterRecordPackages = (
  packages: RecordPackage[],
  filters: RecordPackageFilters
): RecordPackage[] => {
  return packages.filter((pkg) => {
    // Keyword filter
    if (filters.keyword) {
      const keyword = filters.keyword.toLowerCase();
      const matchesKeyword =
        pkg.recordCode.toLowerCase().includes(keyword) ||
        pkg.content.toLowerCase().includes(keyword);

      if (!matchesKeyword) return false;
    }

    // Status filter
    if (filters.status && filters.status !== "" && filters.status !== "all") {
      if (pkg.status !== filters.status) return false;
    }

    return true;
  });
};
