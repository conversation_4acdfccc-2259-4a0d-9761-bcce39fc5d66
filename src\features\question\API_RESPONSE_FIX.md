# API Response Structure Fix

## Vấn đề: Sai cách access API response data

API trả về đúng format nhưng application báo lỗi do access data sai cách.

### API Response Structure:

```json
{
  "data": {
    "active": true,
    "topics": ["<PERSON>", "<PERSON><PERSON> thuật"]
  }
}
```

### Vấn đề phát hiện:

#### ❌ BEFORE: Sai structure

```typescript
// Định nghĩa sai
interface ApiResponse<T> {
  data: T;
}

// Access sai
const response = await QAConfigAPI.getQAConfig();
return response.data; // ❌ Thiếu 1 layer .data
```

#### ✅ AFTER: Đúng structure theo project pattern

```typescript
// Sử dụng BaseResponse từ project
AxiosResponse<BaseResponse<QAConfig>>;

// Access đúng
const response = await QAConfigAPI.getQAConfig();
return response.data.data; // ✅ AxiosResponse.data.BaseResponse.data
```

## C<PERSON>u trúc Response thực tế:

```typescript
// AxiosResponse wrapper
{
  data: BaseResponse<QAConfig>, // ← Axios wrapper
  status: 200,
  statusText: "OK",
  // ... other axios properties
}

// BaseResponse<T> structure
{
  code: number,
  message: string,
  data: QAConfig, // ← Actual data here
  pagination?: Pagination
}

// QAConfig data
{
  active: boolean,
  topics: string[]
}
```

## Các fix đã áp dụng:

### ✅ 1. questionApi.ts

```typescript
// ❌ BEFORE
static async getQAConfig(): Promise<ApiResponse<QAConfig>> {
  return restApi.get(API_ENDPOINTS.PORTAL.PUBLIC.QA_CONFIG);
}

// ✅ AFTER
static async getQAConfig(): Promise<AxiosResponse<BaseResponse<QAConfig>>> {
  return restApi.get<BaseResponse<QAConfig>>(API_ENDPOINTS.PORTAL.PUBLIC.QA_CONFIG);
}
```

### ✅ 2. slices.ts - fetchQAConfig

```typescript
// ❌ BEFORE
const response = await QAConfigAPI.getQAConfig();
return response.data; // Missing one .data layer

// ✅ AFTER
const response = await QAConfigAPI.getQAConfig();
return response.data.data; // AxiosResponse.data.BaseResponse.data
```

### ✅ 3. testConnection.ts

```typescript
// ❌ BEFORE
data: response.data,

// ✅ AFTER
data: response.data.data, // Correct data access
```

## Path để access data:

```typescript
response.data.data;
//  ↑      ↑    ↑
//  │      │    └── Actual QAConfig data
//  │      └─────── BaseResponse wrapper
//  └────────────── AxiosResponse wrapper
```

## Testing:

### Console logs sẽ thấy:

```javascript
// API Response structure:
{
  data: {
    code: 200,
    message: "Success",
    data: {
      active: true,
      topics: ["Chung", "Kỹ thuật"]
    }
  },
  status: 200,
  statusText: "OK"
}
```

### Expected behavior:

1. ✅ API call thành công
2. ✅ Data được parse đúng: `{active: true, topics: ["Chung", "Kỹ thuật"]}`
3. ✅ Form được populate với data từ API
4. ✅ Không còn "data lỗi" errors

## Consistency với project pattern:

Giờ đây QA Config API follows cùng pattern với các API khác:

- `AxiosResponse<BaseResponse<T>>` return type
- `response.data.data` để access actual data
- Type safety với generics
- Consistent error handling
