/**
 * Transform Popover Component
 * Popover for hover transform effects
 */

import React, { useState } from 'react';
import { cn } from '@/lib/utils';
import { ChevronDown, Move, RotateCw, Maximize2, Minimize2, MoveUp, MoveDown } from 'lucide-react';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Button } from '@/components/ui/button';

interface TransformOption {
  value: string;
  label: string;
  icon: React.ReactNode;
  description: string;
}

interface TransformPopoverProps {
  value?: string;
  onValueChange?: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
}

const TRANSFORM_OPTIONS: TransformOption[] = [
  { 
    value: 'none', 
    label: 'None', 
    icon: <div className="w-4 h-4 border border-gray-300 rounded" />,
    description: 'No effect'
  },
  { 
    value: 'hover:scale-105', 
    label: 'Scale Up', 
    icon: <Maximize2 className="h-4 w-4" />,
    description: 'Grow on hover'
  },
  { 
    value: 'hover:scale-95', 
    label: 'Scale Down', 
    icon: <Minimize2 className="h-4 w-4" />,
    description: 'Shrink on hover'
  },
  { 
    value: 'hover:-translate-y-1', 
    label: 'Move Up', 
    icon: <MoveUp className="h-4 w-4" />,
    description: 'Lift on hover'
  },
  { 
    value: 'hover:translate-y-1', 
    label: 'Move Down', 
    icon: <MoveDown className="h-4 w-4" />,
    description: 'Drop on hover'
  },
  { 
    value: 'hover:rotate-1', 
    label: 'Rotate', 
    icon: <RotateCw className="h-4 w-4" />,
    description: 'Slight rotation'
  }
];

export const TransformPopover: React.FC<TransformPopoverProps> = ({
  value = 'none',
  onValueChange,
  placeholder = 'Choose effect',
  disabled = false
}) => {
  const [open, setOpen] = useState(false);
  
  const currentOption = TRANSFORM_OPTIONS.find(opt => opt.value === value) || TRANSFORM_OPTIONS[0];

  const handleValueSelect = (newValue: string) => {
    onValueChange?.(newValue);
    setOpen(false);
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            "h-7 justify-start gap-2 text-xs",
            disabled && "opacity-50 cursor-not-allowed"
          )}
          disabled={disabled}
        >
          <div className="flex items-center gap-2 flex-1">
            {currentOption.icon}
            <span>{currentOption.label}</span>
          </div>
          <ChevronDown className="ml-auto h-3 w-3 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-64 p-2" align="start">
        <div className="space-y-1">
          {TRANSFORM_OPTIONS.map((option) => (
            <button
              key={option.value}
              onClick={() => handleValueSelect(option.value)}
              className={cn(
                "flex items-center gap-3 w-full p-2 text-xs rounded hover:bg-gray-100 transition-colors text-left",
                value === option.value && "bg-blue-100 text-blue-900"
              )}
            >
              <div className="flex-shrink-0 w-5 h-5 flex items-center justify-center">
                {option.icon}
              </div>
              <div className="flex-1">
                <div className="font-medium">{option.label}</div>
                <div className="text-gray-500 text-xs">{option.description}</div>
              </div>
            </button>
          ))}
        </div>
      </PopoverContent>
    </Popover>
  );
};