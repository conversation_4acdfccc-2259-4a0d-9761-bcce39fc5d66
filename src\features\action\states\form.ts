import { FormConfig } from "@/components/form/registry";

// Form config cho tạo mới và chỉnh sửa (chỉ code + name)
export const ActionCreateFormConfig: FormConfig = {
  code: "action-create-form",
  name: "Thông tin quyền",
  note: "Dùng để nhập thông tin quyền",
  config: {
    id: "root",
    type: "group",
    label: "",
    style: {
      frame: "",
      label: "",
      content: "",
      error: "",
    },
    children: [
      {
        id: "code-field",
        type: "field",
        fieldConfig: {
          id: "code",
          label: "Mã quyền",
          data_type: "text",
          input_type: "TextInput",
          placeholder: "<PERSON>hập mã quyền (vd: view_users, edit_posts)",
          default_value: "",
          validation: {
            required: true,
            maxLength: 100,
            pattern: "^[a-zA-Z0-9_-]+$",
          },
        },
        children: [],
      },
      {
        id: "name-field",
        type: "field",
        fieldConfig: {
          id: "name",
          label: "Tên quyền",
          data_type: "text",
          input_type: "TextInput",
          placeholder: "Nhập tên quyền",
          default_value: "",
          validation: {
            required: true,
            maxLength: 200,
          },
        },
        children: [],
      },
    ],
  },
};

// Form config cho xem chi tiết (đầy đủ thông tin)
export const ActionDetailFormConfig: FormConfig = {
  code: "action-detail-form",
  name: "Chi tiết quyền",
  note: "Hiển thị chi tiết thông tin quyền",
  config: {
    id: "root",
    type: "group",
    label: "",
    style: {
      frame: "",
      label: "",
      content: "",
      error: "",
    },
    children: [
      {
        id: "id-field",
        type: "field",
        fieldConfig: {
          id: "id",
          label: "ID",
          data_type: "number",
          input_type: "NumberInput",
          disabled: true,
        },
        children: [],
      },
      {
        id: "code-field",
        type: "field",
        fieldConfig: {
          id: "code",
          label: "Mã quyền",
          data_type: "text",
          input_type: "TextInput",
          disabled: true,
        },
        children: [],
      },
      {
        id: "name-field",
        type: "field",
        fieldConfig: {
          id: "name",
          label: "Tên quyền",
          data_type: "text",
          input_type: "TextInput",
          disabled: true,
        },
        children: [],
      },
      {
        id: "createdAt-field",
        type: "field",
        fieldConfig: {
          id: "createdAt",
          label: "Ngày tạo",
          data_type: "number",
          input_type: "NumberInput",
          disabled: true,
        },
        children: [],
      },
      {
        id: "updatedAt-field",
        type: "field",
        fieldConfig: {
          id: "updatedAt",
          label: "Ngày cập nhật",
          data_type: "number",
          input_type: "NumberInput",
          disabled: true,
        },
        children: [],
      },
    ],
  },
};

// Backward compatibility
export const ActionFormConfig = ActionCreateFormConfig;
