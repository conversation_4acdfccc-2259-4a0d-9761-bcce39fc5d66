import React from "react";

// ============================================================================
// Types
// ============================================================================

interface ImageSectionProps {
  /** Optional custom className */
  className?: string;
}

// ============================================================================
// Main Image Section Component
// ============================================================================

/**
 * Image Section Component
 * 
 * Displays a featured image of Khánh Hòa province
 * Shows the coastal city view with proper aspect ratio
 * 
 * @param props - Component props
 * @returns JSX element for image section
 */
export const ImageSection: React.FC<ImageSectionProps> = ({
  className = ""
}) => {
  return (
    <div className={`w-full ${className}`}>
      <div className="relative">
        <img
          src="https://api.hocnhe.com/storage/v1/public/view/2025-06-04/original/d0b5850f77c9238dad2b6b08c81858df0f324f56-1749010250125.jpg"
          alt="Khánh Hòa - Thành phố biển xinh đẹp"
          className="w-full h-auto rounded-lg shadow-lg object-cover"
          style={{ maxHeight: '400px' }}
          loading="lazy"
        />
        
        {/* Optional overlay with caption */}
        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/50 to-transparent rounded-b-lg">
          <div className="p-4">
            <p className="text-white text-sm font-medium">
              Khánh Hòa - Thành phố biển xinh đẹp
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ImageSection;
