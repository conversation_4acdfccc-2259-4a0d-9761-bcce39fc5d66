import React, { useEffect } from "react";
import { useAppDispatch, useAppSelector } from "@/store/rootReducer";
import SectionTitle from "@/features/post/components/SectionTitle";
import { Breadcrumb } from "@/features/layout/components/breadcrumb/Breadcrumb";

import ContactInfo from "../components/ContactInfo";
import HotlineInfo from "../components/HotlineInfo";
import OrganizationHighlights from "../components/OrganizationHighlights";
import ImageSection from "../components/ImageSection";
import FeedbackSection from "../components/FeedbackSection";
import { fetchOrganizationDataAsync } from "../states/slices";
import {
  selectContactData,
  selectContactLoading,
  selectContactError,
  selectHotlineData,
  selectHotlineLoading,
  selectHotlineError,
  selectNeedsRefresh,
} from "../states/selectors";
import type { OrganizationStructurePageProps } from "../states/type";

/**
 * Organization Structure Page Component
 *
 * Displays comprehensive organization information including contact details,
 * hotline information, organizational highlights, and feedback sections.
 * Implements automatic data fetching with optimized Redux state management.
 *
 * @component
 * @example
 * ```tsx
 * <OrganizationStructurePage className="custom-class" />
 * ```
 *
 * Features:
 * - Organization highlights with numbered sections
 * - Contact information with responsive image layout
 * - Emergency hotline information
 * - Feedback and additional contact methods
 * - Automatic data fetching on component mount
 * - Comprehensive error handling and loading states
 * - Fully responsive design with mobile-first approach
 * - Performance optimized with memoized selectors
 *
 * @param props - Component props
 * @param props.className - Optional CSS class name for custom styling
 * @returns JSX element representing the organization structure page
 */
const OrganizationStructurePage: React.FC<OrganizationStructurePageProps> = ({
  className = "",
}) => {
  // ========================================================================
  // Redux State Management
  // ========================================================================

  const dispatch = useAppDispatch();

  // Contact state selectors - memoized for performance
  const contactData = useAppSelector(selectContactData);
  const contactLoading = useAppSelector(selectContactLoading);
  const contactError = useAppSelector(selectContactError);

  // Hotline state selectors - memoized for performance
  const hotlineData = useAppSelector(selectHotlineData);
  const hotlineLoading = useAppSelector(selectHotlineLoading);
  const hotlineError = useAppSelector(selectHotlineError);

  // Data freshness selector - determines if data needs refresh
  const needsRefresh = useAppSelector(selectNeedsRefresh);

  // ========================================================================
  // Side Effects
  // ========================================================================

  /**
   * Automatically fetch organization data on component mount or when data becomes stale.
   * Uses optimized dependency array to prevent unnecessary re-fetches.
   *
   * Triggers fetch when:
   * - Component mounts and no data exists
   * - Data becomes stale (older than 5 minutes)
   * - Previous fetch failed and retry is needed
   */
  useEffect(() => {
    if (!contactData || !hotlineData || needsRefresh) {
      dispatch(fetchOrganizationDataAsync());
    }
  }, [dispatch, contactData, hotlineData, needsRefresh]);

  // ========================================================================
  // Render
  // ========================================================================

  return (
    <div className={`bg-white min-h-screen ${className}`}>
      <div className="container mx-auto px-4 py-8 max-w-6xl">
        {/* Navigation Breadcrumb */}
        <Breadcrumb
          items={[
            { label: "Giới thiệu", path: "/gioi-thieu/" },
            { label: "Cơ cấu tổ chức" },
          ]}
        />

        {/* Page Header */}
        <header className="mb-8">
          <SectionTitle
            title="CƠ CẤU TỔ CHỨC"
            className="py-4"
          />
        </header>

        {/* Main Content - Optimized Vertical Layout */}
        <main className="flex flex-col space-y-12 py-6">
          {/* Organization Highlights Section */}
          <section aria-labelledby="highlights-heading">
            <OrganizationHighlights />
          </section>

          {/* Contact Information and Image Section - Responsive Grid */}
          <section aria-labelledby="contact-heading" className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-start">
            {/* Contact Information */}
            <div>
              <ContactInfo
                data={contactData}
                loading={contactLoading}
                error={contactError}
              />
            </div>

            {/* Featured Image */}
            <div>
              <ImageSection />
            </div>
          </section>

          {/* Emergency Hotline Information Section */}
          <section aria-labelledby="hotline-heading">
            <HotlineInfo
              data={hotlineData}
              loading={hotlineLoading}
              error={hotlineError}
            />
          </section>

          {/* Feedback and Additional Contact Section */}
          <section aria-labelledby="feedback-heading">
            <FeedbackSection />
          </section>
        </main>
      </div>
    </div>
  );
};

export default OrganizationStructurePage;
