import React from "react";
import { PageContentLoading } from "./PageContentLoading";

/**
 * 🔄 Standard Loading Components
 *
 * ✅ Consolidated loading system
 * ✅ Consistent UX across all pages
 * ✅ Modern UI with professional styling
 */

// Full screen loading for app-level loading (routes, auth, etc.)
export const LoadingPage: React.FC<{ message?: string }> = ({
  message = "Đang tải ứng dụng...",
}) => {
  return (
    <div className="min-h-screen w-full">
      <PageContentLoading message={message} fullHeight={true} />
    </div>
  );
};

// Content area loading for page-level loading
export const PageLoading: React.FC<{ message?: string }> = ({
  message = "Đang tải...",
}) => {
  return <PageContentLoading message={message} fullHeight={false} />;
};

// Export the core component for custom usage
export { PageContentLoading };
