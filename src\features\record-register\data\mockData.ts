export interface RecordRegister {
  id: number;
  requestCode: string;
  requestContent: string;
  organizationName: string;
  storageOffice: string;
  totalDocuments: number;
  submissionCount: number;
  sentDate: string;
  status: "new" | "pending" | "approved" | "rejected";
  year: number;
  createdAt: string;
  updatedAt: string;
}

export interface RecordRegisterFilters {
  keyword: string;
  status: string;
  year: string;
}

export interface RecordRegisterData {
  records: RecordRegister[];
  totalCount: number;
  filters: RecordRegisterFilters;
}

// Status mapping for display
export const statusLabels: Record<RecordRegister["status"], string> = {
  new: "Tạo mới",
  pending: "Chờ duyệt",
  approved: "Đã duyệt",
  rejected: "Từ chối",
};

export const statusColors: Record<RecordRegister["status"], string> = {
  new: "bg-blue-100 text-blue-800",
  pending: "bg-yellow-100 text-yellow-800",
  approved: "bg-green-100 text-green-800",
  rejected: "bg-red-100 text-red-800",
};

// Mock data generator
export const generateMockRecordRegisters = (
  count: number = 100
): RecordRegister[] => {
  const organizations = [
    "H32.03_Sở Công Thương",
    "H32.04_Sở Tài chính",
    "H32.05_Sở Xây dựng",
    "H32.06_Sở Y tế",
    "H32.07_Sở Giáo dục",
    "H32.08_Sở Nông nghiệp",
    "H32.09_Sở Giao thông",
    "H32.10_Sở Văn hóa",
  ];

  const storageOffices = [
    "Sở Công Thương-Tỉnh Khánh Hòa",
    "Sở Tài chính-Tỉnh Khánh Hòa",
    "Sở Xây dựng-Tỉnh Khánh Hòa",
    "Sở Y tế-Tỉnh Khánh Hòa",
    "Sở Giáo dục-Tỉnh Khánh Hòa",
  ];

  const requestContents = [
    "Nộp lưu hồ sơ tài liệu theo kế hoạch 01",
    "Nộp lưu hồ sơ tài liệu theo kế hoạch 02",
    "Nộp lưu hồ sơ tài liệu theo kế hoạch 03",
    "Nộp lưu hồ sơ tài liệu định kỳ",
    "Nộp lưu hồ sơ tài liệu bổ sung",
    "Nộp lưu hồ sơ tài liệu đặc biệt",
    "Nộp lưu hồ sơ tài liệu khẩn cấp",
  ];

  const statuses: RecordRegister["status"][] = [
    "new",
    "pending",
    "approved",
    "rejected",
  ];
  const years = [2023, 2024, 2025];

  const records: RecordRegister[] = [];

  for (let i = 1; i <= count; i++) {
    const year = years[Math.floor(Math.random() * years.length)];
    const status = statuses[Math.floor(Math.random() * statuses.length)];
    const orgIndex = Math.floor(Math.random() * organizations.length);

    // Generate date within the year
    const startDate = new Date(year, 0, 1);
    const endDate = new Date(year, 11, 31);
    const randomDate = new Date(
      startDate.getTime() +
        Math.random() * (endDate.getTime() - startDate.getTime())
    );

    records.push({
      id: i,
      requestCode: `NLLS${String(i).padStart(2, "0")}`,
      requestContent:
        requestContents[Math.floor(Math.random() * requestContents.length)],
      organizationName: organizations[orgIndex],
      storageOffice:
        storageOffices[Math.floor(Math.random() * storageOffices.length)],
      totalDocuments: Math.floor(Math.random() * 50) + 1,
      submissionCount: Math.floor(Math.random() * 5) + 1,
      sentDate: randomDate.toLocaleDateString("vi-VN"),
      status,
      year,
      createdAt: randomDate.toISOString(),
      updatedAt: randomDate.toISOString(),
    });
  }

  // Sort by ID descending (newest first)
  return records.sort((a, b) => b.id - a.id);
};

export const mockRecordRegisters = generateMockRecordRegisters(100);

// Filter functions
export const filterRecordRegisters = (
  records: RecordRegister[],
  filters: RecordRegisterFilters
): RecordRegister[] => {
  return records.filter((record) => {
    // Keyword filter
    if (filters.keyword) {
      const keyword = filters.keyword.toLowerCase();
      const matchesKeyword =
        record.requestCode.toLowerCase().includes(keyword) ||
        record.requestContent.toLowerCase().includes(keyword) ||
        record.organizationName.toLowerCase().includes(keyword) ||
        record.storageOffice.toLowerCase().includes(keyword);

      if (!matchesKeyword) return false;
    }

    // Status filter
    if (filters.status && filters.status !== "" && filters.status !== "all") {
      if (record.status !== filters.status) return false;
    }

    // Year filter
    if (filters.year && filters.year !== "" && filters.year !== "all") {
      if (record.year !== parseInt(filters.year)) return false;
    }

    return true;
  });
};

// Pagination helper
export const paginateRecords = (
  records: RecordRegister[],
  page: number,
  size: number
) => {
  const start = page * size;
  const end = start + size;
  return {
    data: records.slice(start, end),
    pagination: {
      page,
      size,
      totalElements: records.length,
      totalPages: Math.ceil(records.length / size),
    },
  };
};
