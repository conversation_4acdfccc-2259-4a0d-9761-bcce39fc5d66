/**
 * TitleNode - Renders title/heading nodes
 */

import React from "react";
import { FormNode } from "../types";
import { cn } from "@/lib/utils";

interface TitleNodeProps {
  node: FormNode;
}

export const TitleNode: React.FC<TitleNodeProps> = ({ node }) => {
  const { id, styles, properties } = node;
  const containerClass = cn("mb-4", styles?.container);
  const contentClass = cn("text-2xl font-bold text-primary", styles?.content);
  const text = (properties?.text as string) || "";

  return (
    <div key={id} className={containerClass} data-node-id={id}>
      <div className={contentClass}>{text}</div>
    </div>
  );
};
