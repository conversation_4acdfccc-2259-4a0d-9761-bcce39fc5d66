import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { AlertTriangle, Trash2 } from "lucide-react";

interface DeletePageDialogProps {
  open: boolean;
  loading: boolean;
  pageToDelete: {
    id: number;
    title: string;
  } | null;
  onClose: () => void;
  onConfirm: () => void;
}

export const DeletePageDialog: React.FC<DeletePageDialogProps> = ({
  open,
  loading,
  pageToDelete,
  onClose,
  onConfirm,
}) => {
  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-red-500" />
            <PERSON><PERSON><PERSON> trang
          </DialogTitle>
          <DialogDescription className="pt-2">
            Bạn có chắc chắn muốn xóa trang{" "}
            <span className="font-semibold">"{pageToDelete?.title}"</span>?
            <br />
            <span className="text-amber-600 text-sm">
              Trang sẽ được chuyển vào thùng rác và có thể khôi phục lại.
            </span>
          </DialogDescription>
        </DialogHeader>
        <DialogFooter className="gap-2">
          <Button type="button" variant="outline" onClick={onClose}>
            Hủy
          </Button>
          <Button
            type="submit"
            variant="destructive"
            onClick={onConfirm}
            disabled={loading}
          >
            <Trash2 size={16} className="mr-1" />
            {loading ? "Đang xóa..." : "Xóa trang"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
