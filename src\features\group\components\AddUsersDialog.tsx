import React, { useEffect, useState, useCallback, useMemo } from "react";
import { UserData } from "@/features/users/states/type";
import { searchUsers } from "@/features/users/states/api";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { UserSearchList } from "./UserSearchList";
import debounce from "lodash.debounce";

interface AddUsersDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  currentUsers: UserData[];
  onAddUsers: (userIds: number[]) => Promise<void>;
  isLoading: boolean;
}

export const AddUsersDialog: React.FC<AddUsersDialogProps> = ({
  open,
  onOpenChange,
  currentUsers,
  onAddUsers,
  isLoading,
}) => {
  // Dialog states
  const [searchKeyword, setSearchKeyword] = useState("");
  const [searchResults, setSearchResults] = useState<UserData[]>([]);
  const [searchLoading, setSearchLoading] = useState(false);
  const [selectedUserIds, setSelectedUserIds] = useState<number[]>([]);

  // Search users
  const handleSearchUsers = useCallback(
    async (keyword: string) => {
      setSearchLoading(true);
      try {
        const response = await searchUsers({
          keyword: keyword,
          page: 0,
          size: 20,
        });

        if (response.data && response.data.data) {
          // Filter out users already in the group
          const currentUserIds = currentUsers.map((user) => user.id);
          const filteredResults = response.data.data.filter(
            (user: UserData) => !currentUserIds.includes(user.id)
          );
          setSearchResults(filteredResults);
        } else {
          setSearchResults([]);
        }
      } catch (error) {
        console.error("Search failed:", error);
        setSearchResults([]);
      } finally {
        setSearchLoading(false);
      }
    },
    [currentUsers]
  );

  // Debounced search
  const debouncedSearch = useMemo(
    () =>
      debounce((keyword: string) => {
        handleSearchUsers(keyword);
      }, 500),
    [handleSearchUsers]
  );

  // Search when dialog opens or keyword changes
  useEffect(() => {
    if (open) {
      if (searchKeyword.trim()) {
        debouncedSearch(searchKeyword.trim());
      } else {
        handleSearchUsers("");
      }
    }
  }, [open, searchKeyword, debouncedSearch, handleSearchUsers]);

  // Cleanup debounce
  useEffect(() => {
    return () => {
      debouncedSearch.cancel();
    };
  }, [debouncedSearch]);

  // Toggle user selection
  const handleUserToggle = (userId: number) => {
    setSelectedUserIds((prev) =>
      prev.includes(userId)
        ? prev.filter((id) => id !== userId)
        : [...prev, userId]
    );
  };

  // Handle add users
  const handleAddUsers = async () => {
    if (selectedUserIds.length === 0) return;

    try {
      await onAddUsers(selectedUserIds);
      handleClose();
    } catch (error) {
      console.error("Error adding users:", error);
    }
  };

  // Handle close and reset
  const handleClose = () => {
    onOpenChange(false);
    setSelectedUserIds([]);
    setSearchKeyword("");
    setSearchResults([]);
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] flex flex-col bg-white border border-gray-200 shadow-lg">
        <DialogHeader className="bg-white border-b border-gray-100">
          <DialogTitle className="text-gray-900">
            Thêm người dùng vào nhóm
          </DialogTitle>
        </DialogHeader>

        <UserSearchList
          searchKeyword={searchKeyword}
          onSearchKeywordChange={setSearchKeyword}
          searchResults={searchResults}
          searchLoading={searchLoading}
          selectedUserIds={selectedUserIds}
          onUserToggle={handleUserToggle}
        />

        <DialogFooter className="bg-white border-t border-gray-100 mt-4">
          <Button
            variant="outline"
            onClick={handleClose}
            className="bg-white border-gray-200 text-gray-700 hover:bg-gray-50"
          >
            Hủy
          </Button>
          <Button
            onClick={handleAddUsers}
            disabled={selectedUserIds.length === 0 || isLoading}
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            {isLoading ? "Đang thêm..." : "Thêm người dùng"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
