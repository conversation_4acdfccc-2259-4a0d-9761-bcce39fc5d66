/**
 * Form Builder Component Definitions
 * Organized by categories: Layout, Control, Field Basic, Field Template
 */

import { FormNode, FieldValue } from '@/form/types';
import { ComponentDefinition, ComponentCategory } from './types';
import { FIELD_REGISTRY } from '@/form/field/FieldFactory';

/**
 * Helper to create field node
 */
function createFieldNode(
  id: string,
  component: keyof typeof FIELD_REGISTRY,
  objectKey: string,
  label: string,
  dataType: string,
  defaultValue: FieldValue,
  placeholder?: string
): FormNode {
  return {
    id,
    type: "field",
    field: {
      objectKey,
      component,
      dataType,
      defaultValue
    },
    properties: {
      label,
      ...(placeholder && { placeholder })
    }
  };
}

/**
 * Helper to create frame node
 */
function createFrameNode(id: string, title: string): FormNode {
  return {
    id,
    type: "frame",
    children: [],
    properties: {
      title
    },
    styles: {
      container: "space-y-4 p-4 border border-gray-200 rounded-lg"
    }
  };
}

/**
 * Helper to create title node
 */
function createTitleNode(id: string, text: string): FormNode {
  return {
    id,
    type: "title",
    properties: {
      text
    },
    styles: {
      content: "text-xl font-semibold text-gray-900 mb-4"
    }
  };
}

/**
 * Helper to create control node
 */
function createControlNode(
  id: string, 
  text: string, 
  controlType: 'submit' | 'reset' | 'button',
  variant: 'default' | 'outline' | 'destructive' = 'default'
): FormNode {
  return {
    id,
    type: "control",
    properties: {
      text,
      controlType,
      variant
    }
  };
}

// ============================================================================
// LAYOUT COMPONENTS
// ============================================================================

export const LAYOUT_COMPONENTS: ComponentDefinition[] = [
  {
    id: 'frame',
    name: 'Container',
    description: 'Group fields and components together',
    category: 'layout',
    icon: 'Square',
    canHaveChildren: true,
    createNode: (id: string) => createFrameNode(id, 'New Container'),
    canBeChildOf: (parent) => parent.type === 'frame'
  },
  {
    id: 'title',
    name: 'Title/Header',
    description: 'Section title or heading',
    category: 'layout',
    icon: 'Heading',
    canHaveChildren: false,
    createNode: (id: string) => createTitleNode(id, 'Section Title'),
    canBeChildOf: (parent) => parent.type === 'frame'
  }
];

// ============================================================================
// CONTROL COMPONENTS
// ============================================================================

export const CONTROL_COMPONENTS: ComponentDefinition[] = [
  {
    id: 'submit-button',
    name: 'Submit Button',
    description: 'Form submission button',
    category: 'control',
    icon: 'Send',
    canHaveChildren: false,
    createNode: (id: string) => createControlNode(id, 'Submit', 'submit', 'default'),
    canBeChildOf: (parent) => parent.type === 'frame'
  },
  {
    id: 'reset-button',
    name: 'Reset Button',
    description: 'Form reset button',
    category: 'control',
    icon: 'RotateCcw',
    canHaveChildren: false,
    createNode: (id: string) => createControlNode(id, 'Reset', 'reset', 'outline'),
    canBeChildOf: (parent) => parent.type === 'frame'
  },
  {
    id: 'custom-button',
    name: 'Custom Button',
    description: 'Custom action button',
    category: 'control',
    icon: 'MousePointer',
    canHaveChildren: false,
    createNode: (id: string) => createControlNode(id, 'Button', 'button', 'outline'),
    canBeChildOf: (parent) => parent.type === 'frame'
  }
];

// ============================================================================
// FIELD BASIC COMPONENTS
// ============================================================================

export const FIELD_BASIC_COMPONENTS: ComponentDefinition[] = [
  {
    id: 'text-input',
    name: 'Text Input',
    description: 'Single line text input',
    category: 'field-basic',
    icon: 'Type',
    canHaveChildren: false,
    createNode: (id: string) => createFieldNode(
      id, 'TextInput', 'textField', 'Text Field', 'string', '', 'Enter text...'
    ),
    canBeChildOf: (parent) => parent.type === 'frame'
  },
  {
    id: 'number-input',
    name: 'Number Input',
    description: 'Numeric input field',
    category: 'field-basic',
    icon: 'Hash',
    canHaveChildren: false,
    createNode: (id: string) => createFieldNode(
      id, 'NumberInput', 'numberField', 'Number Field', 'number', 0, 'Enter number...'
    ),
    canBeChildOf: (parent) => parent.type === 'frame'
  },
  {
    id: 'password-input',
    name: 'Password Input',
    description: 'Password field with toggle',
    category: 'field-basic',
    icon: 'Lock',
    canHaveChildren: false,
    createNode: (id: string) => createFieldNode(
      id, 'PasswordInput', 'passwordField', 'Password', 'string', '', 'Enter password...'
    ),
    canBeChildOf: (parent) => parent.type === 'frame'
  },
  {
    id: 'textarea-input',
    name: 'Text Area',
    description: 'Multi-line text input',
    category: 'field-basic',
    icon: 'AlignLeft',
    canHaveChildren: false,
    createNode: (id: string) => createFieldNode(
      id, 'TextAreaInput', 'textareaField', 'Text Area', 'string', '', 'Enter text...'
    ),
    canBeChildOf: (parent) => parent.type === 'frame'
  },
  {
    id: 'checkbox-input',
    name: 'Checkbox',
    description: 'Single checkbox input',
    category: 'field-basic',
    icon: 'CheckSquare',
    canHaveChildren: false,
    createNode: (id: string) => createFieldNode(
      id, 'CheckboxInput', 'checkboxField', 'Checkbox Field', 'boolean', false
    ),
    canBeChildOf: (parent) => parent.type === 'frame'
  },
  {
    id: 'toggle-input',
    name: 'Toggle Switch',
    description: 'Toggle switch input',
    category: 'field-basic',
    icon: 'ToggleLeft',
    canHaveChildren: false,
    createNode: (id: string) => createFieldNode(
      id, 'ToggleInput', 'toggleField', 'Toggle Field', 'boolean', false
    ),
    canBeChildOf: (parent) => parent.type === 'frame'
  },
  {
    id: 'dropdown-input',
    name: 'Dropdown',
    description: 'Dropdown selection',
    category: 'field-basic',
    icon: 'ChevronDown',
    canHaveChildren: false,
    createNode: (id: string) => ({
      id,
      type: "field",
      field: {
        objectKey: "dropdownField",
        component: "DropdownInput",
        dataType: "string",
        defaultValue: ""
      },
      properties: {
        label: "Dropdown Field",
        options: ["Option 1", "Option 2", "Option 3"]
      }
    } as FormNode),
    canBeChildOf: (parent) => parent.type === 'frame'
  }
];

// ============================================================================
// FIELD TEMPLATE COMPONENTS
// ============================================================================

export const FIELD_TEMPLATE_COMPONENTS: ComponentDefinition[] = [
  {
    id: 'tab-input',
    name: 'Tab Selection',
    description: 'Tab-based selection input',
    category: 'field-template',
    icon: 'Tabs',
    canHaveChildren: false,
    createNode: (id: string) => ({
      id,
      type: "field",
      field: {
        objectKey: "tabField",
        component: "TabInput",
        dataType: "string",
        defaultValue: ""
      },
      properties: {
        label: "Tab Field",
        options: ["Tab 1", "Tab 2", "Tab 3"]
      }
    } as FormNode),
    canBeChildOf: (parent) => parent.type === 'frame'
  },
  {
    id: 'dropdown-text-search',
    name: 'Searchable Text Dropdown',
    description: 'Dropdown with text search',
    category: 'field-template',
    icon: 'Search',
    canHaveChildren: false,
    createNode: (id: string) => ({
      id,
      type: "field",
      field: {
        objectKey: "searchTextField",
        component: "DropDownTextWithSearch",
        dataType: "string",
        defaultValue: ""
      },
      properties: {
        label: "Searchable Text Field",
        options: ["Option 1", "Option 2", "Option 3"]
      }
    } as FormNode),
    canBeChildOf: (parent) => parent.type === 'frame'
  },
  {
    id: 'dropdown-number-search',
    name: 'Searchable Number Dropdown',
    description: 'Dropdown with number search',
    category: 'field-template',
    icon: 'Search',
    canHaveChildren: false,
    createNode: (id: string) => ({
      id,
      type: "field",
      field: {
        objectKey: "searchNumberField",
        component: "DropDownNumberWithSearch",
        dataType: "number",
        defaultValue: 0
      },
      properties: {
        label: "Searchable Number Field",
        options: [1, 2, 3, 4, 5]
      }
    } as FormNode),
    canBeChildOf: (parent) => parent.type === 'frame'
  },
  {
    id: 'image-picker',
    name: 'Image Picker',
    description: 'Image selection/upload input',
    category: 'field-template',
    icon: 'Image',
    canHaveChildren: false,
    createNode: (id: string) => createFieldNode(
      id, 'ImagePickerInput', 'imageField', 'Image Field', 'string', ''
    ),
    canBeChildOf: (parent) => parent.type === 'frame'
  },
  {
    id: 'category-picker',
    name: 'Category Picker',
    description: 'Category tree selection',
    category: 'field-template',
    icon: 'FolderTree',
    canHaveChildren: false,
    createNode: (id: string) => createFieldNode(
      id, 'PickCategoryInput', 'categoryField', 'Category Field', 'string', ''
    ),
    canBeChildOf: (parent) => parent.type === 'frame'
  },
  {
    id: 'icon-picker',
    name: 'Icon Picker',
    description: 'Icon selection input',
    category: 'field-template',
    icon: 'Star',
    canHaveChildren: false,
    createNode: (id: string) => createFieldNode(
      id, 'PickIconInput', 'iconField', 'Icon Field', 'string', ''
    ),
    canBeChildOf: (parent) => parent.type === 'frame'
  },
  {
    id: 'component-picker',
    name: 'Component Picker',
    description: 'Component selection input',
    category: 'field-template',
    icon: 'Component',
    canHaveChildren: false,
    createNode: (id: string) => createFieldNode(
      id, 'PickComponentInput', 'componentField', 'Component Field', 'string', ''
    ),
    canBeChildOf: (parent) => parent.type === 'frame'
  }
];

// ============================================================================
// COMPONENT REGISTRY
// ============================================================================

export const ALL_COMPONENTS: ComponentDefinition[] = [
  ...LAYOUT_COMPONENTS,
  ...CONTROL_COMPONENTS,
  ...FIELD_BASIC_COMPONENTS,
  ...FIELD_TEMPLATE_COMPONENTS
];

export const COMPONENTS_BY_CATEGORY: Record<ComponentCategory, ComponentDefinition[]> = {
  'layout': LAYOUT_COMPONENTS,
  'control': CONTROL_COMPONENTS,
  'field-basic': FIELD_BASIC_COMPONENTS,
  'field-template': FIELD_TEMPLATE_COMPONENTS
};

/**
 * Get component definition by ID
 */
export function getComponentDefinition(id: string): ComponentDefinition | null {
  return ALL_COMPONENTS.find(comp => comp.id === id) || null;
}

/**
 * Get components by category
 */
export function getComponentsByCategory(category: ComponentCategory): ComponentDefinition[] {
  return COMPONENTS_BY_CATEGORY[category] || [];
}

/**
 * Check if component can be child of parent
 */
export function canBeChildOfComponent(
  componentId: string, 
  parentNode: FormNode
): boolean {
  const component = getComponentDefinition(componentId);
  if (!component || !component.canBeChildOf) return false;
  
  return component.canBeChildOf(parentNode);
}