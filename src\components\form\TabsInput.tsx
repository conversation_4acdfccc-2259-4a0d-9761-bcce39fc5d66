import React from "react";
import { BasicInputProps } from "./registry"; // Đảm bảo import đúng

type TabInputProps = BasicInputProps<"text"> & {
  options?: string[];
};

export const TabInput = React.forwardRef<HTMLDivElement, TabInputProps>(
  ({ isViewMode, value, onChange, options, disabled, name, ...rest }, ref) => {
    return (
      <div className="flex gap-2" ref={ref} {...rest}>
        {options?.map((tab) => (
          <button
            key={tab}
            type="button"
            name={name}
            disabled={disabled}
            onClick={() => !disabled && !isViewMode && onChange(tab)}
            className={`px-4 py-2 rounded border transition
              ${
                value === tab
                  ? "bg-blue-600 text-white border-blue-600"
                  : "bg-white border-gray-300 text-gray-700"
              }
              ${disabled ? "opacity-50 cursor-not-allowed" : "hover:bg-blue-50"}
            `}
          >
            {tab}
          </button>
        ))}
      </div>
    );
  }
);

TabInput.displayName = "TabInput";
