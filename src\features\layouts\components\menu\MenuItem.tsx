import type { CategoryTree } from "@/features/category/states/types";
import { DynamicIcon } from "@/components/other/DynamicIcon";
import { cn } from "@/lib/utils";
import { ChevronDown, ChevronRight } from "lucide-react";

interface MenuItemProps {
  item: CategoryTree;
  onClick: (id: number) => void;
  isActive?: boolean;
  isOpen?: boolean;
}

export const MenuItem: React.FC<MenuItemProps> = ({
  item,
  onClick,
  isActive = false,
  isOpen = false,
}) => {
  return (
    <div
      onClick={() => onClick(item.id)}
      className={cn(
        "w-full flex items-center gap-2 px-3 py-2 text-sm rounded-md transition-colors cursor-pointer",
        isActive ? "bg-primary text-white" : "hover:bg-gray-200"
      )}
    >
      <DynamicIcon icon={item.description.icon} className="h-4 w-4" />
      <span className="flex-grow truncate">{item.name}</span>
      {item.children.length > 0 &&
        (isOpen ? (
          <ChevronDown className="h-4 w-4" />
        ) : (
          <ChevronRight className="h-4 w-4" />
        ))}
    </div>
  );
};
