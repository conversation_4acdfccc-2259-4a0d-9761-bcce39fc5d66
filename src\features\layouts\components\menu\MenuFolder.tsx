import { useEffect, useState } from "react";
import { MenuItem } from "./MenuItem";
import { CategoryTree } from "@/features/category/states/types";

interface MenuFolderProps {
  item: CategoryTree;
  allItems: CategoryTree[];
  selectedId: number | null;
  level?: number;
  onClick: (id: number) => void;
}

function flattenTree(nodes: CategoryTree[]): Map<number, CategoryTree> {
  const map = new Map<number, CategoryTree>();
  const stack = [...nodes];
  while (stack.length > 0) {
    const node = stack.pop()!;
    map.set(node.id, node);
    if (node.children && node.children.length > 0) {
      stack.push(...node.children);
    }
  }
  return map;
}

const isMenuOpenBFS = (
  menuId: number,
  selectedId: number | null,
  menus: CategoryTree[]
): boolean => {
  if (menuId === selectedId) return true;
  // Flatten toàn bộ tree
  const map = flattenTree(menus);
  const queue = [menuId];
  while (queue.length > 0) {
    const current = queue.shift()!;
    const node = map.get(current);
    if (!node) continue;
    for (const child of node.children) {
      if (child.id === selectedId) return true;
      queue.push(child.id);
    }
  }
  return false;
};

export const MenuFolder: React.FC<MenuFolderProps> = ({
  item,
  allItems,
  selectedId,
  level = 0,
  onClick,
}) => {
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    setIsOpen(isMenuOpenBFS(item.id, selectedId, allItems));
  }, [item.id, selectedId, allItems]);

  const handleClick = () => {
    onClick(item.id);
    setIsOpen((o) => !o);
  };

  return (
    <div className="flex flex-col">
      <div
        className="flex items-center cursor-pointer"
        style={{ paddingLeft: `${level * 16}px` }}
      >
        <MenuItem
          item={item}
          onClick={handleClick}
          isActive={item.id === selectedId}
          isOpen={isOpen}
        />
      </div>
      <div
        className={`flex flex-col pl-4 transition-all ${
          isOpen ? "max-h-screen" : "max-h-0 overflow-hidden"
        }`}
      >
        {item.children.map((child) =>
          child.children.length > 0 ? (
            <MenuFolder
              key={child.id}
              item={child}
              allItems={allItems}
              selectedId={selectedId}
              level={level + 1}
              onClick={onClick}
            />
          ) : (
            <div key={child.id} className="pl-4">
              <MenuItem
                item={child}
                onClick={onClick}
                isActive={child.id === selectedId}
                isOpen={isOpen}
              />
            </div>
          )
        )}
      </div>
    </div>
  );
};
