import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTit<PERSON> } from "@/components/ui/card";
import { ImageLoader } from "@/components/image/ImageLoader";
import { Play, Image as ImageIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { LibraryItem } from "../data/mockData";

/**
 * Props interface for LibraryPanel component
 */
export interface LibraryPanelProps {
  /** Type of library content */
  type: "image" | "video";
  /** Array of library items to display */
  items: LibraryItem[];
  /** Panel title */
  title: string;
  /** Additional CSS classes */
  className?: string;
  /** Click handler for individual items */
  onItemClick?: (item: LibraryItem) => void;
}

/**
 * LibraryPanel component displays a grid of library items (images or videos)
 * 
 * @param props - Component props
 * @returns JSX element for library panel
 */
export const LibraryPanel: React.FC<LibraryPanelProps> = ({
  type,
  items,
  title,
  className,
  onItemClick,
}) => {
  // Filter items by type
  const filteredItems = items.filter(item => item.type === type);

  return (
    <Card className={cn("bg-transparent border-gray-200", className)}>
      <CardHeader className="pb-4">
        <CardTitle className="text-lg font-semibold text-gray-900 flex items-center gap-2">
          {type === "image" ? (
            <ImageIcon className="w-5 h-5 text-blue-600" />
          ) : (
            <Play className="w-5 h-5 text-red-600" />
          )}
          {title}
        </CardTitle>
      </CardHeader>
      
      <CardContent className="pt-0">
        <div className="grid grid-cols-2 gap-4">
          {filteredItems.slice(0, 4).map((item) => (
            <div
              key={item.id}
              className="relative group cursor-pointer"
              onClick={() => onItemClick?.(item)}
            >
              <div className="aspect-video rounded-lg overflow-hidden bg-gray-100">
                <ImageLoader
                  src={item.thumbnail}
                  alt={item.title}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  fallbackText={item.title}
                />
                
                {/* Video Play Overlay */}
                {type === "video" && (
                  <div className="absolute inset-0 flex items-center justify-center bg-black/20 group-hover:bg-black/30 transition-colors">
                    <div className="w-12 h-12 bg-white/90 rounded-full flex items-center justify-center">
                      <Play className="w-6 h-6 text-gray-800 ml-1" />
                    </div>
                  </div>
                )}
              </div>
              
              {/* Item Title */}
              <p className="mt-2 text-sm font-medium text-gray-700 line-clamp-2">
                {item.title}
              </p>
            </div>
          ))}
        </div>
        
        {/* Show more indicator if there are more items */}
        {filteredItems.length > 4 && (
          <div className="mt-4 text-center">
            <span className="text-sm text-gray-500">
              +{filteredItems.length - 4} mục khác
            </span>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
