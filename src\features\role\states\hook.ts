import { useNavigate } from "react-router-dom";
import { RolePageMode, RoleAction } from "./slices";

export const useSwitchMode = () => {
  const navigate = useNavigate();

  const switchMode = (
    mode: RolePageMode,
    roleId?: string,
    actionType?: RoleAction
  ) => {
    const params = new URLSearchParams();
    params.set("mode", mode);
    if (roleId) {
      params.set("role", roleId);
    }
    if (actionType) {
      params.set("actionType", actionType);
    }
    navigate(`?${params.toString()}`);
  };

  return switchMode;
};
