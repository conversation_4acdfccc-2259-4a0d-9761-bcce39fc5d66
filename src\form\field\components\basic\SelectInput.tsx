/**
 * SelectInput - Select dropdown component
 */

import React from "react";
import { FieldComponentProps } from "../../FieldFactory";

interface SelectInputProps extends FieldComponentProps {
  options?: string[];
  labels?: string[];
}

export const SelectInput: React.FC<SelectInputProps> = ({
  value,
  onChange,
  onBlur,
  disabled = false,
  placeholder = "",
  id,
  className = "",
  options = [],
  labels = [],
}) => {
  const displayLabels = labels.length === options.length ? labels : options;

  return (
    <select
      id={id}
      value={value as string}
      onChange={(e) => onChange(e.target.value)}
      onBlur={onBlur}
      disabled={disabled}
      className={`flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 ${className}`}
    >
      {placeholder && (
        <option value="" disabled>
          {placeholder}
        </option>
      )}
      {options.map((option, index) => (
        <option key={option} value={option}>
          {displayLabels[index]}
        </option>
      ))}
    </select>
  );
};
