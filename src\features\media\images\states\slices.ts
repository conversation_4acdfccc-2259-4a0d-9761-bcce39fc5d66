import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import type { AlbumQueryParams, Album, Media, MediaQueryParams } from "./types";

export type AlbumPageMode = "list" | "detail";

export interface AlbumsFilterState {
  params: AlbumQueryParams;
  loading: boolean;
  error: string | null;
  refetch: boolean;
  currentAlbum: Album | null; // Current album for detail view
  pageMode: AlbumPageMode; // View mode

  // Media related state
  currentAlbumMedia: Media[];
  mediaLoading: boolean;
  mediaError: string | null;
  mediaParams: MediaQueryParams;
  mediaHasMore: boolean; // Whether there are more media to load
  mediaCurrentPage: number; // Current page for load more
}

export const initialState: AlbumsFilterState = {
  params: {
    type: "image",
    status: "DRAFT",
    page: 0,
    size: 20,
    search: "",
  },
  loading: false,
  error: null,
  refetch: false,
  currentAlbum: null,
  pageMode: "list",

  // Media related state
  currentAlbumMedia: [],
  mediaLoading: false,
  mediaError: null,
  mediaParams: {
    page: 0,
    size: 20,
    search: "",
  },
  mediaHasMore: false,
  mediaCurrentPage: 0,
};

const albumsFilterSlice = createSlice({
  name: "albumsFilter",
  initialState,
  reducers: {
    setFilters(state, action: PayloadAction<Partial<AlbumQueryParams>>) {
      state.params = { ...state.params, ...action.payload };
    },
    setLoading(state, action: PayloadAction<boolean>) {
      state.loading = action.payload;
    },
    setError(state, action: PayloadAction<string | null>) {
      state.error = action.payload;
    },
    triggerRefetch(state) {
      state.refetch = !state.refetch;
    },
    setCurrentAlbum(state, action: PayloadAction<Album | null>) {
      state.currentAlbum = action.payload;
    },
    setPathParams(
      state,
      action: PayloadAction<{
        mode?: AlbumPageMode;
      }>
    ) {
      const { mode } = action.payload;
      if (mode) state.pageMode = mode;
    },
    resetFilters(state) {
      state.params = initialState.params;
      state.loading = false;
      state.error = null;
      state.refetch = false;
      state.currentAlbum = null;
      state.pageMode = "list";
    },

    // Media related actions
    setCurrentAlbumMedia(state, action: PayloadAction<Media[]>) {
      state.currentAlbumMedia = action.payload;
    },
    appendAlbumMedia(state, action: PayloadAction<Media[]>) {
      state.currentAlbumMedia = [...state.currentAlbumMedia, ...action.payload];
    },
    setMediaLoading(state, action: PayloadAction<boolean>) {
      state.mediaLoading = action.payload;
    },
    setMediaError(state, action: PayloadAction<string | null>) {
      state.mediaError = action.payload;
    },
    setMediaParams(state, action: PayloadAction<Partial<MediaQueryParams>>) {
      state.mediaParams = { ...state.mediaParams, ...action.payload };
    },
    setMediaHasMore(state, action: PayloadAction<boolean>) {
      state.mediaHasMore = action.payload;
    },
    setMediaCurrentPage(state, action: PayloadAction<number>) {
      state.mediaCurrentPage = action.payload;
    },
    clearMediaData(state) {
      state.currentAlbumMedia = [];
      state.mediaLoading = false;
      state.mediaError = null;
      state.mediaHasMore = false;
      state.mediaCurrentPage = 0;
    },
  },
});

export const {
  setFilters,
  setLoading,
  setError,
  triggerRefetch,
  setCurrentAlbum,
  setPathParams,
  resetFilters,
  setCurrentAlbumMedia,
  appendAlbumMedia,
  setMediaLoading,
  setMediaError,
  setMediaParams,
  setMediaHasMore,
  setMediaCurrentPage,
  clearMediaData,
} = albumsFilterSlice.actions;

export default albumsFilterSlice.reducer;
