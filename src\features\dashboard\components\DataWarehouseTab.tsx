import {
  FileText,
  Clock,
  CheckCircle,
  XCircle,
  HardDrive,
  <PERSON><PERSON><PERSON>Up,
  <PERSON><PERSON>hart3,
  <PERSON><PERSON><PERSON>,
} from "lucide-react";
import { RecordStatsCard } from "./RecordStatsCard";
import { SimpleChart } from "./SimpleChart";
import { AlertsPanel } from "./AlertsPanel";
import { SimpleCalendar } from "./SimpleCalendar";
import { DataWarehouseData } from "../data/mockData";

interface DataWarehouseTabProps {
  data: DataWarehouseData;
}

export const DataWarehouseTab = ({ data }: DataWarehouseTabProps) => {
  const {
    recordStats,
    processingTrend,
    recordStatus,
    recordTypes,
    alerts,
    calendarEvents,
  } = data;

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900">Kho dữ liệu</h2>
        <p className="text-gray-600 mt-1">
          Thống kê và quản lý hồ sơ, dữ liệu hệ thống
        </p>
      </div>

      {/* Top Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <RecordStatsCard
          title="Hồ sơ đăng ký mới"
          mainValue={recordStats.newRecordsToday}
          subValue={`${recordStats.newRecordsThisWeek} tuần này, ${recordStats.newRecordsThisMonth} tháng này`}
          icon={FileText}
          description="Số lượng hồ sơ mới hôm nay"
          trend={{ value: 15.2, isPositive: true, label: "so với hôm qua" }}
        />

        <RecordStatsCard
          title="Hồ sơ đang xử lý"
          mainValue={recordStats.processingRecords}
          subValue={`${recordStats.averageProcessingTime}h trung bình`}
          icon={Clock}
          description="Đang được xử lý"
          badge={{ text: "Cao", variant: "warning" }}
        />

        <RecordStatsCard
          title="Hồ sơ đã xử lý"
          mainValue={recordStats.completedRecordsWeek}
          subValue={`${recordStats.completedRecordsMonth} trong tháng`}
          icon={CheckCircle}
          description="Hoàn thành tuần này"
          trend={{ value: 8.5, isPositive: true, label: "tuần trước" }}
        />

        <RecordStatsCard
          title="Hồ sơ bị từ chối"
          mainValue={recordStats.rejectedRecords}
          subValue={`${recordStats.rejectedPercentage}% tổng hồ sơ`}
          icon={XCircle}
          description="Tổng số bị từ chối"
          badge={{ text: "Thấp", variant: "success" }}
        />

        <RecordStatsCard
          title="Dung lượng kho dữ liệu"
          mainValue={`${recordStats.storageUsed}GB`}
          subValue={`${recordStats.storagePercentage}% / ${recordStats.storageTotal}GB`}
          icon={HardDrive}
          description="Đang sử dụng"
          badge={{
            text:
              recordStats.storagePercentage > 80 ? "Gần đầy" : "Bình thường",
            variant: recordStats.storagePercentage > 80 ? "error" : "info",
          }}
        />
      </div>

      {/* Visualization Graphs */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Processing Trend Line Chart */}
        <div className="lg:col-span-1">
          <div className="mb-2 flex items-center space-x-2">
            <TrendingUp className="h-4 w-4 text-blue-600" />
            <h3 className="text-sm font-medium text-gray-900">
              Xu hướng xử lý hồ sơ
            </h3>
          </div>
          <SimpleChart
            title="Thời gian xử lý theo ngày"
            data={processingTrend}
            type="bar"
          />
        </div>

        {/* Record Status Bar Chart */}
        <div className="lg:col-span-1">
          <div className="mb-2 flex items-center space-x-2">
            <BarChart3 className="h-4 w-4 text-green-600" />
            <h3 className="text-sm font-medium text-gray-900">
              Tình trạng hồ sơ
            </h3>
          </div>
          <SimpleChart
            title="Phân bố theo trạng thái"
            data={recordStatus}
            type="pie"
          />
        </div>

        {/* Record Types Pie Chart */}
        <div className="lg:col-span-1">
          <div className="mb-2 flex items-center space-x-2">
            <PieChart className="h-4 w-4 text-purple-600" />
            <h3 className="text-sm font-medium text-gray-900">
              Loại hình hồ sơ
            </h3>
          </div>
          <SimpleChart
            title="Phân loại theo loại hình"
            data={recordTypes}
            type="pie"
          />
        </div>
      </div>

      {/* Alerts and Calendar */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Alerts Panel */}
        <div>
          <AlertsPanel alerts={alerts} />
        </div>

        {/* Interactive Calendar */}
        <div>
          <SimpleCalendar events={calendarEvents} />
        </div>
      </div>

      {/* Additional Information */}
      <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
              <span className="text-white text-sm font-bold">📊</span>
            </div>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-blue-800">
              Kho dữ liệu tích hợp
            </h3>
            <div className="mt-2 text-sm text-blue-700">
              <p>
                Hệ thống quản lý hồ sơ tích hợp với khả năng theo dõi thời gian
                thực, cảnh báo tự động và báo cáo chi tiết. Dữ liệu được cập
                nhật liên tục và đồng bộ với tất cả các module khác.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
