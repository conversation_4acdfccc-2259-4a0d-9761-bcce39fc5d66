import { RootState } from "@/store/rootReducer";
import { createSelector } from "@reduxjs/toolkit";
import { Resource } from "./types";

export const selectResourceState = (state: RootState) => state.resourceState;

export const selectResources = createSelector(
  [selectResourceState],
  (resourceState) => resourceState.data
);

export const selectCurrentResource = createSelector(
  [selectResourceState],
  (resourceState) => resourceState.currentResource
);

export const selectPageMode = createSelector(
  [selectResourceState],
  (resourceState) => resourceState.pageMode
);

export const selectActionType = createSelector(
  [selectResourceState],
  (resourceState) => resourceState.actionType
);

export const selectSearchTerm = createSelector(
  [selectResourceState],
  (resourceState) => resourceState.searchTerm
);

export const selectLoading = createSelector(
  [selectResourceState],
  (resourceState) => resourceState.loading
);

export const selectError = createSelector(
  [selectResourceState],
  (resourceState) => resourceState.error
);

export const selectFilteredResources = createSelector(
  [selectResources, selectSearchTerm],
  (resources, searchTerm) => {
    if (!searchTerm) return resources;
    return resources.filter(
      (resource: Resource) =>
        resource.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        resource.code.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }
);
