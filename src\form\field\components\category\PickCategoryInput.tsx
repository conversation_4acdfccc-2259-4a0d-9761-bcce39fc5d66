/**
 * PickCategoryInput - Category picker component for AutoForm
 */

import React from "react";
import { useSearchParams } from "react-router-dom";
import { FieldComponentProps } from "../../FieldFactory";
import { useCategory } from "@/features/category/hooks/useCategoryData";
import { DropDownTextWithSearch } from "../basic/DropDownTextWithSearch";
import { CategoryType } from "@/features/category/states/types";

export const PickCategoryInput: React.FC<FieldComponentProps> = ({
  value,
  onChange,
  onBlur,
  disabled = false,
  placeholder = "Chọn chuyên mục cha",
  id,
  className = "",
}) => {
  const [searchParams] = useSearchParams();
  const urlType = searchParams.get("type") as CategoryType;
  const selectedType = urlType || "public-menu";

  const { data: categories } = useCategory(selectedType);

  const options = ["0", ...categories.map((cat) => String(cat.id))];
  const labels = ["Không có cha", ...categories.map((cat) => cat.name)];

  const handleChange = (
    selectedValue: string | number | boolean | (string | number | boolean)[]
  ) => {
    const strValue = String(selectedValue);
    const numValue = strValue === "0" ? 0 : Number(strValue);
    onChange(numValue);
  };

  const currentValue = value === null || value === 0 ? "0" : String(value);

  return (
    <DropDownTextWithSearch
      value={currentValue}
      onChange={handleChange}
      onBlur={onBlur}
      disabled={disabled}
      placeholder={placeholder}
      options={options}
      labels={labels}
      id={id}
      className={className}
    />
  );
};
