import { ColumnConfig } from "@/components/table/registry";

export type CategoryType = "public-menu" | "admin-menu" | "user-menu";
export type CategoryStatus = "ACTIVE" | "DISABLED";
export type CategoryMode = "view" | "edit" | "create";

export interface CategoryDescription {
  icon: string;
  component: string;
}

export interface CategoryDTO {
  id: number;
  name: string;
  slug: string;
  type: CategoryType;
  description: CategoryDescription;
  priority: number;
  parentId: number | null;
  postId: number | null;
  createdAt: number;
  updatedAt: number;
  status: CategoryStatus;
}

export interface CategoryTree extends CategoryDTO {
  children: CategoryTree[];
}

export function buildCategoryTree(flatList: CategoryDTO[]): CategoryTree[] {
  // B1: Tạo map tạm
  const map = new Map<number, CategoryTree>();
  // B2: Duyệt và tạo node chỉ cho node ACTIVE
  flatList.forEach((item) => {
    if (item.status === "ACTIVE") {
      map.set(item.id, { ...item, children: [] });
    }
  });

  const roots: CategoryTree[] = [];

  // B3: Chỉ kết nối nếu parent cũng tồn tại trong map
  map.forEach((node) => {
    if (node.parentId && map.has(node.parentId)) {
      map.get(node.parentId)!.children.push(node);
    } else if (!node.parentId || !map.has(node.parentId)) {
      // Node là root nếu: không có parentId hoặc parent bị loại (không ACTIVE)
      // -> Nhưng theo yêu cầu, node có parentId nhưng parent không ACTIVE thì KHÔNG đưa vào roots
      if (!node.parentId) {
        roots.push(node);
      }
      // Nếu có parentId mà parent không ACTIVE thì KHÔNG làm gì cả (bị loại)
    }
  });

  // B4: Sắp xếp đệ quy theo priority giảm dần (priority cao lên trên)
  // Lý do sort ở đây:
  // - Đảm bảo tất cả nơi sử dụng tree (TreeSidebar, Category Management) đều có thứ tự nhất quán
  // - Tối ưu hiệu suất: sort một lần thay vì mỗi lần render
  // - Consistent với CategoryTree component trong category management
  const sortChildren = (node: CategoryTree) => {
    node.children.sort((a, b) => b.priority - a.priority);
    node.children.forEach(sortChildren);
  };

  // Sort roots và children
  roots.sort((a, b) => b.priority - a.priority);
  roots.forEach(sortChildren);

  return roots;
}

export const CategoryColumns: ColumnConfig[] = [
  { accessorKey: "id", header: "ID" },
  { accessorKey: "name", header: "Tên chuyên mục" },
  { accessorKey: "slug", header: "Slug", visible: false },
  { accessorKey: "type", header: "Loại", visible: false },

  {
    accessorKey: "description.icon",
    header: "Icon",
    cell: {
      component: "IconCell",
      props: {},
    },
    visible: true,
  },
  {
    accessorKey: "description.component",
    header: "Component",
    cell: {
      component: "TextCell",
      props: {},
    },
    visible: true,
  },

  { accessorKey: "priority", header: "Ưu tiên", visible: false },
  { accessorKey: "parentId", header: "Parent ID", visible: false },
  { accessorKey: "postId", header: "Post ID", visible: false },

  {
    accessorKey: "createdAt",
    header: "Ngày tạo",
    cell: {
      component: "DateTimeCell",
      props: {
        format: "dd/mm/yyyy HH:mm",
      },
    },
    visible: false,
  },
  {
    accessorKey: "updatedAt",
    header: "Cập nhật lần cuối",
    cell: {
      component: "DateTimeCell",
      props: {
        format: "dd/mm/yyyy HH:mm",
      },
    },
    visible: true,
  },
];
