/**
 * CheckboxInput - Checkbox component for AutoForm
 */

import React from "react";
import { FieldComponentProps } from "../../FieldFactory";
import { Checkbox } from "@/components/ui/checkbox";

export const CheckboxInput: React.FC<FieldComponentProps> = ({
  value,
  onChange,
  onBlur,
  disabled = false,
  id,
  className = "",
  placeholder,
}) => {
  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      <Checkbox
        id={id}
        checked={!!value}
        onCheckedChange={onChange}
        onBlur={onBlur}
        disabled={disabled}
      />
      {placeholder && (
        <label
          htmlFor={id}
          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
        >
          {placeholder}
        </label>
      )}
    </div>
  );
};
