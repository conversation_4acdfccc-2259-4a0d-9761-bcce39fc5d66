# Question Feature - API Usage Guide

## Sử dụng API thật thay vì Mock Data

Tính năng hỏi đáp đã được cập nhật để sử dụng API thật theo đúng flow của Java backend.

### 🔄 Question Status Flow

Theo Java enum `QuestionStatus`, flow chuyển đổi trạng thái như sau:

```
NEW → DRAFT → PUBLISHED
 ↓      ↓         ↓
TRASH ← TRASH   DRAFT
```

**Chi tiết flow:**

- **NEW**: Câu hỏi mới được tạo
  - C<PERSON> thể chuyển thành: `DRAFT` (khi có người trả lời) hoặc `TRASH` (xóa)
- **DRAFT**: Câu hỏi có câu trả lời nhưng chưa xuất bản
  - <PERSON><PERSON> thể chuyển thành: `PUBLISHED` (xuất bản) hoặc `TRASH` (xóa)
- **PUBLISHED**: Câu hỏi và câu trả lời đã được xuất bản
  - <PERSON><PERSON> thể chuyển thành: `DRAFT` (gỡ xuống)
- **TRASH**: Câu hỏi đã bị xóa
  - Có thể chuyển thành: `DRAFT` (khôi phục)

### 🚀 Tạo dữ liệu test bằng API

Thay vì sử dụng mock data, bạn có thể tạo dữ liệu test bằng cách gọi API:

#### 1. Sử dụng Development Console

Trong môi trường development, mở browser console và chạy:

```javascript
// Tạo câu hỏi mẫu
await window.questionApiHelpers.createSampleQuestions();

// Hoặc sử dụng UI prompt
window.questionApiHelpers.generateTestData();
```

#### 2. Sử dụng API Helper Functions

```typescript
import {
  createSampleQuestions,
  resetSampleData,
} from "@/features/question/utils/apiTestHelpers";

// Tạo 5 câu hỏi mẫu
await createSampleQuestions();

// Reset và tạo lại dữ liệu
await resetSampleData();
```

#### 3. Tạo câu hỏi thủ công

```typescript
import { useQuestion } from "@/features/question/hooks/useQuestion";

const { createQuestion } = useQuestion();

await createQuestion({
  info: {
    name: "Tên người hỏi",
    email: "<EMAIL>",
  },
  content: {
    title: "Tiêu đề câu hỏi",
    description: "Nội dung chi tiết câu hỏi",
  },
  topic: "Chủ đề",
});
```

### 📋 API Endpoints được sử dụng

#### Public APIs

- `GET /portal/v1/public/questions` - Lấy danh sách câu hỏi đã xuất bản
- `GET /portal/v1/public/questions/{id}` - Lấy chi tiết câu hỏi

#### Private APIs

- `POST /portal/v1/private/questions` - Tạo câu hỏi mới
- `PUT /portal/v1/private/questions/{id}` - Cập nhật câu hỏi

#### Admin APIs

- `GET /portal/v1/admin/questions` - Lấy tất cả câu hỏi (admin)
- `PUT /portal/v1/admin/questions/{id}/published` - Xuất bản câu hỏi
- `PUT /portal/v1/admin/questions/{id}/status/{status}` - Thay đổi trạng thái
- `DELETE /portal/v1/admin/questions/{id}` - Xóa câu hỏi

### 🎯 URL Structure với Search Params

#### Danh sách câu hỏi:

```
/admin/quan-ly-hoi-dap?mode=list&status=NEW&keyword=search&page=0&size=10
```

#### Chi tiết câu hỏi:

```
/admin/quan-ly-hoi-dap?mode=detail&questionId=1
```

### 🔧 Status Management

Các action buttons trong UI sẽ tự động hiển thị dựa trên trạng thái hiện tại và các transition được phép:

- **NEW**: Hiển thị "Chuyển về nháp" và "Chuyển vào thùng rác"
- **DRAFT**: Hiển thị "Xuất bản" và "Chuyển vào thùng rác"
- **PUBLISHED**: Hiển thị "Chuyển về nháp"
- **TRASH**: Hiển thị "Khôi phục về nháp"

### 🐛 Debugging

Để debug API calls, mở browser console và xem:

```javascript
// Xem state hiện tại
console.log(store.getState().question);

// Xem network requests trong DevTools Network tab
// Tất cả API calls sẽ có prefix /portal/v1/
```

### ⚠️ Lưu ý quan trọng

1. **Mock data đã bị loại bỏ**: Tất cả dữ liệu hiện tại đều từ API thật
2. **Status flow**: Phải tuân thủ đúng flow chuyển đổi trạng thái
3. **Permissions**: Một số API cần quyền admin
4. **Error handling**: Tất cả lỗi API sẽ được hiển thị trong UI

### 🔄 Migration từ Mock Data

Nếu bạn đang có mock data và muốn chuyển sang API:

1. Xóa hoặc comment out các import mock data
2. Sử dụng `createSampleQuestions()` để tạo dữ liệu test
3. Kiểm tra network requests trong DevTools
4. Verify data flow qua Redux DevTools
