# Question Feature

Feature quản lý hỏi đáp (Q&A) cho hệ thống KHA Portal.

## 📁 Cấu trúc thư mục

```
src/features/question/
├── components/           # UI Components
│   ├── QuestionList.tsx  # Danh sách câu hỏi
│   └── QuestionForm.tsx  # Form thêm/sửa câu hỏi
├── hooks/               # Custom hooks
│   └── useQuestion.ts   # Hook quản lý state question
├── pages/               # Pages
│   ├── QuestionAdminPage.tsx  # Trang quản lý Q&A
│   └── loadable.tsx     # Lazy loading wrapper
├── states/              # Redux state management
│   ├── types.ts         # TypeScript interfaces
│   ├── slices.ts        # Redux slices & async thunks
│   └── selector.ts      # Redux selectors
├── index.ts             # Export barrel
└── README.md            # Tài liệu này
```

## 🚀 Tính năng

### ✅ Đã hoàn thành

- ✅ Cấu trúc thư mục theo pattern của dự án
- ✅ Redux state management với RTK
- ✅ TypeScript interfaces và types
- ✅ UI Components với Shadcn/ui
- ✅ Custom hook `useQuestion`
- ✅ Lazy loading page
- ✅ Tích hợp vào ComponentRegistry
- ✅ Thêm vào ComponentPicker

### 🔄 Cần hoàn thiện

- ⏳ API integration (hiện tại dùng mock data)
- ⏳ Form validation
- ⏳ Search & filter functionality
- ⏳ Pagination
- ⏳ Rich text editor cho câu trả lời

## 🛠️ Cách sử dụng

### 1. Thêm vào Redux Store

Cần thêm `questionReducer` vào root reducer:

```typescript
// src/store/rootReducer.ts
import { questionReducer } from "@/features/question";

export const rootReducer = combineReducers({
  // ... other reducers
  question: questionReducer,
});
```

### 2. Sử dụng trong Component

```typescript
import { useQuestion } from "@/features/question/hooks/useQuestion";

const MyComponent = () => {
  const {
    questions,
    loading,
    error,
    fetchQuestions,
    createQuestion,
    updateQuestion,
    deleteQuestion,
  } = useQuestion();

  // Sử dụng các methods...
};
```

### 3. Thêm vào Route

Component đã được tự động thêm vào `ComponentRegistry` với tên `QuestionAdminPage`.

## 📋 Data Structure

### Question Interface

```typescript
interface Question {
  id: string;
  question: string; // Câu hỏi
  answer: string; // Câu trả lời
  category?: string; // Danh mục (optional)
  status: "active" | "inactive"; // Trạng thái
  createdAt: string; // Ngày tạo
  updatedAt: string; // Ngày cập nhật
}
```

## 🎯 Các Actions có sẵn

- `fetchQuestions()` - Lấy danh sách câu hỏi
- `createQuestion(data)` - Tạo câu hỏi mới
- `updateQuestion(question)` - Cập nhật câu hỏi
- `deleteQuestion(id)` - Xóa câu hỏi
- `setSelectedQuestion(question)` - Chọn câu hỏi
- `clearError()` - Xóa lỗi

## 🔧 Customization

### Thêm field mới

1. Cập nhật interface trong `types.ts`
2. Cập nhật form trong `QuestionForm.tsx`
3. Cập nhật display trong `QuestionList.tsx`

### Thêm validation

1. Sử dụng react-hook-form hoặc formik
2. Thêm validation schema
3. Cập nhật form component

## 🎯 Bước 3: Q&A Configuration ✅

### ✅ Đã hoàn thành

- ✅ **QA Config Types & API**

  - QAConfig và QAConfigRequest interfaces
  - QAConfigAPI class với getQAConfig/updateQAConfig methods
  - GET endpoint: `/portal/v1/public/config/qa_config` (Public access)
  - POST endpoint: `/portal/v1/admin/config/qa_config` (Admin access)

- ✅ **QuestionConfigAdminPage**

  - Form quản lý trạng thái active/inactive với Switch
  - Quản lý danh sách topics (thêm/xóa) với Badge components
  - UI hiện đại với Cards, responsive layout
  - Summary dashboard với thống kê trực quan

- ✅ **Redux Integration**

  - Thêm qaConfig, qaConfigLoading, qaConfigError vào QuestionState
  - fetchQAConfig và updateQAConfig async thunks
  - Selectors và actions cho QA Config
  - Cập nhật useQuestion hook với QA Config methods

- ✅ **Component Registration**
  - Lazy loading với React.lazy
  - Thêm vào ComponentRegistry và ComponentPicker
  - Export trong index.ts

### 🎨 UI Features

- **Cài Đặt Chung**: Toggle switch để bật/tắt hệ thống Q&A
- **Quản Lý Chủ Đề**: Thêm/xóa topics với input và badge
- **Tóm Tắt Cấu Hình**: Dashboard hiển thị trạng thái và thống kê
- **Error Handling**: Alert components cho lỗi và thông báo
- **Loading States**: Spinner và skeleton loading

## 🎯 Bước 4: Hoàn thiện API Integration ✅

### ✅ Đã hoàn thành

- ✅ **API Integration**

  - Cập nhật `fetchQAConfig` để call public API với fallback to mock data
  - Cải thiện `updateQAConfig` với admin API và proper error handling
  - Thêm `rejectWithValue` để handle API errors properly
  - Correct endpoints: GET public, POST admin

- ✅ **Test Connection Utilities**

  - Tạo `testConnection.ts` với comprehensive error parsing
  - `testQAConfigConnection()` - Test GET API
  - `testQAConfigUpdate()` - Test POST API
  - Detailed error messages cho different failure types

- ✅ **Enhanced Debug Features**

  - Test API button với visual status feedback
  - Debug panel trong development mode
  - Real-time API response display
  - Connection status indicators
  - Comprehensive error logging

- ✅ **Production Ready**
  - Mock data fallback khi API không available
  - Proper error boundaries và user feedback
  - Loading states và timeout handling
  - Development/production environment detection

### 🎨 Testing Features

- **Test API Button**: Real-time connection testing với visual feedback
- **Debug Panel**: JSON response viewer, error details, test history
- **Status Indicators**: Success/Error/Loading states với colors và icons
- **Error Parsing**: Detailed error messages từ network/server/request errors

## 📝 TODO

- [x] Implement API calls trong async thunks ✅
- [ ] Thêm form validation
- [ ] Thêm search/filter
- [ ] Thêm pagination
- [ ] Thêm rich text editor
- [ ] Thêm unit tests
- [ ] Thêm error boundary
- [ ] Optimize performance với React.memo
