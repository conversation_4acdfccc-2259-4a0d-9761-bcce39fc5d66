import { ComponentType } from "react";
import { CategoryType } from "@/features/category/states/types";

export interface RouteConfig {
  type: CategoryType;
  basePath: string;
  layout: ComponentType;
  requiresAuth?: boolean;
  requiresRole?: string[];
  preloadCategories?: boolean;
  staticRoutes?: StaticRoute[];
}

export interface StaticRoute {
  path: string;
  component: ComponentType;
  requiresAuth?: boolean;
  requiresRole?: string[];
}

/**
 * 🚀 Route Configuration System
 *
 * ✅ Benefits:
 * - Centralized route configuration
 * - Type-safe route definitions
 * - Easy to extend with new route types
 * - Support for static + dynamic routes
 * - Authentication & role-based access
 */
export class RouteConfigManager {
  private configs = new Map<CategoryType, RouteConfig>();

  /**
   * Register route configuration
   */
  register(config: RouteConfig) {
    this.configs.set(config.type, config);
  }

  /**
   * Get configuration for route type
   */
  getConfig(type: CategoryType): RouteConfig | null {
    return this.configs.get(type) || null;
  }

  /**
   * Get all registered route types
   */
  getRegisteredTypes(): CategoryType[] {
    return Array.from(this.configs.keys());
  }

  /**
   * Get all configurations
   */
  getAllConfigs(): RouteConfig[] {
    return Array.from(this.configs.values());
  }

  /**
   * Check if route type is registered
   */
  isRegistered(type: CategoryType): boolean {
    return this.configs.has(type);
  }

  /**
   * Get route configuration with validation
   */
  getValidatedConfig(type: CategoryType): RouteConfig {
    const config = this.configs.get(type);
    if (!config) {
      throw new Error(
        `Route configuration for "${type}" not found. Available types: ${this.getRegisteredTypes().join(
          ", "
        )}`
      );
    }
    return config;
  }
}

// ✅ Singleton instance
export const routeConfigManager = new RouteConfigManager();
