export interface ServerMenu {
  id: number;
  parent: number | null;
  url: string;
  fullUrl: string;
  name: string;
  icon: string;
  component: string | null;
  active: boolean;
  visible: boolean;
  accessible: boolean;
  priority: number;
  metadata: Record<string, string>;
  roles: number[];
  children: number[];
}

export interface MenuState {
  menuList: ServerMenu[];
  loaded: boolean;
}

export interface UpdateMenuRole {
  menuId: number;
  roleList: number[];
}
export interface TableMenu {
  id: number;
  fullName: string;
  priority: number;
}
export interface ComponentItem {
  value: string;
  label?: string;
}
export const COMPONENT_NAMES = [
  { value: "MetadataPage", label: "MetadataPage" },
  { value: "RolePage", label: "RolePage" },
  { value: "UserManagePage", label: "UserManagePage" },
  { value: "GroupUserPage", label: "GroupUserPage" },
  { value: "GroupMetaPage", label: "GroupMetaPage" },

  { value: "MenuPage", label: "MenuPage" },
  { value: "MenuForm", label: "MenuForm" },
];
