import React from "react";
import { Search } from "lucide-react";
import { GroupMenu } from "./HeaderMenu";
import { useAppSelector } from "@/store/rootReducer";
import { selectCategoryByType } from "@/features/category/states/selector";
import { buildCategoryTree } from "@/features/category/states/types";

interface PublicNavigationProps {
  showSearch?: boolean;
}

export const PublicNavigation: React.FC<PublicNavigationProps> = ({
  showSearch = true,
}) => {
  const list = useAppSelector((state) =>
    selectCategoryByType(state, "public-menu")
  );

  const menuList = buildCategoryTree(list);

  return (
    <div className="flex items-center justify-between w-full px-6 py-3 bg-white border-t">
      <GroupMenu menuList={menuList} />

      {showSearch && (
        <button className="p-2 rounded-full border text-gray-600 hover:bg-gray-100 transition-colors">
          <Search className="w-5 h-5" />
        </button>
      )}
    </div>
  );
};
