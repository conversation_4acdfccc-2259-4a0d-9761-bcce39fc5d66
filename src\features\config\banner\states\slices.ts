import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import { toast } from "sonner";
import type { BannerState, BannerConfig } from "./type";
import { fetchBannerConfig, updateBannerConfig } from "./api";

const initialState: BannerState = {
  data: null,
  savedData: null,
  loading: false,
  saving: false,
  error: null,
  isDirty: false,
};

// Async thunks
export const fetchBannerAsync = createAsyncThunk(
  "banner/fetchBanner",
  async (_, { rejectWithValue }) => {
    try {
      const response = await fetchBannerConfig();
      return response.data;
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : "Không thể tải cấu hình banner"
      );
    }
  }
);

export const updateBannerAsync = createAsyncThunk(
  "banner/updateBanner",
  async (data: BannerConfig, { rejectWithValue }) => {
    try {
      const response = await updateBannerConfig(data);
      return response.data;
    } catch (error) {
      return rejectWithValue(
        error instanceof Error
          ? error.message
          : "<PERSON>hông thể cập nhật cấu hình banner"
      );
    }
  }
);

const bannerSlice = createSlice({
  name: "banner",
  initialState,
  reducers: {
    setBannerData: (state, action: PayloadAction<BannerConfig>) => {
      state.data = action.payload;
      state.isDirty = true;
    },

    clearError: (state) => {
      state.error = null;
    },

    resetBanner: (state) => {
      state.data = null;
      state.savedData = null;
      state.isDirty = false;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch banner
      .addCase(fetchBannerAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchBannerAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.data = action.payload || null;
        state.savedData = action.payload || null;
        state.isDirty = false;
      })
      .addCase(fetchBannerAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
        toast.error(`Lỗi tải cấu hình banner: ${action.payload}`);
      })

      // Update banner
      .addCase(updateBannerAsync.pending, (state) => {
        state.saving = true;
        state.error = null;
      })
      .addCase(updateBannerAsync.fulfilled, (state, action) => {
        state.saving = false;
        state.data = action.payload || null;
        state.savedData = action.payload || null;
        state.isDirty = false;
        toast.success("Cập nhật cấu hình banner thành công");
      })
      .addCase(updateBannerAsync.rejected, (state, action) => {
        state.saving = false;
        state.error = action.payload as string;
        toast.error(`Lỗi cập nhật cấu hình banner: ${action.payload}`);
      });
  },
});

export const { setBannerData, clearError, resetBanner } = bannerSlice.actions;

export default bannerSlice.reducer;
