// ✅ registry.ts
import { ActionCell } from "./cells/ActionCell";
import { ActionCheckbox } from "./cells/ActionCheckbox";
import { Avatar } from "./cells/Avatar";
import { Badge } from "./cells/Badge";
import { DateTimeCell } from "./cells/DateTimeCell";
import { IconCell } from "./cells/IconCell";
import { SimpleCheckbox } from "./cells/SimpleCheckbox";
import { TextCell } from "./cells/TextCell";
import { ImageCell } from "./cells/ImageCell";
import { PostBadge } from "@/features/post/components/PostBadge";
import { PostAction } from "@/features/post/components/PostAction";
import { AlbumAction } from "@/features/media/images/components/AlbumAction";
import { AlbumBadge } from "@/features/media/images/components/AlbumBadge";

export type ActionType = "edit" | "remove" | "view" | "config" | "download";

export const CellComponentRegistry = {
  Badge,
  Avatar,
  SimpleCheckbox,
  ActionCell,
  ActionCheckbox,
  IconCell,
  TextCell,
  DateTimeCell,
  ImageCell,
  PostBadge,
  PostAction,
  AlbumAction,
  AlbumBadge,
} as const;

export type CellComponentRegistryMap = typeof CellComponentRegistry;
export type ComponentName = keyof CellComponentRegistryMap;

export type CellComponentPropsMap = {
  [K in keyof CellComponentRegistryMap]: React.ComponentProps<
    (typeof CellComponentRegistry)[K]
  > & {
    normalizedValue?: string;
  };
};

export type ComponentCell<K extends ComponentName> = {
  component: K;
  props: Omit<CellComponentPropsMap[K], "value" | "row" | "accessorKey">;
};

export type CellConfig = {
  [K in ComponentName]: ComponentCell<K>;
}[ComponentName];

export interface ColumnConfig {
  accessorKey: string;
  header: string;
  visible?: boolean;
  cell?: CellConfig;
}
