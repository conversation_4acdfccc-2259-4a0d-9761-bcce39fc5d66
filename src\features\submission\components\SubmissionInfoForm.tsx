import { useState } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { SubmissionInfo } from "../data/mockData";

interface SubmissionInfoFormProps {
  submissionInfo: SubmissionInfo;
  onSubmissionInfoChange?: (info: SubmissionInfo) => void;
  readOnly?: boolean;
}

export const SubmissionInfoForm = ({
  submissionInfo,
  onSubmissionInfoChange,
  readOnly = false,
}: SubmissionInfoFormProps) => {
  const [localInfo, setLocalInfo] = useState<SubmissionInfo>(submissionInfo);

  const handleChange = (
    field: keyof SubmissionInfo | keyof SubmissionInfo["contactPerson"],
    value: string | number,
    isContactField = false
  ) => {
    if (readOnly) return;

    let updatedInfo: SubmissionInfo;

    if (isContactField) {
      updatedInfo = {
        ...localInfo,
        contactPerson: {
          ...localInfo.contactPerson,
          [field]: value,
        },
      };
    } else {
      updatedInfo = {
        ...localInfo,
        [field]: value,
      };
    }

    setLocalInfo(updatedInfo);
    onSubmissionInfoChange?.(updatedInfo);
  };

  return (
    <Card className="w-full">
      <CardHeader className="bg-blue-50 border-b">
        <CardTitle className="text-lg font-semibold text-blue-900 flex items-center">
          <span className="bg-blue-600 text-white px-2 py-1 rounded text-sm mr-2">
            !
          </span>
          THÔNG TIN GÓI TIN
        </CardTitle>
      </CardHeader>
      <CardContent className="p-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Left Column */}
          <div className="space-y-4">
            {/* Mã đăng ký nộp lưu */}
            <div className="space-y-2">
              <Label htmlFor="registrationCode" className="text-sm font-medium">
                Mã đăng ký nộp lưu:
              </Label>
              <Input
                id="registrationCode"
                value={localInfo.registrationCode}
                onChange={(e) =>
                  handleChange("registrationCode", e.target.value)
                }
                readOnly={readOnly}
                className="bg-yellow-50"
              />
            </div>

            {/* Mã cơ quan */}
            <div className="space-y-2">
              <Label htmlFor="organizationCode" className="text-sm font-medium">
                Mã cơ quan:
              </Label>
              <Input
                id="organizationCode"
                value={localInfo.organizationCode}
                onChange={(e) =>
                  handleChange("organizationCode", e.target.value)
                }
                readOnly={readOnly}
              />
            </div>

            {/* Tổng số hồ sơ tài liệu đăng ký */}
            <div className="space-y-2">
              <Label
                htmlFor="totalRegisteredRecords"
                className="text-sm font-medium"
              >
                Tổng số hồ sơ tài liệu đăng ký:
              </Label>
              <Input
                id="totalRegisteredRecords"
                type="number"
                value={localInfo.totalRegisteredRecords}
                onChange={(e) =>
                  handleChange(
                    "totalRegisteredRecords",
                    parseInt(e.target.value) || 0
                  )
                }
                readOnly={readOnly}
              />
            </div>

            {/* Tổng số hồ sơ tài liệu đã nộp */}
            <div className="space-y-2">
              <Label
                htmlFor="totalSubmittedRecords"
                className="text-sm font-medium"
              >
                Tổng số hồ sơ tài liệu đã nộp:
              </Label>
              <Input
                id="totalSubmittedRecords"
                type="number"
                value={localInfo.totalSubmittedRecords}
                onChange={(e) =>
                  handleChange(
                    "totalSubmittedRecords",
                    parseInt(e.target.value) || 0
                  )
                }
                readOnly={readOnly}
              />
            </div>

            {/* Thời gian gửi */}
            <div className="space-y-2">
              <Label htmlFor="sendDate" className="text-sm font-medium">
                Thời gian gửi:
              </Label>
              <Input
                id="sendDate"
                value={localInfo.sendDate}
                onChange={(e) => handleChange("sendDate", e.target.value)}
                readOnly={readOnly}
              />
            </div>
          </div>

          {/* Right Column */}
          <div className="space-y-4">
            {/* Tên cơ quan */}
            <div className="space-y-2">
              <Label htmlFor="organizationName" className="text-sm font-medium">
                Tên cơ quan:
              </Label>
              <Input
                id="organizationName"
                value={localInfo.organizationName}
                onChange={(e) =>
                  handleChange("organizationName", e.target.value)
                }
                readOnly={readOnly}
              />
            </div>

            {/* Tổng số hồ sơ tài liệu upload */}
            <div className="space-y-2">
              <Label
                htmlFor="totalUploadedRecords"
                className="text-sm font-medium"
              >
                Tổng số hồ sơ tài liệu upload:
              </Label>
              <Input
                id="totalUploadedRecords"
                type="number"
                value={localInfo.totalUploadedRecords}
                onChange={(e) =>
                  handleChange(
                    "totalUploadedRecords",
                    parseInt(e.target.value) || 0
                  )
                }
                readOnly={readOnly}
                className="bg-yellow-50"
              />
            </div>

            {/* Số lần nộp lưu */}
            <div className="space-y-2">
              <Label htmlFor="submissionCount" className="text-sm font-medium">
                Số lần nộp lưu:
              </Label>
              <Input
                id="submissionCount"
                type="number"
                value={localInfo.submissionCount}
                onChange={(e) =>
                  handleChange("submissionCount", parseInt(e.target.value) || 0)
                }
                readOnly={readOnly}
              />
            </div>

            {/* Ghi chú */}
            <div className="space-y-2">
              <Label htmlFor="note" className="text-sm font-medium">
                Ghi chú:
              </Label>
              <Input
                id="note"
                value={localInfo.note}
                onChange={(e) => handleChange("note", e.target.value)}
                readOnly={readOnly}
              />
            </div>
          </div>
        </div>

        {/* Contact Information Section */}
        <div className="mt-8 pt-6 border-t">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            Thông tin liên hệ
          </h3>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Left Column - Contact */}
            <div className="space-y-4">
              {/* Họ và tên */}
              <div className="space-y-2">
                <Label htmlFor="fullName" className="text-sm font-medium">
                  Họ và tên:
                </Label>
                <Input
                  id="fullName"
                  value={localInfo.contactPerson.fullName}
                  onChange={(e) =>
                    handleChange("fullName", e.target.value, true)
                  }
                  readOnly={readOnly}
                />
              </div>

              {/* Điện thoại */}
              <div className="space-y-2">
                <Label htmlFor="phone" className="text-sm font-medium">
                  Điện thoại:
                </Label>
                <Input
                  id="phone"
                  value={localInfo.contactPerson.phone}
                  onChange={(e) => handleChange("phone", e.target.value, true)}
                  readOnly={readOnly}
                />
              </div>
            </div>

            {/* Right Column - Contact */}
            <div className="space-y-4">
              {/* Chức vụ/Chức danh */}
              <div className="space-y-2">
                <Label htmlFor="position" className="text-sm font-medium">
                  Chức vụ/Chức danh:
                </Label>
                <Input
                  id="position"
                  value={localInfo.contactPerson.position}
                  onChange={(e) =>
                    handleChange("position", e.target.value, true)
                  }
                  readOnly={readOnly}
                />
              </div>

              {/* Địa chỉ Email */}
              <div className="space-y-2">
                <Label htmlFor="email" className="text-sm font-medium">
                  Địa chỉ Email:
                </Label>
                <Input
                  id="email"
                  type="email"
                  value={localInfo.contactPerson.email}
                  onChange={(e) => handleChange("email", e.target.value, true)}
                  readOnly={readOnly}
                />
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
