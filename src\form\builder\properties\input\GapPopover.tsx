/**
 * Gap Popover Component
 * Popover with visual gap spacing options
 */

import React, { useState } from 'react';
import { cn } from '@/lib/utils';
import { ChevronDown, Check } from 'lucide-react';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Button } from '@/components/ui/button';

interface GapOption {
  value: string;
  label: string;
  description: string;
  preview: React.ReactNode;
}

interface GapPopoverProps {
  value?: string;
  onValueChange?: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
}

// Gap options with visual representations
const GAP_OPTIONS: GapOption[] = [
  {
    value: 'none',
    label: '0',
    description: '0px',
    preview: (
      <div className="flex">
        <div className="w-1 h-3 bg-gray-400"></div>
        <div className="w-1 h-3 bg-gray-400"></div>
        <div className="w-1 h-3 bg-gray-400"></div>
      </div>
    )
  },
  {
    value: '1',
    label: '1',
    description: '4px',
    preview: (
      <div className="flex gap-0.5">
        <div className="w-1 h-3 bg-blue-400"></div>
        <div className="w-1 h-3 bg-blue-400"></div>
        <div className="w-1 h-3 bg-blue-400"></div>
      </div>
    )
  },
  {
    value: '2',
    label: '2',
    description: '8px',
    preview: (
      <div className="flex gap-0.5">
        <div className="w-1 h-3 bg-blue-400"></div>
        <div className="w-1 h-3 bg-blue-400"></div>
        <div className="w-1 h-3 bg-blue-400"></div>
      </div>
    )
  },
  {
    value: '3',
    label: '3',
    description: '12px',
    preview: (
      <div className="flex gap-1">
        <div className="w-1 h-3 bg-blue-400"></div>
        <div className="w-1 h-3 bg-blue-400"></div>
        <div className="w-1 h-3 bg-blue-400"></div>
      </div>
    )
  },
  {
    value: '4',
    label: '4',
    description: '16px',
    preview: (
      <div className="flex gap-1">
        <div className="w-1 h-3 bg-blue-400"></div>
        <div className="w-1 h-3 bg-blue-400"></div>
        <div className="w-1 h-3 bg-blue-400"></div>
      </div>
    )
  },
  {
    value: '6',
    label: '6',
    description: '24px',
    preview: (
      <div className="flex gap-1.5">
        <div className="w-1 h-3 bg-blue-400"></div>
        <div className="w-1 h-3 bg-blue-400"></div>
        <div className="w-1 h-3 bg-blue-400"></div>
      </div>
    )
  },
  {
    value: '8',
    label: '8',
    description: '32px',
    preview: (
      <div className="flex gap-2">
        <div className="w-1 h-3 bg-blue-400"></div>
        <div className="w-1 h-3 bg-blue-400"></div>
        <div className="w-1 h-3 bg-blue-400"></div>
      </div>
    )
  },
  {
    value: '12',
    label: '12',
    description: '48px',
    preview: (
      <div className="flex gap-2">
        <div className="w-1 h-3 bg-blue-400"></div>
        <div className="w-1 h-3 bg-blue-400"></div>
      </div>
    )
  }
];

export const GapPopover: React.FC<GapPopoverProps> = ({
  value = '4',  // Default to 4 instead of none
  onValueChange,
  placeholder = 'Chọn khoảng cách',
  disabled = false
}) => {
  const [open, setOpen] = useState(false);

  // Find current option to display
  const currentOption = GAP_OPTIONS.find(opt => opt.value === value) || GAP_OPTIONS.find(opt => opt.value === '4') || GAP_OPTIONS[0];

  const handleValueSelect = (newValue: string) => {
    onValueChange?.(newValue);
    setOpen(false);
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            "h-7 w-full justify-between text-xs",
            disabled && "opacity-50 cursor-not-allowed"
          )}
          disabled={disabled}
        >
          <span className="truncate">{currentOption.label}</span>
          <ChevronDown className="ml-2 h-3 w-3 shrink-0 opacity-50 flex-shrink-0" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-64 p-2" align="start">
        <div className="grid grid-cols-4 gap-2">
          {GAP_OPTIONS.map((option) => (
            <button
              key={option.value}
              onClick={() => handleValueSelect(option.value)}
              className={cn(
                "flex flex-col items-center p-2 text-xs rounded-lg hover:bg-gray-100 transition-colors relative border border-gray-200",
                value === option.value && "bg-blue-100 text-blue-900 ring-1 ring-blue-500 border-blue-200"
              )}
            >
              <div className="flex items-center justify-center w-6 h-4 mb-1">
                {option.preview}
              </div>
              <span className="font-medium text-center">{option.label}</span>
              <span className="text-gray-500 text-xs">{option.description}</span>
              
              {value === option.value && (
                <Check className="h-3 w-3 absolute top-1 right-1 text-blue-600" />
              )}
            </button>
          ))}
        </div>
      </PopoverContent>
    </Popover>
  );
};