import { createAsyncThunk, createSlice, PayloadAction } from "@reduxjs/toolkit";
import {
  AccountResponse,
  initialAccount,
  LoginRequest,
  RegisterRequest,
} from "./type";
import {
  forgotPassword,
  handleLogin,
  handleRegister,
  getMe,
  refreshToken,
  handleLogout,
} from "./api";
import {
  setTokenToCookies,
  TOKEN_TYPE,
  getTokenFromCookies,
  parseJwt,
  removeTokenFromCookies,
} from "@/api/cookies";
import { toast } from "sonner";
import { AxiosError } from "axios";
import store from "@/store/store";

// Hàm để lên lịch refresh token
const scheduleTokenRefresh = () => {
  // Xóa bất kỳ lịch refresh nào đang tồn tại
  if (window.tokenRefreshTimeout) {
    clearTimeout(window.tokenRefreshTimeout);
  }

  const token = getTokenFromCookies(TOKEN_TYPE.ACCESS_TOKEN);
  if (!token) return;

  const tokenData = parseJwt(token);
  if (!tokenData) return;

  const expirationTime = tokenData.expired_at * 1000;
  const currentTime = Date.now();
  const timeUntilExpiration = expirationTime - currentTime;
  const thirtySeconds = 30 * 1000;

  // Nếu token đã hết hạn, không lên lịch nữa
  if (timeUntilExpiration <= 0) {
    console.warn("Token đã hết hạn");
    return;
  }

  // Nếu token còn hơn 30 giây mới hết hạn, lên lịch refresh
  if (timeUntilExpiration > thirtySeconds) {
    const timeToRefresh = timeUntilExpiration - thirtySeconds;
    window.tokenRefreshTimeout = setTimeout(async () => {
      try {
        await store.dispatch(refreshTokenAsync());
      } catch (error) {
        console.error("Failed to refresh token:", error);
      }
    }, timeToRefresh);
  } else {
    // Nếu token sắp hết hạn, refresh ngay lập tức
    store.dispatch(refreshTokenAsync());
  }
};

// Hàm để xóa lịch refresh token (dùng khi logout)
const clearTokenRefreshSchedule = () => {
  if (window.tokenRefreshTimeout) {
    clearTimeout(window.tokenRefreshTimeout);
    window.tokenRefreshTimeout = undefined;
  }
};

// Export để có thể sử dụng ở nơi khác
export { scheduleTokenRefresh, clearTokenRefreshSchedule };

// Thêm type cho window
declare global {
  interface Window {
    tokenRefreshTimeout?: NodeJS.Timeout;
  }
}

export const loginAsync = createAsyncThunk(
  "auth/loginAsync",
  async (request: LoginRequest, { rejectWithValue }) => {
    try {
      const response = await handleLogin(request);
      console.log("[loginAsync] Login response:", response);
      if (response.status === 200 && response.data?.data) {
        // Set tokens to cookies
        const { accessToken, refreshToken } = response.data.data;
        console.log("[loginAsync] Setting tokens to cookies");
        setTokenToCookies(TOKEN_TYPE.ACCESS_TOKEN, accessToken);
        setTokenToCookies(TOKEN_TYPE.REFRESH_TOKEN, refreshToken);
        return response.data;
      }
      return rejectWithValue("Invalid response");
    } catch (error) {
      const axiosError = error as AxiosError;
      if (axiosError.response && axiosError.response.status === 401) {
        toast.error("Tài khoản hoặc mật khẩu không đúng.");
      } else {
        toast.error("Có lỗi xảy ra, vui lòng thử lại sau.");
      }
      return rejectWithValue(axiosError.response?.data);
    }
  }
);

export const registerAsync = createAsyncThunk(
  "auth/registerAsync",
  async (request: RegisterRequest, { rejectWithValue }) => {
    try {
      const response = await handleRegister(request);
      if (response.status === 200 && response.data?.data) {
        return response.data;
      }
      return rejectWithValue("Invalid response");
    } catch (error) {
      const axiosError = error as AxiosError;
      if (axiosError.response && axiosError.response.status === 409) {
        toast.error("Tên đăng nhập, email hoặc số điện thoại đã được sử dụng.");
      } else {
        toast.error("Có lỗi xảy ra, vui lòng thử lại sau.");
      }
      return rejectWithValue(axiosError.response?.data);
    }
  }
);

export const forgotPasswordAsync = createAsyncThunk(
  "auth/forgotPassword",
  async (email: string, { rejectWithValue }) => {
    try {
      await forgotPassword(email);
      toast.success("Vui lòng kiểm tra email để đặt lại mật khẩu.");
    } catch (error) {
      const axiosError = error as AxiosError;
      toast.error("Có lỗi xảy ra, vui lòng thử lại sau.");
      return rejectWithValue(axiosError.response?.data);
    }
  }
);

export const refreshTokenAsync = createAsyncThunk(
  "auth/refreshToken",
  async (_, { rejectWithValue }) => {
    try {
      const response = await refreshToken();
      if (response.status === 200 && response.data?.data) {
        return response.data;
      }
      return rejectWithValue("Invalid response");
    } catch (error) {
      const axiosError = error as AxiosError;
      if (axiosError.response?.status === 401) {
        // Refresh token cũng hết hạn, xóa lịch refresh
        clearTokenRefreshSchedule();
        return rejectWithValue("Refresh token expired");
      }
      return rejectWithValue(axiosError.response?.data);
    }
  }
);

export const checkAuthState = createAsyncThunk(
  "auth/checkAuthState",
  async (_, { dispatch, rejectWithValue }) => {
    try {
      const token = getTokenFromCookies(TOKEN_TYPE.ACCESS_TOKEN);
      if (!token) {
        return rejectWithValue("No token found");
      }

      // Kiểm tra thời gian hết hạn của token
      const tokenData = parseJwt(token);
      if (!tokenData) {
        return rejectWithValue("Invalid token");
      }

      const expirationTime = tokenData.expired_at * 1000;
      const currentTime = Date.now();
      const timeUntilExpiration = expirationTime - currentTime;
      const thirtySeconds = 30 * 1000;

      // Nếu token đã hết hạn hoặc sắp hết hạn trong 30s, refresh trước
      if (timeUntilExpiration <= thirtySeconds) {
        const refreshResult = await dispatch(refreshTokenAsync());
        if (refreshTokenAsync.fulfilled.match(refreshResult)) {
          // Sau khi refresh thành công, gọi getMe với token mới
          const retryResponse = await getMe();
          if (retryResponse.status === 200 && retryResponse.data?.data) {
            // Lên lịch refresh token tiếp theo
            scheduleTokenRefresh();
            return retryResponse.data;
          }
        }
        // Nếu refresh thất bại, xóa token và logout
        removeTokenFromCookies(TOKEN_TYPE.ACCESS_TOKEN);
        removeTokenFromCookies(TOKEN_TYPE.REFRESH_TOKEN);
        return rejectWithValue("Token refresh failed");
      }

      try {
        const response = await getMe();
        if (response.status === 200 && response.data?.data) {
          // Lên lịch refresh token khi tải trang lần đầu
          scheduleTokenRefresh();
          return response.data;
        }
        return rejectWithValue("Invalid response");
      } catch (error) {
        const axiosError = error as AxiosError;
        if (axiosError.response?.status === 401) {
          // Token expired, try to refresh
          const refreshResult = await dispatch(refreshTokenAsync());
          if (refreshTokenAsync.fulfilled.match(refreshResult)) {
            // Retry the original request with new token
            const retryResponse = await getMe();
            if (retryResponse.status === 200 && retryResponse.data?.data) {
              return retryResponse.data;
            }
          }
          // Nếu refresh thất bại, xóa token và logout
          removeTokenFromCookies(TOKEN_TYPE.ACCESS_TOKEN);
          removeTokenFromCookies(TOKEN_TYPE.REFRESH_TOKEN);
          return rejectWithValue("Token expired");
        }
        return rejectWithValue(axiosError.response?.data);
      }
    } catch (error) {
      // Nếu có lỗi không xác định, xóa token và logout
      removeTokenFromCookies(TOKEN_TYPE.ACCESS_TOKEN);
      removeTokenFromCookies(TOKEN_TYPE.REFRESH_TOKEN);
      return rejectWithValue(error);
    }
  }
);

export const logoutAsync = createAsyncThunk(
  "auth/logoutAsync",
  async (_, { rejectWithValue }) => {
    try {
      await handleLogout();
      // Xóa lịch refresh token
      clearTokenRefreshSchedule();
      // Xóa tokens từ cookies
      removeTokenFromCookies(TOKEN_TYPE.ACCESS_TOKEN);
      removeTokenFromCookies(TOKEN_TYPE.REFRESH_TOKEN);
      toast.success("Đăng xuất thành công");
    } catch (error) {
      // Ngay cả khi API logout thất bại, vẫn xóa local state
      clearTokenRefreshSchedule();
      removeTokenFromCookies(TOKEN_TYPE.ACCESS_TOKEN);
      removeTokenFromCookies(TOKEN_TYPE.REFRESH_TOKEN);
      const axiosError = error as AxiosError;
      console.warn("Logout API failed but clearing local state:", axiosError);
      toast.success("Đăng xuất thành công");
      return rejectWithValue(axiosError.response?.data);
    }
  }
);

export const updateAuthState = (
  state: typeof initialAccount,
  data: AccountResponse
): void => {
  state.is_authenticated = true;
  state.account = data;
  setTokenToCookies(TOKEN_TYPE.ACCESS_TOKEN, data.accessToken);
  setTokenToCookies(TOKEN_TYPE.REFRESH_TOKEN, data.refreshToken);
};

interface AuthState {
  is_authenticated: boolean;
  account: AccountResponse | null;
  loading: boolean;
  error: string | null;
  currentRoleId: number | null;
}

const initialState: AuthState = {
  is_authenticated: false,
  account: null,
  loading: false,
  error: null,
  currentRoleId: null,
};

const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    setCurrentRole: (state, action: PayloadAction<number | null>) => {
      state.currentRoleId = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(loginAsync.pending, (state) => {
        state.loading = true;
      })
      .addCase(loginAsync.fulfilled, (state, action) => {
        state.loading = false;
        if (action.payload.data) {
          state.is_authenticated = true;
          state.account = action.payload.data;
          // Nếu account có roles và có role đầu tiên, set làm currentRole
          if (
            action.payload.data.roles &&
            action.payload.data.roles.length > 0
          ) {
            state.currentRoleId = action.payload.data.roles[0];
          }
          setTokenToCookies(
            TOKEN_TYPE.ACCESS_TOKEN,
            action.payload.data.accessToken
          );
          setTokenToCookies(
            TOKEN_TYPE.REFRESH_TOKEN,
            action.payload.data.refreshToken
          );
          // Lên lịch refresh token sau khi login thành công và token đã được lưu
          scheduleTokenRefresh();
        } else {
          state.is_authenticated = false;
          state.account = null;
          state.currentRoleId = null;
        }
      })
      .addCase(loginAsync.rejected, (state) => {
        state.loading = false;
        state.is_authenticated = false;
        state.account = null;
        state.currentRoleId = null;
        // Xóa lịch refresh khi login thất bại
        clearTokenRefreshSchedule();
      })
      .addCase(registerAsync.pending, (state) => {
        state.loading = true;
      })
      .addCase(registerAsync.fulfilled, (state, action) => {
        state.loading = false;
        if (action.payload.data) {
          state.is_authenticated = true;
          state.account = action.payload.data;
          // Nếu account có roles và có role đầu tiên, set làm currentRole
          if (
            action.payload.data.roles &&
            action.payload.data.roles.length > 0
          ) {
            state.currentRoleId = action.payload.data.roles[0];
          }
          // Nếu register trả về token, cũng cần lưu và lên lịch refresh
          if (
            action.payload.data.accessToken &&
            action.payload.data.refreshToken
          ) {
            setTokenToCookies(
              TOKEN_TYPE.ACCESS_TOKEN,
              action.payload.data.accessToken
            );
            setTokenToCookies(
              TOKEN_TYPE.REFRESH_TOKEN,
              action.payload.data.refreshToken
            );
            scheduleTokenRefresh();
          }
        } else {
          state.is_authenticated = false;
          state.account = null;
          state.currentRoleId = null;
        }
      })
      .addCase(registerAsync.rejected, (state) => {
        state.loading = false;
        state.is_authenticated = false;
        state.account = null;
        state.currentRoleId = null;
        // Xóa lịch refresh khi register thất bại
        clearTokenRefreshSchedule();
      })
      .addCase(forgotPasswordAsync.pending, (state) => {
        state.loading = true;
      })
      .addCase(forgotPasswordAsync.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(forgotPasswordAsync.rejected, (state) => {
        state.loading = false;
      })
      .addCase(checkAuthState.pending, (state) => {
        state.loading = true;
      })
      .addCase(checkAuthState.fulfilled, (state, action) => {
        state.loading = false;
        if (action.payload.data) {
          state.is_authenticated = true;
          state.account = action.payload.data;
          // Nếu account có roles và có role đầu tiên, set làm currentRole
          if (
            action.payload.data.roles &&
            action.payload.data.roles.length > 0
          ) {
            state.currentRoleId = action.payload.data.roles[0];
          }
        } else {
          state.is_authenticated = false;
          state.account = null;
          state.currentRoleId = null;
        }
      })
      .addCase(checkAuthState.rejected, (state) => {
        state.loading = false;
        state.is_authenticated = false;
        state.account = null;
        // Xóa lịch refresh khi check auth state thất bại
        clearTokenRefreshSchedule();
      })
      .addCase(refreshTokenAsync.pending, (state) => {
        state.loading = true;
      })
      .addCase(refreshTokenAsync.fulfilled, (state, action) => {
        state.loading = false;
        console.log("Refresh token response:", action.payload);

        // Thử cả 2 cách: direct access và nested data
        let accessToken, refreshToken;

        if (
          action.payload?.data?.accessToken &&
          action.payload?.data?.refreshToken
        ) {
          // Case 1: BaseResponse với nested data
          accessToken = action.payload.data.accessToken;
          refreshToken = action.payload.data.refreshToken;
        } else if (
          (action.payload as any)?.accessToken &&
          (action.payload as any)?.refreshToken
        ) {
          // Case 2: Direct response
          accessToken = (action.payload as any).accessToken;
          refreshToken = (action.payload as any).refreshToken;
        }

        if (accessToken && refreshToken) {
          console.log("Setting new tokens to cookies:", {
            accessToken,
            refreshToken,
          });

          setTokenToCookies(TOKEN_TYPE.ACCESS_TOKEN, accessToken);
          setTokenToCookies(TOKEN_TYPE.REFRESH_TOKEN, refreshToken);
          // Lên lịch refresh token tiếp theo sau khi refresh thành công và token mới đã được lưu
          scheduleTokenRefresh();
        } else {
          console.error(
            "Invalid refresh token response structure:",
            action.payload
          );
        }
      })
      .addCase(refreshTokenAsync.rejected, (state) => {
        state.loading = false;
        state.is_authenticated = false;
        state.account = null;
        // Xóa lịch refresh khi refresh token thất bại
        clearTokenRefreshSchedule();
      })
      .addCase(logoutAsync.pending, (state) => {
        state.loading = true;
      })
      .addCase(logoutAsync.fulfilled, (state) => {
        state.loading = false;
        state.is_authenticated = false;
        state.account = null;
        state.currentRoleId = null;
      })
      .addCase(logoutAsync.rejected, (state) => {
        state.loading = false;
        state.is_authenticated = false;
        state.account = null;
        state.currentRoleId = null;
      });
  },
});

export const { setCurrentRole } = authSlice.actions;

export default authSlice.reducer;
