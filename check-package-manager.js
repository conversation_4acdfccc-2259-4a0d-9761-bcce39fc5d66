// check-package-manager.js
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

// Get current directory for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// ANSI color codes
const colors = {
  reset: "\x1b[0m",
  red: "\x1b[31m",
  green: "\x1b[32m",
  yellow: "\x1b[33m",
  blue: "\x1b[34m",
  gray: "\x1b[90m",
  bold: "\x1b[1m",
};

function colorize(color, text) {
  return `${colors[color]}${text}${colors.reset}`;
}

// Kiểm tra user agent
const userAgent = process.env.npm_config_user_agent || "";

// Kiểm tra lockfile
const hasYarnLock = fs.existsSync(path.join(process.cwd(), "yarn.lock"));
const hasPackageLock = fs.existsSync(
  path.join(process.cwd(), "package-lock.json")
);
const hasPnpmLock = fs.existsSync(path.join(process.cwd(), "pnpm-lock.yaml"));

// Hiển thị thông tin debug
console.log(`\n🔍 Package Manager Detection:`);
console.log(`User Agent: ${userAgent}`);
console.log(`Yarn Lock: ${hasYarnLock ? "✅" : "❌"}`);
console.log(`NPM Lock: ${hasPackageLock ? "❌" : "✅"}`);
console.log(`PNPM Lock: ${hasPnpmLock ? "❌" : "✅"}`);

// Hiển thị summary
if (hasYarnLock && !hasPackageLock && !hasPnpmLock) {
  console.log(`${colorize("green", "✅ Lockfile status: Clean (Yarn only)")}`);
} else if (hasYarnLock && (hasPackageLock || hasPnpmLock)) {
  console.log(
    `${colorize("yellow", "⚠️ Lockfile status: Conflicts detected")}`
  );
} else {
  console.log(`${colorize("red", "❌ Lockfile status: Missing yarn.lock")}`);
}

// Kiểm tra nếu đang dùng npm
if (userAgent.includes("npm") && !userAgent.includes("yarn")) {
  console.error(`
${colorize("red", "🚫 ERROR: This project uses Yarn as package manager!")}

${colorize("yellow", "Please use:")}
  ${colorize("green", "yarn install")} instead of ${colorize(
    "red",
    "npm install"
  )}
  ${colorize("green", "yarn dev")} instead of ${colorize("red", "npm run dev")}
  ${colorize("green", "yarn build")} instead of ${colorize(
    "red",
    "npm run build"
  )}

${colorize("blue", "Why Yarn?")}
  - Consistent lockfile (yarn.lock)
  - Better dependency resolution
  - Faster installation
  - Team standardization

${colorize("gray", "To install Yarn: npm install -g yarn")}
`);
  process.exit(1);
}

// Kiểm tra nếu đang dùng pnpm
if (userAgent.includes("pnpm")) {
  console.error(`
${colorize("red", "🚫 ERROR: This project uses Yarn, not PNPM!")}

${colorize("yellow", "Please use:")}
  ${colorize("green", "yarn install")} instead of ${colorize(
    "red",
    "pnpm install"
  )}
  ${colorize("green", "yarn dev")} instead of ${colorize("red", "pnpm dev")}
`);
  process.exit(1);
}

// Cảnh báo nếu có lockfile conflict
if (hasPackageLock && hasYarnLock) {
  console.warn(`
${colorize(
  "yellow",
  "⚠️  WARNING: Both yarn.lock and package-lock.json found!"
)}
This can cause dependency conflicts.

${colorize("blue", "To fix:")}
  1. ${colorize("green", "rm package-lock.json")}
  2. ${colorize("green", "yarn install")}
`);
}

if (hasPnpmLock && hasYarnLock) {
  console.warn(`
${colorize("yellow", "⚠️  WARNING: Both yarn.lock and pnpm-lock.yaml found!")}
This can cause dependency conflicts.

${colorize("blue", "To fix:")}
  1. ${colorize("green", "rm pnpm-lock.yaml")}
  2. ${colorize("green", "yarn install")}
`);
}

// Kiểm tra nếu không có yarn.lock nhưng đang cài đặt
if (!hasYarnLock && !userAgent.includes("yarn")) {
  console.error(`
${colorize("red", "🚫 ERROR: No yarn.lock found!")}

This project requires Yarn. Please run:
  ${colorize("green", "yarn install")}
`);
  process.exit(1);
}

// Thành công
if (userAgent.includes("yarn")) {
  console.log(`${colorize("green", "✅ Using Yarn - Good!")}\n`);
}
