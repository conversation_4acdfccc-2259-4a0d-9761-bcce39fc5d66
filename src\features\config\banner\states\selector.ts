import { createSelector } from "@reduxjs/toolkit";
import type { RootState } from "@/store/rootReducer";

// Base selectors
export const selectBannerState = (state: RootState) => state.bannerState;

export const selectBannerData = createSelector(
  [selectBannerState],
  (bannerState) => bannerState.data
);

export const selectBannerLoading = createSelector(
  [selectBannerState],
  (bannerState) => bannerState.loading
);

export const selectBannerSaving = createSelector(
  [selectBannerState],
  (bannerState) => bannerState.saving
);

export const selectBannerError = createSelector(
  [selectBannerState],
  (bannerState) => bannerState.error
);

export const selectBannerIsDirty = createSelector(
  [selectBannerState],
  (bannerState) => bannerState.isDirty
);
