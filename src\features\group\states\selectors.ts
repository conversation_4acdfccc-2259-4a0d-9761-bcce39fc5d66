import { RootState } from "@/store/rootReducer";
import { createSelector } from "@reduxjs/toolkit";
import { GroupTree, Group } from "./types";

export const selectGroupState = (state: RootState) => state.groupState;

export const selectGroupData = (state: RootState) => state.groupState.data;

export const selectGroupSelectedId = (state: RootState) =>
  state.groupState.selectedId;

export const selectGroupLoading = (state: RootState) =>
  state.groupState.loading;

export const selectGroupError = (state: RootState) => state.groupState.error;

export const selectGroupRoles = (state: RootState) => state.groupState.roles;

// Get roles for specific group
export const selectGroupRolesByGroupId = createSelector(
  [selectGroupRoles, (_state: RootState, groupId: number) => groupId],
  (roles, groupId) => roles[groupId] || []
);

// Get selected group
export const selectSelectedGroup = createSelector(
  [selectGroupData, selectGroupSelectedId],
  (groups, selectedId) =>
    groups.find((group) => group.id === selectedId) || null
);

// Build group tree structure
export const selectGroupTree = createSelector(
  [selectGroupData],
  (groups): GroupTree[] => {
    const groupMap = new Map<number, GroupTree>();

    // Convert groups to tree nodes
    groups.forEach((group: Group) => {
      groupMap.set(group.id, { ...group, children: [] });
    });

    const rootGroups: GroupTree[] = [];

    // Build parent-child relationships
    groups.forEach((group: Group) => {
      const groupNode = groupMap.get(group.id)!;

      if (group.parentId === null) {
        rootGroups.push(groupNode);
      } else {
        const parent = groupMap.get(group.parentId);
        if (parent) {
          parent.children.push(groupNode);
        }
      }
    });

    return rootGroups;
  }
);

// AdminGroupHasRoleController selectors
export const selectAssignedRoleIds = (state: RootState) =>
  state.groupState.assignedRoleIds;

export const selectAssignedRolesLoading = (state: RootState) =>
  state.groupState.assignedRolesLoading;

export const selectCurrentTab = (state: RootState) =>
  state.groupState.currentTab;

export const selectCurrentAction = (state: RootState) =>
  state.groupState.currentAction;

// Get assigned role IDs for specific group
export const selectAssignedRoleIdsByGroupId = createSelector(
  [selectAssignedRoleIds, (_state: RootState, groupId: number) => groupId],
  (assignedRoleIds, groupId) => assignedRoleIds[groupId] || []
);

// Get loading state for assigned roles for specific group
export const selectAssignedRolesLoadingByGroupId = createSelector(
  [selectAssignedRolesLoading, (_state: RootState, groupId: number) => groupId],
  (assignedRolesLoading, groupId) => assignedRolesLoading[groupId] || false
);

// Get first group in tree for auto-selection
export const selectFirstGroup = createSelector(
  [selectGroupTree],
  (groupTree): Group | null => {
    if (groupTree.length === 0) return null;

    // Get first root group
    const firstRoot = groupTree[0];
    return {
      id: firstRoot.id,
      createdAt: firstRoot.createdAt,
      updatedAt: firstRoot.updatedAt,
      parentId: firstRoot.parentId,
      code: firstRoot.code,
      name: firstRoot.name,
      metadata: firstRoot.metadata,
    };
  }
);
