import React from "react";
import { X, LogOut } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useAppSelector, useAppDispatch } from "@/store/rootReducer";
import { selectAuthState } from "@/features/auth/states/selector";
import { logoutAsync } from "@/features/auth/states/slices";
import { PublicMenuVertical } from "./PublicMenuVertical";
import { RoleSelector } from "./RoleSelector";
import { Button } from "@/components/ui/button";
import { Link } from "react-router-dom";
import { setMobileMenuOpen } from "@/features/layouts/states/slices";
import { selectMobileMenuOpen } from "@/features/layouts/states/selector";

export const MobileSlideMenu: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { is_authenticated, account } = useAppSelector(selectAuthState);
  const isOpen = useAppSelector(selectMobileMenuOpen);

  const handleClose = () => {
    dispatch(setMobileMenuOpen(false));
  };

  const handleLogout = async () => {
    try {
      // Close menu first
      handleClose();

      // Dispatch logout action and wait for completion
      const result = await dispatch(logoutAsync());

      // Navigate to home page after successful logout
      if (logoutAsync.fulfilled.match(result)) {
        navigate("/", { replace: true });
      } else {
        // Even if logout API fails, still navigate to home
        navigate("/", { replace: true });
      }
    } catch (error) {
      console.error("Logout error:", error);
      // Still navigate to home even if there's an error
      navigate("/", { replace: true });
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[1000] lg:hidden">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black/50 backdrop-blur-sm"
        onClick={handleClose}
      />

      {/* Slide Menu */}
      <div className="absolute right-0 top-0 h-full w-[300px] bg-white shadow-lg">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b">
          <h2 className="text-lg font-semibold">Menu</h2>
          <button
            onClick={handleClose}
            className="p-1.5 hover:bg-gray-100 rounded-md transition-colors"
            aria-label="Đóng menu"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto">
          {/* User Info Section - Show at top when authenticated */}
          {is_authenticated && (
            <div className="p-4 border-b bg-gradient-to-r from-primary-50 to-primary-100">
              <div className="flex items-center gap-3">
                {/* Clickable User Info Area */}
                <div
                  className="flex items-center gap-3 flex-1 cursor-pointer hover:bg-white/20 rounded-lg p-2 transition-colors"
                  onClick={() => {
                    handleClose();
                    navigate("/me");
                  }}
                >
                  {account?.metadata?.avatar ? (
                    <img
                      src={account.metadata.avatar as string}
                      alt={account.fullName || "User"}
                      className="w-12 h-12 rounded-full object-cover border-2 border-white shadow-sm"
                    />
                  ) : (
                    <div className="w-12 h-12 rounded-full bg-gray-700 text-white flex items-center justify-center font-semibold text-lg shadow-sm">
                      {(account?.fullName || "U").charAt(0).toUpperCase()}
                    </div>
                  )}

                  <div className="flex-1 min-w-0">
                    <p className="font-semibold text-base text-gray-900 truncate">
                      {account?.fullName || "Người dùng"}
                    </p>
                    <p className="text-sm text-gray-500 truncate">
                      {account?.email || ""}
                    </p>
                  </div>
                </div>

                {/* Logout Icon Button */}
                <button
                  onClick={handleLogout}
                  className="p-2 rounded-lg hover:bg-red-50 text-red-600 transition-colors"
                  aria-label="Đăng xuất"
                >
                  <LogOut className="w-5 h-5" />
                </button>
              </div>
            </div>
          )}

          {/* Login/Register buttons - Show at top when not authenticated */}
          {!is_authenticated && (
            <div className="p-4 border-b bg-gray-50">
              <div className="space-y-3">
                <Link to="/auth/login" onClick={handleClose}>
                  <Button variant="default" size="sm" className="w-full">
                    Đăng nhập
                  </Button>
                </Link>
                <Link to="/auth/register" onClick={handleClose}>
                  <Button variant="ghost" size="sm" className="w-full">
                    Đăng ký
                  </Button>
                </Link>
              </div>
            </div>
          )}

          {/* Role Selector - Show when authenticated */}
          {is_authenticated && (
            <div className="p-4 border-b">
              <RoleSelector onSelect={handleClose} />
            </div>
          )}

          {/* Public Menu */}
          <div className="p-4">
            <PublicMenuVertical onMenuClick={handleClose} />
          </div>
        </div>
      </div>
    </div>
  );
};
