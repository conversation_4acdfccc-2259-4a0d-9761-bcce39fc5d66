import React from "react";
import { BasicInputProps } from "@/components/form/registry";
import { DropDownTextWithSearch } from "@/components/form/ui/DropdownTextWithSearch";
import { componentRegistry } from "../registry/ComponentRegistry";

export interface ComponentOption {
  value: string;
  label: string;
  description?: string;
  category?: "admin" | "public" | "auth";
  requiresAuth?: boolean;
}

/**
 * 🚀 Component Options Generator
 *
 * ✅ Auto-sync with componentRegistry
 * ✅ Categorized options for better UX
 * ✅ Metadata support (auth, description)
 */
export const getComponentOptions = (): ComponentOption[] => {
  const registeredNames = componentRegistry.getRegisteredNames();


  // Component display configurations
  const componentConfigs: Record<string, Omit<ComponentOption, "value">> = {
    // 🌐 Public Components
    Page: {
      label: "Trang nội dung",
      description: "Trang hiển thị nội dung thông thường",
      category: "public",
    },
    OrganizationStructurePage: {
      label: "<PERSON><PERSON> cấu tổ chức",
      description: "C<PERSON> cấu tổ chức",
      category: "public",
    },

    // 🔐 Auth Components
    LoginPage: {
      label: "Trang đăng nhập",
      description: "Form đăng nhập người dùng",
      category: "auth",
    },
    RegisterPage: {
      label: "Trang đăng ký",
      description: "Form đăng ký tài khoản mới",
      category: "auth",
    },

    // 🛠️ Admin Components
    AdminPage: {
      label: "Trang chủ quản trị",
      description: "Dashboard tổng quan hệ thống",
      category: "admin",
      requiresAuth: true,
    },
    CategoryAdminPage: {
      label: "Quản lý chuyên mục",
      description: "CRUD operations cho categories",
      category: "admin",
      requiresAuth: true,
    },
    PageAdminPage: {
      label: "Quản lý trang",
      description: "CRUD operations cho pages",
      category: "admin",
      requiresAuth: true,
    },
    PostAdminPage: {
      label: "Quản lý bài viết",
      description: "CRUD operations cho posts",
      category: "admin",
      requiresAuth: true,
    },
    ImageAdminPage: {
      label: "Quản lý ảnh",
      description: "Upload và quản lý media images",
      category: "admin",
      requiresAuth: true,
    },

    UserAdminPage: {
      label: "Quản lý người dùng",
      description: "CRUD operations cho users",
      category: "admin",
      requiresAuth: true,
    },
    GroupAdminPage: {
      label: "Quản lý nhóm",
      description: "CRUD operations cho user groups",
      category: "admin",
      requiresAuth: true,
    },
    RoleAdminPage: {
      label: "Quản lý vai trò",
      description: "CRUD operations cho roles",
      category: "admin",
      requiresAuth: true,
    },
    ActionAdminPage: {
      label: "Quản lý quyền",
      description: "CRUD operations cho actions",
      category: "admin",
      requiresAuth: true,
    },
    ResourceAdminPage: {
      label: "Quản lý nhóm quyền",
      description: "CRUD operations cho resources",
      category: "admin",
      requiresAuth: true,
    },
    PermissionAdminPage: {
      label: "Quản lý phân quyền",
      description: "CRUD operations cho permissions",
      category: "admin",
      requiresAuth: true,
    },
    QuestionAdminPage: {
      label: "Quản Lý Hỏi Đáp",
      description: "CRUD operations cho Q&A system",
      category: "admin",
      requiresAuth: true,
    },
    QuestionConfigAdminPage: {
      label: "Cấu Hình Hỏi Đáp",
      description: "Configuration settings cho Q&A system",
      category: "admin",
      requiresAuth: true,
    },
    QuestionListAdminPage: {
      label: "Danh Sách Câu Hỏi",
      description: "Xem và quản lý danh sách câu hỏi từ người dùng",
      category: "admin",
      requiresAuth: true,
    },
    DashboardPage: {
      label: "Dashboard",
      description: "Trang tổng quan hệ thống với thống kê và biểu đồ",
      category: "admin",
      requiresAuth: true,
    },
    RecordRegisterAdminPage: {
      label: "Quản lý đăng ký lưu trữ",
      description:
        "Quản lý và theo dõi các yêu cầu đăng ký lưu trữ hồ sơ tài liệu",
      category: "admin",
      requiresAuth: true,
    },
    CreateSubmissionPage: {
      label: "Tạo gói tin nộp lưu",
      description: "Tạo và quản lý gói tin nộp lưu hồ sơ tài liệu",
      category: "admin",
      requiresAuth: true,
    },
    QAManagementPage: {
      label: "Quản lý Q&A",
      description: "Quản lý hệ thống hỏi đáp và phản hồi người dùng",
      category: "admin",
      requiresAuth: true,
    },
    CopyrightManagementPage: {
      label: "Quản lý Bản quyền",
      description: "Quản lý thông tin bản quyền và điều khoản sử dụng",
      category: "admin",
      requiresAuth: true,
    },
    ContactManagementPage: {
      label: "Quản lý Liên hệ",
      description: "Quản lý thông tin liên hệ và phản hồi từ người dùng",
      category: "admin",
      requiresAuth: true,
    },
    BannerManagementPage: {
      label: "Quản lý Banner",
      description: "Quản lý hình ảnh banner và slider trên trang chủ",
      category: "admin",
      requiresAuth: true,
    },
    HotlineManagementPage: {
      label: "Quản lý Đường dây nóng",
      description: "Quản lý thông tin đường dây nóng và hỗ trợ khẩn cấp",
      category: "admin",
      requiresAuth: true,
    },
    IntroductionManagementPage: {
      label: "Quản lý Giới thiệu",
      description: "Quản lý nội dung giới thiệu về tổ chức và dịch vụ",
      category: "admin",
      requiresAuth: true,
    },
    PortalLinkManagementPage: {
      label: "Quản lý Đường link",
      description: "Quản lý các liên kết và điều hướng trong portal",
      category: "admin",
      requiresAuth: true,
    },
    DemoPage: {
      label: "Trang thử nghiệm",
      description: "Trang demo cho việc phát triển và kiểm thử",
      category: "admin",
      requiresAuth: true,
    },
    UserProfilePage: {
      label: "Trang cá nhân",
      description: "Trang cá nhân của người dùng",
      category: "public",
      requiresAuth: true,
    },
  };

  // Generate options from registry + configs
  const options: ComponentOption[] = [
    {
      value: "",
      label: "Chọn Component",
      description: "Vui lòng chọn component để hiển thị",
    },
  ];

  registeredNames.forEach((name) => {
    const config = componentConfigs[name];
    if (config) {
      options.push({
        value: name,
        ...config,
      });
    } else {
      // Fallback for components not in config
      options.push({
        value: name,
        label: name,
        description: `Component ${name}`,
        category: "admin",
      });
    }
  });

  // Sort by category and label
  return options.sort((a, b) => {
    if (a.value === "") return -1;
    if (b.value === "") return 1;

    const categoryOrder = { public: 1, auth: 2, admin: 3 };
    const aOrder = categoryOrder[a.category || "admin"];
    const bOrder = categoryOrder[b.category || "admin"];

    if (aOrder !== bOrder) return aOrder - bOrder;
    return a.label.localeCompare(b.label);
  });
};

type ComponentPickerProps = BasicInputProps<"text">;

/**
 * 🎯 Component Picker for Categories
 *
 * ✅ Auto-synced with componentRegistry
 * ✅ Better UX with descriptions
 * ✅ Type-safe component selection
 */
export const ComponentPicker = React.forwardRef<
  HTMLDivElement,
  ComponentPickerProps
>(({ isViewMode, value, ...rest }, ref) => {
  const options = getComponentOptions();
  const optionValues = options.map((opt) => opt.value);
  const optionLabels = options.map((opt) =>
    opt.description ? `${opt.label} - ${opt.description}` : opt.label
  );

  return (
    <DropDownTextWithSearch
      isViewMode={isViewMode}
      value={value}
      ref={ref}
      {...rest}
      options={optionValues}
      labels={optionLabels}
      placeholder="Tìm kiếm component..."
    />
  );
});

ComponentPicker.displayName = "ComponentPicker";

export default ComponentPicker;
