import React from "react";
import { PortalLinkDisplay } from "../../shared/components/displays";
import { PreviewWrapper } from "../../shared/components/wrappers/PreviewWrapper";
import type { PortalLinkConfig } from "../states/type";

/**
 * Props for the PortalLinkPreview component
 */
interface PortalLinkPreviewProps {
  /** Portal link configuration data to preview */
  data: PortalLinkConfig | null;
  /** Whether to show the preview */
  visible: boolean;
}

/**
 * PortalLinkPreview component displays clean portal link grid
 * without highlighting effects, suitable for reuse in public pages
 */
export const PortalLinkPreview: React.FC<PortalLinkPreviewProps> = ({
  data,
  visible,
}) => {
  if (!visible || !data?.portalLinks || data.portalLinks.length === 0) {
    return null;
  }

  return (
    <PreviewWrapper
      title="Xem trước: Cổng liên kết"
      description="Hiển thị cổng liên kết như sẽ xuất hiện trên trang công khai."
      theme="blue"
      visible={visible}
    >
      <PortalLinkDisplay
        links={data.portalLinks}
        itemsPerPage={3}
        title="LIÊN KẾT CÁC CỔNG"
        showPagination={true}
      />
    </PreviewWrapper>
  );
};


