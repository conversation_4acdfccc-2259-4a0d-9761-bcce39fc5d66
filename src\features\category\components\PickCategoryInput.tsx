import React from "react";
import { useAppSelector } from "@/store/rootReducer";
import { selectCategorySelectedType } from "../states/selector";
import { DropDownNumberWithSearch } from "@/components/form/ui/DropDownNumberWithSearch";
import { BasicInputProps } from "@/components/form/registry";
import { useCategory } from "../hooks/useCategoryData";

type PickCategoryInputProps = BasicInputProps<"number">;

export const PickCategoryInput = React.forwardRef<
  HTMLDivElement,
  PickCategoryInputProps
>(({ ...rest }, ref) => {
  const selectedType = useAppSelector(selectCategorySelectedType);
  const { data: allCategories } = useCategory(selectedType);

  const options = allCategories.map((cat) => cat.id);
  const labels = allCategories.map((cat) => cat.name);

  return (
    <DropDownNumberWithSearch
      ref={ref}
      {...rest}
      options={[0, ...options]}
      labels={["Chọn chuyên mục cha", ...labels]}
    />
  );
});

PickCategoryInput.displayName = "PickCategoryInput";
