import { useNavigate, useLocation } from "react-router-dom";
import { useAppDispatch } from "@/store/rootReducer";
import { setPathParams } from "./slices";
import { ActionPageMode, ActionAction } from "./types";

export const useSwitchMode = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useAppDispatch();

  return (
    mode: ActionPageMode,
    actionId?: string,
    actionType?: ActionAction
  ) => {
    const searchParams = new URLSearchParams(location.search);

    searchParams.set("mode", mode);

    if (actionId) {
      searchParams.set("action", actionId);
    } else {
      searchParams.delete("action");
    }

    if (actionType) {
      searchParams.set("actionType", actionType);
    } else {
      searchParams.delete("actionType");
    }

    // Update Redux state
    dispatch(
      setPathParams({
        mode,
        actionType: actionType || "view",
      })
    );

    // Navigate to new URL
    navigate(`${location.pathname}?${searchParams.toString()}`);
  };
};
