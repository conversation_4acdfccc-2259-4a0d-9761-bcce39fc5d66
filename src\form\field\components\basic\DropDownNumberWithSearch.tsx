/**
 * DropDownNumberWithSearch - Number dropdown with search functionality for AutoForm
 */

import React from "react";
import { FieldComponentProps } from "../../FieldFactory";
import { cn } from "@/lib/utils";

export const DropDownNumberWithSearch: React.FC<FieldComponentProps> = ({
  value,
  onChange,
  onBlur,
  disabled = false,
  placeholder = "Chọn giá trị",
  options = [],
  labels = [],
  id,
  className = "",
}) => {
  const [open, setOpen] = React.useState(false);
  const [search, setSearch] = React.useState("");
  const containerRef = React.useRef<HTMLDivElement>(null);

  // Đóng dropdown khi click ngoài
  React.useEffect(() => {
    if (!open) return;
    const onClick = (e: MouseEvent | TouchEvent) => {
      if (
        containerRef.current &&
        !containerRef.current.contains(e.target as Node)
      ) {
        setOpen(false);
      }
    };
    document.addEventListener("mousedown", onClick);
    document.addEventListener("touchstart", onClick);
    return () => {
      document.removeEventListener("mousedown", onClick);
      document.removeEventListener("touchstart", onClick);
    };
  }, [open]);

  // Convert options to numbers, handle string/number options
  const numberOptions = options.map((opt) => {
    if (typeof opt === "number") return opt;
    if (typeof opt === "string") {
      const num = Number(opt);
      return isNaN(num) ? 0 : num;
    }
    return 0;
  });

  const items =
    labels.length === numberOptions.length
      ? labels.map((label, idx) => ({ label, value: numberOptions[idx] }))
      : numberOptions.map((opt) => ({ label: String(opt), value: opt }));

  const filteredItems = items.filter((item) =>
    item.label.toLowerCase().includes(search.toLowerCase())
  );

  // Handle current value - support null/undefined
  const currentValue =
    typeof value === "number" ? value : value === null ? 0 : Number(value) || 0;
  const currentLabel =
    currentValue === 0
      ? placeholder
      : items.find((item) => item.value === currentValue)?.label ??
        String(currentValue);

  const handleOptionClick = disabled
    ? undefined
    : (itemValue: number) => {
        setOpen(false);
        setSearch("");
        if (itemValue !== currentValue) {
          // Handle null case - if value is 0, might want to send null
          onChange(itemValue === 0 ? 0 : itemValue);
        }
      };

  return (
    <div ref={containerRef} className={cn("relative w-full", className)}>
      <button
        type="button"
        className={cn(
          "w-full text-left px-3 py-1 border rounded-md transition focus-visible:border-ring focus-visible:ring-ring/40 focus-visible:ring-2 outline-none",
          "text-base shadow-xs min-h-9",
          disabled
            ? "bg-muted opacity-50 cursor-not-allowed"
            : "hover:border-primary"
        )}
        onClick={() => !disabled && setOpen((v) => !v)}
        onBlur={onBlur}
        disabled={disabled}
        id={id}
      >
        {currentLabel || (
          <span className="text-muted-foreground">{placeholder}</span>
        )}
      </button>
      {open && (
        <div
          className={cn(
            "absolute z-20 left-0 right-0 mt-1 border rounded-md bg-popover shadow-lg overflow-auto",
            "max-h-60 animate-in fade-in-80"
          )}
        >
          <input
            className={cn(
              "w-full px-3 py-2 border-b text-base outline-none",
              "focus-visible:border-ring"
            )}
            placeholder="Tìm kiếm..."
            value={search}
            autoFocus
            onChange={(e) => setSearch(e.target.value)}
          />
          {filteredItems.length === 0 && (
            <div className="px-3 py-1 text-muted-foreground text-sm">
              Không có kết quả
            </div>
          )}
          {filteredItems.map((item) => (
            <div
              key={item.value}
              className={cn(
                "px-3 py-2 cursor-pointer transition select-none",
                "hover:bg-accent hover:text-accent-foreground",
                item.value === currentValue ? "bg-primary/10 font-semibold" : ""
              )}
              onClick={() => handleOptionClick?.(item.value)}
            >
              {item.label}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};
