import { ColumnConfig } from "@/components/table/registry";

export const RESOURCE_COLUMNS: ColumnConfig[] = [
  {
    accessorKey: "id",
    header: "ID",
  },
  {
    accessorKey: "code",
    header: "<PERSON>ã nhóm quyền",
    cell: {
      component: "TextCell",
      props: {},
    },
  },
  {
    accessorKey: "name",
    header: "Tên nhóm quyền",
    cell: {
      component: "TextCell",
      props: {},
    },
  },
  {
    accessorKey: "actions.length",
    header: "S<PERSON> lượng quyền",
    cell: {
      component: "TextCell",
      props: {},
    },
  },
  {
    accessorKey: "createdAt",
    header: "Ngày tạo",
    cell: {
      component: "DateTimeCell",
      props: {
        format: "dd/MM/yyyy HH:mm",
      },
    },
  },
  {
    accessorKey: "updatedAt",
    header: "<PERSON><PERSON><PERSON> cập nhật",
    cell: {
      component: "DateTimeCell",
      props: {
        format: "dd/MM/yyyy HH:mm",
      },
    },
  },
  {
    accessorKey: "tableActions",
    header: "Thao tác",
    cell: {
      component: "ActionCell",
      props: {
        actions: ["edit"],
      },
    },
  },
];
