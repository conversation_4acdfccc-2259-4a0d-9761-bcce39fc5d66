import React from "react";
import { AlertCircle } from "lucide-react";
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";

// ============================================================================
// Types
// ============================================================================

interface FieldErrorProps {
  /** Error message to display */
  error?: string;
  /** Additional CSS classes */
  className?: string;
  /** Whether to display as tooltip (default: false for inline) */
  asTooltip?: boolean;
  /** Child element to wrap with tooltip (required when asTooltip=true) */
  children?: React.ReactNode;
}

// ============================================================================
// Component
// ============================================================================

/**
 * FieldError component for displaying validation error messages
 * Can show as inline error or as tooltip
 */
export const FieldError: React.FC<FieldErrorProps> = ({
  error,
  className = "",
  asTooltip = false,
  children
}) => {
  if (!error) return asTooltip ? <>{children}</> : null;

  // Tooltip version - wraps children with error tooltip
  if (asTooltip && children) {
    return (
      <Tooltip>
        <TooltipTrigger asChild>
          {children}
        </TooltipTrigger>
        <TooltipContent
          side="top"
          className="max-w-xs !bg-red-600 !text-white !border-red-600 text-xs"
          sideOffset={5}
        >
          <div className="flex items-center gap-1">
            <AlertCircle className="h-3 w-3 flex-shrink-0" />
            <span className="break-words">{error}</span>
          </div>
        </TooltipContent>
      </Tooltip>
    );
  }

  // Inline version (original behavior)
  return (
    <div className={`flex items-center gap-1 text-sm text-red-600 mt-1 ${className}`}>
      <AlertCircle className="h-3 w-3 flex-shrink-0" />
      <span>{error}</span>
    </div>
  );
};

export default FieldError;
