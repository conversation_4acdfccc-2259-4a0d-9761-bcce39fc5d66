import React from "react";
import { HotlineInfoDisplay } from "../../shared/components/displays";
import { PreviewWrapper } from "../../shared/components/wrappers/PreviewWrapper";
import type { HotlineConfig } from "../states/type";

/**
 * Props for the HotlinePreview component
 */
interface HotlinePreviewProps {
  /** Hotline configuration data to preview */
  data: HotlineConfig | null;
  /** Whether to show the preview */
  visible: boolean;
}

/**
 * HotlinePreview component displays clean hotline information
 * without highlighting effects, suitable for reuse in public pages
 */
export const HotlinePreview: React.FC<HotlinePreviewProps> = ({
  data,
  visible,
}) => {
  if (!visible || !data?.hotlines || data.hotlines.length === 0) {
    return null;
  }

  return (
    <PreviewWrapper
      title="Xem trước: Đường dây nóng"
      description="Hiển thị thông tin đường dây nóng như sẽ xuất hiện trên trang công khai."
      theme="blue"
      visible={visible}
    >
      <HotlineInfoDisplay
        hotlines={data.hotlines}
      />
    </PreviewWrapper>
  );
};


