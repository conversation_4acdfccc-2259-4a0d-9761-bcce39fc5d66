/**
 * DropdownInput - Dropdown select component for AutoForm using Shadcn Select
 */

import React from "react";
import { FieldComponentProps } from "../../FieldFactory";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

export const DropdownInput: React.FC<FieldComponentProps> = ({
  value,
  onChange,
  onBlur,
  disabled = false,
  placeholder = "Chọn giá trị",
  options = [],
  labels = [],
  id,
  className = "",
}) => {
  // Convert options to strings for Select component
  const stringOptions = options.map((opt) => String(opt));
  const displayLabels =
    labels.length === stringOptions.length ? labels : stringOptions;
  const currentValue = String(value || "");

  const handleValueChange = disabled
    ? undefined
    : (val: string) => {
        // Convert back to original type if needed
        const originalOption = options.find((opt) => String(opt) === val);
        onChange(originalOption !== undefined ? originalOption : val);
      };

  return (
    <div className={className}>
      <Select
        value={currentValue}
        onValueChange={handleValueChange}
        disabled={disabled}
      >
        <SelectTrigger id={id} className="w-full" onBlur={onBlur}>
          <SelectValue placeholder={placeholder} />
        </SelectTrigger>
        <SelectContent>
          {stringOptions.map((option, index) => (
            <SelectItem key={option} value={option}>
              {displayLabels[index]}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
};
