/**
 * RenderNodeByType - Utility function to render nodes by type
 */

import React from "react";
import { FormNode } from "../types";
import { NODE_REGISTRY } from "./NodeRegistry";

// Helper function để render node dựa trên type
export const renderNodeByType = (node: FormNode): React.ReactElement => {
  const Component = NODE_REGISTRY[node.type];

  if (!Component) {
    return (
      <div
        key={node.id}
        className="p-2 border border-red-300 bg-red-50 text-red-700"
      >
        Unknown node type: {node.type}
      </div>
    );
  }

  return <Component key={node.id} node={node} />;
};
