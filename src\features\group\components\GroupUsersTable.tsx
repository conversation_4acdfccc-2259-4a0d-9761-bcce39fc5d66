import React, { useMemo } from "react";
import { UserData } from "@/features/users/states/type";
import { AutoTable } from "@/components/table/AutoTable";
import { GROUP_USERS_COLUMNS } from "../states/table";

interface GroupUsersTableProps {
  users: UserData[];
  loading: boolean;
  currentPage: number;
  pageSize: number;
  onPageChange: (pageIndex: number) => void;
  onPageSizeChange: (newPageSize: number) => void;
  onAction: (action: string, user: UserData) => void;
}

export const GroupUsersTable: React.FC<GroupUsersTableProps> = ({
  users,
  loading,
  currentPage,
  pageSize,
  onPageChange,
  onPageSizeChange,
  onAction,
}) => {
  // Prepare data for AutoTable
  const tableData = useMemo(() => {
    const result = users.map((user) => ({
      ...user,
      userName: `@${user.userName}`,
      fullName: user.fullName || "<PERSON><PERSON><PERSON> cập nhật",
    }));
    return result;
  }, [users]);

  // Pagination props for AutoTable
  const pagination = {
    pageIndex: currentPage,
    pageSize: pageSize,
    totalCount: 0, // Set to 0 like other modules since server doesn't provide real total
    onPageChange,
    onPageSizeChange,
  };

  if (loading) {
    return (
      <div className="text-center py-8 text-gray-500">Đang tải dữ liệu...</div>
    );
  }

  if (users.length === 0) {
    return (
      <div className="text-center text-muted-foreground py-10">
        Chưa có người dùng nào trong nhóm này
      </div>
    );
  }

  return (
    <AutoTable<UserData>
      columns={GROUP_USERS_COLUMNS}
      data={tableData}
      pagination={pagination}
      onAction={onAction}
    />
  );
};
