/**
 * Collapsible Section Component
 * Reusable collapsible section for properties forms
 */

import React from 'react';
import { ChevronDown, ChevronRight } from 'lucide-react';
import { cn } from '@/lib/utils';

interface CollapsibleSectionProps {
  title: string;
  isOpen: boolean;
  onToggle: () => void;
  children: React.ReactNode;
  disabled?: boolean;
  badge?: string;
  className?: string;
}

export const CollapsibleSection: React.FC<CollapsibleSectionProps> = ({
  title,
  isOpen,
  onToggle,
  children,
  disabled = false,
  badge,
  className
}) => {
  return (
    <div className={cn(
      "bg-white border border-gray-200 rounded-lg overflow-hidden transition-all shadow-sm",
      isOpen 
        ? "border-blue-200 shadow-md" 
        : "hover:border-gray-300",
      className
    )}>
      {/* Section Header */}
      <button
        onClick={onToggle}
        disabled={disabled}
        className={cn(
          "w-full flex items-center justify-between py-2 px-3 text-left transition-colors",
          isOpen 
            ? "bg-blue-50 hover:bg-blue-100 border-b border-blue-100" 
            : "hover:bg-gray-50",
          disabled && "opacity-50 cursor-not-allowed hover:bg-white"
        )}
      >
        <div className="flex items-center gap-2">
          <span className={cn(
            "text-xs font-medium",
            isOpen ? "text-blue-700" : "text-gray-700"
          )}>
            {title}
          </span>
        </div>
        
        <div className="flex items-center">
          {isOpen ? (
            <ChevronDown className={cn(
              "h-3 w-3 transition-colors",
              isOpen ? "text-blue-600" : "text-gray-500"
            )} />
          ) : (
            <ChevronRight className={cn(
              "h-3 w-3 transition-colors", 
              isOpen ? "text-blue-600" : "text-gray-500"
            )} />
          )}
        </div>
      </button>
      
      {/* Section Content */}
      {isOpen && (
        <div className="p-3 bg-gray-50/50">
          {children}
        </div>
      )}
    </div>
  );
};