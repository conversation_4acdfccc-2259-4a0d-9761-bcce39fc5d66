import React, { useState, useEffect } from "react";
import { useAppDispatch, useAppSelector } from "@/store/rootReducer";
import { selectCurrentRole } from "../states/selectors";
import { setCurrentRole } from "../states/slices";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogTrigger,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { RoleAction } from "../states/slices";
import { Edit, Save, X, Trash2 } from "lucide-react";
import { useSwitchMode } from "../states/hook";
import { toast } from "sonner";
import { useLocation, useNavigate } from "react-router-dom";
import { createRole<PERSON><PERSON>, updateRole<PERSON><PERSON>, deleteRole<PERSON><PERSON> } from "../states/api";
import { RoleData } from "../states/type";

interface RoleFormProps {
  currentAction: RoleAction;
  onActionChange: (action: RoleAction) => void;
}

export const RoleForm: React.FC<RoleFormProps> = ({
  currentAction,
  onActionChange,
}) => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const switchMode = useSwitchMode();
  const currentRole = useAppSelector(selectCurrentRole);

  const [formData, setFormData] = useState({
    code: "",
    name: "",
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [confirmDelete, setConfirmDelete] = useState(false);

  // Initialize form data when role changes or mode changes
  useEffect(() => {
    if (currentAction === "create") {
      setFormData({
        code: "",
        name: "",
      });
    } else if (
      currentRole &&
      (currentAction === "edit" || currentAction === "view")
    ) {
      setFormData({
        code: currentRole.code,
        name: currentRole.name,
      });
    }
  }, [currentRole, currentAction]);

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (isSubmitting) return;

    // Validation
    if (!formData.code.trim() || !formData.name.trim()) {
      toast.error("Vui lòng nhập đầy đủ thông tin");
      return;
    }

    setIsSubmitting(true);
    try {
      const payload = {
        code: formData.code,
        name: formData.name,
        metadata: {},
      };

      if (currentAction === "create") {
        const result = await createRoleApi(payload);
        if (result.status === 200) {
          const created = result.data.data as RoleData;
          dispatch(setCurrentRole(created));

          // Update URL to reflect the new role
          const searchParams = new URLSearchParams(location.search);
          searchParams.set("mode", "detail");
          searchParams.set("role", created.id.toString());
          searchParams.set("actionType", "view");
          navigate(`${location.pathname}?${searchParams.toString()}`);

          onActionChange("view");
          toast.success("Tạo vai trò thành công");
        }
      } else if (currentAction === "edit" && currentRole) {
        const result = await updateRoleApi(currentRole.id, payload);
        if (result.status === 200) {
          const updated = result.data.data as RoleData;
          dispatch(setCurrentRole(updated));
          onActionChange("view");
          toast.success("Cập nhật vai trò thành công");
        }
      }
    } catch (error) {
      console.error("Error saving role:", error);
      toast.error(
        "Có lỗi xảy ra khi lưu vai trò. Bạn không thể xóa vai trò đang được sử dụng"
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    if (currentAction === "create") {
      // Nếu đang tạo mới, quay về danh sách
      switchMode("list");
    } else if (currentAction === "edit") {
      onActionChange("view");
      // Reset form data
      if (currentRole) {
        setFormData({
          code: currentRole.code,
          name: currentRole.name,
        });
      }
    }
  };

  const handleDelete = async () => {
    if (!currentRole) return;

    setIsSubmitting(true);
    try {
      await deleteRoleApi(currentRole.id);
      switchMode("list");
      toast.success("Xóa vai trò thành công");
    } catch (error) {
      console.error("Error deleting role:", error);
      toast.error("Có lỗi xảy ra khi xóa vai trò");
    } finally {
      setIsSubmitting(false);
      setConfirmDelete(false);
    }
  };

  const isEditing = currentAction === "edit" || currentAction === "create";
  const isCreating = currentAction === "create";

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle>
            {isCreating ? "Tạo vai trò mới" : "Thông tin vai trò"}
          </CardTitle>
          {!isEditing && currentRole && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => onActionChange("edit")}
            >
              <Edit className="h-4 w-4 mr-2" />
              Chỉnh sửa
            </Button>
          )}
        </div>
      </CardHeader>

      <CardContent>
        {!currentRole && currentAction !== "create" ? (
          <div className="text-center py-8 text-gray-500">
            Chọn tạo mới hoặc chọn một vai trò để xem thông tin
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Basic Info */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label
                  htmlFor="code"
                  className="text-sm font-medium text-gray-600"
                >
                  Mã vai trò <span className="text-red-500">*</span>
                </Label>
                {isEditing ? (
                  <Input
                    id="code"
                    value={formData.code}
                    onChange={(e) => handleInputChange("code", e.target.value)}
                    placeholder="vd: admin, user, manager"
                    disabled={isSubmitting}
                  />
                ) : (
                  <p className="text-base font-semibold text-gray-900 bg-gray-50 p-3 rounded-md">
                    {formData.code}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label
                  htmlFor="name"
                  className="text-sm font-medium text-gray-600"
                >
                  Tên vai trò <span className="text-red-500">*</span>
                </Label>
                {isEditing ? (
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => handleInputChange("name", e.target.value)}
                    placeholder="vd: Quản trị viên, Người dùng"
                    disabled={isSubmitting}
                  />
                ) : (
                  <p className="text-base font-semibold text-gray-900 bg-gray-50 p-3 rounded-md">
                    {formData.name}
                  </p>
                )}
              </div>
            </div>

            {/* System Info - Only show in view mode for existing roles */}
            {!isEditing && currentRole && currentRole.id !== 0 && (
              <div className="pt-4 border-t">
                <Label className="text-sm font-medium text-gray-600 mb-3 block">
                  Thông tin hệ thống
                </Label>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div>
                    <span className="text-gray-500">ID:</span>
                    <span className="ml-2 font-medium">{currentRole.id}</span>
                  </div>
                  <div>
                    <span className="text-gray-500">Ngày tạo:</span>
                    <span className="ml-2 font-medium">
                      {new Date(currentRole.createdAt).toLocaleString("vi-VN")}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-500">Cập nhật:</span>
                    <span className="ml-2 font-medium">
                      {new Date(currentRole.updatedAt).toLocaleString("vi-VN")}
                    </span>
                  </div>
                </div>
              </div>
            )}

            {/* Action Buttons */}
            {isEditing && (
              <div className="flex justify-end gap-2 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleCancel}
                  disabled={isSubmitting}
                >
                  <X className="h-4 w-4 mr-2" />
                  Hủy
                </Button>

                <Button type="submit" disabled={isSubmitting}>
                  <Save className="h-4 w-4 mr-2" />
                  {isCreating ? "Tạo mới" : "Lưu"}
                </Button>

                {currentAction === "edit" && currentRole && (
                  <Dialog open={confirmDelete} onOpenChange={setConfirmDelete}>
                    <DialogTrigger asChild>
                      <Button variant="destructive" disabled={isSubmitting}>
                        <Trash2 className="h-4 w-4 mr-2" />
                        Xóa
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Xác nhận xóa</DialogTitle>
                      </DialogHeader>
                      <p className="text-sm text-muted-foreground">
                        Bạn có chắc chắn muốn xóa vai trò "{currentRole.name}"?
                        Hành động này không thể hoàn tác.
                      </p>
                      <DialogFooter>
                        <Button
                          variant="outline"
                          onClick={() => setConfirmDelete(false)}
                          disabled={isSubmitting}
                        >
                          Hủy
                        </Button>
                        <Button
                          variant="destructive"
                          onClick={handleDelete}
                          disabled={isSubmitting}
                        >
                          Xác nhận xóa
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                )}
              </div>
            )}
          </form>
        )}
      </CardContent>
    </Card>
  );
};
