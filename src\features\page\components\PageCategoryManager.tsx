import React, { useEffect, useState } from "react";
import { useAppDispatch, useAppSelector } from "@/store/rootReducer";
import {
  setSelectedCategory,
  setMode,
  deletePageAsync,
  linkPageToCategoryAsync,
  unlinkPageFromCategoryAsync,
  fetchPagesSummaryAsync,
} from "../states/slices";
import {
  selectSelectedCategoryId,
  selectPageLoading,
} from "../states/selectors";
import {
  useCategoryPageMapping,
  useSelectedCategoryPage,
} from "../states/hooks";
import { useCategory } from "@/features/category/hooks/useCategoryData";

// Import components
import { CategoryPanel } from "./CategoryPanel";
import { PageManagementPanel } from "./PageManagementPanel";
import { DeletePageDialog } from "./DeletePageDialog";
import { CreatePageDialog } from "./CreatePageDialog";

export const PageCategoryManager: React.FC = () => {
  const dispatch = useAppDispatch();

  // Use the category hook instead of manual fetching
  const { data: categories } = useCategory("public-menu");

  // Dialog state
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [createPageDialogOpen, setCreatePageDialogOpen] = useState(false);
  const [pageToDelete, setPageToDelete] = useState<{
    id: number;
    title: string;
  } | null>(null);

  // Sync category-page mapping
  useCategoryPageMapping();

  // Get state from Redux
  const selectedCategoryId = useAppSelector(selectSelectedCategoryId);
  const loading = useAppSelector(selectPageLoading);
  const { hasLinkedPage, linkedPage } = useSelectedCategoryPage();

  // Load empty pages initially since API requires keyword
  useEffect(() => {
    dispatch(fetchPagesSummaryAsync()); // This returns empty array
  }, [dispatch]);

  // === Event Handlers ===

  const handleCategorySelect = (categoryId: number) => {
    // Skip root category (id: 0)
    if (categoryId === 0) return;
    dispatch(setSelectedCategory(categoryId));
    dispatch(setMode("view"));
  };

  const handleOpenCreateDialog = () => {
    if (!selectedCategoryId) {
      alert("Vui lòng chọn chuyên mục trước!");
      return;
    }
    setCreatePageDialogOpen(true);
  };

  const handleLinkExistingPage = async (pageId: number) => {
    if (!selectedCategoryId) return;

    // Check if we need to restore page from TRASH first
    // We'll let the API handle this automatically since we don't have page details here

    await dispatch(
      linkPageToCategoryAsync({
        categoryId: selectedCategoryId,
        pageId,
      })
    );
  };

  const handleUnlinkPage = async () => {
    if (!selectedCategoryId) return;
    await dispatch(unlinkPageFromCategoryAsync(selectedCategoryId));
  };

  const handleEdit = () => {
    window.open(`/edit/${linkedPage?.id}`, "_blank");
  };

  const openDeleteDialog = (pageId: number, pageTitle: string) => {
    setPageToDelete({ id: pageId, title: pageTitle });
    setDeleteDialogOpen(true);
  };

  const handleDeletePage = async () => {
    if (!pageToDelete) return;

    await dispatch(deletePageAsync(pageToDelete.id));
    setDeleteDialogOpen(false);
    setPageToDelete(null);
  };

  const handleDeleteDialogClose = () => {
    setDeleteDialogOpen(false);
    setPageToDelete(null);
  };

  return (
    <div className="flex h-screen">
      {/* Left Panel - Category Tree */}
      <CategoryPanel
        categories={categories}
        selectedCategoryId={selectedCategoryId}
        onCategorySelect={handleCategorySelect}
      />

      {/* Right Panel - Page Management */}
      <div className="flex-1 p-6">
        <PageManagementPanel
          selectedCategoryId={selectedCategoryId}
          categories={categories}
          hasLinkedPage={hasLinkedPage}
          linkedPage={linkedPage}
          loading={loading}
          onEdit={handleEdit}
          onUnlink={handleUnlinkPage}
          onDelete={openDeleteDialog}
          onCreatePage={handleOpenCreateDialog}
          onLinkPage={handleLinkExistingPage}
        />
      </div>

      {/* Create Page Dialog */}
      <CreatePageDialog
        open={createPageDialogOpen}
        onClose={() => setCreatePageDialogOpen(false)}
        selectedCategoryId={selectedCategoryId}
        categories={categories}
      />

      {/* Delete Dialog */}
      <DeletePageDialog
        open={deleteDialogOpen}
        loading={loading}
        pageToDelete={pageToDelete}
        onClose={handleDeleteDialogClose}
        onConfirm={handleDeletePage}
      />
    </div>
  );
};
