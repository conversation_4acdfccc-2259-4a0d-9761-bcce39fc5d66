import React, { useState, useEffect, useCallback } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Plus, Trash2 } from "lucide-react";
import { z } from "zod";

import type { BannerConfig, BannerMetadata, BannerField, EditableBannerItem } from "../states/type";
import { bannerItemSchema } from "../states/validation";
import { FieldError } from "../../shared/components/FieldError";
import { ValidationSummary } from "../../shared/components/ValidationSummary";

/**
 * Props for the BannerTable component
 */
interface BannerTableProps {
  /** Configuration metadata defining table structure and validation */
  metadata: BannerMetadata;
  /** Current banner configuration data */
  data: BannerConfig | null;
  /** Loading state indicator */
  loading: boolean;
  /** Call<PERSON> fired when data changes */
  onDataChange: (newData: BannerConfig) => void;
  /** Callback fired when validation state changes */
  onValidationChange?: (isValid: boolean) => void;
}

/**
 * BannerTable component for managing banner configuration data
 * Provides inline editing capabilities with add/remove functionality
 */
export const BannerTable: React.FC<BannerTableProps> = ({
  metadata,
  data,
  loading,
  onDataChange,
  onValidationChange,
}) => {
  // ============================================================================
  // State Management
  // ============================================================================
  const [editableItems, setEditableItems] = useState<EditableBannerItem[]>([]);
  const [validationErrors, setValidationErrors] = useState<Record<string, Record<string, string>>>({});
  const [showValidationSummary, setShowValidationSummary] = useState(false);

  // ============================================================================
  // Data Processing
  // ============================================================================

  /**
   * Initialize editable items when data changes
   * Converts server data to editable format with unique IDs
   */
  useEffect(() => {
    if (!data) {
      setEditableItems([]);
      return;
    }

    const arrayKey = metadata.arrayKey || 'banners';
    const arrayData = data[arrayKey as keyof BannerConfig] || [];

    const items = arrayData.map((item, index: number) => ({
      id: `item-${index}`,
      data: { ...item },
    }));

    setEditableItems(items);
  }, [data, metadata.arrayKey]);

  // ============================================================================
  // Validation Functions
  // ============================================================================

  /**
   * Validates a single banner item
   */
  const validateItem = useCallback((itemData: Record<string, string>) => {
    try {
      bannerItemSchema.parse(itemData);
      return {};
    } catch (error) {
      if (error instanceof z.ZodError) {
        const fieldErrors: Record<string, string> = {};
        error.errors.forEach((err) => {
          if (err.path.length > 0) {
            fieldErrors[err.path[0] as string] = err.message;
          }
        });
        return fieldErrors;
      }
      return {};
    }
  }, []);

  /**
   * Validates all items and updates validation state
   */
  const validateAllItems = useCallback(() => {
    const newValidationErrors: Record<string, Record<string, string>> = {};
    let hasErrors = false;

    editableItems.forEach((item) => {
      const itemErrors = validateItem(item.data);
      if (Object.keys(itemErrors).length > 0) {
        newValidationErrors[item.id] = itemErrors;
        hasErrors = true;
      }
    });

    setValidationErrors(newValidationErrors);

    // Notify parent component
    if (onValidationChange) {
      onValidationChange(!hasErrors);
    }

    return !hasErrors;
  }, [editableItems, validateItem, onValidationChange]);

  /**
   * Validate items whenever they change
   */
  useEffect(() => {
    if (editableItems.length > 0) {
      validateAllItems();
    }
  }, [editableItems, validateAllItems]);

  /**
   * Memoized function to update config data and notify parent component
   * Converts editable items back to server format
   */
  const updateConfigData = useCallback((items: EditableBannerItem[]) => {
    const arrayKey = metadata.arrayKey || 'banners';
    const arrayData = items.map(item => item.data);
    const newData = { [arrayKey]: arrayData } as unknown as BannerConfig;
    onDataChange(newData);
  }, [metadata.arrayKey, onDataChange]);

  // ============================================================================
  // Event Handlers
  // ============================================================================

  /**
   * Adds a new banner item with default values
   */
  const handleAddItem = () => {
    const newItem: EditableBannerItem = {
      id: `new-${Date.now()}`,
      isNew: true,
      data: metadata.fields.reduce((acc, field) => {
        acc[field.key] = '';
        return acc;
      }, {} as Record<string, string>),
    };

    const newItems = [...editableItems, newItem];
    setEditableItems(newItems);
    updateConfigData(newItems);
  };

  /**
   * Removes a banner item by ID
   */
  const handleRemoveItem = (id: string) => {
    const newItems = editableItems.filter(item => item.id !== id);
    setEditableItems(newItems);
    updateConfigData(newItems);
  };

  /**
   * Updates a specific field value for a banner item
   * Includes real-time validation
   */
  const handleItemChange = (id: string, field: string, value: string) => {
    const newItems = editableItems.map(item =>
      item.id === id
        ? { ...item, data: { ...item.data, [field]: value } }
        : item
    );
    setEditableItems(newItems);
    updateConfigData(newItems);

    // Clear validation summary when user starts fixing errors
    if (showValidationSummary) {
      setShowValidationSummary(false);
    }
  };

  /**
   * Handles field blur for validation
   */
  const handleFieldBlur = (id: string) => {
    const item = editableItems.find(item => item.id === id);
    if (item) {
      const itemErrors = validateItem(item.data);
      setValidationErrors(prev => ({
        ...prev,
        [id]: itemErrors
      }));
    }
  };

  // ============================================================================
  // Render Methods
  // ============================================================================

  /**
   * Renders an input field for a specific banner field
   * Handles different input types with validation styling and tooltip error display
   */
  const renderFieldInput = (
    field: BannerField,
    value: string,
    onChange: (value: string) => void,
    onBlur: () => void,
    error?: string,
    disabled: boolean = false
  ) => {
    const hasError = !!error;
    const inputProps = {
      disabled: disabled || loading,
      placeholder: field.placeholder,
      className: `h-10 ${hasError ? 'border-red-500 focus:border-red-500' : ''}`, // Error styling
    };

    const inputElement = (
      <Input
        type={field.type === 'email' ? 'email' : field.type === 'url' ? 'url' : 'text'}
        value={value || ''}
        onChange={(e) => onChange(e.target.value)}
        onBlur={onBlur}
        {...inputProps}
      />
    );

    // Wrap input with tooltip if there's an error
    return (
      <FieldError error={error} asTooltip={true}>
        {inputElement}
      </FieldError>
    );
  };

  // ============================================================================
  // Loading State
  // ============================================================================

  if (loading && !data) {
    return (
      <Card>
        <CardContent className="p-0 py-4">
          <div className="text-center text-muted-foreground">
            Đang tải dữ liệu...
          </div>
        </CardContent>
      </Card>
    );
  }

  // ============================================================================
  // Main Render
  // ============================================================================

  return (
    <Card>
      <CardContent className="p-0 py-4">
        <div className="space-y-4">
          <div className="flex items-center justify-between px-6">
            <h4 className="text-sm font-medium">
              Danh sách {metadata.title.toLowerCase()} ({editableItems.length} mục)
            </h4>
            <Button onClick={handleAddItem} size="sm">
              <Plus className="h-4 w-4 mr-2" />
              Thêm mục
            </Button>
          </div>

          <ValidationSummary
            errors={Object.values(validationErrors).flatMap(itemErrors => Object.values(itemErrors))}
            show={showValidationSummary && Object.keys(validationErrors).length > 0}
            onDismiss={() => setShowValidationSummary(false)}
            title="Có lỗi trong dữ liệu banner"
          />

          {editableItems.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              Chưa có dữ liệu. Nhấn "Thêm mục" để bắt đầu.
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  {metadata.fields.map((field) => (
                    <TableHead key={field.key}>
                      {field.label}
                      {field.required && <span className="text-red-500 ml-1">*</span>}
                    </TableHead>
                  ))}
                  <TableHead className="w-12">Thao tác</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {editableItems.map((item) => (
                  <TableRow key={item.id}>
                    {metadata.fields.map((field) => (
                      <TableCell key={field.key} className="align-top">
                        {renderFieldInput(
                          field,
                          item.data[field.key],
                          (value) => handleItemChange(item.id, field.key, value),
                          () => handleFieldBlur(item.id),
                          validationErrors[item.id]?.[field.key]
                        )}
                      </TableCell>
                    ))}
                    <TableCell>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRemoveItem(item.id)}
                        className="text-red-600 hover:text-red-800"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
