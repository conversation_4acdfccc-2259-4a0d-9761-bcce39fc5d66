/**
 * Attributes Tab Component
 * Tab for node-specific attributes (properties, field config, etc.)
 */

import React, { useState } from 'react';
import { FormNode } from '@/form/types';
import { PropertiesFormProps, SectionState } from '../types';
import { CollapsibleSection } from './CollapsibleSection';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

export const AttributesTab: React.FC<PropertiesFormProps> = ({
  node,
  onChange,
  disabled = false
}) => {
  const [sections, setSections] = useState<SectionState>({
    general: true,
    field: node.type === 'field',
    control: node.type === 'control'
  });

  const toggleSection = (sectionId: string) => {
    setSections(prev => ({
      ...prev,
      [sectionId]: !prev[sectionId]
    }));
  };

  // Update node properties
  const updateProperties = (updates: Record<string, any>) => {
    onChange({
      properties: {
        ...node.properties,
        ...updates
      }
    });
  };

  // Update field configuration
  const updateField = (updates: Record<string, any>) => {
    onChange({
      field: {
        ...node.field,
        ...updates
      }
    });
  };

  return (
    <div className="space-y-4">
      {/* General Properties Section */}
      <CollapsibleSection
        title="Thuộc tính chung"
        isOpen={sections.general}
        onToggle={() => toggleSection('general')}
        disabled={disabled}
      >
        <div className="space-y-4">

          {/* Common properties based on node type */}
          {(node.type === 'title' || node.type === 'control') && (
            <div className="space-y-1">
              <Label className="text-xs">
                {node.type === 'title' ? 'Tiêu đề' : 'Text nút'}
              </Label>
              <Input
                value={(node.properties?.text as string) || ''}
                onChange={(e) => updateProperties({ text: e.target.value })}
                disabled={disabled}
                className="text-xs"
                placeholder="Nhập nội dung..."
              />
            </div>
          )}

          {node.type === 'field' && (
            <div className="space-y-1">
              <Label className="text-xs">Nhãn trường</Label>
              <Input
                value={(node.properties?.label as string) || ''}
                onChange={(e) => updateProperties({ label: e.target.value })}
                disabled={disabled}
                className="text-xs"
                placeholder="Nhập nhãn..."
              />
            </div>
          )}

          {node.type === 'field' && (
            <div className="space-y-1">
              <Label className="text-xs">Placeholder</Label>
              <Input
                value={(node.properties?.placeholder as string) || ''}
                onChange={(e) => updateProperties({ placeholder: e.target.value })}
                disabled={disabled}
                className="text-xs"
                placeholder="Nhập placeholder..."
              />
            </div>
          )}
        </div>
      </CollapsibleSection>

      {/* Field Configuration Section - only for field nodes */}
      {node.type === 'field' && (
        <CollapsibleSection
          title="Cấu hình trường"
          isOpen={sections.field}
          onToggle={() => toggleSection('field')}
          disabled={disabled}
        >
          <div className="space-y-4">
            {/* Object Key */}
            <div className="space-y-1">
              <Label className="text-xs">Object Key <span className="text-red-500">*</span></Label>
              <Input
                value={node.field?.objectKey || ''}
                onChange={(e) => updateField({ objectKey: e.target.value })}
                disabled={disabled}
                className="text-xs"
                placeholder="user.name"
              />
            </div>

            {/* Component Type */}
            <div className="space-y-1">
              <Label className="text-xs">Loại component</Label>
              <Select
                value={node.field?.component || 'TextInput'}
                onValueChange={(value) => updateField({ component: value })}
                disabled={disabled}
              >
                <SelectTrigger className="text-xs">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="TextInput">Text Input</SelectItem>
                  <SelectItem value="TextAreaInput">Text Area</SelectItem>
                  <SelectItem value="NumberInput">Number Input</SelectItem>
                  <SelectItem value="CheckboxInput">Checkbox</SelectItem>
                  <SelectItem value="SelectInput">Select</SelectItem>
                  <SelectItem value="DropdownInput">Dropdown</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Data Type */}
            <div className="space-y-1">
              <Label className="text-xs">Kiểu dữ liệu</Label>
              <Select
                value={node.field?.dataType || 'string'}
                onValueChange={(value) => updateField({ dataType: value })}
                disabled={disabled}
              >
                <SelectTrigger className="text-xs">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="string">String</SelectItem>
                  <SelectItem value="number">Number</SelectItem>
                  <SelectItem value="boolean">Boolean</SelectItem>
                  <SelectItem value="array">Array</SelectItem>
                  <SelectItem value="object">Object</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Default Value */}
            <div className="space-y-1">
              <Label className="text-xs">Giá trị mặc định</Label>
              <Input
                value={String(node.field?.defaultValue || '')}
                onChange={(e) => updateField({ defaultValue: e.target.value })}
                disabled={disabled}
                className="text-xs"
                placeholder="Giá trị mặc định..."
              />
            </div>
          </div>
        </CollapsibleSection>
      )}

      {/* Control Configuration Section - only for control nodes */}
      {node.type === 'control' && (
        <CollapsibleSection
          title="Cấu hình nút điều khiển"
          isOpen={sections.control}
          onToggle={() => toggleSection('control')}
          disabled={disabled}
        >
          <div className="space-y-4">
            {/* Control Type */}
            <div className="space-y-1">
              <Label className="text-xs">Loại nút</Label>
              <Select
                value={(node.properties?.controlType as string) || 'submit'}
                onValueChange={(value) => updateProperties({ controlType: value })}
                disabled={disabled}
              >
                <SelectTrigger className="text-xs">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="submit">Submit</SelectItem>
                  <SelectItem value="reset">Reset</SelectItem>
                  <SelectItem value="button">Button</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Button Variant */}
            <div className="space-y-1">
              <Label className="text-xs">Variant</Label>
              <Select
                value={(node.properties?.variant as string) || 'default'}
                onValueChange={(value) => updateProperties({ variant: value })}
                disabled={disabled}
              >
                <SelectTrigger className="text-xs">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="default">Default</SelectItem>
                  <SelectItem value="destructive">Destructive</SelectItem>
                  <SelectItem value="outline">Outline</SelectItem>
                  <SelectItem value="secondary">Secondary</SelectItem>
                  <SelectItem value="ghost">Ghost</SelectItem>
                  <SelectItem value="link">Link</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Button Size */}
            <div className="space-y-1">
              <Label className="text-xs">Kích thước</Label>
              <Select
                value={(node.properties?.size as string) || 'default'}
                onValueChange={(value) => updateProperties({ size: value })}
                disabled={disabled}
              >
                <SelectTrigger className="text-xs">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="sm">Small</SelectItem>
                  <SelectItem value="default">Default</SelectItem>
                  <SelectItem value="lg">Large</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CollapsibleSection>
      )}
    </div>
  );
};