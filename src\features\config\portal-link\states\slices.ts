import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import { toast } from "sonner";
import type { PortalLinkState, PortalLinkConfig } from "./type";
import { fetchPortalLinkConfig, updatePortalLinkConfig } from "./api";

// ============================================================================
// Initial State
// ============================================================================

const initialState: PortalLinkState = {
  data: null,
  savedData: null,
  loading: false,
  saving: false,
  error: null,
  isDirty: false,
};

// ============================================================================
// Async Thunks
// ============================================================================

/**
 * Async thunk to fetch portal link configuration
 */
export const fetchPortalLinkAsync = createAsyncThunk(
  "portalLink/fetchConfig",
  async (_, { rejectWithValue }) => {
    try {
      const response = await fetchPortalLinkConfig();

      if (response.status !== 200 || !response.data) {
        throw new Error(
          response.statusText || "Lấy dữ liệu cổng liên kết thất bại!"
        );
      }

      const returnValue = response.data as unknown as {
        data: PortalLinkConfig;
      };

      return returnValue.data;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Đã xảy ra lỗi không xác định";
      toast.error(`Lỗi: ${errorMessage}`);
      return rejectWithValue(errorMessage);
    }
  }
);

/**
 * Async thunk to update portal link configuration
 */
export const updatePortalLinkAsync = createAsyncThunk(
  "portalLink/updateConfig",
  async (data: PortalLinkConfig, { rejectWithValue }) => {
    try {
      const response = await updatePortalLinkConfig(data);

      if (response.status !== 200 || !response.data) {
        throw new Error(
          response.statusText || "Cập nhật cổng liên kết thất bại!"
        );
      }

      toast.success("Cập nhật cổng liên kết thành công");

      const returnValue = response.data as unknown as {
        data: PortalLinkConfig;
      };

      return returnValue.data;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Đã xảy ra lỗi không xác định";
      toast.error(`Lỗi: ${errorMessage}`);
      return rejectWithValue(errorMessage);
    }
  }
);

// ============================================================================
// Slice Definition
// ============================================================================

const portalLinkSlice = createSlice({
  name: "portalLink",
  initialState,
  reducers: {
    /**
     * Mark data as dirty (unsaved changes)
     */
    setDirty: (state, action: PayloadAction<boolean>) => {
      state.isDirty = action.payload;
    },
    /**
     * Clear error state
     */
    clearError: (state) => {
      state.error = null;
    },
    /**
     * Reset state to initial values
     */
    resetState: () => initialState,
  },
  extraReducers: (builder) => {
    // ========================================================================
    // Fetch Portal Link Configuration
    // ========================================================================
    builder
      .addCase(fetchPortalLinkAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchPortalLinkAsync.fulfilled, (state, action) => {
        state.loading = false;
        const serverData = action.payload;
        state.data = serverData;
        state.savedData = serverData;
        state.error = null;
        state.isDirty = false;
      })
      .addCase(fetchPortalLinkAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });

    // ========================================================================
    // Update Portal Link Configuration
    // ========================================================================
    builder
      .addCase(updatePortalLinkAsync.pending, (state) => {
        state.saving = true;
        state.error = null;
      })
      .addCase(updatePortalLinkAsync.fulfilled, (state, action) => {
        state.saving = false;
        const serverData = action.payload;
        state.data = serverData;
        state.savedData = serverData;
        state.error = null;
        state.isDirty = false;
      })
      .addCase(updatePortalLinkAsync.rejected, (state, action) => {
        state.saving = false;
        state.error = action.payload as string;
      });
  },
});

// ============================================================================
// Export Actions and Reducer
// ============================================================================

export const { setDirty, clearError, resetState } = portalLinkSlice.actions;
export default portalLinkSlice.reducer;
