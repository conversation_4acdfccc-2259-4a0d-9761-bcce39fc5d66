import { RootState } from "@/store/rootReducer";
import { UserQueryParams } from "./api";
import { UserPageMode } from "./slices";
import { UserData } from "./type";

export const selectUserQueryParams = (state: RootState): UserQueryParams =>
  state.userFilterState.queryParams;

export const selectUserLoading = (state: RootState): boolean =>
  state.userFilterState.loading;

export const selectUserError = (state: RootState): string | null =>
  state.userFilterState.error;

export const selectUserRefetch = (state: RootState): boolean =>
  state.userFilterState.refetch;

export const selectPageMode = (state: RootState): UserPageMode =>
  state.userFilterState.pathParams.mode;

export const selectCurrentUser = (state: RootState): UserData | null =>
  state.userFilterState.currentUser;
