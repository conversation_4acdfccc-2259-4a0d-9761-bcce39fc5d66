/**
 * ToggleInput - Toggle/Switch input component for AutoForm
 */

import React from "react";
import { FieldComponentProps } from "../../FieldFactory";
import { Switch } from "@/components/ui/switch";

export const ToggleInput: React.FC<FieldComponentProps> = ({
  value,
  onChange,
  onBlur,
  disabled = false,
  options = ["ACTIVE", "DISABLED"],
  labels = [],
  id,
  className = "",
}) => {
  const checked = value === options[0];
  const displayLabels = labels.length >= 2 ? labels : options;

  const handleChange = disabled
    ? undefined
    : (checked: boolean) => {
        onChange(checked ? options[0] : options[1]);
      };

  return (
    <div className={`flex items-center space-x-3 w-fit ${className}`}>
      <Switch
        id={id}
        checked={checked}
        onCheckedChange={handleChange}
        onBlur={onBlur}
        disabled={disabled}
      />
      <label
        htmlFor={id}
        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
      >
        {checked ? displayLabels[0] : displayLabels[1]}
      </label>
    </div>
  );
};
