import { FormStyle } from "./registry";

export const InputWrapper: React.FC<{
  label: string;
  children: React.ReactNode;
  style?: FormStyle;
  required?: boolean;
  error?: string;
}> = ({ label, children, style, required, error }) => {
  const frameClassName =
    style?.frame?.trim() || "w-full flex flex-row gap-2 items-center";
  const labelClassName = style?.label?.trim() || "w-30 text-sm font-semibold";
  const contentClassName = style?.content?.trim() || "w-full";
  const errorClassName =
    style?.error?.trim() || "ml-28 text-red-500 text-sm border-red-500";

  return (
    <div className="w-full">
      <div className={frameClassName}>
        <label className={labelClassName}>
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
        <div className={contentClassName}>{children}</div>
      </div>
      <div>{error && <em className={errorClassName}>{error}</em>}</div>
    </div>
  );
};
