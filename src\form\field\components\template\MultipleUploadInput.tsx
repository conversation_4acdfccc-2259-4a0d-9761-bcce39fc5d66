import React, { useRef, useState, useId } from "react";
import { FieldComponentProps } from "../../FieldFactory";
import {
  Upload,
  Trash2,
  Check<PERSON>ircle,
  AlertCircle,
  Loader2,
} from "lucide-react";
import { uploadFile, getFileViewUrl } from "@/features/demo/states/api";

interface FileItem {
  name: string;
  url: string;
}

export const MultipleUploadInput: React.FC<FieldComponentProps> = ({
  value,
  onChange,
  disabled,
  className,
  id: propId,
}) => {
  // Uploaded files (from value)
  const uploadedFiles: FileItem[] = Array.isArray(value)
    ? value
        .filter((v): v is string => typeof v === "string")
        .map((url) => ({
          name: decodeURIComponent(url.split("/").pop() || ""),
          url,
        }))
    : [];

  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [uploadStatus, setUploadStatus] = useState<
    "idle" | "uploading" | "success" | "error"
  >("idle");
  const [errorMessage, setErrorMessage] = useState<string>("");
  const fileInputRef = useRef<HTMLInputElement>(null);
  const autoId = useId();
  const inputId = propId || `multiple-upload-input-${autoId}`;

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      setUploadStatus("idle");
      setErrorMessage("");
    }
  };

  const handleChooseFile = () => {
    if (uploadStatus === "uploading" || disabled) return;
    fileInputRef.current?.click();
  };

  const handleRemoveFile = (url: string) => {
    const newFiles = uploadedFiles.filter((f) => f.url !== url);
    onChange(newFiles.map((f) => f.url));
  };

  const handleRemoveSelected = () => {
    if (uploadStatus === "uploading" || disabled) return;
    setSelectedFile(null);
    setUploadStatus("idle");
    setErrorMessage("");
    if (fileInputRef.current) fileInputRef.current.value = "";
  };

  const handleUpload = async () => {
    if (!selectedFile || uploadStatus === "uploading" || disabled) return;
    setUploadStatus("uploading");
    setErrorMessage("");
    try {
      const response = await uploadFile(selectedFile);
      if (response.code === "SUCCESS") {
        const fileUrl = getFileViewUrl(response.data.name);
        onChange([...uploadedFiles.map((f) => f.url), fileUrl]);
        setUploadStatus("success");
        setSelectedFile(null);
        if (fileInputRef.current) fileInputRef.current.value = "";
      } else {
        throw new Error(response.message || "Upload failed");
      }
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : "Upload failed";
      setErrorMessage(errorMsg);
      setUploadStatus("error");
    }
  };

  const isDisabled = disabled || uploadStatus === "uploading";

  return (
    <div className={className || "w-full"}>
      {/* Hidden file input with label for accessibility */}
      <label htmlFor={inputId} className="sr-only">
        Chọn file tải lên
      </label>
      <div className="flex items-center space-x-2">
        <div
          className={
            "flex-1 px-3 py-1.5 border border-gray-300 rounded-lg bg-white flex items-center justify-between text-gray-800 text-xs h-8" +
            (isDisabled ? " opacity-75" : "")
          }
        >
          {selectedFile ? (
            <span className="truncate mr-2 text-gray-800 text-xs">
              {selectedFile.name.length > 32
                ? selectedFile.name.slice(0, 16) +
                  "..." +
                  selectedFile.name.slice(-8)
                : selectedFile.name}
            </span>
          ) : (
            <span className="truncate mr-2 text-gray-500 text-xs">
              Chọn file tải lên
            </span>
          )}
          <input
            ref={fileInputRef}
            id={inputId}
            type="file"
            onChange={handleFileSelect}
            className="hidden"
            accept="*/*"
            disabled={isDisabled}
          />
          {selectedFile && (
            <button
              type="button"
              onClick={handleRemoveSelected}
              className={
                "h-5 w-5 p-0 hover:bg-gray-100 rounded-full ml-2" +
                (isDisabled
                  ? " opacity-50 cursor-not-allowed hover:bg-transparent"
                  : "")
              }
              disabled={isDisabled}
            >
              <Trash2 className="h-3 w-3" />
            </button>
          )}
        </div>
        <button
          type="button"
          onClick={selectedFile ? handleUpload : handleChooseFile}
          className={
            "px-2 h-8 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 flex items-center" +
            (isDisabled
              ? " opacity-50 cursor-not-allowed hover:bg-blue-600"
              : "")
          }
          disabled={isDisabled || (uploadStatus === "success" && !selectedFile)}
        >
          {uploadStatus === "uploading" ? (
            <>
              <Loader2 className="h-3.5 w-3.5 mr-1 animate-spin" />
              Đang tải...
            </>
          ) : selectedFile ? (
            <>
              <Upload className="h-3.5 w-3.5 mr-1" />
              Tải lên
            </>
          ) : (
            <>
              <Upload className="h-3.5 w-3.5 mr-1" />
              Chọn file
            </>
          )}
        </button>
      </div>
      {/* Error message */}
      {uploadStatus === "error" && errorMessage && (
        <div className="mt-2 text-xs text-red-600 flex items-center space-x-1">
          <AlertCircle className="h-4 w-4" />
          <span>{errorMessage}</span>
        </div>
      )}
      {/* Uploaded files */}
      {uploadedFiles.length > 0 && (
        <div className="w-full border rounded-lg bg-white divide-y divide-gray-100 shadow-sm mt-2">
          {uploadedFiles.map((file) => (
            <div
              key={file.url}
              className="flex items-center px-3 py-2 space-x-2 group"
            >
              <a
                href={file.url}
                target="_blank"
                rel="noopener noreferrer"
                className="flex-1 text-blue-600 underline truncate max-w-xs"
                title={file.url}
              >
                {file.name.length > 32
                  ? file.name.slice(0, 16) + "..." + file.name.slice(-8)
                  : file.name}
              </a>
              <CheckCircle className="h-4 w-4 text-green-500" />
              <button
                type="button"
                onClick={() => handleRemoveFile(file.url)}
                className="p-1 rounded hover:bg-gray-100"
                disabled={isDisabled}
              >
                <Trash2 className="h-4 w-4 text-gray-500" />
              </button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};
