/**
 * Component Selector Modal
 * Modal for selecting components to add to the form
 */

import React, { useState } from 'react';
import { FormNode } from '@/form/types';
import { useFormBuilder } from '../context/FormBuilderContext';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { 
  Layout, 
  Type, 
  Square, 
  Edit3,
  Calendar,
  Check,
  ToggleLeft,
  FileImage,
  Palette,
  ChevronRight,
  Plus
} from 'lucide-react';

interface ComponentOption {
  id: string;
  type: FormNode['type'];
  name: string;
  description: string;
  icon: React.ReactNode;
  preview?: React.ReactNode;
  defaultProps?: Partial<FormNode>;
}

interface ComponentCategory {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  components: ComponentOption[];
}

// Define component categories
const COMPONENT_CATEGORIES: ComponentCategory[] = [
  {
    id: 'layout',
    name: '<PERSON><PERSON> cục',
    description: '<PERSON>hung chứa và cấu trúc',
    icon: <Layout className="w-4 h-4" />,
    components: [
      {
        id: 'frame',
        type: 'frame',
        name: 'Khung',
        description: 'Container để chứa các component khác',
        icon: <Square className="w-4 h-4" />,
        preview: (
          <div className="w-full h-12 border-2 border-dashed border-gray-300 rounded flex items-center justify-center">
            <span className="text-xs text-gray-500">Khung chứa</span>
          </div>
        ),
        defaultProps: {
          properties: {
            title: 'New Frame'
          },
          styles: {
            container: 'space-y-4 p-4 border rounded-lg'
          },
          children: []
        }
      }
    ]
  },
  {
    id: 'input',
    name: 'Nhập liệu',
    description: 'Các field để nhập dữ liệu',
    icon: <Edit3 className="w-4 h-4" />,
    components: [
      {
        id: 'text-input',
        type: 'field',
        name: 'Text Input',
        description: 'Ô nhập văn bản',
        icon: <Type className="w-4 h-4" />,
        preview: (
          <div className="w-full">
            <div className="text-xs text-gray-700 mb-1">Label</div>
            <div className="w-full h-8 border border-gray-300 rounded px-2 flex items-center text-xs text-gray-500">
              Placeholder text...
            </div>
          </div>
        ),
        defaultProps: {
          field: {
            objectKey: 'textField',
            component: 'TextInput',
            dataType: 'string',
            defaultValue: ''
          },
          properties: {
            label: 'Text Field',
            placeholder: 'Enter text...'
          }
        }
      },
      {
        id: 'number-input',
        type: 'field',
        name: 'Number Input',
        description: 'Ô nhập số',
        icon: <Type className="w-4 h-4" />,
        preview: (
          <div className="w-full">
            <div className="text-xs text-gray-700 mb-1">Number</div>
            <div className="w-full h-8 border border-gray-300 rounded px-2 flex items-center text-xs text-gray-500">
              0
            </div>
          </div>
        ),
        defaultProps: {
          field: {
            objectKey: 'numberField',
            component: 'NumberInput',
            dataType: 'number',
            defaultValue: 0
          },
          properties: {
            label: 'Number Field',
            placeholder: 'Enter number...'
          }
        }
      },
      {
        id: 'checkbox',
        type: 'field',
        name: 'Checkbox',
        description: 'Ô tích chọn',
        icon: <Check className="w-4 h-4" />,
        preview: (
          <div className="w-full flex items-center gap-2">
            <div className="w-4 h-4 border border-gray-300 rounded flex items-center justify-center">
              <Check className="w-3 h-3 text-blue-500" />
            </div>
            <span className="text-xs text-gray-700">Checkbox Label</span>
          </div>
        ),
        defaultProps: {
          field: {
            objectKey: 'checkboxField',
            component: 'CheckboxInput',
            dataType: 'boolean',
            defaultValue: false
          },
          properties: {
            label: 'Checkbox Field'
          }
        }
      },
      {
        id: 'toggle',
        type: 'field',
        name: 'Toggle',
        description: 'Công tắc bật/tắt',
        icon: <ToggleLeft className="w-4 h-4" />,
        preview: (
          <div className="w-full flex items-center justify-between">
            <span className="text-xs text-gray-700">Toggle Label</span>
            <div className="w-8 h-4 bg-blue-500 rounded-full relative">
              <div className="w-3 h-3 bg-white rounded-full absolute top-0.5 right-0.5"></div>
            </div>
          </div>
        ),
        defaultProps: {
          field: {
            objectKey: 'toggleField',
            component: 'ToggleInput',
            dataType: 'boolean',
            defaultValue: false
          },
          properties: {
            label: 'Toggle Field'
          }
        }
      }
    ]
  },
  {
    id: 'content',
    name: 'Nội dung',
    description: 'Hiển thị văn bản và media',
    icon: <FileImage className="w-4 h-4" />,
    components: [
      {
        id: 'title',
        type: 'title',
        name: 'Tiêu đề',
        description: 'Văn bản tiêu đề',
        icon: <Type className="w-4 h-4" />,
        preview: (
          <div className="w-full">
            <h3 className="text-sm font-bold text-gray-900">Tiêu đề mẫu</h3>
          </div>
        ),
        defaultProps: {
          properties: {
            text: 'New Title'
          },
          styles: {
            content: 'text-lg font-bold text-gray-900'
          }
        }
      }
    ]
  },
  {
    id: 'control',
    name: 'Điều khiển',
    description: 'Nút bấm và các action',
    icon: <Palette className="w-4 h-4" />,
    components: [
      {
        id: 'button',
        type: 'control',
        name: 'Button',
        description: 'Nút bấm',
        icon: <Square className="w-4 h-4" />,
        preview: (
          <div className="w-full">
            <button className="w-full h-8 bg-blue-500 text-white rounded text-xs font-medium">
              Button Text
            </button>
          </div>
        ),
        defaultProps: {
          properties: {
            text: 'Button',
            controlType: 'button',
            variant: 'default'
          }
        }
      },
      {
        id: 'submit',
        type: 'control',
        name: 'Submit',
        description: 'Nút gửi form',
        icon: <ChevronRight className="w-4 h-4" />,
        preview: (
          <div className="w-full">
            <button className="w-full h-8 bg-green-500 text-white rounded text-xs font-medium">
              Submit
            </button>
          </div>
        ),
        defaultProps: {
          properties: {
            text: 'Submit',
            controlType: 'submit',
            variant: 'default'
          }
        }
      }
    ]
  }
];

export const ComponentSelectorModal: React.FC = () => {
  const { state, hideComponentSelector, addNode, generateId } = useFormBuilder();
  const [selectedCategory, setSelectedCategory] = useState<string>('layout');
  const [selectedComponent, setSelectedComponent] = useState<ComponentOption | null>(null);

  const currentCategory = COMPONENT_CATEGORIES.find(cat => cat.id === selectedCategory);

  const handleComponentSelect = (component: ComponentOption) => {
    setSelectedComponent(component);
  };

  const handleAddComponent = () => {
    if (selectedComponent && state.editor.componentSelectorParentId) {
      // Create new node with generated ID
      const newNode: FormNode = {
        id: generateId(),
        type: selectedComponent.type,
        ...selectedComponent.defaultProps
      };

      // Add the node to the parent
      addNode(state.editor.componentSelectorParentId, newNode);
      
      // Close modal and reset selection
      hideComponentSelector();
      setSelectedComponent(null);
    }
  };

  const handleClose = () => {
    hideComponentSelector();
    setSelectedComponent(null);
  };

  return (
    <Dialog open={state.editor.showComponentSelector} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl h-[600px] p-0">
        <DialogHeader className="p-6 pb-4">
          <DialogTitle className="text-lg">Thêm Component</DialogTitle>
        </DialogHeader>

        <div className="flex h-full">
          {/* Categories Sidebar */}
          <div className="w-64 border-r bg-gray-50 p-4">
            <div className="space-y-2">
              {COMPONENT_CATEGORIES.map((category) => (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={cn(
                    "w-full flex items-center gap-3 p-3 rounded-lg text-left transition-colors",
                    selectedCategory === category.id 
                      ? "bg-blue-100 text-blue-900 border border-blue-200" 
                      : "bg-white hover:bg-gray-100 border border-gray-200"
                  )}
                >
                  <div className="flex-shrink-0">
                    {category.icon}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="text-sm font-medium">{category.name}</div>
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* Components Grid */}
          <div className="flex-1 p-6">
            {currentCategory && (
              <div className="h-full flex flex-col">
                <div className="mb-4">
                  <h3 className="text-base font-semibold flex items-center gap-2">
                    {currentCategory.icon}
                    {currentCategory.name}
                  </h3>
                </div>

                <div className="flex-1 grid grid-cols-2 gap-4 overflow-y-auto">
                  {currentCategory.components.map((component) => (
                    <button
                      key={component.id}
                      onClick={() => handleComponentSelect(component)}
                      className={cn(
                        "p-4 border rounded-lg text-left transition-all hover:shadow-md",
                        selectedComponent?.id === component.id
                          ? "border-blue-500 bg-blue-50 ring-2 ring-blue-200"
                          : "border-gray-200 hover:border-gray-300"
                      )}
                    >
                      <div className="flex items-start gap-3 mb-3">
                        <div className="flex-shrink-0 p-2 bg-gray-100 rounded">
                          {component.icon}
                        </div>
                        <div className="flex-1 min-w-0">
                          <h4 className="text-sm font-medium">{component.name}</h4>
                        </div>
                      </div>
                      
                      {component.preview && (
                        <div className="bg-white border border-gray-200 rounded p-3">
                          {component.preview}
                        </div>
                      )}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="border-t p-4 flex items-center justify-between bg-gray-50">
          <div className="flex items-center gap-2">
            {selectedComponent && (
              <Badge variant="secondary" className="text-xs">
                {selectedComponent.name}
              </Badge>
            )}
          </div>
          
          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={handleClose} size="sm">
              Hủy
            </Button>
            <Button 
              onClick={handleAddComponent} 
              disabled={!selectedComponent}
              size="sm"
              className="gap-1"
            >
              <Plus className="w-3 h-3" />
              Thêm
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};