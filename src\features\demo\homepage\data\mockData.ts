// ===== TYPES =====

export interface LibraryItem {
  id: number;
  title: string;
  url: string;
  thumbnail: string;
  type: "image" | "video";
}

export interface ProcessingTicket {
  id: number;
  ticketName: string;
  ticketCode: string;
  createdDate: string;
  status: "pending" | "processing" | "completed" | "rejected";
}

export interface StatData {
  number: string;
  name: string;
  description: string;
}

// ===== MOCK DATA =====

export const mockLibraryItems: LibraryItem[] = [
  {
    id: 1,
    title: "<PERSON><PERSON>nh ảnh cảnh quan <PERSON>ánh <PERSON>",
    url: "https://api.hocnhe.com/storage/v1/public/view/2025-06-04/original/d0b5850f77c9238dad2b6b08c81858df0f324f56-1749010250125.jpg",
    thumbnail:
      "https://api.hocnhe.com/storage/v1/public/view/2025-06-04/original/d0b5850f77c9238dad2b6b08c81858df0f324f56-1749010250125.jpg",
    type: "image",
  },
  {
    id: 2,
    title: "Video giới thiệu du lịch",
    url: "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
    thumbnail: "https://picsum.photos/300/200?random=2",
    type: "video",
  },
  {
    id: 3,
    title: "Bộ sưu tập ảnh văn hóa",
    url: "https://picsum.photos/300/200?random=3",
    thumbnail: "https://picsum.photos/300/200?random=3",
    type: "image",
  },
  {
    id: 4,
    title: "Video hướng dẫn dịch vụ",
    url: "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
    thumbnail: "https://picsum.photos/300/200?random=4",
    type: "video",
  },
  {
    id: 5,
    title: "Hình ảnh lễ hội địa phương",
    url: "https://picsum.photos/300/200?random=5",
    thumbnail: "https://picsum.photos/300/200?random=5",
    type: "image",
  },
  {
    id: 6,
    title: "Video quảng bá du lịch",
    url: "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
    thumbnail: "https://picsum.photos/300/200?random=6",
    type: "video",
  },
];

export const mockProcessingTickets: ProcessingTicket[] = [
  {
    id: 1,
    ticketName: "Đăng ký kinh doanh",
    ticketCode: "DKK001",
    createdDate: "2025-06-15",
    status: "pending",
  },
  {
    id: 2,
    ticketName: "Cấp phép xây dựng",
    ticketCode: "CPX002",
    createdDate: "2025-06-14",
    status: "processing",
  },
  {
    id: 3,
    ticketName: "Đăng ký tạm trú",
    ticketCode: "DTT003",
    createdDate: "2025-06-13",
    status: "completed",
  },
  {
    id: 4,
    ticketName: "Cấp giấy phép lao động",
    ticketCode: "GLD004",
    createdDate: "2025-06-12",
    status: "rejected",
  },
];

export const mockStats: StatData[] = [
  {
    number: "1,234",
    name: "Người dùng",
    description: "Tổng số người dùng đã đăng ký",
  },
  {
    number: "567",
    name: "Bài viết",
    description: "Số bài viết đã được xuất bản",
  },
  {
    number: "89",
    name: "Dịch vụ",
    description: "Số dịch vụ công trực tuyến",
  },
  {
    number: "12",
    name: "Phòng ban",
    description: "Số phòng ban tham gia",
  },
];

export const mockDoublePanelsData = [
  {
    leftPanel: {
      type: "image",
      content:
        "https://api.hocnhe.com/storage/v1/public/view/2025-06-04/original/d0b5850f77c9238dad2b6b08c81858df0f324f56-1749010250125.jpg",
      title: "Cảnh quan Khánh Hòa",
    },
    rightPanel: {
      type: "text",
      content:
        "Khánh Hòa là một tỉnh ven biển thuộc khu vực Nam Trung Bộ Việt Nam, nổi tiếng với những bãi biển đẹp và các hoạt động du lịch phong phú.",
      title: "01",
    },
  },
  {
    leftPanel: {
      type: "image",
      content:
        "https://api.hocnhe.com/storage/v1/public/view/2025-06-04/original/d0b5850f77c9238dad2b6b08c81858df0f324f56-1749010250125.jpg",
      title: "Cảnh quan Khánh Hòa",
    },
    rightPanel: {
      type: "text",
      content:
        "Khánh Hòa là một tỉnh ven biển thuộc khu vực Nam Trung Bộ Việt Nam, nổi tiếng với những bãi biển đẹp và các hoạt động du lịch phong phú.",
      title: "01",
    },
  },
];
