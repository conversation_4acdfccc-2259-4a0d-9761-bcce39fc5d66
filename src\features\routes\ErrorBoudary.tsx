import { useEffect, useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { LoadingPage } from "@/components/loading/LoadingPage";

export const ErrorBoundary = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [countdown, setCountdown] = useState(3);

  useEffect(() => {
    // Give some time for dynamic routes to load
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          // Try to navigate to home or show 404
          if (location.pathname.startsWith("/admin")) {
            navigate("/admin");
          } else {
            navigate("/");
          }
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [navigate, location.pathname]);

  if (countdown > 0) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <LoadingPage />
        <p className="mt-4 text-gray-600"><PERSON><PERSON> tải trang... ({countdown}s)</p>
      </div>
    );
  }

  return null;
};
