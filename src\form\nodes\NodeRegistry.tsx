/**
 * NodeRegistry - Mapping từ FormNode type đến Component
 */

import React from "react";
import { FormNode, FormNodeType } from "../types";
import { FrameNode } from "./FrameNode";
import { TitleNode } from "./TitleNode";
import { FieldNode } from "./FieldNode";
import { ControlNode } from "./ControlNode";
import { RedirectNode } from "./RedirectNode";

// Component props - tất cả node đều chỉ cần node prop
export interface NodeComponentProps {
  node: FormNode;
}

// Type cho component function
export type NodeComponent = React.FC<NodeComponentProps>;

// Mapping từ node type đến component
export const NODE_REGISTRY: Record<FormNodeType, NodeComponent> = {
  frame: FrameNode,
  title: TitleNode,
  field: FieldNode,
  control: ControlNode,
  redirect: RedirectNode,
};
