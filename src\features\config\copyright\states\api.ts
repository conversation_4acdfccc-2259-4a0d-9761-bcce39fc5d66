import { restApi } from "@/api/restApi";
import type { CopyrightConfig } from "./type";
import { AxiosResponse } from "axios";

// Base URLs for config API
const PUBLIC_CONFIG_URL = "/portal/v1/public/config";
const ADMIN_CONFIG_URL = "/portal/v1/admin/config";

// GET /portal/v1/public/config/copy_right
export async function fetchCopyrightConfig(): Promise<
  AxiosResponse<CopyrightConfig>
> {
  const res = await restApi.get<AxiosResponse<CopyrightConfig>>(
    `${PUBLIC_CONFIG_URL}/copy_right`
  );
  return res as unknown as AxiosResponse<CopyrightConfig>;
}

// POST /portal/v1/admin/config/copy_right
export async function updateCopyrightConfig(
  data: CopyrightConfig
): Promise<AxiosResponse<CopyrightConfig>> {
  const res = await restApi.post<AxiosResponse<CopyrightConfig>>(
    `${ADMIN_CONFIG_URL}/copy_right`,
    data
  );
  return res as unknown as AxiosResponse<CopyrightConfig>;
}
