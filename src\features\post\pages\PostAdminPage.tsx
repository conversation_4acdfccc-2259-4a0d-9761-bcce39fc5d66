import { useEffect, useState } from "react";
import { useLocation } from "react-router-dom";
import { useAppDispatch, useAppSelector } from "@/store/rootReducer";
import { setFilters, triggerRefetch } from "../states/slices";
import { PostStatus } from "../states/types";
import { selectPostQueryParams } from "../states/selector";
import { PostTable } from "../components/PostTable";
import { PageLoading } from "@/components/loading/LoadingPage";

const STATUS_VALUES: PostStatus[] = [
  "DRAFT",
  "REVIEW",
  "PUBLISHED",
  "UNPUBLISHED",
  "TRASH",
  "REJECTED",
];

const PostAdminPage = () => {
  const location = useLocation();
  const dispatch = useAppDispatch();
  const params = useAppSelector(selectPostQueryParams);
  const [isUrlSyncing, setIsUrlSyncing] = useState(false);

  // URL/Redux sync logic - ONLY active when this component is mounted
  useEffect(() => {
    console.log("🔄 PostAdminPage URL Sync - Location:", location.pathname);

    // Get the last segment of pathname
    const pathSegments = location.pathname.split("/").filter(Boolean);
    const lastSegment = pathSegments[pathSegments.length - 1];

    console.log("🧩 Path segments:", pathSegments);
    console.log("🎯 Last segment:", lastSegment);

    // Determine expected status from URL path end
    let expectedStatus: PostStatus;

    // Special case: URL ends with "quan-ly-bai-viet" (no status segment) = DRAFT
    if (lastSegment === "quan-ly-bai-viet" || lastSegment === "post-admin") {
      expectedStatus = "DRAFT";
    } else {
      // Check if last segment matches any status
      const upperSegment = lastSegment?.toUpperCase();
      if (STATUS_VALUES.includes(upperSegment as PostStatus)) {
        expectedStatus = upperSegment as PostStatus;
      } else {
        expectedStatus = "DRAFT"; // Default fallback
      }
    }

    console.log("🎯 Expected status from URL:", expectedStatus);
    console.log("🔍 Current Redux status:", params.status);

    // Compare with current Redux status
    const currentStatus = params.status;

    if (currentStatus === expectedStatus) {
      // Case 1: Location khớp với redux => chỉ cần trigger refresh (k sửa status)
      console.log("✅ Status matches - Triggering refresh only");
      setIsUrlSyncing(true);
      dispatch(triggerRefetch());
      // Stop syncing after a short delay
      setTimeout(() => setIsUrlSyncing(false), 200);
    } else {
      // Case 3: Location không khớp redux => Update lại status trong redux => Tự nó cũng refresh
      console.log(
        "🔄 Status mismatch - Updating Redux status:",
        expectedStatus
      );
      setIsUrlSyncing(true);
      dispatch(setFilters({ status: expectedStatus, page: 0 }));
    }
  }, [location.pathname, dispatch]);

  // Watch for Redux status changes to stop loading
  useEffect(() => {
    if (!isUrlSyncing) return;

    const pathSegments = location.pathname.split("/").filter(Boolean);
    const lastSegment = pathSegments[pathSegments.length - 1];

    let expectedStatus: PostStatus;

    if (lastSegment === "quan-ly-bai-viet" || lastSegment === "post-admin") {
      expectedStatus = "DRAFT";
    } else {
      const upperSegment = lastSegment?.toUpperCase();
      if (STATUS_VALUES.includes(upperSegment as PostStatus)) {
        expectedStatus = upperSegment as PostStatus;
      } else {
        expectedStatus = "DRAFT";
      }
    }

    console.log(
      "👀 Watching params change - Expected:",
      expectedStatus,
      "Current:",
      params.status
    );

    // If Redux status now matches URL, stop syncing
    if (params.status === expectedStatus) {
      console.log("✅ Redux status synced with URL - Stopping sync");
      setIsUrlSyncing(false);
    }
  }, [params.status, location.pathname, isUrlSyncing]);

  // Show full page loading when syncing, otherwise show PostTable
  if (isUrlSyncing) {
    return <PageLoading message="Đang tải danh sách bài viết..." />;
  }

  return <PostTable isUrlSyncing={false} />;
};

export default PostAdminPage;
