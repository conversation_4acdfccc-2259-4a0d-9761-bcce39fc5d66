import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { InformationPortalTab } from "../components/InformationPortalTab";
import { DataWarehouseTab } from "../components/DataWarehouseTab";
import { generateMockDashboardData, DashboardData } from "../data/mockData";

const DashboardPage = () => {
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(
    null
  );
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Simulate API call with mock data
    const loadData = async () => {
      setLoading(true);
      // Simulate network delay
      await new Promise((resolve) => setTimeout(resolve, 500));
      const data = generateMockDashboardData();
      setDashboardData(data);
      setLoading(false);
    };

    loadData();
  }, []);

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-48 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="h-96 bg-gray-200 rounded"></div>
            <div className="h-96 bg-gray-200 rounded"></div>
            <div className="h-96 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!dashboardData) {
    return (
      <div className="p-6">
        <div className="text-center py-12">
          <p className="text-gray-500">Không thể tải dữ liệu dashboard</p>
        </div>
      </div>
    );
  }

  const {
    stats,
    userGrowth,
    contentDistribution,
    recentActivities,
    dataWarehouse,
  } = dashboardData;

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600 mt-1">Tổng quan hoạt động hệ thống</p>
      </div>

      {/* Tabs */}
      <Tabs defaultValue="portal" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="portal">Cổng thông tin</TabsTrigger>
          <TabsTrigger value="warehouse">Kho dữ liệu</TabsTrigger>
        </TabsList>

        <TabsContent value="portal" className="mt-6">
          <InformationPortalTab
            stats={stats}
            userGrowth={userGrowth}
            contentDistribution={contentDistribution}
            recentActivities={recentActivities}
          />
        </TabsContent>

        <TabsContent value="warehouse" className="mt-6">
          <DataWarehouseTab data={dataWarehouse} />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default DashboardPage;
