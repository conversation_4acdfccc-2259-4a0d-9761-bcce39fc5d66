import React, { useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Users, Settings } from "lucide-react";
import { useAppSelector, useAppDispatch } from "@/store/rootReducer";
import { RoleSelectionPopup } from "../components/RoleSelectionPopup";
import { PermissionMatrix } from "../components/PermissionMatrix";
import { usePermissionQueryParams } from "../hooks/usePermissionQueryParams";
import {
  setRoleSelectionOpen,
  fetchRolePermissionsThunk,
  setMode,
  setSelectedRole,
} from "../states/slices";
import { fetchRolesThunk } from "@/features/role/states/slices";
import { fetchResourcesThunk } from "@/features/resource/states/slices";
import { fetchActionsThunk } from "@/features/action/states/slices";
import {
  selectSelectedRoleId,
  selectSelectedRole,
  selectLoadingStates,
} from "../states/selectors";

const PermissionAdminPage: React.FC = () => {
  const dispatch = useAppDispatch();

  // URL sync
  usePermissionQueryParams();

  const selectedRoleId = useAppSelector(selectSelectedRoleId);
  const selectedRole = useAppSelector(selectSelectedRole);
  const { isLoading } = useAppSelector(selectLoadingStates);

  // Load initial data once
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        // Load roles, resources, and actions in parallel
        await Promise.all([
          dispatch(fetchRolesThunk()).unwrap(),
          dispatch(fetchResourcesThunk()).unwrap(),
          dispatch(fetchActionsThunk()).unwrap(),
        ]);
      } catch (error) {
        console.error("Failed to load initial data:", error);
      }
    };

    loadInitialData();
  }, [dispatch]);

  // Load permissions when a valid role is selected
  useEffect(() => {
    if (selectedRoleId) {
      // Fetch permissions for the selected role
      dispatch(fetchRolePermissionsThunk(selectedRoleId));
      // Ensure we're in view mode when switching roles
      dispatch(setMode("view"));
    }
  }, [selectedRoleId, dispatch]);

  // Cleanup when component unmounts - only clear Redux state
  useEffect(() => {
    return () => {
      // Only clear Redux state, let URL be handled by navigation
      dispatch(setSelectedRole(null));
      dispatch(setMode("view"));
    };
  }, [dispatch]);

  const handleSelectRole = () => {
    dispatch(setRoleSelectionOpen(true));
  };

  if (isLoading) {
    return (
      <div className="container mx-auto p-6 max-w-7xl">
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Đang tải dữ liệu...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 max-w-7xl space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Quản lý phân quyền
          </h1>
          <p className="text-gray-600 mt-1">
            Cấu hình quyền truy cập cho từng vai trò trong hệ thống
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Settings className="h-5 w-5 text-gray-500" />
        </div>
      </div>

      {/* Role Selection Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Users className="w-5 h-5 mr-2" />
            Bước 1: Chọn vai trò
          </CardTitle>
        </CardHeader>
        <CardContent>
          {!selectedRole ? (
            <div className="text-center py-8">
              <div className="mb-4">
                <Users className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                <p className="text-gray-600">
                  Chưa chọn vai trò nào. Vui lòng chọn vai trò để bắt đầu phân
                  quyền.
                </p>
              </div>
              <Button
                onClick={handleSelectRole}
                className="bg-blue-600 hover:bg-blue-700"
              >
                <Users className="w-4 h-4 mr-2" />
                Chọn vai trò
              </Button>
            </div>
          ) : (
            <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg border border-blue-200">
              <div className="flex-1">
                <h3 className="font-medium text-blue-900">
                  {selectedRole.name}
                </h3>
                <p className="text-sm text-blue-700 mt-1">
                  Mã: <span className="font-mono">{selectedRole.code}</span>
                  <span className="mx-2">•</span>
                  ID: {selectedRole.id}
                </p>
                <p className="text-xs text-blue-600 mt-1">
                  Tạo:{" "}
                  {new Date(selectedRole.createdAt).toLocaleDateString("vi-VN")}
                </p>
              </div>
              <Button
                onClick={handleSelectRole}
                variant="outline"
                size="sm"
                className="border-blue-300 text-blue-700 hover:bg-blue-100"
              >
                Đổi vai trò
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Permission Matrix Section */}
      {selectedRoleId && selectedRole && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                Bước 2: Cấu hình quyền
              </h2>
              <p className="text-gray-600 text-sm mt-1">
                Thiết lập quyền truy cập cho vai trò "{selectedRole.name}"
              </p>
            </div>
          </div>

          <PermissionMatrix
            roleId={selectedRoleId}
            roleName={selectedRole.name}
          />
        </div>
      )}

      {/* Instructions */}
      {!selectedRoleId && (
        <Card>
          <CardHeader>
            <CardTitle>Hướng dẫn sử dụng</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">
                  Quy trình phân quyền:
                </h4>
                <ol className="text-sm text-gray-600 space-y-1 list-decimal list-inside">
                  <li>Chọn vai trò cần phân quyền</li>
                  <li>Xem quyền hiện tại được cấp</li>
                  <li>Nhấn "Chỉnh sửa" để thay đổi</li>
                  <li>Tick/bỏ tick các quyền cần thiết</li>
                  <li>Nhấn "Lưu thay đổi" để cập nhật</li>
                </ol>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 mb-2">
                  Lưu ý quan trọng:
                </h4>
                <ul className="text-sm text-gray-600 space-y-1 list-disc list-inside">
                  <li>Mỗi resource chỉ hiển thị actions được hỗ trợ</li>
                  <li>Thay đổi được lưu tự động vào server</li>
                  <li>Có thể hủy thay đổi trước khi lưu</li>
                  <li>URL được đồng bộ để có thể bookmark</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Role Selection Popup */}
      <RoleSelectionPopup
        onRoleSelect={(role) => {
          console.log("Selected role:", role);
        }}
      />
    </div>
  );
};

export default PermissionAdminPage;
