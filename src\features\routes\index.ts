// 🚀 Routes Module Exports
export { AppRouter } from "./AppRouter";
export { ErrorBoundary } from "./ErrorBoudary";

// Route Builders
export { useAdminRoutes } from "./builders/AdminRoutes";
export { usePublicRoutes } from "./builders/PublicRoutes";
export { useUserRoutes } from "./builders/UserRoutes";

// Component Registry
export { componentRegistry } from "./registry/ComponentRegistry";

// Component Picker
export {
  ComponentPicker,
  getComponentOptions,
} from "./components/ComponentPicker";
export type { ComponentOption } from "./components/ComponentPicker";

// Route Configuration
export type { RouteConfig } from "./config/RouteConfig";

// Dynamic Route Builder
export { useDynamicRouteBuilder } from "./builders/DynamicRouteBuilder";
