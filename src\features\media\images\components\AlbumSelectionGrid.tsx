import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Loader2, Image as ImageIcon, Video } from "lucide-react";
import { fetchAlbumsByType } from "../states/api";
import { Album, AlbumType } from "../states/types";
import { toast } from "sonner";

interface AlbumSelectionGridProps {
  albumType: AlbumType;
  currentAlbumId?: number;
  onSelectAlbum: (album: Album) => void;
}

export function AlbumSelectionGrid({
  albumType,
  currentAlbumId,
  onSelectAlbum,
}: AlbumSelectionGridProps) {
  const [albums, setAlbums] = useState<Album[]>([]);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(0);

  const loadAlbums = async (pageNum: number = 0, reset: boolean = false) => {
    if (loading) return;

    setLoading(true);
    try {
      const response = await fetchAlbumsByType(albumType, {
        page: pageNum,
        size: 12,
        // status: "PUBLISHED", // Only show published albums
      });

      if (reset) {
        setAlbums(response.data);
      } else {
        setAlbums((prev) => [...prev, ...response.data]);
      }

      setHasMore(response.hasMore || false);
      setPage(pageNum);
    } catch (error) {
      toast.error("Không thể tải danh sách thư viện: " + error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadAlbums(0, true);
  }, [albumType]);

  const handleLoadMore = () => {
    if (hasMore && !loading) {
      loadAlbums(page + 1, false);
    }
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case "PUBLISHED":
        return "default";
      case "DRAFT":
        return "secondary";
      case "REVIEW":
        return "outline";
      default:
        return "secondary";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "PUBLISHED":
        return "Đã xuất bản";
      case "DRAFT":
        return "Bản nháp";
      case "REVIEW":
        return "Đang xem xét";
      case "UNPUBLISHED":
        return "Chưa xuất bản";
      default:
        return status;
    }
  };

  // Filter out current album
  const filteredAlbums = albums.filter(
    (album) => album.id !== currentAlbumId
  );

  if (loading && albums.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-2" />
          <p className="text-sm text-muted-foreground">Đang tải thư viện...</p>
        </div>
      </div>
    );
  }

  if (filteredAlbums.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="w-16 h-16 bg-muted rounded-lg flex items-center justify-center mx-auto mb-3">
            {albumType === "image" ? (
              <ImageIcon className="w-8 h-8 text-muted-foreground" />
            ) : (
              <Video className="w-8 h-8 text-muted-foreground" />
            )}
          </div>
          <p className="text-sm text-muted-foreground">
            Không có thư viện {albumType === "image" ? "ảnh" : "video"} nào khác
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex flex-col gap-2">
        {filteredAlbums.map((album) => (
          <div
            key={album.id}
            className="border rounded-lg p-4 cursor-pointer hover:bg-muted/50 transition-colors w-full"
            onClick={() => onSelectAlbum(album)}
          >
            {/* Album Info - Horizontal Layout */}
            <div className="flex items-center justify-between w-full">
              <div className="flex items-center gap-3">
                {/* Album Cover Image */}
                <div className="w-12 h-12 rounded-md overflow-hidden bg-muted flex-shrink-0">
                  {album.coverImage ? (
                    <img
                      src={album.coverImage}
                      alt={album.name}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center">
                      {albumType === "image" ? (
                        <ImageIcon className="w-6 h-6 text-muted-foreground" />
                      ) : (
                        <Video className="w-6 h-6 text-muted-foreground" />
                      )}
                    </div>
                  )}
                </div>

                {/* Album Name */}
                <h4 className="font-medium text-sm">{album.name}</h4>
              </div>

              {/* Status Badge */}
              <Badge
                variant={getStatusBadgeVariant(album.status)}
                className="text-xs flex-shrink-0"
              >
                {getStatusText(album.status)}
              </Badge>
            </div>
          </div>
        ))}
      </div>

      {/* Load More Button */}
      {hasMore && (
        <div className="flex justify-center">
          <Button
            variant="outline"
            onClick={handleLoadMore}
            disabled={loading}
            size="sm"
          >
            {loading && <Loader2 className="w-4 h-4 animate-spin mr-2" />}
            Xem thêm
          </Button>
        </div>
      )}
    </div>
  );
}
