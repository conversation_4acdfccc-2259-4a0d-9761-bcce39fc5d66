import React from "react";
import { Menu } from "lucide-react";
import { Logo } from "@/components/other/Logo";
import { UserInfoDesktop } from "./UserInfoDesktop";
import { PublicMenuHorizontal } from "./PublicMenuHorizontal";
import { MobileSlideMenu } from "./MobileSlideMenu";
import { RoleSelector } from "./RoleSelector";
import { useAppSelector, useAppDispatch } from "@/store/rootReducer";
import { selectAuthState } from "@/features/auth/states/selector";
import {
  selectShowSidebarToggle,
  selectShowPublicMenu,
} from "../../states/selector";
import { toggleMobileMenu, toggleSidebar } from "../../states/slices";
import { Button } from "@/components/ui/button";
import { Link } from "react-router-dom";

export const SharedHeader: React.FC = () => {
  const dispatch = useAppDispatch();
  const { is_authenticated } = useAppSelector(selectAuthState);
  const showToggle = useAppSelector(selectShowSidebarToggle);
  const showPublicMenu = useAppSelector(selectShowPublicMenu);

  const handleMobileMenuToggle = () => {
    dispatch(toggleMobileMenu());
  };

  const handleSidebarToggle = () => {
    dispatch(toggleSidebar());
  };

  return (
    <>
      {/* Fixed Header with two rows */}
      <div className="fixed top-0 left-0 w-full bg-white shadow-md z-10">
        {/* Top row - Logo and Auth Info */}
        <div className="flex items-center justify-between px-4 h-16 border-b">
          <div className="flex items-center gap-3">
            {/* Toggle Button (when sidebar is collapsed) */}
            {showToggle && (
              <button
                onClick={handleSidebarToggle}
                className="p-1.5 bg-gray-50 border rounded-md hover:bg-gray-100 transition-colors"
                aria-label="Mở menu"
              >
                <Menu className="w-4 h-4 text-gray-600" />
              </button>
            )}

            <Logo />
          </div>

          {/* Desktop - Auth Info or Login/Register */}
          <div className="hidden lg:flex items-center gap-4">
            {is_authenticated && <RoleSelector />}
            {is_authenticated ? (
              <UserInfoDesktop />
            ) : (
              <div className="flex items-center gap-3">
                <Link to="/auth/login">
                  <Button variant="default" size="sm">
                    Đăng nhập
                  </Button>
                </Link>
                <Link to="/auth/register">
                  <Button variant="ghost" size="sm">
                    Đăng ký
                  </Button>
                </Link>
              </div>
            )}
          </div>

          {/* Mobile/Tablet - Always show only Toggle button */}
          <button
            onClick={handleMobileMenuToggle}
            className="lg:hidden p-1.5 bg-gray-50 border rounded-md hover:bg-gray-100 transition-colors"
            aria-label="Mở menu"
          >
            <Menu className="w-4 h-4 text-gray-600" />
          </button>
        </div>

        {/* Bottom row - Public Menu (when enabled) */}
        {showPublicMenu && (
          <div className="h-12 border-b">
            <PublicMenuHorizontal />
          </div>
        )}
      </div>

      {/* Mobile Slide Menu */}
      <MobileSlideMenu />
    </>
  );
};
