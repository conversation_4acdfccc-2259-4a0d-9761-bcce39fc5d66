import React, { useEffect, useState } from "react";
import { useAppDispatch, useAppSelector } from "@/store/rootReducer";
import {
  selectSelectedGroup,
  selectGroupRolesByGroupId,
  selectGroupLoading,
} from "../states/selectors";
import {
  fetchGroupRolesThunk,
  createGroupRoleThunk,
  updateGroupRoleThunk,
  deleteGroupRoleThunk,
} from "../states/slices";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Plus, Pencil, Trash2, Shield } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { GroupRole } from "../states/types";

export const GroupRoles: React.FC = () => {
  const dispatch = useAppDispatch();
  const selectedGroup = useAppSelector(selectSelectedGroup);
  const loading = useAppSelector(selectGroupLoading);

  const roles = useAppSelector((state) =>
    selectedGroup ? selectGroupRolesByGroupId(state, selectedGroup.id) : []
  );

  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [editingRole, setEditingRole] = useState<GroupRole | null>(null);
  const [confirmDelete, setConfirmDelete] = useState<{
    show: boolean;
    role: GroupRole | null;
  }>({ show: false, role: null });

  const [formData, setFormData] = useState({
    name: "",
    description: "",
    permissions: [] as string[],
  });

  useEffect(() => {
    if (selectedGroup) {
      dispatch(fetchGroupRolesThunk(selectedGroup.id));
    }
  }, [dispatch, selectedGroup]);

  useEffect(() => {
    if (editingRole) {
      setFormData({
        name: editingRole.name,
        description: editingRole.description,
        permissions: [...editingRole.permissions],
      });
    } else {
      setFormData({
        name: "",
        description: "",
        permissions: [],
      });
    }
  }, [editingRole]);

  const handleCreateRole = async () => {
    if (!selectedGroup || !formData.name) return;

    await dispatch(
      createGroupRoleThunk({
        groupId: selectedGroup.id,
        data: {
          name: formData.name,
          description: formData.description,
          permissions: formData.permissions,
        },
      })
    );

    setShowCreateDialog(false);
    setFormData({ name: "", description: "", permissions: [] });
  };

  const handleUpdateRole = async () => {
    if (!selectedGroup || !editingRole || !formData.name) return;

    await dispatch(
      updateGroupRoleThunk({
        groupId: selectedGroup.id,
        roleId: editingRole.id,
        data: {
          name: formData.name,
          description: formData.description,
          permissions: formData.permissions,
        },
      })
    );

    setShowEditDialog(false);
    setEditingRole(null);
  };

  const handleDeleteRole = async () => {
    if (!selectedGroup || !confirmDelete.role) return;

    await dispatch(
      deleteGroupRoleThunk({
        groupId: selectedGroup.id,
        roleId: confirmDelete.role.id,
      })
    );

    setConfirmDelete({ show: false, role: null });
  };

  const handleAddPermission = (permission: string) => {
    if (!formData.permissions.includes(permission)) {
      setFormData((prev) => ({
        ...prev,
        permissions: [...prev.permissions, permission],
      }));
    }
  };

  const handleRemovePermission = (permission: string) => {
    setFormData((prev) => ({
      ...prev,
      permissions: prev.permissions.filter((p) => p !== permission),
    }));
  };

  const availablePermissions = [
    "read",
    "write",
    "delete",
    "manage_members",
    "manage_roles",
    "admin",
  ];

  if (!selectedGroup) {
    return (
      <div className="p-6 text-center text-gray-500">
        Chọn một nhóm để xem danh sách vai trò
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-lg font-semibold">Vai trò nhóm ({roles.length})</h3>

        <Button onClick={() => setShowCreateDialog(true)} disabled={loading}>
          <Plus className="h-4 w-4 mr-2" />
          Tạo vai trò
        </Button>
      </div>

      {/* Roles Table */}
      {roles.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          Chưa có vai trò nào
        </div>
      ) : (
        <div className="border rounded-lg">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Tên vai trò</TableHead>
                <TableHead>Mô tả</TableHead>
                <TableHead>Quyền hạn</TableHead>
                <TableHead className="text-right">Hành động</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {roles.map((role) => (
                <TableRow key={role.id}>
                  <TableCell className="font-medium">
                    <div className="flex items-center">
                      <Shield className="h-4 w-4 mr-2 text-orange-500" />
                      {role.name}
                    </div>
                  </TableCell>
                  <TableCell>{role.description}</TableCell>
                  <TableCell>
                    <div className="flex flex-wrap gap-1">
                      {role.permissions.map((permission) => (
                        <Badge
                          key={permission}
                          variant="secondary"
                          className="text-xs"
                        >
                          {permission}
                        </Badge>
                      ))}
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex gap-1 justify-end">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setEditingRole(role);
                          setShowEditDialog(true);
                        }}
                        disabled={loading}
                      >
                        <Pencil className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setConfirmDelete({ show: true, role })}
                        disabled={loading}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}

      {/* Create Role Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Tạo vai trò mới</DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <Label htmlFor="roleName">Tên vai trò *</Label>
              <Input
                id="roleName"
                value={formData.name}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, name: e.target.value }))
                }
                placeholder="Nhập tên vai trò"
              />
            </div>

            <div>
              <Label htmlFor="roleDesc">Mô tả</Label>
              <Textarea
                id="roleDesc"
                value={formData.description}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    description: e.target.value,
                  }))
                }
                placeholder="Nhập mô tả vai trò"
              />
            </div>

            <div>
              <Label>Quyền hạn</Label>
              <div className="grid grid-cols-2 gap-2 mt-2">
                {availablePermissions.map((permission) => (
                  <Button
                    key={permission}
                    variant={
                      formData.permissions.includes(permission)
                        ? "default"
                        : "outline"
                    }
                    size="sm"
                    onClick={() => {
                      if (formData.permissions.includes(permission)) {
                        handleRemovePermission(permission);
                      } else {
                        handleAddPermission(permission);
                      }
                    }}
                  >
                    {permission}
                  </Button>
                ))}
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowCreateDialog(false)}
              disabled={loading}
            >
              Hủy
            </Button>
            <Button
              onClick={handleCreateRole}
              disabled={loading || !formData.name}
            >
              Tạo
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Role Dialog */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Chỉnh sửa vai trò</DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <Label htmlFor="editRoleName">Tên vai trò *</Label>
              <Input
                id="editRoleName"
                value={formData.name}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, name: e.target.value }))
                }
                placeholder="Nhập tên vai trò"
              />
            </div>

            <div>
              <Label htmlFor="editRoleDesc">Mô tả</Label>
              <Textarea
                id="editRoleDesc"
                value={formData.description}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    description: e.target.value,
                  }))
                }
                placeholder="Nhập mô tả vai trò"
              />
            </div>

            <div>
              <Label>Quyền hạn</Label>
              <div className="grid grid-cols-2 gap-2 mt-2">
                {availablePermissions.map((permission) => (
                  <Button
                    key={permission}
                    variant={
                      formData.permissions.includes(permission)
                        ? "default"
                        : "outline"
                    }
                    size="sm"
                    onClick={() => {
                      if (formData.permissions.includes(permission)) {
                        handleRemovePermission(permission);
                      } else {
                        handleAddPermission(permission);
                      }
                    }}
                  >
                    {permission}
                  </Button>
                ))}
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowEditDialog(false)}
              disabled={loading}
            >
              Hủy
            </Button>
            <Button
              onClick={handleUpdateRole}
              disabled={loading || !formData.name}
            >
              Cập nhật
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Role Confirmation Dialog */}
      <Dialog
        open={confirmDelete.show}
        onOpenChange={(open) => setConfirmDelete({ show: open, role: null })}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Xác nhận xóa vai trò</DialogTitle>
          </DialogHeader>

          <p>
            Bạn có chắc chắn muốn xóa vai trò{" "}
            <strong>{confirmDelete.role?.name}</strong>?
          </p>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setConfirmDelete({ show: false, role: null })}
              disabled={loading}
            >
              Hủy
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteRole}
              disabled={loading}
            >
              Xóa
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};
