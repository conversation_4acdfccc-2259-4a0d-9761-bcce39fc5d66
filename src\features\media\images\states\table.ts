import { ColumnConfig } from "@/components/table/registry";

export const album_columns: ColumnConfig[] = [
  { accessorKey: "id", header: "ID" },
  {
    accessorKey: "coverImage",
    header: "Ảnh bìa",
    cell: {
      component: "ImageCell",
      props: {
        className: "w-20 h-20 object-cover rounded-md",
      },
    },
  },
  { accessorKey: "name", header: "Tên thư viện ảnh" },
  {
    accessorKey: "status",
    header: "Trạng thái",
    cell: {
      component: "AlbumBadge",
      props: {},
    },
  },
  { accessorKey: "type", header: "Loại", visible: false },
  {
    accessorKey: "description.text",
    header: "<PERSON><PERSON> tả",
    visible: false,
  },
  {
    accessorKey: "updatedAt",
    header: "Cập nhật lần cuối",
    visible: false,
    cell: {
      component: "DateTimeCell",
      props: {
        format: "dd/mm/yyyy HH:mm",
      },
    },
  },
  {
    accessorKey: "actions",
    header: "<PERSON><PERSON> tác",
    cell: {
      component: "AlbumAction",
      props: {},
    },
  },
];
