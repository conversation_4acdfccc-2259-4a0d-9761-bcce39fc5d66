import React, { useMemo } from "react";
import { RouteObject } from "react-router-dom";
import { useAppSelector } from "@/store/rootReducer";
import { selectCategoryByType } from "@/features/category/states/selector";
import { CategoryType, CategoryDTO } from "@/features/category/states/types";
import { componentRegistry } from "../registry/ComponentRegistry";
import { PageNotFound } from "@/features/notfound/loadable";

interface RouteBuilderOptions {
  type: CategoryType;
  basePath?: string;
  includeStaticRoutes?: boolean;
  buildNestedPaths?: boolean;
}

/**
 * 🚀 Dynamic Route Builder
 *
 * ✅ Key Features:
 * - Database-driven route generation
 * - Hierarchical path building
 * - Component registry integration
 * - Automatic category loading
 * - Flexible configuration
 */
export const useDynamicRouteBuilder = (options: RouteBuilderOptions) => {
  const {
    type,
    includeStaticRoutes = true,
    buildNestedPaths = false,
  } = options;

  // Get categories from Redux store
  const categories = useAppSelector((state) => {
    try {
      return selectCategoryByType(state, type);
    } catch (error) {
      console.warn(`Error getting categories for type "${type}":`, error);
      return [];
    }
  });

  // Note: Categories are loaded by AppRouter's useMultipleCategoriesSimple
  // No auto-loading here to prevent duplicate API calls

  // Build hierarchical path for nested categories
  const buildFullPath = (
    category: CategoryDTO,
    allCategories: CategoryDTO[]
  ): string => {
    if (!buildNestedPaths) {
      return category.slug;
    }

    const categoryMap = new Map<number, CategoryDTO>();
    allCategories.forEach((cat) => categoryMap.set(cat.id, cat));

    const pathSegments: string[] = [];
    let current: CategoryDTO | undefined = category;

    while (current) {
      pathSegments.unshift(current.slug);
      if (current.parentId === null) {
        break;
      }
      current = categoryMap.get(current.parentId);
    }

    return pathSegments.join("/");
  };

  // Generate dynamic routes from categories
  const dynamicRoutes = useMemo<RouteObject[]>(() => {
    console.log(
      `🏗️ Building routes for ${type}, categories:`,
      categories.length
    );

    const routes: RouteObject[] = categories
      .filter((cat) => {
        // Only include categories with valid component references
        const hasComponent = !!cat?.description?.component;
        if (!hasComponent) {
          console.warn(
            `Category "${cat.name}" (${cat.slug}) missing component reference`
          );
        }
        return hasComponent;
      })
      .map<RouteObject>((cat) => {
        const fullPath = buildFullPath(cat, categories);
        const componentName = cat.description.component;

        console.log(`📍 Creating route: ${fullPath} → ${componentName}`);

        return {
          path: fullPath,
          element: componentRegistry.renderComponent(componentName),
          // Add metadata for debugging
          id: `${type}-${cat.id}`,
        };
      });

    console.log(`✅ Generated ${routes.length} dynamic routes for ${type}`);
    return routes;
  }, [categories, type, buildNestedPaths]);

  // Static fallback routes
  const staticRoutes = useMemo<RouteObject[]>(() => {
    if (!includeStaticRoutes) return [];

    const routes: RouteObject[] = [];

    // Add fallback route for unknown paths
    routes.push({
      path: "*",
      element: React.createElement(PageNotFound),
      id: `${type}-fallback`,
    });

    return routes;
  }, [type, includeStaticRoutes]);

  // Combine all routes
  const allRoutes = useMemo<RouteObject[]>(() => {
    return [...dynamicRoutes, ...staticRoutes];
  }, [dynamicRoutes, staticRoutes]);

  return {
    routes: allRoutes,
    dynamicRoutes,
    staticRoutes,
    categoriesLoaded: categories.length > 0,
    categoryCount: categories.length,
    isReady: categories.length > 0, // Routes ready when categories are loaded
  };
};

// Removed buildRoutesForType to avoid hook rules violation
