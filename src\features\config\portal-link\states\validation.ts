import { z } from "zod";

/**
 * Validation schema for a single portal link item
 * Validates title, URL, and image fields with appropriate constraints
 */
export const portalLinkItemSchema = z.object({
  title: z.string().min(1, "<PERSON>ui lòng nhập Tiêu đề").trim(),
  url: z.string().min(1, "Vui lòng nhập URL").url("URL không hợp lệ").trim(),
  image: z
    .string()
    .min(1, "Vui lòng nhập URL")
    .url("URL hình ảnh không hợp lệ")
    .trim(),
});

/**
 * Validation schema for the complete portal link configuration
 * Validates the array of portal link items
 */
export const portalLinkConfigSchema = z.object({
  portalLinks: z
    .array(portalLinkItemSchema)
    .min(1, "<PERSON><PERSON>i có ít nhất một cổng liên kết"),
});

/**
 * Type inference for portal link item validation
 */
export type PortalLinkItemValidation = z.infer<typeof portalLinkItemSchema>;

/**
 * Type inference for portal link config validation
 */
export type PortalLinkConfigValidation = z.infer<typeof portalLinkConfigSchema>;
