import React, { useState, useEffect } from "react";
import { useAppDispatch, useAppSelector } from "@/store/rootReducer";
import { selectCurrentResource } from "../states/selectors";
import {
  createResourceThunk,
  updateResourceThunk,
  deleteResourceThunk,
  setCurrentResource,
} from "../states/slices";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Dialog,
  DialogTrigger,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { ResourceAction, ResourceRequest } from "../states/types";
import { Edit, Save, X, Trash2 } from "lucide-react";
import { useSwitchMode } from "../states/hooks";
import { fetchActionsThunk } from "@/features/action/states/slices";
import { selectActions } from "@/features/action/states/selectors";
import { toast } from "sonner";
import { useLocation, useNavigate } from "react-router-dom";

interface ResourceFormProps {
  currentAction: ResourceAction;
  onActionChange: (action: ResourceAction) => void;
}

export const ResourceForm: React.FC<ResourceFormProps> = ({
  currentAction,
  onActionChange,
}) => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const switchMode = useSwitchMode();
  const currentResource = useAppSelector(selectCurrentResource);
  const actions = useAppSelector(selectActions);

  const [formData, setFormData] = useState({
    code: "",
    name: "",
    actions: [] as number[],
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [confirmDelete, setConfirmDelete] = useState(false);

  // Fetch actions for checkbox options
  useEffect(() => {
    dispatch(fetchActionsThunk());
  }, [dispatch]);

  // Initialize form data when resource changes or mode changes
  useEffect(() => {
    if (currentAction === "create") {
      setFormData({
        code: "",
        name: "",
        actions: [],
      });
    } else if (
      currentResource &&
      (currentAction === "edit" || currentAction === "view")
    ) {
      setFormData({
        code: currentResource.code,
        name: currentResource.name,
        actions: currentResource.actions || [],
      });
    }
  }, [currentResource, currentAction]);

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleActionToggle = (actionId: number, checked: boolean) => {
    setFormData((prev) => ({
      ...prev,
      actions: checked
        ? [...prev.actions, actionId]
        : prev.actions.filter((id) => id !== actionId),
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (isSubmitting) return;

    // Validation
    if (!formData.code.trim() || !formData.name.trim()) {
      toast.error("Vui lòng nhập đầy đủ thông tin");
      return;
    }

    setIsSubmitting(true);
    try {
      const payload: ResourceRequest = {
        code: formData.code,
        name: formData.name,
        actions: formData.actions,
        metadata: {},
      };

      if (currentAction === "create") {
        const result = await dispatch(createResourceThunk(payload)).unwrap();
        dispatch(setCurrentResource(result));

        // Update URL to reflect the new resource
        const searchParams = new URLSearchParams(location.search);
        searchParams.set("mode", "detail");
        searchParams.set("resource", result.id.toString());
        searchParams.set("actionType", "view");
        navigate(`${location.pathname}?${searchParams.toString()}`);

        onActionChange("view");
      } else if (currentAction === "edit" && currentResource) {
        const result = await dispatch(
          updateResourceThunk({
            id: currentResource.id,
            data: payload,
          })
        ).unwrap();
        dispatch(setCurrentResource(result));
        onActionChange("view");
      }
    } catch (error) {
      console.error("Error saving resource:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    if (currentAction === "create") {
      onActionChange("view");
    } else if (currentAction === "edit") {
      onActionChange("view");
      // Reset form data
      if (currentResource) {
        setFormData({
          code: currentResource.code,
          name: currentResource.name,
          actions: currentResource.actions || [],
        });
      }
    }
  };

  const handleDelete = async () => {
    if (!currentResource) return;

    setIsSubmitting(true);
    try {
      // First, clear current resource to prevent any race conditions
      dispatch(setCurrentResource(null));

      // Then delete the resource from server
      await dispatch(deleteResourceThunk(currentResource.id)).unwrap();

      // Finally navigate to list mode (this will clear URL params)
      switchMode("list");

      setConfirmDelete(false);
    } catch (error) {
      console.error("Error deleting resource:", error);
      // If delete failed, we might want to restore the resource
      // but for now just navigate back to list
      switchMode("list");
    } finally {
      setIsSubmitting(false);
    }
  };

  const isEditing = currentAction === "edit" || currentAction === "create";
  const isCreating = currentAction === "create";

  // Get selected actions for view mode
  const selectedActions = actions.filter((action) =>
    formData.actions.includes(action.id)
  );

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle>
            {isCreating ? "Tạo nhóm quyền mới" : "Thông tin nhóm quyền"}
          </CardTitle>
          {!isEditing && currentResource && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => onActionChange("edit")}
            >
              <Edit className="h-4 w-4 mr-2" />
              Chỉnh sửa
            </Button>
          )}
        </div>
      </CardHeader>

      <CardContent>
        {!currentResource && currentAction !== "create" ? (
          <div className="text-center py-8 text-gray-500">
            Chọn tạo mới hoặc chọn một nhóm quyền để xem thông tin
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Basic Info */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label
                  htmlFor="code"
                  className="text-sm font-medium text-gray-600"
                >
                  Mã nhóm quyền <span className="text-red-500">*</span>
                </Label>
                {isEditing ? (
                  <Input
                    id="code"
                    value={formData.code}
                    onChange={(e) => handleInputChange("code", e.target.value)}
                    placeholder="vd: user_management, post_management"
                    disabled={isSubmitting}
                  />
                ) : (
                  <p className="text-base font-semibold text-gray-900 bg-gray-50 p-3 rounded-md">
                    {formData.code}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label
                  htmlFor="name"
                  className="text-sm font-medium text-gray-600"
                >
                  Tên nhóm quyền <span className="text-red-500">*</span>
                </Label>
                {isEditing ? (
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => handleInputChange("name", e.target.value)}
                    placeholder="Nhập tên nhóm quyền"
                    disabled={isSubmitting}
                  />
                ) : (
                  <p className="text-base font-semibold text-gray-900 bg-gray-50 p-3 rounded-md">
                    {formData.name}
                  </p>
                )}
              </div>
            </div>

            {/* Actions Selection */}
            <div className="space-y-4">
              <Label className="text-sm font-medium text-gray-600">
                Quyền thuộc nhóm này ({formData.actions.length} quyền)
              </Label>

              {/* View Mode - Show as badges */}
              {!isEditing && (
                <div className="border rounded-lg p-4 min-h-[120px]">
                  {selectedActions.length === 0 ? (
                    <p className="text-sm text-gray-500 italic">
                      Chưa có quyền nào được gán cho nhóm này
                    </p>
                  ) : (
                    <div className="flex flex-wrap gap-2">
                      {selectedActions.map((action) => (
                        <Badge
                          key={action.id}
                          variant="secondary"
                          className="px-3 py-1 text-sm"
                        >
                          {action.name} ({action.code})
                        </Badge>
                      ))}
                    </div>
                  )}
                </div>
              )}

              {/* Edit/Create Mode - Show as checkboxes */}
              {isEditing && (
                <div className="border rounded-lg p-4 max-h-64 overflow-y-auto">
                  {actions.length === 0 ? (
                    <p className="text-sm text-gray-500">
                      Đang tải danh sách quyền...
                    </p>
                  ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {actions.map((action) => (
                        <div
                          key={action.id}
                          className="flex items-center space-x-2"
                        >
                          <Checkbox
                            id={`action-${action.id}`}
                            checked={formData.actions.includes(action.id)}
                            onCheckedChange={(checked) =>
                              handleActionToggle(action.id, checked as boolean)
                            }
                            disabled={isSubmitting}
                          />
                          <Label
                            htmlFor={`action-${action.id}`}
                            className="text-sm cursor-pointer"
                          >
                            {action.name} ({action.code})
                          </Label>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* System Info - Only show in view mode for existing resources */}
            {!isEditing && currentResource && currentResource.id !== 0 && (
              <div className="pt-4 border-t">
                <Label className="text-sm font-medium text-gray-600 mb-3 block">
                  Thông tin hệ thống
                </Label>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div>
                    <span className="text-gray-500">ID:</span>
                    <span className="ml-2 font-medium">
                      {currentResource.id}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-500">Ngày tạo:</span>
                    <span className="ml-2 font-medium">
                      {new Date(currentResource.createdAt).toLocaleString(
                        "vi-VN"
                      )}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-500">Cập nhật:</span>
                    <span className="ml-2 font-medium">
                      {new Date(currentResource.updatedAt).toLocaleString(
                        "vi-VN"
                      )}
                    </span>
                  </div>
                </div>
              </div>
            )}

            {/* Action Buttons */}
            {isEditing && (
              <div className="flex justify-end gap-2 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleCancel}
                  disabled={isSubmitting}
                >
                  <X className="h-4 w-4 mr-2" />
                  Hủy
                </Button>

                <Button type="submit" disabled={isSubmitting}>
                  <Save className="h-4 w-4 mr-2" />
                  {isCreating ? "Tạo mới" : "Lưu"}
                </Button>

                {currentAction === "edit" && currentResource && (
                  <Dialog open={confirmDelete} onOpenChange={setConfirmDelete}>
                    <DialogTrigger asChild>
                      <Button variant="destructive" disabled={isSubmitting}>
                        <Trash2 className="h-4 w-4 mr-2" />
                        Xóa
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Xác nhận xóa</DialogTitle>
                      </DialogHeader>
                      <p className="text-sm text-muted-foreground">
                        Bạn có chắc chắn muốn xóa nhóm quyền "
                        {currentResource.name}"? Hành động này không thể hoàn
                        tác.
                      </p>
                      <DialogFooter>
                        <Button
                          variant="outline"
                          onClick={() => setConfirmDelete(false)}
                        >
                          Hủy
                        </Button>
                        <Button
                          variant="destructive"
                          onClick={handleDelete}
                          disabled={isSubmitting}
                        >
                          Xác nhận xóa
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                )}
              </div>
            )}
          </form>
        )}
      </CardContent>
    </Card>
  );
};
