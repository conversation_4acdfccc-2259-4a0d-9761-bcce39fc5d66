import React, { useEffect, useState, useCallback, useRef } from "react";
import { useAppDispatch, useAppSelector } from "@/store/rootReducer";

import { ConfigPageLayout } from "../../shared/components/layouts";
import { HotlineTable } from "../components/HotlineTable";
import { HotlinePreview } from "../components/HotlinePreview";
import type { HotlineConfig, HotlineMetadata } from "../states/type";
import {
  fetchHotlineAsync,
  updateHotlineAsync,
  setDirty,
  clearError,
} from "../states/slices";
import {
  selectHotlineData,
  selectHotlineSavedData,
  selectHotlineLoading,
  selectHotlineSaving,
  selectHotlineError,
  selectHotlineIsDirty,
  selectHotlineIsOperating,
} from "../states/selector";
import { toast } from "sonner";

// ============================================================================
// Configuration
// ============================================================================

const HOTLINE_METADATA: HotlineMetadata = {
  title: 'Đường dây nóng',
  description: 'Quản lý thông tin các đường dây nóng hỗ trợ khách hàng',
  type: 'array',
  arrayKey: 'hotlines',
  fields: [
    {
      key: 'name',
      label: 'Tên đường dây',
      type: 'text',
      required: true,
      placeholder: 'Nhập tên đường dây nóng'
    },
    {
      key: 'phone',
      label: 'Số điện thoại',
      type: 'text',
      required: true,
      placeholder: '1900-1234'
    },
    {
      key: 'email',
      label: 'Email',
      type: 'email',
      required: true,
      placeholder: '<EMAIL>'
    },
  ],
};

// ============================================================================
// Component
// ============================================================================

export const HotlineManagementPage: React.FC = () => {
  const dispatch = useAppDispatch();
  const data = useAppSelector(selectHotlineData);
  const savedData = useAppSelector(selectHotlineSavedData);
  const loading = useAppSelector(selectHotlineLoading);
  const saving = useAppSelector(selectHotlineSaving);
  const error = useAppSelector(selectHotlineError);
  const isDirty = useAppSelector(selectHotlineIsDirty);
  const isOperating = useAppSelector(selectHotlineIsOperating);

  const [isValidationValid, setIsValidationValid] = useState(true);
  const [currentData, setCurrentData] = useState<HotlineConfig | null>(null);
  const [showPreview, setShowPreview] = useState(false);
  const [showValidationWarning, setShowValidationWarning] = useState(false);
  const validateTableRef = useRef<(() => boolean) | undefined>(undefined);

  useEffect(() => {
    dispatch(fetchHotlineAsync());
    return () => {
      dispatch(clearError());
    };
  }, [dispatch]);

  useEffect(() => {
    if (data) {
      setCurrentData(data);
    }
  }, [data]);

  // ============================================================================
  // Event Handlers
  // ============================================================================

  const handleDataChange = useCallback((newData: HotlineConfig) => {
    setCurrentData(newData);
    dispatch(setDirty(true));
  }, [dispatch]);

  const handleValidationChange = useCallback((isValid: boolean) => {
    setIsValidationValid(isValid);
  }, []);

  const handleSave = useCallback(async () => {
    // Validate data before saving
    const isDataValid = validateTableRef.current ? validateTableRef.current() : true;

    if (!isDataValid) {
      setShowValidationWarning(true);
      return;
    }

    setShowValidationWarning(false);

    if (!currentData) {
      return;
    }

    try {
      await dispatch(updateHotlineAsync(currentData)).unwrap();
    } catch (error) {
      toast.error("Lưu đường dây nóng thất bại! " + String(error));
    }
  }, [dispatch, currentData, validateTableRef]);

  const handleRefresh = useCallback(() => {
    dispatch(fetchHotlineAsync());
  }, [dispatch]);

  const handlePreview = useCallback(() => {
    setShowPreview(!showPreview);
  }, [showPreview]);

  const saveDisabled = !isValidationValid || isOperating || !currentData;

  return (
    <ConfigPageLayout
      title="Quản lý đường dây nóng"
      description="Cấu hình thông tin các đường dây nóng hỗ trợ khách hàng"
      metadata={HOTLINE_METADATA}
      loading={saving}
      error={error}
      onSave={handleSave}
      onRefresh={handleRefresh}
      onPreview={handlePreview}
      showPreview={showPreview}
      saveDisabled={saveDisabled}
      isDirty={isDirty}
      hasValidationErrors={showValidationWarning}
    >
      <div className="flex flex-col h-full space-y-6">
        <div className="flex-shrink-0">
          <HotlineTable
            metadata={HOTLINE_METADATA}
            data={currentData}
            loading={loading}
            onDataChange={handleDataChange}
            onValidationChange={handleValidationChange}
            onValidationRequest={validateTableRef}
          />
        </div>

        {showPreview && (
          <HotlinePreview
            data={savedData}
            visible={showPreview}
          />
        )}
      </div>
    </ConfigPageLayout>
  );
};

export default HotlineManagementPage;
