import { combineReducers } from "redux";
import { TypedUseSelectorHook, useDispatch, useSelector } from "react-redux";
import { AppDispatch } from "./store";
import categoryReducer from "@/features/category/states/slices";

import postsFilterReducer from "@/features/post/states/slices";
import userFilterSReducer from "@/features/users/states/slices";
import authReducer from "@/features/auth/states/slices";
import pageReducer from "@/features/page/states/slices";
import groupReducer from "@/features/group/states/slices";
import roleReducer from "@/features/role/states/slices";
import actionReducer from "@/features/action/states/slices";
import resourceReducer from "@/features/resource/states/slices";
import permissionReducer from "@/features/permission/states/slices";
import bannerReducer from "@/features/config/banner/states/slices";
import contactReducer from "@/features/config/contact/states/slices";
import portalLinkReducer from "@/features/config/portal-link/states/slices";

import hotlineReducer from "@/features/config/hotline/states/slices";

import copyrightReducer from "@/features/config/copyright/states/slices";
import organizationStructureReducer from "@/features/about/organization-structure/states/slices";
import albumsFilterReducer from "@/features/media/images/states/slices";
import questionReducer from "@/features/question/states/slices";
import questionFilterReducer from "@/features/question/states/questionFilterSlice";
import layoutReducer from "@/features/layouts/states/slices";

const rootReducer = combineReducers({
  categoryState: categoryReducer,
  postsFilterState: postsFilterReducer,
  userFilterState: userFilterSReducer,
  auth: authReducer,
  pageState: pageReducer,
  groupState: groupReducer,
  roleState: roleReducer,
  actionState: actionReducer,
  resourceState: resourceReducer,
  permissionState: permissionReducer,
  bannerState: bannerReducer,
  contactState: contactReducer,
  portalLink: portalLinkReducer,

  hotlineState: hotlineReducer,

  copyrightState: copyrightReducer,
  organizationStructure: organizationStructureReducer,
  albumsFilterState: albumsFilterReducer,
  question: questionReducer,
  questionFilter: questionFilterReducer,
  layout: layoutReducer,
});

export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;
export const useAppDispatch = useDispatch.withTypes<AppDispatch>();
export type RootState = ReturnType<typeof rootReducer>;
export default rootReducer;
