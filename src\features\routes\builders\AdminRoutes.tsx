import React from "react";
import { RouteObject } from "react-router-dom";
import { useDynamicRouteBuilder } from "./DynamicRouteBuilder";
import AdminPage from "@/features/admin/pages/AdminPage";
import { componentRegistry } from "../registry/ComponentRegistry";

/**
 * 🔧 Admin Routes Builder
 *
 * ✅ Features:
 * - Dynamic routes from admin-menu categories
 * - Default admin dashboard route
 * - Flat path structure (no nesting)
 */
export const useAdminRoutes = () => {
  const {
    routes: dynamicRoutes,
    isReady,
    categoryCount,
  } = useDynamicRouteBuilder({
    type: "admin-menu",
    includeStaticRoutes: true,
    buildNestedPaths: false, // Admin routes are flat
  });

  // Add default admin dashboard route + static test routes
  const adminRoutes: RouteObject[] = [
    {
      path: "",
      element: React.createElement(AdminPage),
      id: "admin-dashboard",
    },
    // Static test route for QuestionListAdminPage (until added to database)
    {
      path: "quan-ly-hoi-dap",
      element: componentRegistry.renderComponent("QuestionListAdminPage"),
      id: "question-list-admin-static",
    },
    // Static test route for BannerManagementPage
    {
      path: "quan-ly-banner",
      element: componentRegistry.renderComponent("BannerManagementPage"),
      id: "banner-management-static",
    },
    ...dynamicRoutes,
  ];

  return {
    routes: adminRoutes,
    isReady,
    categoryCount,
  };
};
