import React from "react";
import { cn } from "@/lib/utils";

/**
 * Copyright configuration interface
 */
export interface CopyrightConfig {
  content: string;
}

/**
 * Props for CopyrightDisplay component
 */
export interface CopyrightDisplayProps {
  /** Copyright configuration data */
  copyright: CopyrightConfig | null;
  /** Additional CSS classes */
  className?: string;
  /** Default copyright text if no config provided */
  defaultText?: string;
}

/**
 * Clean, reusable copyright display component
 * Suitable for both preview and public page usage
 */
export const CopyrightDisplay: React.FC<CopyrightDisplayProps> = ({
  copyright,
  className,
  defaultText = "© 2024 Bản quyền thuộc về Sở Nộ<PERSON> Vụ tỉnh <PERSON>.",
}) => {
  const copyrightText = copyright?.content || defaultText;

  return (
    <div className={cn("text-center py-4 text-gray-600 text-sm border-t border-gray-200", className)}>
      <p>{copyrightText}</p>
    </div>
  );
};
