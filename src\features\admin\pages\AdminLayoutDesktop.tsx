import { useMemo } from "react";
import { Outlet } from "react-router";
import { X } from "lucide-react";
import { SharedHeader } from "@/features/layouts/components/header/SharedHeader";
import { TreeSidebar } from "@/features/layouts/components/menu";
import {
  buildCategoryTree,
  CategoryTree,
} from "@/features/category/states/types";
import { LoadingPage } from "@/components/loading/LoadingPage";
import { useCategory } from "@/features/category/hooks/useCategoryData";
import { ProtectedRoute } from "@/components/auth/ProtectedRoute";
import { useAppDispatch } from "@/store/rootReducer";
import { useLayoutManager } from "@/features/layouts/hooks/useLayoutManager";
import { toggleSidebar, setHasSidebar } from "@/features/layouts/states/slices";
import { useEffect } from "react";

const AdminLayoutDesktop: React.FC = () => {
  const dispatch = useAppDispatch();
  const { data: flatMenuList, loading } = useCategory("admin-menu");

  // Set hasSidebar when component mounts
  useEffect(() => {
    dispatch(setHasSidebar(true));
    return () => {
      dispatch(setHasSidebar(false));
    };
  }, [dispatch]);

  // Use layout manager
  const { sidebarVisible, sidebarLocked, headerHeight } = useLayoutManager();

  const treeMenuList: CategoryTree[] = useMemo(
    () => buildCategoryTree(flatMenuList),
    [flatMenuList]
  );

  const handleToggle = () => {
    dispatch(toggleSidebar());
  };

  const onMenuClick = () => {
    // Desktop không cần auto-close
  };

  if (loading || !treeMenuList.length) return <LoadingPage />;

  return (
    <ProtectedRoute>
      <div className="w-full h-full min-h-screen bg-primary-foreground">
        {/* Shared Header - Public menu + User info */}
        <SharedHeader />

        {/* Main Content Area */}
        <div
          className="w-full flex"
          style={{
            marginTop: `${headerHeight}px`,
            minHeight: `calc(100vh - ${headerHeight}px)`,
          }}
        >
          {/* Admin Sidebar (when expanded) - No user info header */}
          {sidebarVisible && (
            <div className="w-80 bg-white border-r flex flex-col shadow-sm">
              {/* Admin Menu Header - Simple title only */}
              <div className="p-4 border-b bg-gradient-to-r from-primary-50 to-primary-100">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-semibold text-sm text-gray-900">
                      Chức năng quản trị
                    </p>
                    <p className="text-xs text-primary-600">Admin Panel</p>
                  </div>

                  {/* Close Button - Only show if toggle is allowed */}
                  {!sidebarLocked && (
                    <button
                      onClick={handleToggle}
                      className="p-1 rounded-md hover:bg-white/50 transition-colors"
                      aria-label="Đóng menu"
                    >
                      <X className="w-5 h-5 text-gray-600" />
                    </button>
                  )}
                </div>
              </div>

              {/* Admin Menu Sidebar */}
              <div className="flex-1 p-2 overflow-y-auto">
                <TreeSidebar
                  menuList={treeMenuList}
                  onMenuClick={onMenuClick}
                  pathPrefix="/admin"
                />
              </div>
            </div>
          )}

          {/* Main Content */}
          <div className="flex-1 bg-white p-4 overflow-y-auto">
            <Outlet />
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
};

export default AdminLayoutDesktop;
