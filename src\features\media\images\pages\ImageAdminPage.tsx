import { useEffect, useState } from "react";
import { useLocation } from "react-router-dom";
import { useAppSelector, useAppDispatch } from "@/store/rootReducer";
import {
  selectCurrentAlbum,
  selectPageMode,
  selectAlbumQueryParams,
} from "../states/selector";
import { setPathParams, setCurrentAlbum, setFilters } from "../states/slices";
import { fetchAlbum } from "../states/api";
import { AlbumPageMode } from "../states/slices";
import { AlbumStatus } from "../states/types";
import { useSwitchMode, useAlbumMedia } from "../states/hooks";
import { AlbumTable } from "../components/AlbumTable";
import { AlbumDetail } from "../components/AlbumDetail";
import { PageLoading } from "@/components/loading/LoadingPage";
import { toast } from "sonner";

const STATUS_VALUES: AlbumStatus[] = [
  "DRAFT",
  "REVIEW",
  "PUBLISHED",
  "UNPUBLISHED",
  "TRASH",
  "REJECTED",
];

/**
 * Main component for album management
 * Handles album listing, detail view, and URL synchronization
 */
const ImageAdminPage = () => {
  const dispatch = useAppDispatch();
  const location = useLocation();
  const { switchMode } = useSwitchMode();
  const { clearMedia } = useAlbumMedia();

  const currentAlbum = useAppSelector(selectCurrentAlbum);
  const pageMode = useAppSelector(selectPageMode);
  const albumParams = useAppSelector(selectAlbumQueryParams);
  const [isUrlSyncing, setIsUrlSyncing] = useState(false);

  // Callback to reload album data when cover image is updated
  const handleAlbumUpdated = async () => {
    if (currentAlbum?.id) {
      try {
        const response = await fetchAlbum(currentAlbum.id);
        dispatch(setCurrentAlbum(response.data.data));
      } catch (error) {
        toast.error("Không thể tải lại thông tin thư viện: " + (error as Error).message);
        // Error reloading album - silently fail
      }
    }
  };

  // URL/Redux sync logic for album status - ONLY active when this component is mounted
  useEffect(() => {
    // Only sync if we're on album management routes (not detail mode)
    const searchParams = new URLSearchParams(location.search);
    const mode = searchParams.get("mode");

    // Skip sync for detail mode - it has its own logic
    if (mode === "detail") {
      setIsUrlSyncing(false);
      return;
    }

    // Get the last segment of pathname for status detection
    const pathSegments = location.pathname.split("/").filter(Boolean);
    const lastSegment = pathSegments[pathSegments.length - 1];

    // Determine expected status from URL path end
    let expectedStatus: AlbumStatus;

    // Special case: URL ends with album management route (no status segment) = DRAFT
    if (lastSegment === "quan-ly-hinh-anh" || lastSegment === "album-admin") {
      expectedStatus = "DRAFT";
    } else {
      // Check if last segment matches any status
      const upperSegment = lastSegment?.toUpperCase();
      if (STATUS_VALUES.includes(upperSegment as AlbumStatus)) {
        expectedStatus = upperSegment as AlbumStatus;
      } else {
        expectedStatus = "DRAFT"; // Default fallback
      }
    }

    // Compare with current Redux status
    const currentStatus = albumParams.status;

    if (currentStatus === expectedStatus) {
      // Case 1: Location khớp với redux => show loading briefly for smooth UX
      setIsUrlSyncing(true);
      // Stop syncing after a short delay for smooth UI
      setTimeout(() => setIsUrlSyncing(false), 200);
    } else {
      // Case 3: Location không khớp redux => Update lại status trong redux => Tự nó cũng refresh
      setIsUrlSyncing(true);
      dispatch(setFilters({ status: expectedStatus, page: 0 }));
    }
  }, [location.pathname, dispatch]);

  // Watch for Redux status changes to stop loading
  useEffect(() => {
    if (!isUrlSyncing) return;

    const searchParams = new URLSearchParams(location.search);
    const mode = searchParams.get("mode");

    if (mode === "detail") {
      setIsUrlSyncing(false);
      return;
    }

    const pathSegments = location.pathname.split("/").filter(Boolean);
    const lastSegment = pathSegments[pathSegments.length - 1];

    let expectedStatus: AlbumStatus;

    if (lastSegment === "quan-ly-hinh-anh" || lastSegment === "album-admin") {
      expectedStatus = "DRAFT";
    } else {
      const upperSegment = lastSegment?.toUpperCase();
      if (STATUS_VALUES.includes(upperSegment as AlbumStatus)) {
        expectedStatus = upperSegment as AlbumStatus;
      } else {
        expectedStatus = "DRAFT";
      }
    }

    // If Redux status now matches URL, stop syncing
    if (albumParams.status === expectedStatus) {
      setIsUrlSyncing(false);
    }
  }, [albumParams.status, location.pathname, isUrlSyncing]);

  // Original query params logic for detail mode
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const mode = searchParams.get("mode") as AlbumPageMode;
    const albumId = searchParams.get("albumid");

    if (!mode) {
      dispatch(setPathParams({ mode: "list" }));
    } else {
      dispatch(setPathParams({ mode }));

      // If we have an album ID and we're in detail mode, load the album
      if (mode === "detail" && albumId && albumId !== "0") {
        const id = parseInt(albumId);
        if (!isNaN(id)) {
          // Check if we already have this album in Redux
          if (currentAlbum && currentAlbum.id === id) {
            // Album already loaded, no need to fetch again
          } else {
            // Load album data from API
            const loadAlbum = async () => {
              try {
                const response = await fetchAlbum(id);
                dispatch(setCurrentAlbum(response.data.data));
              } catch (error) {
                toast.error("Không thể tải thư viện ảnh: " + (error as Error).message);
                // If failed to load, go back to list
                switchMode("list");
              }
            };
            loadAlbum();
          }
        }
      } else if (mode === "list") {
        dispatch(setCurrentAlbum(null));
        clearMedia(); // Clear media data when going back to list
      }
    }
  }, [location.search, dispatch, currentAlbum, switchMode, clearMedia]);

  // Show AlbumDetail for detail mode
  if (pageMode === "detail" && currentAlbum) {
    return (
      <AlbumDetail album={currentAlbum} onAlbumUpdated={handleAlbumUpdated} />
    );
  }

  // Show full page loading when syncing, otherwise show AlbumTable
  if (isUrlSyncing) {
    return <PageLoading message="Đang tải thư viện ảnh..." />;
  }

  return <AlbumTable isUrlSyncing={false} />;
};

export default ImageAdminPage;
