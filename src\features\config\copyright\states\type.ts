export interface CopyrightConfig {
  content: string;
}

export interface CopyrightState {
  data: CopyrightConfig | null;
  savedData: CopyrightConfig | null;
  loading: boolean;
  saving: boolean;
  error: string | null;
  isDirty: boolean;
}

export interface CopyrightFormProps {
  data: CopyrightConfig | null;
  loading: boolean;
  onDataChange: (newData: CopyrightConfig) => void;
  onValidationChange?: (isValid: boolean) => void;
}

// Props for copyright page layout component
export interface CopyrightPageLayoutProps {
  title: string;
  description: string;
  children: React.ReactNode;
  loading?: boolean;
  error?: string | null;
  onSave?: () => void;
  onRefresh?: () => void;
  onPreview?: () => void;
  showPreview?: boolean;
  saveDisabled?: boolean;
  isDirty?: boolean;
}
