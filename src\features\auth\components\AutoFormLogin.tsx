import { AutoForm } from "@/form/context";
import { LoginFormConfig } from "../states/mock";
import { useAppDispatch } from "@/store/rootReducer";
import { loginAsync } from "../states/slices";
import { useNavigate, useLocation } from "react-router";
import { LoginRequest } from "../states/type";

interface LocationState {
  returnUrl?: string;
}

export const AutoFormLogin: React.FC = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const location = useLocation();

  const initialData: LoginRequest = {
    userName: "",
    password: "",
  };

  const handleSubmit = async (data: LoginRequest) => {
    const loginData: LoginRequest = {
      userName: data.userName,
      password: data.password,
    };
    console.log(JSON.stringify(loginData));
    const resultAction = await dispatch(loginAsync(loginData));
    if (loginAsync.fulfilled.match(resultAction)) {
      // <PERSON><PERSON><PERSON>n về returnUrl nếu có, hoặc về trang chủ
      const returnUrl = (location.state as LocationState)?.returnUrl || "/";
      navigate(returnUrl, { replace: true });
    }
  };

  const node = LoginFormConfig.config;

  return (
    <AutoForm<LoginRequest>
      node={node}
      viewOnly={false}
      initialData={initialData}
      onSubmit={handleSubmit}
      validationMode="onSubmit"
      className="space-y-4"
    />
  );
};
