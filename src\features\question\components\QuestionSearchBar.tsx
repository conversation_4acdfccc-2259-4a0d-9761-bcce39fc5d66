import React, { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Search, X } from "lucide-react";

interface QuestionSearchBarProps {
  keyword: string;
  onSearch: (keyword: string) => void;
}

export const QuestionSearchBar: React.FC<QuestionSearchBarProps> = ({
  keyword,
  onSearch,
}) => {
  const [searchValue, setSearchValue] = useState(keyword);

  // Sync with external keyword changes
  useEffect(() => {
    setSearchValue(keyword);
  }, [keyword]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSearch(searchValue.trim());
  };

  const handleClear = () => {
    setSearchValue("");
    onSearch("");
  };

  return (
    <form onSubmit={handleSubmit} className="flex gap-2">
      <div className="relative flex-1">
        <Input
          type="text"
          placeholder="<PERSON><PERSON><PERSON> kiếm câu hỏi..."
          value={searchValue}
          onChange={(e) => setSearchValue(e.target.value)}
          className="pr-8"
        />
        {searchValue && (
          <Button
            type="button"
            variant="ghost"
            size="sm"
            className="absolute right-1 top-1/2 -translate-y-1/2 h-6 w-6 p-0"
            onClick={handleClear}
          >
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>
      <Button type="submit" variant="outline">
        <Search className="h-4 w-4 mr-2" />
        Tìm kiếm
      </Button>
    </form>
  );
};
