/**
 * Auto Save Hook
 * Automatically saves form builder state to localStorage
 */

import { useEffect, useRef } from 'react';
import { FormBuilderState } from '../core/types';

const STORAGE_KEY = 'formBuilder:autoSave';
const AUTOSAVE_DELAY = 1000; // 1 second

interface AutoSaveData {
  formConfig: FormBuilderState['formConfig'];
  timestamp: number;
  version: string;
}

export const useAutoSave = (formConfig: any, isDirty: boolean = false) => {
  const lastSaveRef = useRef<string>('');

  // Save to localStorage
  const saveToStorage = (config: any) => {
    try {
      const autoSaveData: AutoSaveData = {
        formConfig: config,
        timestamp: Date.now(),
        version: '1.0.0'
      };
      
      const serialized = JSON.stringify(autoSaveData);
      
      // Only save if data has changed
      if (serialized !== lastSaveRef.current) {
        localStorage.setItem(STORAGE_KEY, serialized);
        lastSaveRef.current = serialized;
        console.log('[AutoSave] Saved to localStorage');
      }
    } catch (error) {
      console.error('[AutoSave] Failed to save:', error);
    }
  };

  // Custom debounce implementation
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  
  const debouncedSave = useRef((config: any) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    timeoutRef.current = setTimeout(() => {
      saveToStorage(config);
    }, AUTOSAVE_DELAY);
  }).current;

  // Auto save when config changes
  useEffect(() => {
    if (isDirty && formConfig) {
      debouncedSave(formConfig);
    }
  }, [formConfig, isDirty, debouncedSave]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);
};

/**
 * Load autosaved data from localStorage
 */
export const loadAutoSave = (): any | null => {
  try {
    const saved = localStorage.getItem(STORAGE_KEY);
    if (!saved) return null;

    const data = JSON.parse(saved) as AutoSaveData;
    
    // Check if data is not too old (24 hours)
    const maxAge = 24 * 60 * 60 * 1000;
    if (Date.now() - data.timestamp > maxAge) {
      localStorage.removeItem(STORAGE_KEY);
      return null;
    }

    return data.formConfig;
  } catch (error) {
    console.error('[AutoSave] Failed to load:', error);
    return null;
  }
};

/**
 * Clear autosaved data
 */
export const clearAutoSave = () => {
  localStorage.removeItem(STORAGE_KEY);
  console.log('[AutoSave] Cleared localStorage');
};