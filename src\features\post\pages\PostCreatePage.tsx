import { PostAutoForm } from "../components/PostAutoForm";
import { CreatePost } from "../states/types";

export const PostCreatePage: React.FC = () => {
  const handleSubmit = (data: CreatePost) => {
    console.log("Post create data:", data);
  };

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">Tạo bài viết mới</h1>
      <PostAutoForm onSubmit={handleSubmit} />
    </div>
  );
};
