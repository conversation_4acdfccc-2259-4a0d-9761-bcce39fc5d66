import Cookies from "js-cookie";

export interface Token {
  token: string;
  expired_at: number;
}

export const initialToken: Token = {
  token: "",
  expired_at: 0,
};

export const TOKEN_TYPE = {
  ACCESS_TOKEN: "access_token",
  REFRESH_TOKEN: "refresh_token",
};

export const setTokenToCookies = (key: string, token: string) => {
  try {
    const tokenData = parseJwt(token);
    if (!tokenData) {
      console.error("Invalid token format");
      return;
    }

    // Set token with expiration time
    Cookies.set(key, token, {
      expires: new Date(tokenData.expired_at * 1000), // Convert to milliseconds
      secure: process.env.NODE_ENV === "production",
      sameSite: "strict",
    });
  } catch (error) {
    console.error("Error setting token to cookies:", error);
  }
};

export const getTokenFromCookies = (key: string) => {
  return Cookies.get(key);
};

export const removeTokenFromCookies = (key: string) => {
  Cookies.remove(key);
};

export const parseJwt = (jwt: string): Token | null => {
  try {
    const parts = jwt.split(".");
    if (parts.length !== 3) {
      return null;
    }
    const payload = atob(parts[1]);
    const parsedPayload = JSON.parse(payload);

    // Convert expiration time to milliseconds
    const expired_at = parsedPayload.exp
      ? parsedPayload.exp
      : Date.now() / 1000;

    return {
      token: jwt,
      expired_at,
    };
  } catch (error) {
    console.error("Error parsing JWT:", error);
    return null;
  }
};

export const isTokenExpiringSoon = (tokenKey: string): boolean => {
  const token = getTokenFromCookies(tokenKey);
  if (!token) return false;

  try {
    const tokenData = parseJwt(token);
    if (!tokenData) return false;

    const expirationTime = tokenData.expired_at * 1000; // Convert to milliseconds
    const currentTime = Date.now();
    const fiveMinutes = 5 * 60 * 1000; // 5 minutes in milliseconds

    return expirationTime - currentTime <= fiveMinutes;
  } catch {
    return false;
  }
};
