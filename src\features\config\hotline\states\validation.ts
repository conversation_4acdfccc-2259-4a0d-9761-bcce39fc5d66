import { z } from "zod";

/**
 * Validation schema for a single hotline item
 * Validates name, phone, and email fields with appropriate constraints
 */
export const hotlineItemSchema = z.object({
  name: z
    .string()
    .min(1, "<PERSON>ui lòng nhập Tên")
    .max(100, "Tên không được vượt quá 100 ký tự")
    .trim(),
  phone: z
    .string()
    .min(1, "Vui lòng nhập Số điện thoại")
    .regex(/^\d{10}$/, "Số điện thoại phải có đúng 10 chữ số")
    .trim(),
  email: z
    .string()
    .min(1, "Vui lòng nhập Email")
    .email("Email không hợp lệ")
    .max(100, "Email không được vượt quá 100 ký tự")
    .trim(),
});

/**
 * Validation schema for the complete hotline configuration
 * Validates the array of hotline items
 */
export const hotlineConfigSchema = z.object({
  hotlines: z
    .array(hotlineItemSchema)
    .min(1, "<PERSON>ải có ít nhất một đường dây nóng")
    .max(50, "Không được vượt quá 50 đường dây nóng"),
});

/**
 * Type inference for hotline item validation
 */
export type HotlineItemValidation = z.infer<typeof hotlineItemSchema>;

/**
 * Type inference for hotline config validation
 */
export type HotlineConfigValidation = z.infer<typeof hotlineConfigSchema>;
