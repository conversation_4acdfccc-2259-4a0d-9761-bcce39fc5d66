import React, { useMemo } from "react";
import { createBrowser<PERSON>outer, RouterProvider } from "react-router-dom";
import { ErrorBoundary } from "./ErrorBoudary";
import MainLayout from "@/features/layouts/MainLayout";
import { AdminLayout } from "../admin/pages/loadable";
import { UserLayout } from "../auth/pages/loadable";
import { LoadingPage } from "@/components/loading/LoadingPage";
import { AlbumDetailPage } from "../media/images/pages/loadable";
import { PostAdminPage } from "../post/pages/loadable";
import FormEditorTestPage from "../form-editor/pages/FormEditorTestPage";
import { useMultipleCategoriesSimple } from "@/features/category/hooks/useCategoryData";
import { CategoryType } from "@/features/category/states/types";

// Import route builders
import { useAdminRoutes } from "./builders/AdminRoutes";
import { usePublicRoutes } from "./builders/PublicRoutes";
import { useUserRoutes } from "./builders/UserRoutes";
import { AuthRoutesBuilder } from "./builders/AuthRoutes";
import { EditRoutes } from "./builders/EditRoutes";

// CRITICAL: Constant array to prevent infinite re-renders
const REQUIRED_CATEGORY_TYPES: CategoryType[] = [
  "public-menu",
  "admin-menu",
  "user-menu",
];

/**
 * 🚀 AppRouter - Dynamic Route System
 *
 * ✅ Features:
 * - Dynamic component registry (no switch statement hell)
 * - Separated route builders by domain (admin/public/user)
 * - Database-driven route generation
 * - Routes guard prevents 404 on refresh
 * - Performance optimized with single API calls
 * - Extensible architecture for new route types
 */
export const AppRouter: React.FC = () => {
  console.log("🎯 AppRouter starting...");

  // CRITICAL: Load ALL categories first before building routes
  const { loading: categoriesLoading, allLoaded } = useMultipleCategoriesSimple(
    REQUIRED_CATEGORY_TYPES
  );

  // Get routes from specialized builders
  const { routes: adminRoutes, isReady: adminReady } = useAdminRoutes();
  const { routes: publicRoutes, isReady: publicReady } = usePublicRoutes();
  const { routes: userRoutes, isReady: userReady } = useUserRoutes();
  const authRoute = AuthRoutesBuilder.build();
  const authReady = true; // Auth routes are always ready

  console.log("📊 Routes status:", {
    categoriesLoading,
    allLoaded,
    adminReady,
    publicReady,
    userReady,
    authReady,
    adminCount: adminRoutes.length,
    publicCount: publicRoutes.length,
    userCount: userRoutes.length,
    authRoute: authRoute ? 1 : 0,
  });

  // Create router with all route configurations
  const router = useMemo(() => {
    console.log("🏗️ Building router configuration...");

    return createBrowserRouter([
      // 🌐 Public routes with MainLayout
      {
        path: "/",
        errorElement: <ErrorBoundary />,
        element: <MainLayout />,
        children: publicRoutes,
        id: "public-layout",
      },

      // 🔧 Admin routes with AdminLayout
      {
        path: "/admin",
        errorElement: <ErrorBoundary />,
        element: <AdminLayout />,
        children: adminRoutes,
        id: "admin-layout",
      },

      // 👤 User routes (profile, editor) - Ready for implementation
      {
        path: "/me",
        errorElement: <ErrorBoundary />,
        element: <UserLayout />,
        children: userRoutes,
        id: "user-layout",
      },

      // 🔐 Auth routes with standalone AuthLayout
      authRoute,

      // ✏️ Edit routes with fullscreen EditLayout
      ...EditRoutes,

      // 📍 Legacy static routes (to be migrated to dynamic)
      {
        path: "/admin/media/albums/:id",
        errorElement: <ErrorBoundary />,
        element: <AlbumDetailPage />,
        id: "album-detail-legacy",
      },
      {
        path: "/admin/post-admin",
        errorElement: <ErrorBoundary />,
        element: <PostAdminPage />,
        id: "post-admin-legacy",
      },

      // 🧪 Form Editor Test Route
      {
        path: "/form-editor",
        errorElement: <ErrorBoundary />,
        element: <FormEditorTestPage />,
        id: "form-editor-test",
      },
    ]);
  }, [publicRoutes, adminRoutes, userRoutes, authRoute]);

  // 🛡️ ROUTES GUARD: Critical for preventing 404 on F5 refresh
  // Only render router when ALL required categories are loaded
  const isRouterReady =
    allLoaded && adminReady && publicReady && userReady && authReady;

  if (categoriesLoading || !isRouterReady) {
    console.log("⏳ Routes guard: Categories not ready, showing loading...");
    console.log("⏳ Status:", {
      categoriesLoading,
      allLoaded,
      adminReady,
      publicReady,
      userReady,
      authReady,
    });
    return <LoadingPage />;
  }

  console.log("✅ Routes guard: All categories ready, rendering router");
  console.log("🎯 Final router with routes:", {
    publicRoutes: publicRoutes.length,
    adminRoutes: adminRoutes.length,
    userRoutes: userRoutes.length,
    authRoute: authRoute ? 1 : 0,
  });

  return <RouterProvider router={router} />;
};
