import { useState, useEffect, useMemo } from "react";
import { Button } from "@/components/ui/button";
import { Plus, Download, Upload } from "lucide-react";
import { RecordFilters } from "../components/RecordFilters";
import { RecordTable } from "../components/RecordTable";
import { RecordPagination } from "../components/RecordPagination";
import {
  RecordRegister,
  RecordRegisterFilters,
  mockRecordRegisters,
  filterRecordRegisters,
  paginateRecords,
} from "../data/mockData";

const RecordRegisterAdminPage = () => {
  const [loading, setLoading] = useState(true);
  const [selectedIds, setSelectedIds] = useState<number[]>([]);
  const [filters, setFilters] = useState<RecordRegisterFilters>({
    keyword: "",
    status: "",
    year: "",
  });
  const [pagination, setPagination] = useState({
    page: 0,
    size: 10,
  });

  // Filter and paginate data
  const filteredRecords = useMemo(() => {
    return filterRecordRegisters(mockRecordRegisters, filters);
  }, [filters]);

  const paginatedData = useMemo(() => {
    return paginateRecords(filteredRecords, pagination.page, pagination.size);
  }, [filteredRecords, pagination.page, pagination.size]);

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => setLoading(false), 500);
    return () => clearTimeout(timer);
  }, []);

  // Reset to first page when filters change
  useEffect(() => {
    setPagination((prev) => ({ ...prev, page: 0 }));
  }, [filters]);

  const handleFiltersChange = (newFilters: RecordRegisterFilters) => {
    setFilters(newFilters);
  };

  const handleSearch = () => {
    // Already handled by useMemo, just trigger re-render
    console.log("Searching with filters:", filters);
  };

  const handleReset = () => {
    setFilters({
      keyword: "",
      status: "",
      year: "",
    });
  };

  const handlePageChange = (page: number) => {
    setPagination((prev) => ({ ...prev, page }));
  };

  const handlePageSizeChange = (size: number) => {
    setPagination({ page: 0, size });
  };

  const handleView = (record: RecordRegister) => {
    console.log("View record:", record);
    // TODO: Implement view modal/page
  };

  const handleEdit = (record: RecordRegister) => {
    console.log("Edit record:", record);
    // TODO: Implement edit modal/page
  };

  const handleDelete = (record: RecordRegister) => {
    console.log("Delete record:", record);
    // TODO: Implement delete confirmation
  };

  const handleSubmit = (record: RecordRegister) => {
    console.log("Submit record:", record);
    // TODO: Implement submit for approval
  };

  const handleSelectionChange = (selectedIds: number[]) => {
    setSelectedIds(selectedIds);
  };

  const handleCreateNew = () => {
    console.log("Create new record");
    // TODO: Implement create new record
  };

  const handleBulkExport = () => {
    console.log("Export records:", selectedIds);
    // TODO: Implement bulk export
  };

  const handleBulkImport = () => {
    console.log("Import records");
    // TODO: Implement bulk import
  };

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-64 mb-6"></div>
          <div className="h-32 bg-gray-200 rounded mb-6"></div>
          <div className="h-96 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            Quản lý đăng ký lưu trữ hồ sơ
          </h1>
          <p className="text-gray-600 mt-1">
            Quản lý và theo dõi các yêu cầu đăng ký lưu trữ hồ sơ tài liệu
          </p>
        </div>

        <div className="flex space-x-2">
          <Button
            variant="outline"
            onClick={handleBulkImport}
            className="flex items-center space-x-2"
          >
            <Upload className="h-4 w-4" />
            <span>Import</span>
          </Button>

          {selectedIds.length > 0 && (
            <Button
              variant="outline"
              onClick={handleBulkExport}
              className="flex items-center space-x-2"
            >
              <Download className="h-4 w-4" />
              <span>Export ({selectedIds.length})</span>
            </Button>
          )}

          <Button
            onClick={handleCreateNew}
            className="flex items-center space-x-2"
          >
            <Plus className="h-4 w-4" />
            <span>Tạo mới</span>
          </Button>
        </div>
      </div>

      {/* Filters */}
      <RecordFilters
        filters={filters}
        onFiltersChange={handleFiltersChange}
        onSearch={handleSearch}
        onReset={handleReset}
      />

      {/* Summary */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-center justify-between">
          <div className="text-sm text-blue-800">
            <span className="font-medium">Tổng số bản ghi:</span>{" "}
            {paginatedData.pagination.totalElements}
          </div>
          {selectedIds.length > 0 && (
            <div className="text-sm text-blue-800">
              <span className="font-medium">Đã chọn:</span> {selectedIds.length}{" "}
              bản ghi
            </div>
          )}
        </div>
      </div>

      {/* Table */}
      <RecordTable
        records={paginatedData.data}
        onView={handleView}
        onEdit={handleEdit}
        onDelete={handleDelete}
        onSubmit={handleSubmit}
        onSelectionChange={handleSelectionChange}
      />

      {/* Pagination */}
      <div className="bg-white rounded-lg border shadow-sm">
        <RecordPagination
          pagination={paginatedData.pagination}
          onPageChange={handlePageChange}
          onPageSizeChange={handlePageSizeChange}
        />
      </div>
    </div>
  );
};

export default RecordRegisterAdminPage;
