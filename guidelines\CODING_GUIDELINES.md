# 📋 Coding Guidelines & Best Practices

> **<PERSON><PERSON><PERSON> đích**: <PERSON>r<PERSON><PERSON> rush coding, đảm bảo code quality và architecture consistency
> **Áp dụng**: Mọi task coding, từ nhỏ đến lớn

## 🚨 **Common Mistakes to Avoid**

- ❌ **Rush to code** without planning architecture
- ❌ **Leave unused code/imports** behind
- ❌ **Inconsistent naming** throughout development
- ❌ **No backward review** of existing code
- ❌ **Piecemeal refactoring** without overall plan
- ❌ **Create-then-cleanup** approach (creates tech debt)

## 🔄 **3-Phase Workflow**

### **Phase 1: Plan & Analysis** 🎯

**BEFORE writing any code:**

1. **🔍 Understand Requirements**

   - Read user request carefully
   - Clarify ambiguous points
   - Understand the "why" not just "what"

2. **🏗️ Architecture Planning**

   - Sketch overall structure
   - Plan file organization
   - Consider existing patterns
   - Think about naming conventions

3. **📝 Impact Analysis**

   - List files to create/modify/delete
   - Check dependencies and imports
   - Review related existing code
   - Identify potential conflicts

4. **✅ Confirm Direction**
   - Ask user if approach is correct
   - Show planned structure if complex
   - Get feedback before implementation

### **Phase 2: Implementation** 💻

**WHILE coding:**

1. **📁 Structure First**

   - Create folder structure
   - Set up files with correct names
   - Plan imports/exports

2. **🧹 Clean As You Go**

   - Remove unused imports immediately
   - Delete obsolete code right away
   - Keep consistent naming from start

3. **📦 Consistent Patterns**

   - Follow established conventions
   - Use same patterns as existing code
   - Maintain import/export consistency

4. **💭 Meaningful Comments**
   - Explain intent, not just implementation
   - Document complex logic
   - Note architectural decisions

### **Phase 3: Review & Cleanup** 🔍

**AFTER implementation:**

1. **🔍 Code Review**

   - Check for unused imports/variables
   - Look for leftover debug code
   - Verify naming consistency

2. **🧪 Quality Check**

   - Run `yarn build` to verify
   - Check TypeScript errors
   - Test basic functionality

3. **📚 Documentation**

   - Update relevant docs
   - Add/update comments
   - Explain architectural changes

4. **♻️ Refactor Opportunities**

   - Look for code duplication
   - Check for better patterns
   - Consider performance implications

5. **✅ Final Review**
   - Review ALL changes made
   - Ensure nothing is broken
   - Confirm approach was correct

## 📋 **Pre-Task Checklist**

Before starting ANY coding task:

- [ ] **Read related existing code**
- [ ] **Plan architecture/structure first**
- [ ] **List exact files to touch**
- [ ] **Check naming conventions**
- [ ] **Understand impact scope**

## 📋 **During-Task Checklist**

While implementing:

- [ ] **Clean up immediately** (don't accumulate debt)
- [ ] **Consistent naming** from start
- [ ] **Remove unused code** right away
- [ ] **Follow existing patterns**
- [ ] **Meaningful commits** with clear messages

## 📋 **Post-Task Checklist**

After implementation:

- [ ] **Remove ALL unused imports/code**
- [ ] **Run `yarn build`** and fix errors
- [ ] **Review all changes** made
- [ ] **Check for refactoring opportunities**
- [ ] **Update documentation** if needed

## 🎯 **Specific Practices**

### **File Organization**

- Use clear, descriptive names
- Group related functionality
- Follow established folder structure
- Don't mix concerns in single file

### **Import/Export Management**

- Remove unused imports immediately
- Use consistent import patterns
- Export only what's needed
- Keep import order consistent

### **Naming Conventions**

- Be consistent across project
- Use domain-specific terms
- Avoid generic names like "utils", "helpers"
- Make intent clear from name

### **Refactoring Approach**

- Plan the end state first
- Make incremental, tested changes
- Don't leave partial refactors
- Clean up old code immediately

## 🚫 **Red Flags to Watch For**

If you catch yourself doing these, STOP and reconsider:

- Writing code without planning structure
- Creating "temporary" solutions
- Leaving TODO comments everywhere
- Copy-pasting without understanding
- Creating files with generic names
- Adding features without removing old code
- Making changes without testing

## 🤝 **Accountability**

- **User can reference this file** to remind me of commitments
- **I will ask for direction** when architecture is unclear
- **I will show planned structure** for complex changes
- **I will admit when I need to step back and plan**

---

**Remember**: _"Slow down to speed up"_ - Better to plan well than refactor later!
