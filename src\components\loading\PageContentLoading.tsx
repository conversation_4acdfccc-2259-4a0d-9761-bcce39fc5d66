import React from "react";
import { Loader2 } from "lucide-react";

interface PageContentLoadingProps {
  message?: string;
  fullHeight?: boolean;
}

/**
 * 🔄 Page Content Loading Overlay
 *
 * ✅ Covers entire content area (excluding header/sidebar)
 * ✅ Prevents UI jumping from partial loading
 * ✅ Professional loading experience
 * ✅ Customizable message and height
 */
export const PageContentLoading: React.FC<PageContentLoadingProps> = ({
  message = "Đang tải...",
  fullHeight = true,
}) => {
  return (
    <div
      className={`
        flex flex-col items-center justify-center
        bg-background/80 backdrop-blur-sm
        ${fullHeight ? "min-h-[calc(100vh-4rem)]" : "min-h-[400px]"}
        w-full relative
      `}
    >
      {/* Loading Spinner */}
      <div className="flex flex-col items-center gap-4">
        <div className="relative">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <div className="absolute inset-0 rounded-full border-2 border-primary/20 animate-pulse" />
        </div>

        {/* Loading Message */}
        <div className="text-center space-y-2">
          <p className="text-sm font-medium text-foreground">{message}</p>
          <p className="text-xs text-muted-foreground">
            Vui lòng chờ một chút...
          </p>
        </div>
      </div>

      {/* Subtle Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="h-full w-full bg-gradient-to-br from-primary/10 to-secondary/10" />
      </div>
    </div>
  );
};

export default PageContentLoading;
