// src/layouts/AdminLayout.tsx
import useMediaQuery from "@/hooks/useMediaQuery";
import AdminLayoutDesktop from "./AdminLayoutDesktop";
import AdminLayoutMobile from "./AdminLayoutMobile";

const AdminLayout: React.FC = () => {
  const isMobile = useMediaQuery("(max-width: 768px)"); // iPad mini and below

  return isMobile ? <AdminLayoutMobile /> : <AdminLayoutDesktop />;
};

export default AdminLayout;
