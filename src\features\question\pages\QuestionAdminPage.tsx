import React, { useEffect } from "react";
import { useSearchParams } from "react-router-dom";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { LoadingPage } from "@/components/loading/LoadingPage";
import { AlertCircle } from "lucide-react";
import { useQuestion } from "../hooks/useQuestion";
import { QuestionListView } from "../components/QuestionListView";
import { QuestionDetailView } from "../components/QuestionDetailView";
import { QuestionFilters } from "../components/QuestionFilters";
import { QuestionSearchBar } from "../components/QuestionSearchBar";
import type { QuestionStatus } from "../states/types";

export const QuestionAdminPage: React.FC = () => {
  const [searchParams, setSearchParams] = useSearchParams();

  const {
    questions,
    questionsLoading: loading,
    questionsError: error,
    questionsPagination,
    fetchQuestions,
    fetchQuestionById,
    selectedQuestion,
    clearQuestionsError,
  } = useQuestion();

  // Parse search params
  const mode = searchParams.get("mode") || "list";
  const status = searchParams.get("status") as QuestionStatus | "all" | null;
  const keyword = searchParams.get("keyword") || "";
  const page = parseInt(searchParams.get("page") || "0");
  const size = parseInt(searchParams.get("size") || "10");
  const questionId = searchParams.get("questionId");

  // Update search params helper
  const updateSearchParams = (
    updates: Record<string, string | number | null>
  ) => {
    const newParams = new URLSearchParams(searchParams);

    Object.entries(updates).forEach(([key, value]) => {
      if (value === null || value === "" || value === "all") {
        newParams.delete(key);
      } else {
        newParams.set(key, String(value));
      }
    });

    setSearchParams(newParams);
  };

  // Fetch data based on mode
  useEffect(() => {
    if (mode === "list") {
      const params = {
        keyword: keyword || undefined,
        status: status !== "all" && status ? status : undefined,
        page,
        size,
      };
      fetchQuestions(params);
    } else if (mode === "detail" && questionId) {
      fetchQuestionById(parseInt(questionId));
    }
  }, [
    mode,
    status,
    keyword,
    page,
    size,
    questionId,
    fetchQuestions,
    fetchQuestionById,
  ]);

  // Clear error when params change
  useEffect(() => {
    if (error) {
      clearQuestionsError();
    }
  }, [mode, status, keyword, page, questionId, clearQuestionsError, error]);

  // Event handlers
  const handleStatusChange = (newStatus: QuestionStatus | "all") => {
    updateSearchParams({
      status: newStatus,
      page: 0, // Reset to first page when changing status
    });
  };

  const handleSearch = (searchKeyword: string) => {
    updateSearchParams({
      keyword: searchKeyword,
      page: 0, // Reset to first page when searching
    });
  };

  const handlePageChange = (newPage: number) => {
    updateSearchParams({ page: newPage });
  };

  const handleSizeChange = (newSize: number) => {
    updateSearchParams({
      size: newSize,
      page: 0, // Reset to first page when changing page size
    });
  };

  const handleViewDetail = (id: number) => {
    updateSearchParams({
      mode: "detail",
      questionId: id,
    });
  };

  const handleBackToList = () => {
    updateSearchParams({
      mode: "list",
      questionId: null,
    });
  };

  if (loading) return <LoadingPage />;

  return (
    <div className="w-full h-full flex flex-col gap-4">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">
          {mode === "detail" ? "Chi Tiết Câu Hỏi" : "Quản Lý Hỏi Đáp"}
        </h1>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Lỗi</AlertTitle>
          <AlertDescription>{String(error)}</AlertDescription>
        </Alert>
      )}

      {/* Content */}
      {mode === "list" ? (
        <div className="flex-1 flex flex-col gap-4">
          {/* Status Filter Tabs */}
          <QuestionFilters
            currentStatus={status || "all"}
            onStatusChange={handleStatusChange}
          />

          {/* Search Bar */}
          <QuestionSearchBar keyword={keyword} onSearch={handleSearch} />

          {/* Question List with Pagination */}
          <QuestionListView
            questions={questions}
            pagination={
              questionsPagination || { page: 0, size: 10, totalElements: 0 }
            }
            onPageChange={handlePageChange}
            onSizeChange={handleSizeChange}
            onViewDetail={handleViewDetail}
            currentPage={page}
            currentSize={size}
          />
        </div>
      ) : (
        <QuestionDetailView
          question={selectedQuestion}
          onBackToList={handleBackToList}
        />
      )}
    </div>
  );
};

export default QuestionAdminPage;
