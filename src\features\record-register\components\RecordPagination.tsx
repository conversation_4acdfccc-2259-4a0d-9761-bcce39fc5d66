import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
} from "lucide-react";

interface PaginationInfo {
  page: number;
  size: number;
  totalElements: number;
  totalPages: number;
}

interface RecordPaginationProps {
  pagination: PaginationInfo;
  onPageChange: (page: number) => void;
  onPageSizeChange: (size: number) => void;
}

export const RecordPagination = ({
  pagination,
  onPageChange,
  onPageSizeChange,
}: RecordPaginationProps) => {
  const { page, size, totalElements, totalPages } = pagination;
  const startItem = page * size + 1;
  const endItem = Math.min((page + 1) * size, totalElements);

  const canGoPrevious = page > 0;
  const canGoNext = page < totalPages - 1;

  const handleFirstPage = () => onPageChange(0);
  const handlePreviousPage = () => onPageChange(page - 1);
  const handleNextPage = () => onPageChange(page + 1);
  const handleLastPage = () => onPageChange(totalPages - 1);

  const handlePageSizeChange = (newSize: string) => {
    onPageSizeChange(parseInt(newSize));
    onPageChange(0); // Reset to first page when changing page size
  };

  if (totalElements === 0) {
    return null;
  }

  return (
    <div className="flex items-center justify-between px-4 py-3 bg-white border-t">
      {/* Info */}
      <div className="flex items-center space-x-2 text-sm text-gray-600">
        <span>
          {startItem}-{endItem}/{totalElements}
        </span>
        <Select value={size.toString()} onValueChange={handlePageSizeChange}>
          <SelectTrigger className="h-8 w-16">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="10">10</SelectItem>
            <SelectItem value="20">20</SelectItem>
            <SelectItem value="50">50</SelectItem>
            <SelectItem value="100">100</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Navigation */}
      <div className="flex items-center space-x-1">
        <Button
          variant="outline"
          size="sm"
          onClick={handleFirstPage}
          disabled={!canGoPrevious}
          className="h-8 w-8 p-0"
        >
          <ChevronsLeft className="h-4 w-4" />
        </Button>

        <Button
          variant="outline"
          size="sm"
          onClick={handlePreviousPage}
          disabled={!canGoPrevious}
          className="h-8 w-8 p-0"
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>

        <div className="flex items-center space-x-1 px-2">
          <span className="text-sm text-gray-600">
            Trang {page + 1} / {totalPages}
          </span>
        </div>

        <Button
          variant="outline"
          size="sm"
          onClick={handleNextPage}
          disabled={!canGoNext}
          className="h-8 w-8 p-0"
        >
          <ChevronRight className="h-4 w-4" />
        </Button>

        <Button
          variant="outline"
          size="sm"
          onClick={handleLastPage}
          disabled={!canGoNext}
          className="h-8 w-8 p-0"
        >
          <ChevronsRight className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
};
