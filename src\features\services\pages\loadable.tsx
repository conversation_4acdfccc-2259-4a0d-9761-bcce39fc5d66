import { LoadingPage } from "@/components/loading/LoadingPage";
import { lazyLoad } from "@/logic/utils/lazyload";

export const RegisterAccountPage = lazyLoad(
  () => import("./RegisterAccountPage"),
  (module) => module.default,
  { fallback: <LoadingPage /> }
);

export const DocumentAccessPage = lazyLoad(
  () => import("./DocumentAccessPage"),
  (module) => module.default,
  { fallback: <LoadingPage /> }
);

export const DocumentDepositPage = lazyLoad(
  () => import("./DocumentDepositPage"),
  (module) => module.default,
  { fallback: <LoadingPage /> }
);
