import React from "react";
import { GroupMenu } from "./HeaderMenu";
import { useAppSelector } from "@/store/rootReducer";
import { selectCategoryByType } from "@/features/category/states/selector";
import { buildCategoryTree } from "@/features/category/states/types";

export const PublicMenuHorizontal: React.FC = () => {
  const list = useAppSelector((state) =>
    selectCategoryByType(state, "public-menu")
  );

  const menuList = buildCategoryTree(list);

  return (
    <div className="flex items-center justify-between w-full px-6 py-2 bg-white border-t">
      <div className="flex items-center gap-4">
        {/* Public Menu */}
        <GroupMenu menuList={menuList} />
      </div>
    </div>
  );
};
