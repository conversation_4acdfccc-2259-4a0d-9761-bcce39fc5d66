import React, { useRef, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Upload,
  Trash2,
  Check<PERSON>ircle,
  AlertCircle,
  Loader2,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { uploadFile, getFileViewUrl } from "@/features/demo/states/api";

type UploadStatus = "idle" | "uploading" | "success" | "error";

interface SimpleUploadInputProps {
  onUploadSuccess?: (fileUrl: string) => void;
  onUploadError?: (error: string) => void;
}

export const SimpleUploadInput = ({
  onUploadSuccess,
  onUploadError,
}: SimpleUploadInputProps) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [uploadStatus, setUploadStatus] = useState<UploadStatus>("idle");
  const [errorMessage, setErrorMessage] = useState<string>("");
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      setUploadStatus("idle");
      setErrorMessage("");
    }
  };

  const handleChooseFile = () => {
    if (uploadStatus === "uploading") return; // Prevent interaction during upload
    fileInputRef.current?.click();
  };

  const handleRemoveFile = () => {
    if (uploadStatus === "uploading") return; // Prevent interaction during upload
    setSelectedFile(null);
    setUploadStatus("idle");
    setErrorMessage("");
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const handleUpload = async () => {
    if (!selectedFile || uploadStatus === "uploading") return;

    setUploadStatus("uploading");
    setErrorMessage("");

    try {
      const response = await uploadFile(selectedFile);

      if (response.code === "SUCCESS") {
        const fileUrl = getFileViewUrl(response.data.name);
        setUploadStatus("success");
        onUploadSuccess?.(fileUrl);
      } else {
        throw new Error(response.message || "Upload failed");
      }
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : "Upload failed";
      setErrorMessage(errorMsg);
      setUploadStatus("error");
      onUploadError?.(errorMsg);
    }
  };

  const isDisabled = uploadStatus === "uploading";

  return (
    <div className="w-full max-w-md">
      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        onChange={handleFileSelect}
        className="hidden"
        accept="*/*"
        disabled={isDisabled}
      />

      {!selectedFile ? (
        // Frame 11: Initial state - Choose file
        <div className="flex items-center space-x-2">
          <div
            onClick={handleChooseFile}
            className={cn(
              "flex-1 px-4 py-2.5 border-2 border-dashed border-gray-300 rounded-lg",
              "bg-gray-50 hover:bg-gray-100 cursor-pointer transition-colors",
              "focus:border-primary focus:ring-2 focus:ring-primary/20",
              "text-gray-600 text-sm h-10",
              isDisabled && "opacity-50 cursor-not-allowed hover:bg-gray-50"
            )}
          >
            <div className="flex items-center space-x-2">
              <Upload className="h-4 w-4" />
              <span>Chọn file tải lên</span>
            </div>
          </div>
          <Button
            onClick={handleChooseFile}
            variant="default"
            className="px-4 h-10"
            disabled={isDisabled}
          >
            <Upload className="h-4 w-4 mr-2" />
            Tải lên
          </Button>
        </div>
      ) : (
        // Frame 12: File selected state - Show filename with remove option
        <div className="flex items-center space-x-2">
          <div
            className={cn(
              "flex-1 px-4 py-2.5 border border-gray-300 rounded-lg",
              "bg-white flex items-center justify-between",
              "text-gray-800 text-sm h-10",
              isDisabled && "opacity-75"
            )}
          >
            <span className="truncate mr-2">{selectedFile.name}</span>
            <div className="flex items-center space-x-2">
              {/* Upload status indicators */}
              {uploadStatus === "success" && (
                <CheckCircle className="h-4 w-4 text-green-500" />
              )}
              {uploadStatus === "error" && (
                <AlertCircle className="h-4 w-4 text-red-500" />
              )}

              {/* Delete button */}
              <Button
                onClick={handleRemoveFile}
                variant="ghost"
                size="sm"
                className={cn(
                  "h-6 w-6 p-0 hover:bg-gray-100 rounded-full",
                  isDisabled &&
                    "opacity-50 cursor-not-allowed hover:bg-transparent"
                )}
                disabled={isDisabled}
              >
                <Trash2 className="h-3.5 w-3.5" />
              </Button>
            </div>
          </div>

          {/* Upload button */}
          <Button
            onClick={handleUpload}
            variant="default"
            className="px-4 h-10"
            disabled={isDisabled || uploadStatus === "success"}
          >
            {uploadStatus === "uploading" ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Đang tải...
              </>
            ) : (
              <>
                <Upload className="h-4 w-4 mr-2" />
                Tải lên
              </>
            )}
          </Button>
        </div>
      )}

      {/* Error message */}
      {uploadStatus === "error" && errorMessage && (
        <div className="mt-2 text-sm text-red-600 flex items-center space-x-1">
          <AlertCircle className="h-4 w-4" />
          <span>{errorMessage}</span>
        </div>
      )}
    </div>
  );
};

export default SimpleUploadInput;
