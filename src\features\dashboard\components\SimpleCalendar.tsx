import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { CalendarEvent } from "../data/mockData";
import { Calendar, Clock, Users, AlertCircle } from "lucide-react";

interface SimpleCalendarProps {
  events: CalendarEvent[];
}

const getEventIcon = (type: CalendarEvent["type"]) => {
  switch (type) {
    case "deadline":
      return AlertCircle;
    case "meeting":
      return Users;
    case "reminder":
      return Clock;
    default:
      return Calendar;
  }
};

const getEventColor = (type: CalendarEvent["type"]) => {
  switch (type) {
    case "deadline":
      return "text-red-600 bg-red-50 border-red-200";
    case "meeting":
      return "text-blue-600 bg-blue-50 border-blue-200";
    case "reminder":
      return "text-green-600 bg-green-50 border-green-200";
    default:
      return "text-gray-600 bg-gray-50 border-gray-200";
  }
};

const getBadgeVariant = (type: CalendarEvent["type"]) => {
  switch (type) {
    case "deadline":
      return "destructive";
    case "meeting":
      return "default";
    case "reminder":
      return "secondary";
    default:
      return "secondary";
  }
};

const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  const today = new Date();
  const tomorrow = new Date(today);
  tomorrow.setDate(tomorrow.getDate() + 1);

  if (date.toDateString() === today.toDateString()) {
    return "Hôm nay";
  } else if (date.toDateString() === tomorrow.toDateString()) {
    return "Ngày mai";
  } else {
    return date.toLocaleDateString("vi-VN", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    });
  }
};

const getDaysUntil = (dateString: string) => {
  const targetDate = new Date(dateString);
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  targetDate.setHours(0, 0, 0, 0);

  const diffTime = targetDate.getTime() - today.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays === 0) return "Hôm nay";
  if (diffDays === 1) return "1 ngày nữa";
  if (diffDays === -1) return "Hôm qua";
  if (diffDays > 0) return `${diffDays} ngày nữa`;
  return `${Math.abs(diffDays)} ngày trước`;
};

export const SimpleCalendar = ({ events }: SimpleCalendarProps) => {
  // Sort events by date
  const sortedEvents = [...events].sort(
    (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime()
  );

  const upcomingEvents = sortedEvents.filter(
    (event) => new Date(event.date) >= new Date()
  );

  const pastEvents = sortedEvents.filter(
    (event) => new Date(event.date) < new Date()
  );

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Calendar className="h-5 w-5" />
          <span>Lịch tương tác</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4 max-h-80 overflow-y-auto">
          {/* Upcoming Events */}
          {upcomingEvents.length > 0 && (
            <div>
              <h4 className="text-sm font-medium text-gray-900 mb-3">
                Sự kiện sắp tới
              </h4>
              <div className="space-y-3">
                {upcomingEvents.map((event) => {
                  const Icon = getEventIcon(event.type);
                  const colorClass = getEventColor(event.type);
                  const badgeVariant = getBadgeVariant(event.type);

                  return (
                    <div
                      key={event.id}
                      className={`p-3 rounded-lg border ${colorClass}`}
                    >
                      <div className="flex items-start space-x-3">
                        <Icon className="h-4 w-4 mt-0.5 flex-shrink-0" />
                        <div className="flex-1 space-y-1">
                          <div className="flex items-center justify-between">
                            <p className="text-sm font-medium">{event.title}</p>
                            <Badge variant={badgeVariant} className="text-xs">
                              {event.type === "deadline"
                                ? "Deadline"
                                : event.type === "meeting"
                                ? "Họp"
                                : "Nhắc nhở"}
                            </Badge>
                          </div>
                          <div className="flex items-center justify-between text-xs">
                            <span>{formatDate(event.date)}</span>
                            <span className="text-muted-foreground">
                              {getDaysUntil(event.date)}
                            </span>
                          </div>
                          {event.description && (
                            <p className="text-xs opacity-80">
                              {event.description}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          {/* Past Events (if any, limited to recent ones) */}
          {pastEvents.length > 0 && (
            <div>
              <h4 className="text-sm font-medium text-gray-600 mb-3">Đã qua</h4>
              <div className="space-y-2">
                {pastEvents.slice(-2).map((event) => {
                  const Icon = getEventIcon(event.type);

                  return (
                    <div
                      key={event.id}
                      className="p-2 rounded border border-gray-200 bg-gray-50 opacity-60"
                    >
                      <div className="flex items-center space-x-2">
                        <Icon className="h-3 w-3 text-gray-400" />
                        <div className="flex-1">
                          <p className="text-xs font-medium text-gray-600">
                            {event.title}
                          </p>
                          <p className="text-xs text-gray-500">
                            {formatDate(event.date)}
                          </p>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          {events.length === 0 && (
            <div className="text-center py-6 text-muted-foreground">
              <Calendar className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">Không có sự kiện nào</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
