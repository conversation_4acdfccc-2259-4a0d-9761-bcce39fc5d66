import { RootState } from "@/store/rootReducer";
import { CategoryType } from "./types";
import { CategoryDTO } from "./types";
import { createSelector } from "@reduxjs/toolkit";

// Updated selectors for new state structure
export const selectCategoryTypes = (state: RootState) =>
  state.categoryState.types;

export const selectCategoryByType = createSelector(
  [selectCategoryTypes, (_: RootState, type: CategoryType) => type],
  (types, type) => {
    if (!types || !types[type]) {
      return [];
    }
    return types[type].data || [];
  }
);

export const selectCategorySelectedType = (state: RootState) =>
  state.categoryState.selectedType;

export const selectCategorySelectedId = (state: RootState) =>
  state.categoryState.selectedId;

export const selectCategoryMode = (state: RootState) =>
  state.categoryState.mode;

export const selectCategoryCreateParentId = (state: RootState) =>
  state.categoryState.createParentId;

// New enhanced selectors for loading states
export const selectCategoryLoadingByType = createSelector(
  [selectCategoryTypes, (_: RootState, type: CategoryType) => type],
  (types, type) => {
    if (!types || !types[type]) {
      return false;
    }
    return types[type].loading || false;
  }
);

export const selectCategoryErrorByType = createSelector(
  [selectCategoryTypes, (_: RootState, type: CategoryType) => type],
  (types, type) => {
    if (!types || !types[type]) {
      return null;
    }
    return types[type].error || null;
  }
);

export const selectCategoryLastFetchedByType = createSelector(
  [selectCategoryTypes, (_: RootState, type: CategoryType) => type],
  (types, type) => {
    if (!types || !types[type]) {
      return null;
    }
    return types[type].lastFetched || null;
  }
);

// Simplified selector - just check if we have lastFetched timestamp
export const selectCategoryIsFreshByType = createSelector(
  [selectCategoryLastFetchedByType],
  (lastFetched) => {
    // Simply return true if we have data, let the thunk handle time logic
    return !!lastFetched;
  }
);

// Global operation selectors
export const selectCategoryCreating = (state: RootState) =>
  state.categoryState.creating;

export const selectCategoryUpdating = (state: RootState) =>
  state.categoryState.updating;

export const selectCategoryDeleting = (state: RootState) =>
  state.categoryState.deleting;

export const selectCategoryGlobalError = (state: RootState) =>
  state.categoryState.globalError;

// Legacy compatibility selectors - MEMOIZED to prevent object recreation
export const selectCategoryData = createSelector(
  [selectCategoryTypes],
  (types) => ({
    "admin-menu": types["admin-menu"].data,
    "user-menu": types["user-menu"].data,
    "public-menu": types["public-menu"].data,
  })
);

// Legacy loading selector - returns true if any type is loading
export const selectCategoryLoading = (state: RootState) => {
  const types = state.categoryState.types;
  return (
    Object.values(types).some((typeState) => typeState.loading) ||
    state.categoryState.creating ||
    state.categoryState.updating ||
    state.categoryState.deleting
  );
};

// Utility selectors
export const selectCategoryHasDataByType = createSelector(
  [selectCategoryByType],
  (data) => data.length > 0
);

export const selectCategoryNeedsRefreshByType = createSelector(
  [selectCategoryHasDataByType, selectCategoryLoadingByType],
  (hasData, isLoading) => {
    // Simplified logic - only refresh if no data and not loading
    return !isLoading && !hasData;
  }
);

// Get category by id across all types
export const selectCategoryById = createSelector(
  [selectCategoryTypes, (_: RootState, id: number) => id],
  (types, id) => {
    for (const typeState of Object.values(types)) {
      const category = typeState.data.find((cat) => cat.id === id);
      if (category) return category;
    }
    return undefined;
  }
);

// Find category by slug across all types - memoized
export const selectCategoryBySlug = createSelector(
  [selectCategoryData, (_: RootState, slug: string) => slug],
  (categoryData, slug) => {
    for (const categories of Object.values(categoryData)) {
      const found = categories.find((cat) => cat.slug === slug);
      if (found) return found;
    }
    return null;
  }
);

// Helper function to build full path including parent slugs
const buildCategoryFullPath = (
  category: CategoryDTO,
  allCategories: CategoryDTO[]
): string => {
  const categoryMap = new Map<number, CategoryDTO>();
  allCategories.forEach((cat) => categoryMap.set(cat.id, cat));

  const pathSegments: string[] = [];
  let current: CategoryDTO | undefined = category;

  while (current) {
    pathSegments.unshift(current.slug);
    if (current.parentId === null) {
      break;
    }
    current = categoryMap.get(current.parentId);
  }

  return pathSegments.join("/");
};

// Find category and its linked page ID for Page component - memoized
export const selectCategoryPageInfo = createSelector(
  [
    selectCategoryBySlug,
    (state: RootState, slug: string) => {
      const category = selectCategoryBySlug(state, slug);
      return category ? selectCategoryByType(state, category.type) : [];
    },
  ],
  (category, allCategories) => {
    if (!category) {
      return {
        category: null,
        pageId: null,
        fullPath: null,
      };
    }

    // Build full path for nested categories
    const fullPath = buildCategoryFullPath(category, allCategories);

    return {
      category,
      pageId: category.postId,
      fullPath,
    };
  }
);
