/**
 * Form Tree Panel
 * Left panel showing form structure as expandable tree
 */

import React from "react";
import { FormNode } from '@/form/types';
import { useFormBuilder } from "../context/FormBuilderContext";
import { cn } from '@/lib/utils';
import { 
  ChevronRight, 
  ChevronDown, 
  Plus, 
  Eye,
  EyeOff,
  Copy,
  Trash2,
  Square,
  Type,
  FileText,
  MousePointer
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuSeparator, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';

interface TreeNodeProps {
  node: FormNode;
  level: number;
  isSelected: boolean;
  isExpanded: boolean;
  onSelect: (nodeId: string) => void;
  onToggleExpand: (nodeId: string) => void;
  onAddChild: (parentId: string) => void;
  onDuplicate: (nodeId: string) => void;
  onDelete: (nodeId: string) => void;
  onMove: (nodeId: string, newParentId: string, newIndex: number) => void;
  mode: 'design' | 'preview' | 'code';
  isDragging?: boolean;
  isDragOver?: boolean;
}

// Get icon for node type
const getNodeIcon = (type: FormNode['type']) => {
  switch (type) {
    case 'frame': return <Square className="w-3 h-3" />;
    case 'field': return <Type className="w-3 h-3" />;
    case 'title': return <FileText className="w-3 h-3" />;
    case 'control': return <MousePointer className="w-3 h-3" />;
    default: return <Square className="w-3 h-3" />;
  }
};

// Get display name for node
const getNodeDisplayName = (node: FormNode): string => {
  if (node.properties?.label) return node.properties.label;
  if (node.properties?.text) return node.properties.text;
  if (node.field?.objectKey) return node.field.objectKey;
  return node.id;
};

// Tree Node Component
const TreeNode: React.FC<TreeNodeProps> = ({
  node,
  level,
  isSelected,
  isExpanded,
  onSelect,
  onToggleExpand,
  onAddChild,
  onDuplicate,
  onDelete,
  onMove,
  mode,
  isDragging = false,
  isDragOver = false
}) => {
  const hasChildren = node.children && node.children.length > 0;
  const canAddChildren = node.type === 'frame';
  const canDelete = node.id !== 'form-root'; // Cannot delete root node
  const canDrag = node.id !== 'form-root'; // Cannot drag root node
  const indentWidth = level * 16;

  const handleNodeClick = () => {
    if (mode === 'design') {
      onSelect(node.id);
    }
  };

  const handleToggleExpand = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (hasChildren) {
      onToggleExpand(node.id);
    }
  };

  const handleAddChild = (e: React.MouseEvent) => {
    e.stopPropagation();
    onAddChild(node.id);
  };

  const handleDuplicate = (e: React.MouseEvent) => {
    e.stopPropagation();
    onDuplicate(node.id);
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (canDelete) {
      onDelete(node.id);
    }
  };

  // Drag & Drop handlers
  const handleDragStart = (e: React.DragEvent) => {
    if (!canDrag || mode !== 'design') {
      e.preventDefault();
      return;
    }
    e.dataTransfer.setData('text/plain', node.id);
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOver = (e: React.DragEvent) => {
    if (mode !== 'design') return;
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  const handleDrop = (e: React.DragEvent) => {
    if (mode !== 'design') return;
    e.preventDefault();
    e.stopPropagation();

    const draggedNodeId = e.dataTransfer.getData('text/plain');
    if (!draggedNodeId || draggedNodeId === node.id) return;

    // Determine drop position based on cursor position
    const rect = e.currentTarget.getBoundingClientRect();
    const relativeY = e.clientY - rect.top;
    const dropPosition = relativeY < rect.height / 2 ? 'before' : 'after';

    if (canAddChildren && dropPosition === 'after') {
      // Drop as child (into the node)
      onMove(draggedNodeId, node.id, 0);
    } else {
      // Drop as sibling (before or after the node)
      // This needs parent context which will be handled in the container
      console.log(`Drop ${draggedNodeId} ${dropPosition} ${node.id}`);
    }
  };

  return (
    <div className="select-none">
      {/* Node Row */}
      <div 
        className={cn(
          "flex items-center gap-1 py-1 px-2 rounded text-sm transition-colors",
          mode === 'design' ? "cursor-pointer hover:bg-gray-100" : "cursor-default",
          isSelected && "bg-blue-100 text-blue-900 ring-1 ring-blue-300",
          isDragging && "opacity-50",
          isDragOver && "bg-yellow-100 border border-yellow-400"
        )}
        style={{ marginLeft: indentWidth }}
        onClick={handleNodeClick}
        draggable={canDrag && mode === 'design'}
        onDragStart={handleDragStart}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
      >
        {/* Expand/Collapse Button */}
        <button
          onClick={handleToggleExpand}
          className={cn(
            "flex items-center justify-center w-4 h-4 hover:bg-gray-200 rounded",
            !hasChildren && "invisible"
          )}
        >
          {hasChildren && (
            isExpanded ? (
              <ChevronDown className="w-3 h-3" />
            ) : (
              <ChevronRight className="w-3 h-3" />
            )
          )}
        </button>

        {/* Node Icon */}
        <div className="flex items-center justify-center w-4 h-4 text-gray-500">
          {getNodeIcon(node.type)}
        </div>

        {/* Node Label */}
        <div className="flex-1 min-w-0 flex items-center gap-2">
          <span className="text-xs font-mono text-gray-400 uppercase">
            {node.type}
          </span>
          <span className="truncate font-medium">
            {getNodeDisplayName(node)}
          </span>
        </div>

        {/* Actions */}
        {mode === 'design' && (
          <div className="flex items-center gap-1">
            {/* Add Child Button */}
            {canAddChildren && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleAddChild}
                className="h-6 w-6 p-0 hover:bg-blue-100"
                title="Add child component"
              >
                <Plus className="w-3 h-3" />
              </Button>
            )}

            {/* Context Menu */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0 hover:bg-gray-200"
                  onClick={(e) => e.stopPropagation()}
                >
                  <span className="w-3 h-3 flex items-center justify-center">⋮</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-40">
                <DropdownMenuItem onClick={handleDuplicate}>
                  <Copy className="w-3 h-3 mr-2" />
                  Duplicate
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem 
                  onClick={handleDelete}
                  disabled={!canDelete}
                  className="text-red-600 focus:text-red-600"
                >
                  <Trash2 className="w-3 h-3 mr-2" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        )}
      </div>

      {/* Children */}
      {hasChildren && isExpanded && (
        <div>
          {node.children!.map((child) => (
            <TreeNodeContainer key={child.id} node={child} level={level + 1} />
          ))}
        </div>
      )}
    </div>
  );
};

// Container for TreeNode with context
const TreeNodeContainer: React.FC<{ node: FormNode; level: number }> = ({ node, level }) => {
  const { 
    state, 
    selectNode, 
    toggleNodeExpand, 
    showComponentSelector, 
    duplicateNode, 
    deleteNode,
    moveNode
  } = useFormBuilder();

  const [dragState, setDragState] = React.useState<{
    isDragging: boolean;
    isDragOver: boolean;
  }>({ isDragging: false, isDragOver: false });

  const isSelected = state.editor.selectedNodeId === node.id;
  const isExpanded = state.editor.expandedNodes.has(node.id);

  const handleMove = (nodeId: string, newParentId: string, newIndex: number) => {
    moveNode(nodeId, newParentId, newIndex);
  };

  // Enhanced drag handlers with state management
  const handleDragStart = () => {
    setDragState(prev => ({ ...prev, isDragging: true }));
  };

  const handleDragEnd = () => {
    setDragState({ isDragging: false, isDragOver: false });
  };

  const handleDragEnter = () => {
    setDragState(prev => ({ ...prev, isDragOver: true }));
  };

  const handleDragLeave = () => {
    setDragState(prev => ({ ...prev, isDragOver: false }));
  };

  return (
    <div
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
      onDragEnter={handleDragEnter}
      onDragLeave={handleDragLeave}
    >
      <TreeNode
        node={node}
        level={level}
        isSelected={isSelected}
        isExpanded={isExpanded}
        onSelect={selectNode}
        onToggleExpand={toggleNodeExpand}
        onAddChild={showComponentSelector}
        onDuplicate={duplicateNode}
        onDelete={deleteNode}
        onMove={handleMove}
        mode={state.editor.mode}
        isDragging={dragState.isDragging}
        isDragOver={dragState.isDragOver}
      />
    </div>
  );
};

/**
 * Form Tree Panel - Shows form structure
 */
export const FormTreePanel: React.FC = () => {
  const { state } = useFormBuilder();

  return (
    <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
      
      {/* Tree Content */}
      <div className="flex-1 p-2 overflow-auto">
        <TreeNodeContainer node={state.formConfig.uiConfig} level={0} />
      </div>
    </div>
  );
};