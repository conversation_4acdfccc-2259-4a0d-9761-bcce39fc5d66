import { InputWrapper } from "./InputWrapper";
import {
  BasicInputProps,
  DataType,
  DataTypeValue,
  FieldConfig,
  inputRegistry,
} from "./registry";

type DynamicFieldProps<K extends DataType> = {
  isViewMode: boolean;
  field: FieldConfig<K>;
  value: DataTypeValue[K];
  onChange: (value: DataTypeValue[K]) => void;
  onBlur?: () => void;
  error?: string;
};

export function DynamicField<K extends DataType>({
  isViewMode,
  field,
  value,
  onChange,
  onBlur,
  error,
}: DynamicFieldProps<K>) {
  const Comp = inputRegistry[
    field.input_type
  ] as unknown as React.ComponentType<BasicInputProps<K>>;
  return (
    <InputWrapper
      label={field.label}
      required={field.validation?.required}
      style={field.style}
      error={error}
    >
      <Comp
        isViewMode={isViewMode}
        value={value}
        onChange={onChange}
        onBlur={onBlur}
        placeholder={field.placeholder}
        name={field.label}
        id={`${field.input_type}-${Date.now()}`}
        options={field.options}
        labels={field.labels}
        default_value={field.default_value}
        disabled={field.disabled}
      />
    </InputWrapper>
  );
}
