// File: src/features/category/components/CategoryTabs.tsx
import React from "react";
import type { CategoryType } from "../states/types";
import { cn } from "@/lib/utils";

interface CategoryTabsProps {
  value: CategoryType;
  onChange: (v: CategoryType) => void;
}

const tabList: { key: CategoryType; label: string }[] = [
  { key: "public-menu", label: "Công khai" },
  { key: "user-menu", label: "Người dùng" },
  { key: "admin-menu", label: "Quản trị viên" },
];

export const CategoryTabs: React.FC<CategoryTabsProps> = ({
  value,
  onChange,
}) => {
  return (
    <div className="flex border-b">
      {tabList.map(({ key, label }) => (
        <button
          key={key}
          onClick={() => onChange(key)}
          className={cn(
            "px-4 py-2 -mb-px font-medium",
            value === key
              ? "border-b-2 border-primary text-primary"
              : "border-b-2 border-transparent text-gray-600 hover:text-gray-800"
          )}
        >
          {label}
        </button>
      ))}
    </div>
  );
};
