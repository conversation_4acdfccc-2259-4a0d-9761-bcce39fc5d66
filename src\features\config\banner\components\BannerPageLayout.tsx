import React from "react";
import { <PERSON>, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Save, RefreshCw, AlertCircle, Loader2 } from "lucide-react";

import type { BannerMetadata } from "../states/type";
import { ConfigStatusBadges } from "../../shared/components/ConfigStatusBadges";

/**
 * Props interface for the BannerPageLayout component
 *
 * @interface BannerPageLayoutProps
 */
interface BannerPageLayoutProps {
  /** Configuration metadata for page title and description */
  metadata: BannerMetadata;
  /** Loading state indicator */
  loading: boolean;
  /** Saving state indicator */
  saving: boolean;
  /** Error message to display */
  error: string | null;
  /** Whether there are unsaved changes */
  isDirty: boolean;
  /** Whether there are validation errors in form fields */
  hasValidationErrors: boolean;
  /** Callback fired when save button is clicked */
  onSave: () => void;
  /** Callback fired when refresh button is clicked */
  onRefresh: () => void;
  /** Callback fired when error alert is dismissed */
  onClearError: () => void;
  /** Custom action buttons to render instead of default ones */
  actionButtons?: React.ReactNode;
  /** Child components to render in the main content area */
  children: React.ReactNode;
}

/**
 * BannerPageLayout component provides a consistent layout for banner configuration pages.
 *
 * Features:
 * - Page header with title and description from metadata
 * - Status badges for unsaved changes and validation errors
 * - Action buttons (refresh and save) with loading states
 * - Error alert display with dismissal functionality
 * - Main content area for child components
 *
 * @param props - The component props
 * @param props.metadata - Configuration metadata containing title and description
 * @param props.loading - Whether the page is in loading state
 * @param props.saving - Whether save operation is in progress
 * @param props.error - Error message to display, if any
 * @param props.isDirty - Whether there are unsaved changes
 * @param props.hasValidationErrors - Whether there are validation errors
 * @param props.onSave - Callback for save button click
 * @param props.onRefresh - Callback for refresh button click
 * @param props.onClearError - Callback for error dismissal
 * @param props.children - Content to render in the main area
 */
export const BannerPageLayout: React.FC<BannerPageLayoutProps> = ({
  metadata,
  loading,
  saving,
  error,
  isDirty,
  hasValidationErrors,
  onSave,
  onRefresh,
  onClearError,
  actionButtons,
  children,
}) => {
  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{metadata.title}</h1>
          <p className="text-muted-foreground">
            {metadata.description}
          </p>
        </div>

        <div className="flex items-center gap-3">
          <ConfigStatusBadges
            isDirty={isDirty}
            hasValidationErrors={hasValidationErrors}
          />

          <div className="flex items-center gap-2">
            {actionButtons ? (
              actionButtons
            ) : (
              <>
                {onRefresh && (
                  <Button
                    variant="outline"
                    onClick={onRefresh}
                    disabled={loading}
                    size="sm"
                  >
                    <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                    Làm mới
                  </Button>
                )}

                {onSave && (
                  <Button
                    onClick={onSave}
                    disabled={!isDirty || saving}
                    size="sm"
                    className="min-w-[100px]"
                  >
                    {saving ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Đang lưu...
                      </>
                    ) : (
                      <>
                        <Save className="h-4 w-4 mr-2" />
                        Lưu
                      </>
                    )}
                  </Button>
                )}
              </>
            )}
          </div>
        </div>
      </div>

      {/* Error Alert */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-red-600" />
              <span className="text-red-800">{error}</span>
              <Button
                variant="ghost"
                size="sm"
                onClick={onClearError}
                className="ml-auto text-red-600 hover:text-red-800"
              >
                Đóng
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Main Content */}
      {children}
    </div>
  );
};
