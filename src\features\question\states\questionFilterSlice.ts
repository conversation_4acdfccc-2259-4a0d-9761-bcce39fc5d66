import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import type { QuestionQueryParams, QuestionFilterState } from "./types";

export const initialQuestionFilterState: QuestionFilterState = {
  params: {
    status: "NEW", // Default tab
    page: 0,
    size: 20,
    keyword: "",
  },
  loading: false,
  error: null,
  refetch: false,
};

const questionFilterSlice = createSlice({
  name: "questionFilter",
  initialState: initialQuestionFilterState,
  reducers: {
    setQuestionFilters(
      state,
      action: PayloadAction<Partial<QuestionQueryParams>>
    ) {
      state.params = { ...state.params, ...action.payload };
    },
    setQuestionLoading(state, action: PayloadAction<boolean>) {
      state.loading = action.payload;
    },
    setQuestionError(state, action: PayloadAction<string | null>) {
      state.error = action.payload;
    },
    triggerQuestionRefetch(state) {
      state.refetch = !state.refetch;
      console.log("🔄 triggerQuestionRefetch", state.refetch);
    },
    resetQuestionFilters(state) {
      state.params = initialQuestionFilterState.params;
      state.loading = false;
      state.error = null;
      state.refetch = false;
    },
  },
});

export const {
  setQuestionFilters,
  setQuestionLoading,
  setQuestionError,
  triggerQuestionRefetch,
  resetQuestionFilters,
} = questionFilterSlice.actions;

export default questionFilterSlice.reducer;
