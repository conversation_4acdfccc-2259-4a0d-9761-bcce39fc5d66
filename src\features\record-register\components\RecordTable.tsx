import { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Eye, Edit, Trash2, Send, MoreVertical } from "lucide-react";
import { RecordRegister, statusLabels, statusColors } from "../data/mockData";

interface RecordTableProps {
  records: RecordRegister[];
  onView: (record: RecordRegister) => void;
  onEdit: (record: RecordRegister) => void;
  onDelete: (record: RecordRegister) => void;
  onSubmit: (record: RecordRegister) => void;
  onSelectionChange: (selectedIds: number[]) => void;
}

export const RecordTable = ({
  records,
  onView,
  onEdit,
  onDelete,
  onSubmit,
  onSelectionChange,
}: RecordTableProps) => {
  const [selectedIds, setSelectedIds] = useState<number[]>([]);
  const [selectAll, setSelectAll] = useState(false);

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      const allIds = records.map((record) => record.id);
      setSelectedIds(allIds);
      onSelectionChange(allIds);
    } else {
      setSelectedIds([]);
      onSelectionChange([]);
    }
    setSelectAll(checked);
  };

  const handleSelectRecord = (recordId: number, checked: boolean) => {
    let newSelectedIds: number[];
    if (checked) {
      newSelectedIds = [...selectedIds, recordId];
    } else {
      newSelectedIds = selectedIds.filter((id) => id !== recordId);
    }
    setSelectedIds(newSelectedIds);
    onSelectionChange(newSelectedIds);
    setSelectAll(newSelectedIds.length === records.length);
  };

  const getActionButtons = (record: RecordRegister) => {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
            <MoreVertical className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-48">
          <DropdownMenuItem onClick={() => onView(record)}>
            <Eye className="mr-2 h-4 w-4 text-blue-600" />
            <span>Xem chi tiết</span>
          </DropdownMenuItem>

          {(record.status === "new" || record.status === "rejected") && (
            <DropdownMenuItem onClick={() => onEdit(record)}>
              <Edit className="mr-2 h-4 w-4 text-orange-600" />
              <span>Chỉnh sửa</span>
            </DropdownMenuItem>
          )}

          {record.status === "new" && (
            <DropdownMenuItem onClick={() => onSubmit(record)}>
              <Send className="mr-2 h-4 w-4 text-green-600" />
              <span>Gửi duyệt</span>
            </DropdownMenuItem>
          )}

          {(record.status === "new" || record.status === "rejected") && (
            <DropdownMenuItem
              onClick={() => onDelete(record)}
              className="text-red-600 focus:text-red-600"
            >
              <Trash2 className="mr-2 h-4 w-4" />
              <span>Xóa bỏ</span>
            </DropdownMenuItem>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
    );
  };

  if (records.length === 0) {
    return (
      <div className="bg-white rounded-lg border">
        <div className="p-8 text-center text-gray-500">
          <div className="mb-2">Không có dữ liệu</div>
          <div className="text-sm">Vui lòng thử lại với bộ lọc khác</div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg border shadow-sm">
      <Table>
        <TableHeader>
          <TableRow className="bg-gray-50">
            <TableHead className="w-12">
              <Checkbox checked={selectAll} onCheckedChange={handleSelectAll} />
            </TableHead>
            <TableHead className="w-16">STT</TableHead>
            <TableHead className="w-24">Mã yêu cầu</TableHead>
            <TableHead className="min-w-[200px]">Nội dung yêu cầu</TableHead>
            <TableHead className="min-w-[180px]">Tên cơ quan</TableHead>
            <TableHead className="min-w-[180px]">Phòng lưu trữ</TableHead>
            <TableHead className="w-20">Tổng số tài liệu</TableHead>
            <TableHead className="w-16">Số lần nộp</TableHead>
            <TableHead className="w-24">Ngày gửi</TableHead>
            <TableHead className="w-24">Trạng thái</TableHead>
            <TableHead className="w-20">Thao tác</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {records.map((record, index) => (
            <TableRow key={record.id} className="hover:bg-gray-50">
              <TableCell>
                <Checkbox
                  checked={selectedIds.includes(record.id)}
                  onCheckedChange={(checked) =>
                    handleSelectRecord(record.id, checked as boolean)
                  }
                />
              </TableCell>
              <TableCell className="font-medium">{index + 1}</TableCell>
              <TableCell className="font-mono text-sm">
                {record.requestCode}
              </TableCell>
              <TableCell>
                <div
                  className="max-w-[200px] truncate"
                  title={record.requestContent}
                >
                  {record.requestContent}
                </div>
              </TableCell>
              <TableCell>
                <div
                  className="max-w-[180px] truncate"
                  title={record.organizationName}
                >
                  {record.organizationName}
                </div>
              </TableCell>
              <TableCell>
                <div
                  className="max-w-[180px] truncate"
                  title={record.storageOffice}
                >
                  {record.storageOffice}
                </div>
              </TableCell>
              <TableCell className="text-center">
                {record.totalDocuments}
              </TableCell>
              <TableCell className="text-center">
                <span className="inline-flex items-center justify-center w-6 h-6 text-xs font-medium text-blue-600 bg-blue-100 rounded-full">
                  {record.submissionCount}
                </span>
              </TableCell>
              <TableCell className="text-sm">{record.sentDate}</TableCell>
              <TableCell>
                <Badge
                  variant="secondary"
                  className={statusColors[record.status]}
                >
                  {statusLabels[record.status]}
                </Badge>
              </TableCell>
              <TableCell>{getActionButtons(record)}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};
