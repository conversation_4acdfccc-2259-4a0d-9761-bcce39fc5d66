import { useRef, useState } from "react";
import { CategoryFormNode } from "../forms/CategoryFormNode";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { AutoForm, AutoFormRef } from "@/form";
import { CategoryDTO, CategoryType, CategoryStatus } from "../states/types";
import { Save, Trash2, Pencil, X } from "lucide-react";
import {
  Dialog,
  DialogTrigger,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  selectCategoryCreating,
  selectCategoryUpdating,
  selectCategoryDeleting,
  selectCategoryCreateParentId,
} from "../states/selector";
import { useAppSelector, useAppDispatch } from "@/store/rootReducer";
import {
  createCategoryThunk,
  updateCategoryThunk,
  deleteCategoryThunk,
  setSelectedId,
  setCategoryMode,
} from "../states/slices";
import { useCategory } from "../hooks/useCategoryData";

interface AutoFormCategoryProps {
  selectedType: CategoryType;
  selectedId: number;
  mode: "create" | "view" | "edit";
  onModeChange: (mode: "create" | "view" | "edit") => void;
}

// Define category form data interface
interface CategoryFormData {
  id: number;
  type: CategoryType;
  parentId: number | null;
  postId: number | null;
  name: string;
  slug: string;
  description: {
    icon: string;
    component: string;
  };
  priority: number;
  status: CategoryStatus;
}

export const AutoFormCategory: React.FC<AutoFormCategoryProps> = ({
  selectedType,
  selectedId,
  mode,
  onModeChange,
}) => {
  const formRef = useRef<AutoFormRef<CategoryFormData>>(null);
  const [confirmDelete, setConfirmDelete] = useState(false);
  const dispatch = useAppDispatch();

  const { data: allCategories } = useCategory(selectedType);

  const isCreating = useAppSelector(selectCategoryCreating);
  const isUpdating = useAppSelector(selectCategoryUpdating);
  const isDeleting = useAppSelector(selectCategoryDeleting);
  const createParentId = useAppSelector(selectCategoryCreateParentId);

  const category =
    selectedId > 0
      ? allCategories.find((cat) => cat.id === selectedId)
      : undefined;

  const isCreateMode = mode === "create";
  const isEditMode = mode === "edit";
  const isViewMode = mode === "view";

  const initialData: CategoryFormData = (() => {
    if (isCreateMode) {
      return {
        id: 0,
        type: selectedType,
        parentId: createParentId || null,
        postId: null,
        name: "",
        slug: "",
        description: {
          icon: "Dot",
          component: "",
        },
        priority: 0,
        status: "ACTIVE",
      };
    }

    if ((isEditMode || isViewMode) && category) {
      return {
        id: category.id,
        type: category.type,
        parentId: category.parentId || null,
        postId: category.postId || null,
        name: category.name,
        slug: category.slug,
        description: {
          icon: category.description?.icon || "Dot",
          component: category.description?.component || "",
        },
        priority: category.priority,
        status: category.status,
      };
    }

    return {
      id: 0,
      type: selectedType,
      parentId: null,
      postId: null,
      name: "",
      slug: "",
      description: {
        icon: "Dot",
        component: "",
      },
      priority: 0,
      status: "ACTIVE",
    };
  })();

  const onSubmitForm = (data: CategoryFormData) => {
    if (data.id === 0) {
      // Create mode - don't include id, createdAt, updatedAt
      const createData: Omit<CategoryDTO, "id" | "createdAt" | "updatedAt"> = {
        type: data.type,
        parentId: data.parentId,
        postId: null, // Always null for new categories
        name: data.name,
        slug: data.slug,
        description: data.description,
        priority: data.priority,
        status: data.status,
      };

      handleCreate(createData);
    } else {
      // Edit mode - include all fields
      const categoryData: CategoryDTO = {
        id: data.id,
        type: data.type,
        parentId: data.parentId,
        postId: data.postId,
        name: data.name,
        slug: data.slug,
        description: data.description,
        priority: data.priority,
        status: data.status,
        createdAt: category?.createdAt || Date.now(),
        updatedAt: Date.now(),
      };
      handleUpdate(categoryData);
    }
  };

  const handleCreate = async (
    newData: Omit<CategoryDTO, "id" | "createdAt" | "updatedAt">
  ) => {
    try {
      const resultAction = await dispatch(createCategoryThunk(newData));

      if (createCategoryThunk.fulfilled.match(resultAction)) {
        const created = resultAction.payload;

        if (created && created.id) {
          // Dispatch Redux actions to update state
          dispatch(setSelectedId(created.id));
          dispatch(setCategoryMode("view"));
        }
      }
    } catch (error) {
      console.error("Create category exception:", error);
    }
  };

  const handleUpdate = async (data: CategoryDTO) => {
    try {
      const resultAction = await dispatch(
        updateCategoryThunk({ id: data.id, payload: data })
      );

      if (updateCategoryThunk.fulfilled.match(resultAction)) {
        // Stay on the updated category with view mode - dispatch Redux actions
        dispatch(setSelectedId(data.id));
        dispatch(setCategoryMode("view"));
      }
    } catch (error) {
      console.error("Update category exception:", error);
    }
  };

  const handleRemove = async (data: CategoryDTO) => {
    try {
      const resultAction = await dispatch(
        deleteCategoryThunk({ id: data.id, type: data.type })
      );

      if (deleteCategoryThunk.fulfilled.match(resultAction)) {
        const targetId = data.parentId ?? 0;

        // Target to parent category (or root if no parent) with view mode - dispatch Redux actions
        dispatch(setSelectedId(targetId));
        dispatch(setCategoryMode("view"));
      }
    } catch (error) {
      console.error("Delete category exception:", error);
    }
  };

  const onRemove = () => {
    if (!category) return;
    handleRemove(category);
  };

  const handleSubmit = () => {
    if (formRef.current) {
      formRef.current.submitForm();
    }
  };

  const handleEditClick = () => {
    onModeChange("edit");
  };

  const handleCancelClick = () => {
    onModeChange("view");
  };

  return (
    <Card className="h-fit">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
        <CardTitle className="text-lg font-medium">
          {isCreateMode
            ? "Tạo danh mục mới"
            : isEditMode
            ? "Chỉnh sửa danh mục"
            : category?.name || "Chi tiết danh mục"}
        </CardTitle>
        {isViewMode && selectedId !== 0 && (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleEditClick}
            disabled={isUpdating}
          >
            <Pencil className="w-4 h-4 mr-2" />
            Chỉnh sửa
          </Button>
        )}
      </CardHeader>

      <CardContent>
        <AutoForm<CategoryFormData>
          ref={formRef}
          key={`${selectedId}-${mode}`}
          node={CategoryFormNode}
          viewOnly={isViewMode}
          initialData={initialData}
          onSubmit={onSubmitForm}
          validationMode="onChange"
        />

        {/* Action buttons - nằm ngoài form như bạn yêu cầu */}
        {!isViewMode && (
          <div className="flex justify-end gap-2 mt-6 pt-4 border-t">
            <Button
              size="sm"
              variant="default"
              onClick={handleSubmit}
              disabled={isCreating || isUpdating}
            >
              <Save className="mr-2 h-4 w-4" />
              {isCreateMode
                ? isCreating
                  ? "Đang tạo..."
                  : "Tạo mới"
                : isUpdating
                ? "Đang lưu..."
                : "Lưu"}
            </Button>

            {isEditMode && (
              <Dialog open={confirmDelete} onOpenChange={setConfirmDelete}>
                <DialogTrigger asChild>
                  <Button
                    className="w-24 text-white"
                    variant="destructive"
                    disabled={isDeleting}
                  >
                    <Trash2 className="mr-2 h-4 w-4" />
                    {isDeleting ? "Đang xóa..." : "Xóa"}
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Bạn có chắc muốn xoá?</DialogTitle>
                  </DialogHeader>
                  <p className="text-sm text-muted-foreground">
                    Hành động này không thể hoàn tác. Dữ liệu sẽ bị xoá vĩnh
                    viễn.
                  </p>
                  <DialogFooter className="mt-4">
                    <Button
                      size="icon"
                      className="w-28"
                      variant="default"
                      onClick={() => setConfirmDelete(false)}
                      disabled={isDeleting}
                    >
                      Huỷ
                    </Button>
                    <Button
                      size="icon"
                      className="w-28 text-white"
                      variant="destructive"
                      onClick={() => {
                        onRemove();
                        setConfirmDelete(false);
                      }}
                      disabled={isDeleting}
                    >
                      {isDeleting ? "Đang xóa..." : "Xác nhận xoá"}
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            )}

            {!isCreateMode && (
              <Button
                size="sm"
                variant="outline"
                onClick={handleCancelClick}
                disabled={isCreating || isUpdating || isDeleting}
              >
                <X className="mr-2 h-4 w-4" />
                Hủy bỏ
              </Button>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};
