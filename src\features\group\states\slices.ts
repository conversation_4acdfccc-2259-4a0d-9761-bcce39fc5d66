import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import {
  GroupState,
  CreateGroupRequest,
  UpdateGroupRequest,
  GroupRole,
  GroupTab,
  GroupAction,
} from "./types";
import {
  fetchGroups,
  fetchGroupById,
  createGroup,
  updateGroup,
  deleteGroup,
  addUsersToGroup,
  removeMemberFromGroup,
  fetchGroupRoles,
  createGroupRole,
  updateGroupRole,
  deleteGroupRole,
  // AdminGroupHasRoleController APIs
  fetchGroupRoleIds,
  assignRolesToGroup,
  removeRoleFromGroup,
} from "./api";
import { toast } from "sonner";

const initialState: GroupState = {
  data: [],
  selectedId: 0,
  currentTab: "basic",
  currentAction: "view",
  loading: false,
  error: null,
  roles: {},
  assignedRoleIds: {},
  assignedRolesLoading: {},
};

// Group async thunks
export const fetchGroupsThunk = createAsyncThunk(
  "group/fetchGroups",
  async () => {
    const response = await fetchGroups();
    if (!response.data) {
      throw new Error("No data");
    }
    return response.data;
  }
);

export const fetchGroupByIdThunk = createAsyncThunk(
  "group/fetchById",
  async (id: number) => {
    const response = await fetchGroupById(id);
    if (!response.data) {
      throw new Error("No data");
    }
    return response.data;
  }
);

export const createGroupThunk = createAsyncThunk(
  "group/create",
  async (data: CreateGroupRequest, { dispatch }) => {
    const response = await createGroup(data);
    if (!response.data) {
      throw new Error("Failed to create group");
    }
    toast.success("Tạo nhóm thành công");

    // Auto focus on the newly created group
    dispatch(setSelectedId(response.data.id));

    return response.data;
  }
);

export const updateGroupThunk = createAsyncThunk(
  "group/update",
  async ({ id, payload }: UpdateGroupRequest) => {
    const response = await updateGroup(id, payload);
    if (!response.data) {
      throw new Error("Failed to update group");
    }
    toast.success("Cập nhật nhóm thành công");
    return response.data;
  }
);

export const deleteGroupThunk = createAsyncThunk(
  "group/delete",
  async (id: number) => {
    await deleteGroup(id);
    toast.success("Xóa nhóm thành công");
    return id;
  }
);

// New batch add users thunk
export const addUsersToGroupThunk = createAsyncThunk(
  "group/addUsers",
  async ({ groupId, userIds }: { groupId: number; userIds: number[] }) => {
    await addUsersToGroup(groupId, userIds);
    toast.success(`Đã thêm ${userIds.length} người dùng vào nhóm`);
    return { groupId, userIds };
  }
);

export const removeMemberFromGroupThunk = createAsyncThunk(
  "group/removeMember",
  async ({ groupId, userId }: { groupId: number; userId: number }) => {
    await removeMemberFromGroup(groupId, userId);
    toast.success("Xóa thành viên thành công");
    return { groupId, userId };
  }
);

// Group roles async thunks
export const fetchGroupRolesThunk = createAsyncThunk(
  "group/fetchRoles",
  async (groupId: number) => {
    const response = await fetchGroupRoles(groupId);
    if (!response.data) {
      throw new Error("No data");
    }
    return { groupId, roles: response.data };
  }
);

export const createGroupRoleThunk = createAsyncThunk(
  "group/createRole",
  async ({
    groupId,
    data,
  }: {
    groupId: number;
    data: Omit<GroupRole, "id">;
  }) => {
    const response = await createGroupRole(groupId, data);
    if (!response.data) {
      throw new Error("Failed to create role");
    }
    toast.success("Tạo vai trò thành công");
    return { groupId, role: response.data };
  }
);

export const updateGroupRoleThunk = createAsyncThunk(
  "group/updateRole",
  async ({
    groupId,
    roleId,
    data,
  }: {
    groupId: number;
    roleId: number;
    data: Partial<GroupRole>;
  }) => {
    const response = await updateGroupRole(groupId, roleId, data);
    if (!response.data) {
      throw new Error("Failed to update role");
    }
    toast.success("Cập nhật vai trò thành công");
    return { groupId, role: response.data };
  }
);

export const deleteGroupRoleThunk = createAsyncThunk(
  "group/deleteRole",
  async ({ groupId, roleId }: { groupId: number; roleId: number }) => {
    await deleteGroupRole(groupId, roleId);
    toast.success("Xóa vai trò thành công");
    return { groupId, roleId };
  }
);

// AdminGroupHasRoleController - Group-Role relationship thunks
export const fetchGroupRoleIdsThunk = createAsyncThunk(
  "group/fetchRoleIds",
  async (groupId: number) => {
    const response = await fetchGroupRoleIds(groupId);
    if (!response.data) {
      throw new Error("No data");
    }
    return { groupId, roleIds: response.data };
  }
);

// New batch assign roles thunk
export const assignRolesToGroupThunk = createAsyncThunk(
  "group/assignRoles",
  async ({ groupId, roleIds }: { groupId: number; roleIds: number[] }) => {
    await assignRolesToGroup(groupId, roleIds);
    toast.success(`Đã gán ${roleIds.length} vai trò cho nhóm`);
    return { groupId, roleIds };
  }
);

export const removeRoleFromGroupThunk = createAsyncThunk(
  "group/removeRole",
  async ({ groupId, roleId }: { groupId: number; roleId: number }) => {
    await removeRoleFromGroup(groupId, roleId);
    toast.success("Gỡ vai trò thành công");
    return { groupId, roleId };
  }
);

const groupSlice = createSlice({
  name: "group",
  initialState,
  reducers: {
    setSelectedId: (state, action) => {
      state.selectedId = action.payload;
    },
    setCurrentTab: (state, action: { payload: GroupTab }) => {
      state.currentTab = action.payload;
    },
    setCurrentAction: (state, action: { payload: GroupAction }) => {
      state.currentAction = action.payload;
    },
    setTabAndAction: (
      state,
      action: { payload: { tab: GroupTab; action: GroupAction } }
    ) => {
      state.currentTab = action.payload.tab;
      state.currentAction = action.payload.action;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    // Fetch groups
    builder
      .addCase(fetchGroupsThunk.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchGroupsThunk.fulfilled, (state, action) => {
        state.loading = false;
        state.data = action.payload;
      })
      .addCase(fetchGroupsThunk.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || "Failed to fetch groups";
        toast.error("Lỗi tải danh sách nhóm");
      });

    // Create group
    builder
      .addCase(createGroupThunk.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createGroupThunk.fulfilled, (state, action) => {
        state.loading = false;
        state.data.push(action.payload);
      })
      .addCase(createGroupThunk.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || "Failed to create group";
        toast.error("Lỗi tạo nhóm");
      });

    // Update group
    builder
      .addCase(updateGroupThunk.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateGroupThunk.fulfilled, (state, action) => {
        state.loading = false;
        const index = state.data.findIndex(
          (group) => group.id === action.payload.id
        );
        if (index !== -1) {
          state.data[index] = action.payload;
        }
      })
      .addCase(updateGroupThunk.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || "Failed to update group";
        toast.error("Lỗi cập nhật nhóm");
      });

    // Delete group
    builder
      .addCase(deleteGroupThunk.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteGroupThunk.fulfilled, (state, action) => {
        state.loading = false;
        state.data = state.data.filter((group) => group.id !== action.payload);
        if (state.selectedId === action.payload) {
          state.selectedId = 0;
        }
      })
      .addCase(deleteGroupThunk.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || "Failed to delete group";
        toast.error("Lỗi xóa nhóm");
      });

    // Member operations - no state management, just for toast notifications
    builder
      .addCase(addUsersToGroupThunk.fulfilled, () => {
        // No state update needed, component handles data refresh
      })
      .addCase(removeMemberFromGroupThunk.fulfilled, () => {
        // No state update needed, component handles data refresh
      });

    // Fetch group roles
    builder.addCase(fetchGroupRolesThunk.fulfilled, (state, action) => {
      state.roles[action.payload.groupId] = action.payload.roles;
    });

    // Create role
    builder.addCase(createGroupRoleThunk.fulfilled, (state, action) => {
      const { groupId, role } = action.payload;
      if (!state.roles[groupId]) {
        state.roles[groupId] = [];
      }
      state.roles[groupId].push(role);
    });

    // Update role
    builder.addCase(updateGroupRoleThunk.fulfilled, (state, action) => {
      const { groupId, role } = action.payload;
      if (state.roles[groupId]) {
        const index = state.roles[groupId].findIndex((r) => r.id === role.id);
        if (index !== -1) {
          state.roles[groupId][index] = role;
        }
      }
    });

    // Delete role
    builder.addCase(deleteGroupRoleThunk.fulfilled, (state, action) => {
      const { groupId, roleId } = action.payload;
      if (state.roles[groupId]) {
        state.roles[groupId] = state.roles[groupId].filter(
          (role) => role.id !== roleId
        );
      }
    });

    // AdminGroupHasRoleController reducers
    builder
      .addCase(fetchGroupRoleIdsThunk.pending, (state, action) => {
        const groupId = action.meta.arg;
        state.assignedRolesLoading[groupId] = true;
      })
      .addCase(fetchGroupRoleIdsThunk.fulfilled, (state, action) => {
        const { groupId, roleIds } = action.payload;
        state.assignedRoleIds[groupId] = roleIds;
        state.assignedRolesLoading[groupId] = false;
      })
      .addCase(fetchGroupRoleIdsThunk.rejected, (state, action) => {
        const groupId = action.meta.arg;
        state.assignedRolesLoading[groupId] = false;
        toast.error("Lỗi tải danh sách vai trò");
      });

    // Batch assign roles to group
    builder.addCase(assignRolesToGroupThunk.fulfilled, (state, action) => {
      const { groupId, roleIds } = action.payload;
      if (!state.assignedRoleIds[groupId]) {
        state.assignedRoleIds[groupId] = [];
      }
      // Add new roles to existing ones (avoid duplicates)
      roleIds.forEach((roleId) => {
        if (!state.assignedRoleIds[groupId].includes(roleId)) {
          state.assignedRoleIds[groupId].push(roleId);
        }
      });
    });

    // Remove role from group
    builder.addCase(removeRoleFromGroupThunk.fulfilled, (state, action) => {
      const { groupId, roleId } = action.payload;
      if (state.assignedRoleIds[groupId]) {
        state.assignedRoleIds[groupId] = state.assignedRoleIds[groupId].filter(
          (id) => id !== roleId
        );
      }
    });
  },
});

export const {
  setSelectedId,
  setCurrentTab,
  setCurrentAction,
  setTabAndAction,
  clearError,
} = groupSlice.actions;
export default groupSlice.reducer;
