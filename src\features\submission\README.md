# Submission Feature

## 📋 Overview

The Submission feature provides a comprehensive solution for creating and managing document submission packages in the KHA Portal system. This feature allows users to create information packages for document archive submissions with detailed forms and record package management.

## 🚀 Features

### ✅ Core Functionality

- **Submission Info Form**: Comprehensive form for package information including organization details, submission counts, and contact information
- **Record Package Table**: Interactive table for managing record packages with filtering, sorting, and bulk operations
- **Professional UI**: Modern, responsive design following the project's design system
- **Mock Data Integration**: Complete mock data structure for development and testing

### ✅ Components

#### SubmissionInfoForm

- Two-column responsive layout
- Organization and submission details
- Contact information section
- Form validation and state management
- Professional card-based design with blue header

#### RecordPackageTable

- Comprehensive data table with record packages
- Status badges (<PERSON><PERSON><PERSON> mới, Mở rộng, <PERSON>hay thế)
- Action buttons (View, Edit, Delete, Download)
- Bulk selection and operations
- Import/Export functionality
- Pagination controls
- Keyword filtering

## 📁 File Structure

```
src/features/submission/
├── components/
│   ├── SubmissionInfoForm.tsx     # Main form component
│   └── RecordPackageTable.tsx     # Table component with filters
├── data/
│   └── mockData.ts                # Mock data and interfaces
├── pages/
│   ├── CreateSubmissionPage.tsx   # Main page component
│   └── loadable.tsx               # Lazy loading configuration
├── index.ts                       # Feature exports
└── README.md                      # This file
```

## 🔧 Data Interfaces

### SubmissionInfo

```typescript
interface SubmissionInfo {
  id: string;
  registrationCode: string; // Mã đăng ký nộp lưu
  organizationCode: string; // Mã cơ quan
  organizationName: string; // Tên cơ quan
  totalRegisteredRecords: number; // Tổng số hồ sơ đăng ký
  totalSubmittedRecords: number; // Tổng số hồ sơ đã nộp
  totalUploadedRecords: number; // Tổng số hồ sơ upload
  submissionCount: number; // Số lần nộp lưu
  sendDate: string; // Thời gian gửi
  note: string; // Ghi chú
  contactPerson: {
    fullName: string; // Họ và tên
    position: string; // Chức vụ/Chức danh
    phone: string; // Điện thoại
    email: string; // Địa chỉ Email
  };
  status: "draft" | "submitted" | "processing" | "approved" | "rejected";
  createdAt: string;
  updatedAt: string;
}
```

### RecordPackage

```typescript
interface RecordPackage {
  id: number;
  recordCode: string; // Mã hồ sơ
  content: string; // Nội dung
  size: string; // Kích thước
  recordOrder: string; // Số thứ tự hồ sơ
  status: "new" | "expanded" | "replaced"; // Trạng thái
  documentCount: number; // Số lượng tài liệu
  createdAt: string;
  updatedAt: string;
}
```

## 🎨 UI Components

### Status Colors

- **Tạo mới (New)**: Blue background (`bg-blue-100 text-blue-800`)
- **Mở rộng (Expanded)**: Green background (`bg-green-100 text-green-800`)
- **Thay thế (Replaced)**: Yellow background (`bg-yellow-100 text-yellow-800`)

### Form Layout

- Responsive two-column grid
- Professional card design with blue header
- Yellow highlighting for important fields
- Clear section separation

### Table Features

- Checkbox selection for bulk operations
- Action buttons with icons
- Status badges with appropriate colors
- Responsive design with horizontal scroll
- Professional pagination controls

## 🔗 Integration

### Component Registry

The feature is registered in the ComponentRegistry as:

```typescript
this.register("CreateSubmissionPage", {
  component: CreateSubmissionPage,
  isLazy: true,
  requiresAuth: true,
});
```

### Route Configuration

- Component name: `CreateSubmissionPage`
- Label: "Tạo gói tin nộp lưu"
- Description: "Tạo và quản lý gói tin nộp lưu hồ sơ tài liệu"
- Category: Admin
- Requires authentication

## 🚀 Usage

### Basic Implementation

```tsx
import { CreateSubmissionPage } from "@/features/submission";

// Use in routes or direct rendering
<CreateSubmissionPage />;
```

### Component Usage

```tsx
import {
  SubmissionInfoForm,
  RecordPackageTable
} from "@/features/submission";

// Form component
<SubmissionInfoForm
  submissionInfo={submissionInfo}
  onSubmissionInfoChange={handleInfoChange}
  readOnly={false}
/>

// Table component
<RecordPackageTable
  packages={packages}
  filters={filters}
  onFiltersChange={handleFiltersChange}
  onView={handleView}
  onEdit={handleEdit}
  onDelete={handleDelete}
  selectedIds={selectedIds}
  onSelectionChange={handleSelectionChange}
/>
```

## 🎯 Mock Data

The feature includes comprehensive mock data:

- **5 sample record packages** with realistic Vietnamese content
- **Complete submission info** matching the form fields
- **Filtering and sorting functions** for table operations
- **Status labels and colors** for UI display

## 📝 TODO

- [ ] Real API integration
- [ ] Form validation
- [ ] File upload functionality
- [ ] Document preview modals
- [ ] Export/Import implementation
- [ ] Advanced filtering options
- [ ] Real-time status updates

## 🔧 Development

### Adding New Fields

1. Update interfaces in `data/mockData.ts`
2. Add form fields in `SubmissionInfoForm.tsx`
3. Update table columns in `RecordPackageTable.tsx`
4. Regenerate mock data if needed

### Styling Guidelines

- Use Tailwind CSS classes
- Follow existing color scheme (blue primary)
- Maintain responsive design
- Use Radix UI components for consistency

### Testing

- All components include mock data for development
- Console logging for all action handlers
- Responsive design tested on mobile/desktop
- Status color verification

## 📚 Dependencies

- React 18+
- TypeScript
- Tailwind CSS
- Radix UI components
- Lucide React icons
- Project's lazy loading utilities
