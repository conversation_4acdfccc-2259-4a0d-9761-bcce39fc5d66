import React from "react";
import { MapPin, Phone, Mail } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Skeleton } from "@/components/ui/skeleton";
import type { ContactInfoProps, ContactItem } from "../states/type";

// ============================================================================
// Contact Item Component
// ============================================================================

/**
 * Individual contact item display component
 */
interface ContactItemComponentProps {
  /** Contact item data */
  contact: ContactItem;
  /** Optional custom className */
  className?: string;
}

const ContactItemComponent: React.FC<ContactItemComponentProps> = ({
  contact,
  className = ""
}) => {
  return (
    <div className={`space-y-2 ${className}`}>
      {/* Contact Name */}
      <h4 className="font-semibold text-base text-gray-900">
        {contact.name}
      </h4>

      {/* Contact Details */}
      <div className="space-y-1">
        {/* Address */}
        {contact.address && (
          <div className="flex items-start gap-2 text-sm text-gray-600">
            <MapPin className="h-4 w-4 mt-0.5 flex-shrink-0 text-gray-500" />
            <span className="break-words">{contact.address}</span>
          </div>
        )}

        {/* Phone */}
        {contact.phone && (
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <Phone className="h-4 w-4 flex-shrink-0 text-gray-500" />
            <a
              href={`tel:${contact.phone}`}
              className="hover:text-blue-600 transition-colors"
            >
              {contact.phone}
            </a>
          </div>
        )}

        {/* Email */}
        {contact.email && (
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <Mail className="h-4 w-4 flex-shrink-0 text-gray-500" />
            <a
              href={`mailto:${contact.email}`}
              className="hover:text-blue-600 transition-colors break-all"
            >
              {contact.email}
            </a>
          </div>
        )}
      </div>
    </div>
  );
};

// ============================================================================
// Loading Skeleton Component
// ============================================================================

/**
 * Loading skeleton for contact information
 */
const ContactInfoSkeleton: React.FC = () => {
  return (
    <div className="space-y-6">
      {[1, 2].map((index) => (
        <div key={index} className="space-y-2">
          <Skeleton className="h-5 w-48" />
          <div className="space-y-1">
            <div className="flex items-start gap-2">
              <Skeleton className="h-4 w-4 mt-0.5" />
              <Skeleton className="h-4 w-full max-w-md" />
            </div>
            <div className="flex items-center gap-2">
              <Skeleton className="h-4 w-4" />
              <Skeleton className="h-4 w-32" />
            </div>
            <div className="flex items-center gap-2">
              <Skeleton className="h-4 w-4" />
              <Skeleton className="h-4 w-40" />
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

// ============================================================================
// Main Contact Info Component
// ============================================================================

/**
 * Contact information display component
 * 
 * Displays organization contact information including addresses, phone numbers, and emails
 * Handles loading states and error conditions gracefully
 * 
 * @param props - Component props
 * @returns JSX element for contact information display
 */
export const ContactInfo: React.FC<ContactInfoProps> = ({
  data,
  loading,
  error,
}) => {

  let contentRender = (<></>);


  // ========================================================================
  // Error State
  // ========================================================================

  if (error) {
    contentRender = (
      <Alert variant="destructive">
        <AlertDescription>
          Không thể tải thông tin liên hệ: {error}
        </AlertDescription>
      </Alert>
    );
  } else if (loading) {
    // ========================================================================
    // Loading State
    // ========================================================================
    contentRender = (
      <ContactInfoSkeleton />
    );
  } else if (!data || !data.contacts || data.contacts.length === 0) {
    // ========================================================================
    // No Data State
    // ========================================================================
    contentRender = (
      <div className="text-center py-8 text-gray-500">
        <MapPin className="h-12 w-12 mx-auto mb-4 text-gray-300" />
        <p>Chưa có thông tin liên hệ</p>
      </div>
    );
  } else {
    contentRender = (
      <div className="space-y-6">
        {data?.contacts.map((contact, index) => (
          <ContactItemComponent
            key={`contact-${index}-${contact.name}`}
            contact={contact}
          />
        ))}
      </div>
    );
  }

  // ========================================================================
  // Data Display State
  // ========================================================================

  return (
    <div className="w-full">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">
        Thông tin liên hệ
      </h3>
      {contentRender}
    </div>
  );
};

export default ContactInfo;
