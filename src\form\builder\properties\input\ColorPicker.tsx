import React, { useState } from 'react';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

// Tailwind color palette - sorted by popularity and separated grays
const TAILWIND_COLORS = {
  // Popular colors first
  gray: ['50', '100', '200', '300', '400', '500', '600', '700', '800', '900', '950'],
  red: ['50', '100', '200', '300', '400', '500', '600', '700', '800', '900', '950'],
  blue: ['50', '100', '200', '300', '400', '500', '600', '700', '800', '900', '950'],
  green: ['50', '100', '200', '300', '400', '500', '600', '700', '800', '900', '950'],
  yellow: ['50', '100', '200', '300', '400', '500', '600', '700', '800', '900', '950'],
  orange: ['50', '100', '200', '300', '400', '500', '600', '700', '800', '900', '950'],
  purple: ['50', '100', '200', '300', '400', '500', '600', '700', '800', '900', '950'],
  pink: ['50', '100', '200', '300', '400', '500', '600', '700', '800', '900', '950'],
  
  // Secondary popular colors
  emerald: ['50', '100', '200', '300', '400', '500', '600', '700', '800', '900', '950'],
  sky: ['50', '100', '200', '300', '400', '500', '600', '700', '800', '900', '950'],
  amber: ['50', '100', '200', '300', '400', '500', '600', '700', '800', '900', '950'],
  rose: ['50', '100', '200', '300', '400', '500', '600', '700', '800', '900', '950'],
  indigo: ['50', '100', '200', '300', '400', '500', '600', '700', '800', '900', '950'],
  teal: ['50', '100', '200', '300', '400', '500', '600', '700', '800', '900', '950'],
  
  // Gray variants (separated for easier selection)
  slate: ['50', '100', '200', '300', '400', '500', '600', '700', '800', '900', '950'],
  zinc: ['50', '100', '200', '300', '400', '500', '600', '700', '800', '900', '950'],
  neutral: ['50', '100', '200', '300', '400', '500', '600', '700', '800', '900', '950'],
  stone: ['50', '100', '200', '300', '400', '500', '600', '700', '800', '900', '950'],
  
  // Less common colors
  lime: ['50', '100', '200', '300', '400', '500', '600', '700', '800', '900', '950'],
  cyan: ['50', '100', '200', '300', '400', '500', '600', '700', '800', '900', '950'],
  violet: ['50', '100', '200', '300', '400', '500', '600', '700', '800', '900', '950'],
  fuchsia: ['50', '100', '200', '300', '400', '500', '600', '700', '800', '900', '950'],
};

// Special colors
const SPECIAL_COLORS = [
  { label: 'Trong suốt', value: 'transparent', bg: 'transparent' },
  { label: 'Đen', value: 'black', bg: 'bg-black' },
  { label: 'Trắng', value: 'white', bg: 'bg-white' },
  { label: 'Hiện tại', value: 'current', bg: 'bg-gray-400' },
  { label: 'Kế thừa', value: 'inherit', bg: 'bg-gray-300' },
];

interface ColorPickerProps {
  value?: string;
  onChange: (color: string) => void;
  disabled?: boolean;
  placeholder?: string;
  className?: string;
  renderValue?: (value: string) => React.ReactNode;
}

export const ColorPicker: React.FC<ColorPickerProps> = ({
  value,
  onChange,
  disabled = false,
  placeholder = "Chọn màu",
  className,
  renderValue
}) => {
  const [isOpen, setIsOpen] = useState(false);

  // Parse current color to display
  const getCurrentColorInfo = () => {
    if (!value) return { label: placeholder, bg: 'bg-gray-200' };
    
    // Check special colors
    const special = SPECIAL_COLORS.find(c => c.value === value);
    if (special) return { label: special.label, bg: special.bg };
    
    // Parse Tailwind color pattern (e.g., "red-500", "blue-100")
    const match = value.match(/^(\w+)-(\d+)$/);
    if (match) {
      const [, colorName, shade] = match;
      return { 
        label: `${colorName}-${shade}`, 
        bg: `bg-${colorName}-${shade}` 
      };
    }
    
    return { label: value, bg: 'bg-gray-200' };
  };

  const currentColor = getCurrentColorInfo();

  const handleColorSelect = (color: string) => {
    onChange(color);
    setIsOpen(false);
  };

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          disabled={disabled}
          className={cn(
            "justify-start text-left font-normal flex items-center",
            !renderValue && "w-full",
            className
          )}
        >
          {renderValue ? renderValue(value || '') : (
            <div className="flex items-center gap-2">
              <div 
                className={cn(
                  "w-4 h-4 rounded border border-gray-300",
                  currentColor.bg === 'transparent' 
                    ? 'bg-transparent bg-[url("data:image/svg+xml,%3csvg width=\'100%25\' height=\'100%25\' xmlns=\'http://www.w3.org/2000/svg\'%3e%3cdefs%3e%3cpattern id=\'a\' patternUnits=\'userSpaceOnUse\' width=\'8\' height=\'8\'%3e%3cpath d=\'m0 0h4v4h-4zm4 4h4v4h-4z\' fill=\'%23f3f4f6\'/%3e%3c/pattern%3e%3c/defs%3e%3crect width=\'100%25\' height=\'100%25\' fill=\'url(%23a)\'/%3e%3c/svg%3e")]'
                    : currentColor.bg
                )}
              />
              <span className="text-sm">{currentColor.label}</span>
            </div>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[480px] p-4">
        <div className="space-y-4">
          {/* Special Colors */}
          <div>
            <h4 className="text-sm font-medium mb-3">Màu đặc biệt</h4>
            <div className="grid grid-cols-5 gap-2">
              {SPECIAL_COLORS.map((color) => (
                <button
                  key={color.value}
                  onClick={() => handleColorSelect(color.value)}
                  className={cn(
                    "w-10 h-10 rounded-full hover:scale-110 transition-transform border border-gray-200",
                    color.bg === 'transparent' 
                      ? 'bg-transparent bg-[url("data:image/svg+xml,%3csvg width=\'100%25\' height=\'100%25\' xmlns=\'http://www.w3.org/2000/svg\'%3e%3cdefs%3e%3cpattern id=\'a\' patternUnits=\'userSpaceOnUse\' width=\'8\' height=\'8\'%3e%3cpath d=\'m0 0h4v4h-4zm4 4h4v4h-4z\' fill=\'%23f3f4f6\'/%3e%3c/pattern%3e%3c/defs%3e%3crect width=\'100%25\' height=\'100%25\' fill=\'url(%23a)\'/%3e%3c/svg%3e")]'
                      : color.bg,
                    value === color.value && "ring-2 ring-blue-500"
                  )}
                  title={color.label}
                />
              ))}
            </div>
          </div>

          {/* Tailwind Colors */}
          <div className="space-y-4 max-h-96 overflow-y-auto">
            {Object.entries(TAILWIND_COLORS).map(([colorName, shades]) => (
              <div key={colorName}>
                <h5 className="text-xs font-medium mb-2 capitalize">{colorName}</h5>
                <div className="grid grid-cols-11 gap-1.5">
                  {shades.map((shade) => {
                    const colorValue = `${colorName}-${shade}`;
                    return (
                      <button
                        key={shade}
                        onClick={() => handleColorSelect(colorValue)}
                        className={cn(
                          `w-6 h-6 rounded-full bg-${colorName}-${shade} hover:scale-110 transition-transform border border-gray-200`,
                          value === colorValue && "ring-2 ring-blue-500"
                        )}
                        title={colorValue}
                      />
                    );
                  })}
                </div>
              </div>
            ))}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
};