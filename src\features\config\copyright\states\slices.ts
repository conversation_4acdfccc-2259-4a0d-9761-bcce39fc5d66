import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import { toast } from "sonner";
import type { CopyrightState, CopyrightConfig } from "./type";
import { fetchCopyrightConfig, updateCopyrightConfig } from "./api";

// ============================================================================
// Initial State
// ============================================================================

const initialState: CopyrightState = {
  data: null,
  savedData: null,
  loading: false,
  saving: false,
  error: null,
  isDirty: false,
};

// ============================================================================
// Async Thunks
// ============================================================================

/**
 * Async thunk to fetch copyright configuration
 */
export const fetchCopyrightAsync = createAsyncThunk(
  "copyright/fetchConfig",
  async (_, { rejectWithValue }) => {
    try {
      const response = await fetchCopyrightConfig();

      if (response.status !== 200 || !response.data) {
        throw new Error(
          response.statusText || "Failed to fetch copyright configuration"
        );
      }

      const returnValue = response.data as unknown as { data: CopyrightConfig };
      return returnValue.data;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error occurred";
      toast.error(`Không thể tải cấu hình bản quyền: ${errorMessage}`);
      return rejectWithValue(errorMessage);
    }
  }
);

/**
 * Async thunk to update copyright configuration
 */
export const updateCopyrightAsync = createAsyncThunk(
  "copyright/updateConfig",
  async (data: CopyrightConfig, { rejectWithValue }) => {
    try {
      const response = await updateCopyrightConfig(data);

      if (response.status !== 200 || !response.data) {
        throw new Error(
          response.statusText || "Failed to update copyright configuration"
        );
      }

      toast.success("Cập nhật cấu hình bản quyền thành công");
      const returnValue = response.data as unknown as { data: CopyrightConfig };
      return returnValue.data;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error occurred";
      toast.error(`Không thể cập nhật cấu hình bản quyền: ${errorMessage}`);
      return rejectWithValue(errorMessage);
    }
  }
);

// ============================================================================
// Slice Definition
// ============================================================================

const copyrightSlice = createSlice({
  name: "copyright",
  initialState,
  reducers: {
    /**
     * Mark data as dirty (unsaved changes)
     */
    setDirty: (state, action: PayloadAction<boolean>) => {
      state.isDirty = action.payload;
    },
    /**
     * Clear error state
     */
    clearError: (state) => {
      state.error = null;
    },
    /**
     * Reset state to initial values
     */
    resetState: () => initialState,
  },
  extraReducers: (builder) => {
    // ========================================================================
    // Fetch Copyright Configuration
    // ========================================================================
    builder
      .addCase(fetchCopyrightAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchCopyrightAsync.fulfilled, (state, action) => {
        state.loading = false;
        const serverData = action.payload;
        state.data = serverData;
        state.savedData = serverData;
        state.error = null;
        state.isDirty = false;
      })
      .addCase(fetchCopyrightAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });

    // ========================================================================
    // Update Copyright Configuration
    // ========================================================================
    builder
      .addCase(updateCopyrightAsync.pending, (state) => {
        state.saving = true;
        state.error = null;
      })
      .addCase(updateCopyrightAsync.fulfilled, (state, action) => {
        state.saving = false;
        const serverData = action.payload;
        state.data = serverData;
        state.savedData = serverData;
        state.error = null;
        state.isDirty = false;
      })
      .addCase(updateCopyrightAsync.rejected, (state, action) => {
        state.saving = false;
        state.error = action.payload as string;
      });
  },
});

// ============================================================================
// Export Actions and Reducer
// ============================================================================

export const { setDirty, clearError, resetState } = copyrightSlice.actions;
export default copyrightSlice.reducer;
