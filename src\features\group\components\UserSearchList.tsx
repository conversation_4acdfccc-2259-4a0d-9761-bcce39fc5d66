import React from "react";
import { UserData } from "@/features/users/states/type";
import { Input } from "@/components/ui/input";
import { Search } from "lucide-react";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { Checkbox } from "@/components/ui/checkbox";
import { Skeleton } from "@/components/ui/skeleton";

interface UserSearchListProps {
  searchKeyword: string;
  onSearchKeywordChange: (keyword: string) => void;
  searchResults: UserData[];
  searchLoading: boolean;
  selectedUserIds: number[];
  onUserToggle: (userId: number) => void;
}

export const UserSearchList: React.FC<UserSearchListProps> = ({
  searchKeyword,
  onSearchKeywordChange,
  searchResults,
  searchLoading,
  selectedUserIds,
  onUserToggle,
}) => {
  return (
    <div className="space-y-4 flex-1 overflow-hidden bg-white">
      {/* Search */}
      <div className="flex items-center space-x-2 px-1">
        <Search className="h-4 w-4 text-gray-400" />
        <Input
          placeholder="Tìm kiếm người dùng..."
          value={searchKeyword}
          onChange={(e) => onSearchKeywordChange(e.target.value)}
          className="bg-white border-gray-200"
        />
      </div>

      {/* Users List */}
      <div className="border rounded-lg h-80 flex flex-col bg-white border-gray-200">
        <div className="p-3 border-b bg-gray-50 text-sm font-medium text-gray-800 border-gray-100">
          Danh sách người dùng khả dụng
        </div>

        <div className="flex-1 overflow-y-auto p-2 bg-white">
          {searchLoading ? (
            <div className="space-y-2">
              {[...Array(5)].map((_, index) => (
                <div
                  key={index}
                  className="flex items-center space-x-3 p-2 rounded"
                >
                  <Skeleton className="h-4 w-4" />
                  <Skeleton className="h-8 w-8 rounded-full" />
                  <div className="flex-1 space-y-1">
                    <Skeleton className="h-4 w-32" />
                    <Skeleton className="h-3 w-48" />
                  </div>
                </div>
              ))}
            </div>
          ) : searchResults.length === 0 ? (
            <div className="p-8 text-center text-gray-600">
              {searchKeyword.trim()
                ? "Không tìm thấy người dùng nào"
                : "Nhập từ khóa để tìm kiếm người dùng"}
            </div>
          ) : (
            <div className="space-y-1">
              {searchResults.map((user: UserData) => (
                <div
                  key={user.id}
                  className="flex items-center space-x-3 p-2 hover:bg-gray-50 rounded cursor-pointer border border-transparent hover:border-gray-200"
                  onClick={() => onUserToggle(user.id)}
                >
                  <Checkbox
                    checked={selectedUserIds.includes(user.id)}
                    onCheckedChange={() => onUserToggle(user.id)}
                  />
                  <Avatar className="h-8 w-8">
                    <AvatarImage src="" />
                    <AvatarFallback>
                      {user.fullName?.charAt(0) || user.userName.charAt(0)}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1 min-w-0">
                    <div className="font-medium truncate text-gray-900">
                      {user.fullName || user.userName}
                    </div>
                    <div className="text-sm text-gray-600 truncate">
                      {user.email}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Selected count */}
      {selectedUserIds.length > 0 && (
        <div className="text-sm text-blue-700 bg-blue-50 p-3 rounded border border-blue-200">
          Đã chọn {selectedUserIds.length} người dùng
        </div>
      )}
    </div>
  );
};
