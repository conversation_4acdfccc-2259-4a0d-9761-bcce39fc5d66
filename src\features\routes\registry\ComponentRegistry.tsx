import React, { Suspense, ComponentType } from "react";
import { PageNotFound } from "@/features/notfound/loadable";
import { LoadingPage } from "@/components/loading/LoadingPage";

// Lazy imports for better performance
const Page = React.lazy(() => import("@/features/page/pages/Page"));
const CategoryAdminPage = React.lazy(
  () => import("@/features/category/pages/CategoryAdminPage")
);
const ImageAdminPage = React.lazy(
  () => import("@/features/media/images/pages/ImageAdminPage")
);

const PostAdminPage = React.lazy(
  () => import("@/features/post/pages/PostAdminPage")
);
const UserAdminPage = React.lazy(
  () => import("@/features/users/pages/UserAdminPage")
);
const GroupAdminPage = React.lazy(
  () => import("@/features/group/pages/GroupAdminPage")
);
const RoleAdminPage = React.lazy(
  () => import("@/features/role/pages/RoleAdminPage")
);
const PageAdminPage = React.lazy(
  () => import("@/features/page/pages/PageAdminPage")
);
const ActionAdminPage = React.lazy(
  () => import("@/features/action/pages/ActionAdminPage")
);
const ResourceAdminPage = React.lazy(
  () => import("@/features/resource/pages/ResourceAdminPage")
);
const PermissionAdminPage = React.lazy(
  () => import("@/features/permission/pages/PermissionAdminPage")
);
const QuestionAdminPage = React.lazy(
  () => import("@/features/question/pages/QuestionAdminPage")
);
const QuestionConfigAdminPage = React.lazy(
  () => import("@/features/question/pages/QuestionConfigAdminPage")
);
const QuestionListAdminPage = React.lazy(
  () => import("@/features/question/pages/QuestionListAdminPage")
);
const DashboardPage = React.lazy(
  () => import("@/features/dashboard/pages/DashboardPage")
);
const RecordRegisterAdminPage = React.lazy(
  () => import("@/features/record-register/pages/RecordRegisterAdminPage")
);
const CreateSubmissionPage = React.lazy(
  () => import("@/features/submission/pages/CreateSubmissionPage")
);

const UserProfilePage = React.lazy(
  () => import("@/features/auth/pages/UserProfilePage")
);

// Form Builder
const FormBuilderPage = React.lazy(
  () => import("@/form/builder/pages/FormBuilderPage")
);

// Sync imports for critical pages
import AdminPage from "@/features/admin/pages/AdminPage";
import { LoginPage } from "@/features/auth/pages/loadable";
import { RegisterPage } from "@/features/auth/pages/loadable";
import { OrganizationStructurePage } from "@/features/about/organization-structure/pages/loadable";

import { CopyrightManagementPage } from "@/features/config/copyright/pages/loadable";
import { BannerManagementPage } from "@/features/config/banner/pages/loadable";
import { ContactManagementPage } from "@/features/config/contact/pages/loadable";
import { HotlineManagementPage } from "@/features/config/hotline/pages/loadable";

import { PortalLinkManagementPage } from "@/features/config/portal-link/pages/loadable";
import { DemoPage } from "@/features/demo/pages/loadable";

interface ComponentConfig {
  component: ComponentType;
  isLazy?: boolean;
  requiresAuth?: boolean;
  roles?: string[];
}

/**
 * 🚀 Dynamic Component Registry
 *
 * ✅ Benefits:
 * - No switch statement hell
 * - Easy to add new components
 * - Lazy loading support
 * - Type-safe component references
 * - Metadata support (auth, roles)
 */
class ComponentRegistry {
  private registry = new Map<string, ComponentConfig>();

  constructor() {
    this.initializeDefaultComponents();
  }

  private initializeDefaultComponents() {
    // ✅ Admin Components (Lazy loaded)
    this.register("CategoryAdminPage", {
      component: CategoryAdminPage,
      isLazy: true,
      requiresAuth: true,
    });
    this.register("ImageAdminPage", {
      component: ImageAdminPage,
      isLazy: true,
      requiresAuth: true,
    });

    this.register("PostAdminPage", {
      component: PostAdminPage,
      isLazy: true,
      requiresAuth: true,
    });
    this.register("UserAdminPage", {
      component: UserAdminPage,
      isLazy: true,
      requiresAuth: true,
    });
    this.register("GroupAdminPage", {
      component: GroupAdminPage,
      isLazy: true,
      requiresAuth: true,
    });
    this.register("RoleAdminPage", {
      component: RoleAdminPage,
      isLazy: true,
      requiresAuth: true,
    });
    this.register("PageAdminPage", {
      component: PageAdminPage,
      isLazy: true,
      requiresAuth: true,
    });
    this.register("ActionAdminPage", {
      component: ActionAdminPage,
      isLazy: true,
      requiresAuth: true,
    });
    this.register("ResourceAdminPage", {
      component: ResourceAdminPage,
      isLazy: true,
      requiresAuth: true,
    });
    this.register("PermissionAdminPage", {
      component: PermissionAdminPage,
      isLazy: true,
      requiresAuth: true,
    });
    this.register("QuestionAdminPage", {
      component: QuestionAdminPage,
      isLazy: true,
      requiresAuth: true,
    });
    this.register("QuestionConfigAdminPage", {
      component: QuestionConfigAdminPage,
      isLazy: true,
      requiresAuth: true,
    });
    this.register("QuestionListAdminPage", {
      component: QuestionListAdminPage,
      isLazy: true,
      requiresAuth: true,
    });
    this.register("DashboardPage", {
      component: DashboardPage,
      isLazy: true,
      requiresAuth: true,
    });
    this.register("RecordRegisterAdminPage", {
      component: RecordRegisterAdminPage,
      isLazy: true,
      requiresAuth: true,
    });
    this.register("CreateSubmissionPage", {
      component: CreateSubmissionPage,
      isLazy: true,
      requiresAuth: true,
    });
    this.register("OrganizationStructurePage", {
      component: OrganizationStructurePage,
      isLazy: true,
      requiresAuth: true,
    });
    this.register("UserProfilePage", {
      component: UserProfilePage,
      isLazy: true,
      requiresAuth: true,
    });

    this.register("CopyrightManagementPage", {
      component: CopyrightManagementPage,
      isLazy: true,
      requiresAuth: true,
    });
    this.register("ContactManagementPage", {
      component: ContactManagementPage,
      isLazy: true,
      requiresAuth: true,
    });
    this.register("BannerManagementPage", {
      component: BannerManagementPage,
      isLazy: true,
      requiresAuth: true,
    });
    this.register("HotlineManagementPage", {
      component: HotlineManagementPage,
      isLazy: true,
      requiresAuth: true,
    });

    this.register("PortalLinkManagementPage", {
      component: PortalLinkManagementPage,
      isLazy: true,
      requiresAuth: true,
    });
    this.register("DemoPage", {
      component: DemoPage,
      isLazy: true,
      requiresAuth: true,
    });
    // ✅ Form Builder
    this.register("FormBuilderPage", {
      component: FormBuilderPage,
      isLazy: true,
      requiresAuth: true,
    });

    // ✅ Core Components (Sync loaded)
    this.register("AdminPage", { component: AdminPage, requiresAuth: true });
    this.register("Page", { component: Page, isLazy: true });
    this.register("LoginPage", { component: LoginPage });
    this.register("RegisterPage", { component: RegisterPage });
  }

  /**
   * Register a new component
   */
  register(name: string, config: ComponentConfig) {
    this.registry.set(name, config);
  }

  /**
   * Get component configuration
   */
  getConfig(name: string): ComponentConfig | null {
    return this.registry.get(name) || null;
  }

  /**
   * Render component with lazy loading support
   */
  renderComponent(name: string | null): React.ReactElement {
    if (!name) {
      return <PageNotFound />;
    }

    const config = this.registry.get(name);
    if (!config) {
      console.warn(`Component "${name}" not found in registry`);
      return <PageNotFound />;
    }

    const { component: Component, isLazy = false } = config;

    // ✅ Lazy components wrapped in Suspense
    if (isLazy) {
      return (
        <Suspense fallback={<LoadingPage />}>
          <Component />
        </Suspense>
      );
    }

    // ✅ Sync components rendered directly
    return <Component />;
  }

  /**
   * Get all registered component names
   */
  getRegisteredNames(): string[] {
    return Array.from(this.registry.keys());
  }

  /**
   * Check if component requires authentication
   */
  requiresAuth(name: string): boolean {
    const config = this.registry.get(name);
    return config?.requiresAuth || false;
  }

  /**
   * Get required roles for component
   */
  getRequiredRoles(name: string): string[] {
    const config = this.registry.get(name);
    return config?.roles || [];
  }

  /**
   * Direct component access (for routes)
   */
  FormBuilderPage() {
    return this.renderComponent("FormBuilderPage");
  }
}

// ✅ Singleton instance
export const componentRegistry = new ComponentRegistry();

/**
 * Hook to use component registry
 */
export const useComponentRegistry = () => {
  return {
    renderComponent: (name: string | null) =>
      componentRegistry.renderComponent(name),
    requiresAuth: (name: string) => componentRegistry.requiresAuth(name),
    getRequiredRoles: (name: string) =>
      componentRegistry.getRequiredRoles(name),
    getRegisteredNames: () => componentRegistry.getRegisteredNames(),
  };
};

/**
 * Helper function for backward compatibility
 */
export const importPage = (name: string | null): React.ReactElement => {
  return componentRegistry.renderComponent(name);
};
