import React from "react";
import { RouteObject } from "react-router-dom";
import { useDynamicRouteBuilder } from "./DynamicRouteBuilder";
import { UserPage } from "@/features/auth/pages/loadable";
// import { componentRegistry } from "../registry/ComponentRegistry";

/**
 * 👤 User Routes Builder
 *
 * ✅ Features:
 * - Dynamic routes from user-menu categories
 * - Profile management routes
 * - Content editor routes
 * - User-specific functionality
 */
export const useUserRoutes = () => {
  const {
    routes: dynamicRoutes,
    isReady,
    categoryCount,
  } = useDynamicRouteBuilder({
    type: "user-menu",
    includeStaticRoutes: true,
    buildNestedPaths: true, // User routes can be nested (profile/settings/editor)
  });

  // Add default user dashboard route
  const staticUserRoutes: RouteObject[] = [
    {
      path: "",
      element: React.createElement(UserPage),
      id: "user-dashboard",
    },
    // TODO: Add when more user components are implemented
    // {
    //   path: "profile",
    //   element: componentRegistry.renderComponent("UserProfilePage"),
    //   id: "user-profile",
    // },
    // {
    //   path: "editor",
    //   element: componentRegistry.renderComponent("PostEditorPage"),
    //   id: "post-editor",
    // },
  ];

  // Combine all user routes
  const userRoutes: RouteObject[] = [...staticUserRoutes, ...dynamicRoutes];

  return {
    routes: userRoutes,
    isReady,
    categoryCount,
    // Helper methods for extending routes
    addUserRoute: (route: RouteObject) => {
      userRoutes.push(route);
    },
  };
};

/**
 * Register new user components in registry
 * Call this when implementing user-menu components
 */
export const registerUserComponents = () => {
  // TODO: Implement when components are ready
  // componentRegistry.register("UserProfilePage", {
  //   component: UserProfilePage,
  //   isLazy: true,
  //   requiresAuth: true
  // });
  // componentRegistry.register("PostEditorPage", {
  //   component: PostEditorPage,
  //   isLazy: true,
  //   requiresAuth: true
  // });

  console.log("🚧 User components registration pending implementation");
};
