import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ChevronDown } from "lucide-react";
import { useNavigate, useLocation } from "react-router-dom";
import clsx from "clsx";
import { CategoryTree } from "@/features/category/states/types";

interface SingleMenuProps {
  menu: CategoryTree;
  active: boolean;
}

export const SingleMenu: React.FC<SingleMenuProps> = ({ menu, active }) => {
  const navigate = useNavigate();

  const handleNavigation = (path: string) => {
    // Luôn điều hướng về đường dẫn public, không giữ prefix và thay thế hoàn toàn URL
    navigate(path.startsWith("/") ? path : "/" + path, { replace: true });
  };

  // Nếu không có menu con: xử lý click ngay
  if (!menu.children?.length) {
    return (
      <div
        onClick={() => handleNavigation(menu.slug)}
        className={clsx(
          "inline-flex gap-1 items-center border-b-2 cursor-pointer",
          active
            ? "border-primary text-primary font-semibold"
            : "border-transparent text-accent-foreground hover:text-primary hover:border-primary"
        )}
      >
        {menu.name}
      </div>
    );
  }

  // Nếu có menu con: dùng Dropdown
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <div
          className={clsx(
            "inline-flex gap-1 items-center border-b-2 cursor-pointer",
            active
              ? "border-primary text-primary font-semibold"
              : "border-transparent text-accent-foreground hover:text-primary hover:border-primary"
          )}
        >
          {menu.name}
          <ChevronDown className="w-4 h-4" />
        </div>
      </DropdownMenuTrigger>

      <DropdownMenuContent
        sideOffset={8}
        align="start"
        className="min-w-[200px] z-[11]"
        style={{ position: "fixed" }}
      >
        {menu.children.map((item) => (
          <DropdownMenuItem
            key={item.slug}
            onClick={() => handleNavigation(`${menu.slug}/${item.slug}`)}
            className="cursor-pointer"
          >
            {item.name}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

interface GroupMenuProps {
  menuList: CategoryTree[];
}

export const GroupMenu: React.FC<GroupMenuProps> = ({ menuList }) => {
  const location = useLocation();

  const isActive = (menu: CategoryTree): boolean => {
    const currentPath = location.pathname;
    const currentPrefix = currentPath.split("/")[1];
    const isAdminOrMe = currentPrefix === "me" || currentPrefix === "admin";

    // Trang chủ
    if (menu.slug === "/") {
      return currentPath === "/";
    }

    // Chỉ kiểm tra active khi không ở trang /me hoặc /admin
    if (isAdminOrMe) {
      return false;
    }

    // Khớp chính xác hoặc là prefix
    const menuPath = "/" + menu.slug;
    if (currentPath === menuPath || currentPath.startsWith(menuPath + "/")) {
      return true;
    }

    // Kiểm tra children
    return (
      menu.children?.some((child) => {
        const childPath = "/" + child.slug;
        return (
          currentPath === childPath || currentPath.startsWith(childPath + "/")
        );
      }) ?? false
    );
  };

  return (
    <nav className="flex items-center gap-6">
      {menuList.map((menu) => (
        <SingleMenu key={menu.id} menu={menu} active={isActive(menu)} />
      ))}
    </nav>
  );
};
