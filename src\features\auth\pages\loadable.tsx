import { LoadingPage } from "@/components/loading/LoadingPage";
import { lazyLoad } from "@/logic/utils/lazyload";

export const LoginPage = lazyLoad(
  () => import("./LoginPage"),
  (module) => module.default,
  { fallback: <LoadingPage /> }
);

export const RegisterPage = lazyLoad(
  () => import("./RegisterPage"),
  (module) => module.default,
  { fallback: <LoadingPage /> }
);

export const UserPage = lazyLoad(
  () => import("./UserPage"),
  (module) => module.default,
  { fallback: <LoadingPage /> }
);

export const UserProfilePage = lazyLoad(
  () => import("./UserProfilePage"),
  (module) => module.default,
  { fallback: <LoadingPage /> }
);

export const UserLayout = lazyLoad(
  () => import("./UserLayout"),
  (module) => module.default,
  { fallback: <LoadingPage /> }
);
