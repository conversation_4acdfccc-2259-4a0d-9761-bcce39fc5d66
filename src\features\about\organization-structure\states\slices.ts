import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { toast } from "sonner";
import type {
  ContactState,
  HotlineState,
  OrganizationStructureState,
} from "./type";
import { fetchOrganizationData, extractErrorMessage } from "./api";

// ============================================================================
// Initial States
// ============================================================================

const initialContactState: ContactState = {
  data: null,
  loading: false,
  error: null,
  lastUpdated: null,
};

const initialHotlineState: HotlineState = {
  data: null,
  loading: false,
  error: null,
  lastUpdated: null,
};

const initialState: OrganizationStructureState = {
  contacts: initialContactState,
  hotlines: initialHotlineState,
};

// ============================================================================
// Async Thunks
// ============================================================================

/**
 * Async thunk to fetch both contact and hotline configurations
 */
export const fetchOrganizationDataAsync = createAsyncThunk(
  "organizationStructure/fetchOrganizationData",
  async (_, { rejectWithValue }) => {
    try {
      const data = await fetchOrganizationData();
      return data;
    } catch (error) {
      const errorMessage = extractErrorMessage(error);
      toast.error(`Failed to load organization data: ${errorMessage}`);
      return rejectWithValue(errorMessage);
    }
  }
);

// ============================================================================
// Redux Slice
// ============================================================================

const organizationStructureSlice = createSlice({
  name: "organizationStructure",
  initialState,
  reducers: {
    // ========================================================================
    // Contact Reducers
    // ========================================================================

    /**
     * Clear contact error state
     */
    clearContactError: (state) => {
      state.contacts.error = null;
    },

    /**
     * Reset contact state to initial values
     */
    resetContactState: (state) => {
      state.contacts = initialContactState;
    },

    // ========================================================================
    // Hotline Reducers
    // ========================================================================

    /**
     * Clear hotline error state
     */
    clearHotlineError: (state) => {
      state.hotlines.error = null;
    },

    /**
     * Reset hotline state to initial values
     */
    resetHotlineState: (state) => {
      state.hotlines = initialHotlineState;
    },

    // ========================================================================
    // Combined Reducers
    // ========================================================================

    /**
     * Clear all error states
     */
    clearAllErrors: (state) => {
      state.contacts.error = null;
      state.hotlines.error = null;
    },

    /**
     * Reset entire organization structure state
     */
    resetOrganizationState: (state) => {
      state.contacts = initialContactState;
      state.hotlines = initialHotlineState;
    },
  },

  extraReducers: (builder) => {
    // ========================================================================
    // Organization Data Async Thunk Handlers
    // ========================================================================

    builder
      .addCase(fetchOrganizationDataAsync.pending, (state) => {
        state.contacts.loading = true;
        state.hotlines.loading = true;
        state.contacts.error = null;
        state.hotlines.error = null;
      })
      .addCase(fetchOrganizationDataAsync.fulfilled, (state, action) => {
        const { contacts, hotlines } = action.payload;
        const timestamp = Date.now();

        // Update contact state
        state.contacts.loading = false;
        state.contacts.data = contacts;
        state.contacts.lastUpdated = contacts ? timestamp : null;
        state.contacts.error = contacts ? null : "Failed to load contact data";

        // Update hotline state
        state.hotlines.loading = false;
        state.hotlines.data = hotlines;
        state.hotlines.lastUpdated = hotlines ? timestamp : null;
        state.hotlines.error = hotlines ? null : "Failed to load hotline data";
      })
      .addCase(fetchOrganizationDataAsync.rejected, (state, action) => {
        const errorMessage = action.payload as string;

        state.contacts.loading = false;
        state.contacts.error = errorMessage;

        state.hotlines.loading = false;
        state.hotlines.error = errorMessage;
      });
  },
});

// ============================================================================
// Export Actions and Reducer
// ============================================================================

export const {
  clearContactError,
  resetContactState,
  clearHotlineError,
  resetHotlineState,
  clearAllErrors,
  resetOrganizationState,
} = organizationStructureSlice.actions;

export default organizationStructureSlice.reducer;
