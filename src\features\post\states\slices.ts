import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import type { PostQueryParams } from "./api";

export interface PostsFilterState {
  params: PostQueryParams;
  loading: boolean;
  error: string | null;
  refetch: boolean;
}

export const initialState: PostsFilterState = {
  params: {
    type: "ARTICLE",
    status: "DRAFT",
    page: 0,
    size: 10,
    keyword: "",
  },
  loading: false,
  error: null,
  refetch: false,
};

const postsFilterSlice = createSlice({
  name: "postsFilter",
  initialState,
  reducers: {
    setFilters(state, action: PayloadAction<Partial<PostQueryParams>>) {
      state.params = { ...state.params, ...action.payload };
    },
    setLoading(state, action: PayloadAction<boolean>) {
      state.loading = action.payload;
    },
    setError(state, action: PayloadAction<string | null>) {
      state.error = action.payload;
    },
    triggerRefetch(state) {
      state.refetch = !state.refetch;
      console.log("triggerRefetch", state.refetch);
    },
    resetFilters(state) {
      state.params = initialState.params;
      state.loading = false;
      state.error = null;
      state.refetch = false;
    },
  },
});

export const {
  setFilters,
  setLoading,
  setError,
  triggerRefetch,
  resetFilters,
} = postsFilterSlice.actions;

export default postsFilterSlice.reducer;
