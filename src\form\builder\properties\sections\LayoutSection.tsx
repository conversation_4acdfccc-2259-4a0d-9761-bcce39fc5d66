/**
 * Layout Section Component
 * For Frame nodes - layout direction, grid, and gap settings
 */

import React from 'react';
import { FormNode } from '@/form/types';
import { PropertiesFormProps, LayoutType, GridConfig } from '../types';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { LayoutPopover } from '../input/LayoutPopover';
import { GapPopover } from '../input/GapPopover';
import { AlignmentPopover } from '../input/AlignmentPopover';

export const LayoutSection: React.FC<PropertiesFormProps> = ({
  node,
  onChange,
  disabled = false
}) => {
  const currentClasses = node.styles?.container || '';

  // Detect current layout type
  const getLayoutType = (): LayoutType => {
    if (currentClasses.includes('grid')) return 'grid';
    if (currentClasses.includes('flex-col')) return 'vertical';
    if (currentClasses.includes('flex') && !currentClasses.includes('flex-col')) return 'horizontal';
    return 'default';
  };

  const currentLayout = getLayoutType();

  // Extract grid configuration
  const getGridConfig = (): GridConfig => {
    const rowsMatch = currentClasses.match(/grid-rows-(\d+)/);
    const colsMatch = currentClasses.match(/grid-cols-(\d+)/);
    return {
      rows: rowsMatch ? parseInt(rowsMatch[1]) : 2,
      cols: colsMatch ? parseInt(colsMatch[1]) : 2
    };
  };

  const gridConfig = getGridConfig();

  // Extract gap value
  const getGapValue = (): string => {
    const gapMatch = currentClasses.match(/gap-(\w+)/);
    return gapMatch ? gapMatch[1] : '4'; // Default to 4 instead of none
  };

  // Extract space-y value for default layout
  const getSpaceYValue = (): string => {
    const spaceMatch = currentClasses.match(/space-y-(\w+)/);
    return spaceMatch ? spaceMatch[1] : '4';
  };

  const currentGap = getGapValue();
  const currentSpaceY = getSpaceYValue();

  // Extract current justify content
  const getCurrentJustify = (): string => {
    if (currentClasses.includes('justify-start')) return 'start';
    if (currentClasses.includes('justify-center')) return 'center';
    if (currentClasses.includes('justify-end')) return 'end';
    if (currentClasses.includes('justify-between')) return 'between';
    if (currentClasses.includes('justify-around')) return 'around';
    if (currentClasses.includes('justify-evenly')) return 'evenly';
    return 'start';
  };

  // Extract current align items
  const getCurrentAlign = (): string => {
    if (currentClasses.includes('items-start')) return 'start';
    if (currentClasses.includes('items-center')) return 'center';
    if (currentClasses.includes('items-end')) return 'end';
    if (currentClasses.includes('items-stretch')) return 'stretch';
    if (currentClasses.includes('items-baseline')) return 'baseline';
    return 'start';
  };

  // Clean up classes utility
  const cleanupClasses = (classes: string): string => {
    return classes
      .split(' ')
      .filter(cls => cls.trim() !== '')
      .filter((cls, index, arr) => arr.indexOf(cls) === index) // Remove duplicates
      .join(' ')
      .trim();
  };

  // Update layout type
  const updateLayoutType = (layoutType: LayoutType) => {
    let classArray = currentClasses.split(' ').filter(cls => cls.trim() !== '');

    // Remove all layout-related classes
    classArray = classArray.filter(cls => 
      !cls.match(/^(flex|flex-col|flex-row|grid|grid-rows-\d+|grid-cols-\d+|space-y-\w+|gap-\w+|justify-\w+|items-\w+)$/)
    );

    // Add new layout classes based on type
    switch (layoutType) {
      case 'horizontal':
        classArray.push('flex', 'flex-row');
        // Add default gap if switching to flex and no gap exists
        if (!currentClasses.match(/gap-\w+/)) {
          classArray.push('gap-4');
        }
        break;
      case 'vertical':
        classArray.push('flex', 'flex-col');
        // Add default gap if switching to flex and no gap exists
        if (!currentClasses.match(/gap-\w+/)) {
          classArray.push('gap-4');
        }
        break;
      case 'grid':
        classArray.push('grid', `grid-rows-${gridConfig.rows}`, `grid-cols-${gridConfig.cols}`);
        // Add default gap if switching to grid and no gap exists
        if (!currentClasses.match(/gap-\w+/)) {
          classArray.push('gap-4');
        }
        break;
      case 'default':
      default:
        classArray.push(`space-y-${currentSpaceY}`);
        break;
    }

    const newClasses = cleanupClasses(classArray.join(' '));

    onChange({
      styles: {
        ...node.styles,
        container: newClasses
      }
    });
  };

  // Update grid configuration
  const updateGridConfig = (config: Partial<GridConfig>) => {
    const newConfig = { ...gridConfig, ...config };
    let classArray = currentClasses.split(' ').filter(cls => cls.trim() !== '');

    // Remove existing grid size classes
    classArray = classArray.filter(cls => 
      !cls.match(/^grid-(rows|cols)-\d+$/)
    );

    // Add new grid size classes
    classArray.push(`grid-rows-${newConfig.rows}`, `grid-cols-${newConfig.cols}`);

    const newClasses = cleanupClasses(classArray.join(' '));

    onChange({
      styles: {
        ...node.styles,
        container: newClasses
      }
    });
  };

  // Update gap
  const updateGap = (gapValue: string) => {
    let classArray = currentClasses.split(' ').filter(cls => cls.trim() !== '');

    // Remove existing gap classes
    classArray = classArray.filter(cls => 
      !cls.match(/^gap-\w+$/)
    );

    // Add new gap class if not 'none'
    if (gapValue && gapValue !== 'none') {
      classArray.push(`gap-${gapValue}`);
    }

    const newClasses = cleanupClasses(classArray.join(' '));

    onChange({
      styles: {
        ...node.styles,
        container: newClasses
      }
    });
  };

  // Update space-y value for default layout
  const updateSpaceY = (spaceValue: string) => {
    let classArray = currentClasses.split(' ').filter(cls => cls.trim() !== '');

    // Remove existing space-y classes
    classArray = classArray.filter(cls => 
      !cls.match(/^space-y-\w+$/)
    );

    // Add new space-y class
    if (spaceValue && spaceValue !== 'none') {
      classArray.push(`space-y-${spaceValue}`);
    }

    const newClasses = cleanupClasses(classArray.join(' '));

    onChange({
      styles: {
        ...node.styles,
        container: newClasses
      }
    });
  };

  // Update justify content
  const updateJustify = (justify: string) => {
    let classArray = currentClasses.split(' ').filter(cls => cls.trim() !== '');

    // Remove existing justify classes
    classArray = classArray.filter(cls => 
      !cls.match(/^justify-\w+$/)
    );

    // Add new justify class
    if (justify && justify !== 'start') {
      classArray.push(`justify-${justify}`);
    }

    const newClasses = cleanupClasses(classArray.join(' '));

    onChange({
      styles: {
        ...node.styles,
        container: newClasses
      }
    });
  };

  // Update align items
  const updateAlign = (align: string) => {
    let classArray = currentClasses.split(' ').filter(cls => cls.trim() !== '');

    // Remove existing items classes
    classArray = classArray.filter(cls => 
      !cls.match(/^items-\w+$/)
    );

    // Add new items class
    if (align && align !== 'start') {
      classArray.push(`items-${align}`);
    }

    const newClasses = cleanupClasses(classArray.join(' '));

    onChange({
      styles: {
        ...node.styles,
        container: newClasses
      }
    });
  };


  return (
    <div className="space-y-2">
      {/* Layout Type Selector */}
      <div className="space-y-1">
        <Label className="text-xs">Bố cục</Label>
        <LayoutPopover
          value={currentLayout}
          onValueChange={(value) => updateLayoutType(value as LayoutType)}
          disabled={disabled}
        />
      </div>

      {/* Grid Configuration - only for grid layout */}
      {currentLayout === 'grid' && (
        <div className="space-y-2">
          <Label className="text-xs">Lưới</Label>
          
          <div className="grid grid-cols-2 gap-3">
            <div className="space-y-1">
              <Label className="text-xs text-gray-600">Số hàng</Label>
              <Input
                type="number"
                min="1"
                max="12"
                value={gridConfig.rows}
                onChange={(e) => updateGridConfig({ rows: parseInt(e.target.value) || 1 })}
                disabled={disabled}
                className="text-xs"
              />
            </div>
            
            <div className="space-y-1">
              <Label className="text-xs text-gray-600">Số cột</Label>
              <Input
                type="number"
                min="1"
                max="12"
                value={gridConfig.cols}
                onChange={(e) => updateGridConfig({ cols: parseInt(e.target.value) || 1 })}
                disabled={disabled}
                className="text-xs"
              />
            </div>
          </div>

          {/* Quick grid presets */}
          <div className="space-y-1">
            <Label className="text-xs">Presets</Label>
            <div className="grid grid-cols-3 gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => updateGridConfig({ rows: 2, cols: 2 })}
                disabled={disabled}
                className="text-xs h-8"
              >
                2×2
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => updateGridConfig({ rows: 3, cols: 3 })}
                disabled={disabled}
                className="text-xs h-8"
              >
                3×3
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => updateGridConfig({ rows: 2, cols: 3 })}
                disabled={disabled}
                className="text-xs h-8"
              >
                2×3
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Alignment - for flex and grid layouts */}
      {(currentLayout === 'horizontal' || currentLayout === 'vertical' || currentLayout === 'grid') && (
        <div className="grid grid-cols-2 gap-3">
          <div className="space-y-1">
            <Label className="text-xs">Justify</Label>
            <AlignmentPopover
              value={getCurrentJustify()}
              onValueChange={updateJustify}
              disabled={disabled}
              type="justify"
            />
          </div>
          
          <div className="space-y-1">
            <Label className="text-xs">Align</Label>
            <AlignmentPopover
              value={getCurrentAlign()}
              onValueChange={updateAlign}
              disabled={disabled}
              type="align"
            />
          </div>
        </div>
      )}

      {/* Gap Settings - for flex and grid layouts */}
      {(currentLayout === 'horizontal' || currentLayout === 'vertical' || currentLayout === 'grid') && (
        <div className="space-y-1">
          <Label className="text-xs">Khoảng cách</Label>
          <GapPopover
            value={currentGap}
            onValueChange={updateGap}
            disabled={disabled}
          />
        </div>
      )}

      {/* Space-Y Settings - for default layout only */}
      {currentLayout === 'default' && (
        <div className="space-y-1">
          <Label className="text-xs">Khoảng cách dọc</Label>
          <GapPopover
            value={currentSpaceY}
            onValueChange={updateSpaceY}
            disabled={disabled}
          />
        </div>
      )}
    </div>
  );
};