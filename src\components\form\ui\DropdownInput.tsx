import React from "react";
import { BasicInputProps } from "../registry";
import { DropdownInput as BasicDropdownInput } from "@/form/field/components/basic/DropdownInput";

type DropdownInputProps = BasicInputProps<"text">;

export const DropdownInput = React.forwardRef<
  HTMLDivElement,
  DropdownInputProps
>(
  (
    {
      isViewMode,
      value,
      onChange,
      options = [],
      labels,
      placeholder = "Chọn giá trị",
      disabled,
      name,
      id,
      ...rest
    },
    ref
  ) => {
    const handleChange = (
      newValue: string | number | boolean | (string | number | boolean)[]
    ) => {
      onChange(newValue as string);
    };

    return (
      <div ref={ref} {...rest}>
        <BasicDropdownInput
          value={value}
          onChange={handleChange}
          options={options}
          labels={labels || []}
          placeholder={placeholder}
          disabled={disabled || (isViewMode && !disabled)}
          id={id}
        />
      </div>
    );
  }
);

DropdownInput.displayName = "DropdownInput";
