import React, { useState } from "react";
import { useAppSelector } from "@/store/rootReducer";
import { selectCurrentUser } from "../states/selectors";
import { Button } from "@/components/ui/button";
import { AlertTriangle, CheckCircle, LockKeyhole, LockKeyholeOpen } from "lucide-react";
import { Toolt<PERSON>, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";
import UserVerifyForm from "./UserVerifyForm";
import { Dialog } from "@/components/ui/dialog";

export const UserBasicInfo: React.FC = () => {
  const user = useAppSelector(selectCurrentUser);

  const [isVerifyDialogOpen, setIsVerifyDialogOpen] = useState(false);
  const [verifyType, setVerifyType] = useState<string>("");

  const onVerifyToggle = (type: string) => {
    if (!user) return;
    setVerifyType(type);
    setIsVerifyDialogOpen(true);
  }

  if (!user) return null;
  const labelClass = "min-w-[12rem]"
  const detailClass = "min-w-[19rem] flex font-semibold flex-wrap"
  return (
    <div
      className={
        "inline-flex flex-col items-start space-y-2 text-base max-w-full flex-wrap" +
        (user ? "" : "hidden")
      }
    >
      <div className="flex flex-row flex-wrap w-full">
        <div className={labelClass}>Mã người dùng:</div>
        <div className={detailClass}>{user.id}</div>
      </div>

      <div className="flex flex-row flex-wrap w-full">
        <div className={labelClass}>Tài khoản:</div>
        <div className={detailClass}>{user.userName}</div>
      </div>

      <div className="flex flex-row flex-wrap w-full">
        <div className={labelClass}>Họ tên:</div>
        <div className={detailClass}>{user.fullName}</div>
      </div>

      <div className="flex flex-row flex-wrap w-full">
        <div className={labelClass}>Email:</div>
        <div className={detailClass}>
          {/* Verified Email UI */}
          <div className="flex items-center flex-wrap w-full">
            <Tooltip>
              <TooltipTrigger>
                <div className="flex items-center justify-between py-2 h-[24px] cursor-default">
                  <div className="flex items-center space-x-2">
                    {user.emailVerified ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : (
                      <AlertTriangle className="h-5 w-5 text-orange-400" />
                    )}
                    <span className="font-medium text-black">{user.email}</span>
                  </div>
                </div>
              </TooltipTrigger>
              <TooltipContent>
                {user.emailVerified ? "Đã xác thực" : "Chưa xác thực"}
              </TooltipContent>
            </Tooltip>
            <Button
              variant="link"
              className="text-blue-600 underline text-base h-[24px]"
              onClick={() => {
                if (user.emailVerified) {
                  onVerifyToggle("email_unverify")
                } else {
                  onVerifyToggle("email")
                }
              }}
            >
              {user.emailVerified ? "Hủy xác thực" : "Xác thực"}
            </Button>
          </div>
        </div>
      </div>

      <div className="flex flex-row flex-wrap w-full">
        <div className={labelClass}>Số điện thoại:</div>
        <div className={detailClass}>
          {/* Verified Email UI */}
          <div className="flex items-center flex-wrap w-full">
            <Tooltip>
              <TooltipTrigger>
                <div className="flex items-center justify-between py-2 h-[24px] cursor-default">
                  <div className="flex items-center space-x-2">
                    {user.phoneVerified ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : (
                      <AlertTriangle className="h-5 w-5 text-orange-400" />
                    )}
                    <span className="font-medium text-black">{user.phone}</span>
                  </div>
                </div>
              </TooltipTrigger>
              <TooltipContent>
                {user.phoneVerified ? "Đã xác thực" : "Chưa xác thực"}
              </TooltipContent>
            </Tooltip>
            <Button
              variant="link"
              className="text-blue-600 underline text-base h-[24px]"
              onClick={() => {
                if (user.phoneVerified) {
                  onVerifyToggle("phone_unverify")
                } else {
                  onVerifyToggle("phone")
                }
              }}
            >
              {user.phoneVerified ? "Hủy xác thực" : "Xác thực"}
            </Button>
          </div>
        </div>
      </div>

      <div className="flex flex-row flex-wrap w-full">
        <div className={labelClass}>Trạng thái khoá:</div>
        <div className={detailClass}>
          {user.locked ? (
            <span className="text-red-600 flex flex-row gap-2">
              <LockKeyhole />
              {user.lockedUntil && user.lockedUntil > 0 ?
                "(đến: " + new Date(user.lockedUntil * 1000).toLocaleString("vi-VN", {
                  hour: "2-digit",
                  minute: "2-digit",
                  second: "2-digit",
                }) + ":" + new Date(user.lockedUntil * 1000).getMilliseconds().toString().padStart(3, '0')
                + " " +
                new Date(user.lockedUntil * 1000).toLocaleString("vi-VN", {
                  day: "2-digit",
                  month: "2-digit",
                  year: "numeric",
                }) + ")" : "(vĩnh viễn)"}</span>
          ) : (
            <span className="text-green-600 flex flex-row gap-2">
              <LockKeyholeOpen />
            </span>
          )}
          <Button
            variant="link"
            className="text-blue-600 underline h-[24px] text-base"
            onClick={() => {
              if (user.locked) {
                onVerifyToggle("unlock")
              } else {
                onVerifyToggle("lock")
              }
            }}
          >
            {user.locked ? "Mở khóa" : "Khóa"}
          </Button>
        </div>
      </div>

      <div className="flex flex-row flex-wrap w-full">
        <div className={labelClass}>Ngày tạo:</div>
        <div className={detailClass}>
          {new Date(user.createdAt).toLocaleString("vi-VN")}
        </div>
      </div>

      <div className="flex flex-row flex-wrap w-full">
        <div className={labelClass}>Ngày cập nhật:</div>
        <div className={detailClass}>
          {new Date(user.updatedAt).toLocaleString("vi-VN")}
        </div>
      </div>

      {isVerifyDialogOpen && (
        <div className="fixed inset-0 z-50 p-4 flex w-full items-center justify-center bg-black/60 overflow-y-auto scrolling-touch">
          <Dialog>
            <UserVerifyForm
              type={verifyType}
              user={user}
              onCancel={() => setIsVerifyDialogOpen(false)}
            />
          </Dialog>
        </div>
      )}
    </div >
  );
};
