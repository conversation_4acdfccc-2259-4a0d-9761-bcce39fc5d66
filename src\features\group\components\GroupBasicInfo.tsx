import React, { useState, useEffect } from "react";
import { useAppDispatch, useAppSelector } from "@/store/rootReducer";
import {
  selectSelectedGroup,
  selectGroupData,
  selectGroupLoading,
} from "../states/selectors";
import {
  createGroupThunk,
  updateGroupThunk,
  deleteGroupThunk,
  setSelectedId,
} from "../states/slices";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogTrigger,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { GroupAction, Group } from "../states/types";
import { Edit, Save, X, Trash2 } from "lucide-react";
import { GroupParentSelector } from "./GroupParentSelector";
import { useNavigate, useLocation } from "react-router-dom";

interface GroupBasicInfoProps {
  currentAction: GroupAction;
  onActionChange: (action: GroupAction) => void;
}

export const GroupBasicInfo: React.FC<GroupBasicInfoProps> = ({
  currentAction,
  onActionChange,
}) => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const selectedGroup = useAppSelector(selectSelectedGroup);
  const allGroups = useAppSelector(selectGroupData);
  const loading = useAppSelector(selectGroupLoading);

  const [formData, setFormData] = useState({
    parentId: null as number | null,
    code: "",
    name: "",
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [confirmDelete, setConfirmDelete] = useState(false);

  // Initialize form data when group changes or mode changes
  useEffect(() => {
    if (currentAction === "create") {
      setFormData({
        parentId: selectedGroup?.id || null, // Use selected group as parent for new child
        code: "",
        name: "",
      });
    } else if (
      selectedGroup &&
      (currentAction === "edit" || currentAction === "view")
    ) {
      setFormData({
        parentId: selectedGroup.parentId,
        code: selectedGroup.code,
        name: selectedGroup.name,
      });
    }
  }, [selectedGroup, currentAction]);

  const handleInputChange = (field: string, value: string | number | null) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (isSubmitting) return;

    setIsSubmitting(true);
    try {
      if (currentAction === "create") {
        const result = await dispatch(createGroupThunk(formData));
        onActionChange("view");

        // Update URL to focus on the new group
        if (
          result.payload &&
          typeof result.payload === "object" &&
          "id" in result.payload
        ) {
          const newGroupId = (result.payload as Group).id;
          const searchParams = new URLSearchParams(location.search);
          searchParams.set("group", newGroupId.toString());
          searchParams.set("tab", "basic");
          searchParams.set("action", "view");
          navigate(`${location.pathname}?${searchParams.toString()}`);
        }
      } else if (currentAction === "edit" && selectedGroup) {
        await dispatch(
          updateGroupThunk({
            id: selectedGroup.id,
            payload: formData,
          })
        );
        onActionChange("view");
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    if (currentAction === "create") {
      onActionChange("view");
    } else if (currentAction === "edit") {
      onActionChange("view");
      // Reset form data
      if (selectedGroup) {
        setFormData({
          parentId: selectedGroup.parentId,
          code: selectedGroup.code,
          name: selectedGroup.name,
        });
      }
    }
  };

  const handleDelete = async () => {
    if (!selectedGroup) return;

    setIsSubmitting(true);
    try {
      await dispatch(deleteGroupThunk(selectedGroup.id));

      // Clear selection and go back to default view
      dispatch(setSelectedId(0));
      onActionChange("view");

      // Update URL to remove group selection
      const searchParams = new URLSearchParams(location.search);
      searchParams.delete("group");
      searchParams.set("action", "view");
      navigate(`${location.pathname}?${searchParams.toString()}`);
    } catch (error) {
      console.error("Error deleting group:", error);
    } finally {
      setIsSubmitting(false);
      setConfirmDelete(false);
    }
  };

  const isEditing = currentAction === "edit" || currentAction === "create";
  const isCreating = currentAction === "create";

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle>
            {isCreating ? "Tạo nhóm mới" : "Thông tin cơ bản"}
          </CardTitle>
          {!isEditing && selectedGroup && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => onActionChange("edit")}
            >
              <Edit className="h-4 w-4 mr-2" />
              Chỉnh sửa
            </Button>
          )}
        </div>
      </CardHeader>

      <CardContent>
        {!selectedGroup && currentAction !== "create" ? (
          <div className="text-center py-8 text-gray-500">
            Chọn một nhóm để xem thông tin
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Parent Group Selector */}
            <div className="space-y-2">
              <Label>Nhóm cha</Label>
              {isEditing ? (
                <GroupParentSelector
                  allGroups={allGroups}
                  selectedParentId={formData.parentId}
                  onParentChange={(parentId) =>
                    handleInputChange("parentId", parentId)
                  }
                  excludeId={selectedGroup?.id} // Exclude current group when editing
                  placeholder="Chọn nhóm cha (để trống nếu là nhóm gốc)"
                />
              ) : (
                <div className="p-3 bg-gray-50 rounded border">
                  {formData.parentId ? (
                    (() => {
                      const parentGroup = allGroups.find(
                        (g) => g.id === formData.parentId
                      );
                      return parentGroup ? (
                        <div>
                          <div className="font-medium">{parentGroup.name}</div>
                          <div className="text-sm text-gray-500">
                            {parentGroup.code}
                          </div>
                        </div>
                      ) : (
                        <span className="text-gray-500">
                          Nhóm cha không tồn tại
                        </span>
                      );
                    })()
                  ) : (
                    <span className="text-gray-500">Nhóm gốc</span>
                  )}
                </div>
              )}
            </div>

            {/* Code Field */}
            <div className="space-y-2">
              <Label htmlFor="code">Mã nhóm</Label>
              {isEditing ? (
                <Input
                  id="code"
                  value={formData.code}
                  onChange={(e) => handleInputChange("code", e.target.value)}
                  placeholder="Nhập mã nhóm..."
                  required
                />
              ) : (
                <div className="p-3 bg-gray-50 rounded border">
                  {formData.code || "Chưa có mã"}
                </div>
              )}
            </div>

            {/* Name Field */}
            <div className="space-y-2">
              <Label htmlFor="name">Tên nhóm</Label>
              {isEditing ? (
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange("name", e.target.value)}
                  placeholder="Nhập tên nhóm..."
                  required
                />
              ) : (
                <div className="p-3 bg-gray-50 rounded border">
                  {formData.name || "Chưa có tên"}
                </div>
              )}
            </div>

            {/* Action Buttons */}
            {isEditing && (
              <div className="flex justify-end space-x-2 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleCancel}
                  disabled={isSubmitting || loading}
                >
                  <X className="h-4 w-4 mr-2" />
                  Hủy
                </Button>

                {/* Nút xóa chỉ hiện khi edit group có sẵn */}
                {currentAction === "edit" && selectedGroup && (
                  <Dialog open={confirmDelete} onOpenChange={setConfirmDelete}>
                    <DialogTrigger asChild>
                      <Button
                        type="button"
                        variant="destructive"
                        disabled={isSubmitting || loading}
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Xóa
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Xác nhận xóa nhóm</DialogTitle>
                      </DialogHeader>
                      <p className="text-sm text-muted-foreground">
                        Bạn có chắc chắn muốn xóa nhóm "{selectedGroup.name}"?
                        Hành động này không thể hoàn tác.
                      </p>
                      <DialogFooter>
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => setConfirmDelete(false)}
                          disabled={isSubmitting}
                        >
                          Hủy
                        </Button>
                        <Button
                          type="button"
                          variant="destructive"
                          onClick={handleDelete}
                          disabled={isSubmitting}
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          {isSubmitting ? "Đang xóa..." : "Xác nhận xóa"}
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                )}

                <Button type="submit" disabled={isSubmitting || loading}>
                  <Save className="h-4 w-4 mr-2" />
                  {isSubmitting
                    ? "Đang lưu..."
                    : isCreating
                    ? "Tạo nhóm"
                    : "Lưu thay đổi"}
                </Button>
              </div>
            )}
          </form>
        )}
      </CardContent>
    </Card>
  );
};
