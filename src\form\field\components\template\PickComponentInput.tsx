/**
 * PickComponentInput - Component picker for AutoForm
 */

import React from "react";
import { FieldComponentProps } from "../../FieldFactory";
import { getComponentOptions } from "@/features/routes/components/ComponentPicker";
import { DropDownTextWithSearch } from "../basic/DropDownTextWithSearch";

export const PickComponentInput: React.FC<FieldComponentProps> = ({
  value,
  onChange,
  onBlur,
  disabled = false,
  placeholder = "Tìm kiếm component...",
  id,
  className = "",
}) => {
  const options = getComponentOptions();
  const optionValues = options.map((opt) => opt.value);
  const optionLabels = options.map((opt) =>
    opt.description ? `${opt.label} - ${opt.description}` : opt.label
  );

  return (
    <DropDownTextWithSearch
      value={value}
      onChange={onChange}
      onBlur={onBlur}
      disabled={disabled}
      placeholder={placeholder}
      options={optionValues}
      labels={optionLabels}
      id={id}
      className={className}
    />
  );
};
