import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ImageLoader } from "@/components/image/ImageLoader";
import { Image, X, Loader2 } from "lucide-react";
import { toast } from "sonner";
import { setCoverImage, deleteCoverImage } from "../states/api";

interface CoverImageManagerProps {
  albumId: number;
  currentCoverImage?: string;
  onCoverImageUpdated?: () => void;
}

export const CoverImageManager: React.FC<CoverImageManagerProps> = ({
  albumId,
  currentCoverImage,
  onCoverImageUpdated,
}) => {
  const [imageUrl, setImageUrl] = useState("");
  const [isSettingCover, setIsSettingCover] = useState(false);
  const [isDeletingCover, setIsDeletingCover] = useState(false);

  const handleSetCoverImage = async () => {
    if (!imageUrl.trim()) {
      toast.error("Vui lòng nhập URL ảnh");
      return;
    }

    try {
      setIsSettingCover(true);
      await setCoverImage(albumId, imageUrl.trim());
      toast.success("Đã đặt ảnh bìa thành công!");
      setImageUrl("");
      onCoverImageUpdated?.();
    } catch (error) {
      toast.error("Có lỗi xảy ra khi đặt ảnh bìa: " + (error as Error).message);
    } finally {
      setIsSettingCover(false);
    }
  };

  const handleDeleteCoverImage = async () => {
    try {
      setIsDeletingCover(true);
      await deleteCoverImage(albumId);
      toast.success("Đã xóa ảnh bìa thành công!");
      onCoverImageUpdated?.();
    } catch (error) {
      toast.error("Có lỗi xảy ra khi xóa ảnh bìa: " + (error as Error).message);
    } finally {
      setIsDeletingCover(false);
    }
  };

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle className="text-lg font-semibold">
          Quản lý ảnh bìa Album
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Current Cover Image Preview */}
        {currentCoverImage && (
          <div className="space-y-2">
            <Label className="text-sm font-medium">Ảnh bìa hiện tại:</Label>
            <div className="w-full h-48 border rounded-lg overflow-hidden flex items-center justify-center bg-gray-50">
              <ImageLoader
                src={currentCoverImage}
                alt="Current cover image"
                className="w-full h-full object-contain"
              />
            </div>
          </div>
        )}

        {/* Set New Cover Image */}
        <div className="space-y-2">
          <Label htmlFor="image-url" className="text-sm font-medium">
            URL ảnh mới:
          </Label>
          <Input
            id="image-url"
            type="url"
            value={imageUrl}
            onChange={(e) => setImageUrl(e.target.value)}
            placeholder="https://example.com/image.jpg"
            className="w-full"
          />
        </div>

        {/* Action Buttons */}
        <div className="space-y-2">
          <Button
            onClick={handleSetCoverImage}
            disabled={isSettingCover || !imageUrl.trim()}
            className="w-full"
          >
            {isSettingCover ? (
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <Image className="w-4 h-4 mr-2" />
            )}
            {isSettingCover ? "Đang đặt ảnh bìa..." : "Đặt làm ảnh bìa"}
          </Button>

          {currentCoverImage && (
            <Button
              variant="outline"
              onClick={handleDeleteCoverImage}
              disabled={isDeletingCover}
              className="w-full border-red-300 text-red-700 hover:bg-red-50"
            >
              {isDeletingCover ? (
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <X className="w-4 h-4 mr-2" />
              )}
              {isDeletingCover ? "Đang xóa ảnh bìa..." : "Xóa ảnh bìa"}
            </Button>
          )}
        </div>

        {/* Info */}
        <div className="text-xs text-gray-500 space-y-1">
          <p>
            • Sử dụng API: PUT /portal/v1/admin/albums/{albumId}/cover-image
          </p>
          <p>• Đặt ảnh bìa: {`{"coverImage": "url"}`}</p>
          <p>• Xóa ảnh bìa: {`{"coverImage": null}`}</p>
        </div>
      </CardContent>
    </Card>
  );
};
