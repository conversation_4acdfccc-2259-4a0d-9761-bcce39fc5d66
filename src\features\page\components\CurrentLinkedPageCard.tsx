import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Edit, Unlink, Trash2 } from "lucide-react";
import { Post } from "@/features/post/states/types";

interface CurrentLinkedPageCardProps {
  linkedPage: Post;
  onEdit: () => void;
  onUnlink: () => void;
  onDelete: (pageId: number, pageTitle: string) => void;
}

export const CurrentLinkedPageCard: React.FC<CurrentLinkedPageCardProps> = ({
  linkedPage,
  onEdit,
  onUnlink,
  onDelete,
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Trang hiện tại</span>
          <div className="space-x-2">
            <Button variant="outline" size="sm" onClick={onEdit}>
              <Edit size={16} className="mr-1" />
              Chỉnh sửa
            </Button>
            <Button variant="outline" size="sm" onClick={onUnlink}>
              <Unlink size={16} className="mr-1" />
              <PERSON><PERSON><PERSON> li<PERSON><PERSON> kết
            </Button>
            <Button
              className="text-white"
              variant="destructive"
              size="sm"
              onClick={() => onDelete(linkedPage.id, linkedPage.title)}
            >
              <Trash2 size={16} className="mr-1" />
              Xóa
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <p>
            <strong>Tiêu đề:</strong> {linkedPage.title}
          </p>
          <p>
            <strong>Slug:</strong> {linkedPage.slug}
          </p>
          <p>
            <strong>ID:</strong> {linkedPage.id}
          </p>
          <p>
            <strong>Trạng thái:</strong> {linkedPage.status}
          </p>
          <p>
            <strong>Mô tả:</strong>{" "}
            {linkedPage.excerpt?.description || "Không có"}
          </p>
        </div>
      </CardContent>
    </Card>
  );
};
