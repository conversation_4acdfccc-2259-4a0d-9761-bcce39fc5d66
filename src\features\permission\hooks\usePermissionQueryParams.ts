import { useSearchParams } from "react-router-dom";
import { useCallback, useEffect, useRef } from "react";
import { useAppDispatch } from "@/store/rootReducer";
import { setPathParams } from "../states/slices";
import { PermissionQueryParams, PermissionPageMode } from "../states/types";

export const usePermissionQueryParams = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const dispatch = useAppDispatch();
  const isInitialized = useRef(false);

  // Parse query params from URL
  const getQueryParams = useCallback((): PermissionQueryParams => {
    const roleId = searchParams.get("roleId");
    const mode = searchParams.get("mode") as PermissionPageMode;

    return {
      roleId: roleId || undefined,
      mode: mode && ["view", "edit"].includes(mode) ? mode : undefined,
    };
  }, [searchParams]);

  // Update URL when state changes
  const updateUrl = useCallback(
    (params: PermissionQueryParams) => {
      const newSearchParams = new URLSearchParams();

      // Only add params that have actual values
      if (params.roleId && params.roleId !== "undefined") {
        newSearchParams.set("roleId", params.roleId);
      }

      if (params.mode && params.mode !== "view" && params.mode !== undefined) {
        newSearchParams.set("mode", params.mode);
      }

      // Always update URL, even if clearing all params
      const currentUrlParams = searchParams.toString();
      const newUrlParams = newSearchParams.toString();

      if (currentUrlParams !== newUrlParams) {
        setSearchParams(newSearchParams, { replace: true });
      }
    },
    [searchParams, setSearchParams]
  );

  // Sync URL to Redux - this is the main direction of data flow
  useEffect(() => {
    const queryParams = getQueryParams();
    const roleId = queryParams.roleId ? parseInt(queryParams.roleId, 10) : null;
    const mode = queryParams.mode || "view";

    // Always sync URL to Redux when URL changes
    dispatch(setPathParams({ roleId, mode }));

    if (!isInitialized.current) {
      isInitialized.current = true;
    }
  }, [searchParams, dispatch, getQueryParams]);

  return {
    queryParams: getQueryParams(),
    updateUrl,
  };
};
