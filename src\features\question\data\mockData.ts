import { Question, Answer } from "../states/types";

export const mockQuestions: Question[] = [
  {
    id: 1,
    asker: {
      fullName: "Nguyễn Văn A",
      phoneNumber: "0912345678",
      email: "ng<PERSON><PERSON><PERSON>@example.com",
      address: "123 Đường ABC, Phường XYZ, Quận 1, TP.HCM",
    },
    content: {
      title: "Làm thế nào để đăng nhập vào hệ thống?",
      question:
        "Tôi gặp khó khăn khi đăng nhập vào hệ thống. Bạn có thể hướng dẫn chi tiết các bước đăng nhập không?",
    },
    status: "PUBLISHED",
    topic: "<PERSON>ăng nhập",
    createdAt: 1640995200000, // 2022-01-01 00:00:00
    updatedAt: 1640995200000,
  },
  {
    id: 2,
    asker: {
      fullName: "Trần Thị B",
      phoneNumber: "0987654321",
      email: "<EMAIL>",
      address: "456 Đường DEF, <PERSON><PERSON><PERSON><PERSON> UVW, Quận 2, TP.HCM",
    },
    content: {
      title: "Tô<PERSON> có thể thay đổi thông tin cá nhân ở đâu?",
      question:
        "Tôi muốn cập nhật thông tin hồ sơ cá nhân như số điện thoại, địa chỉ. Có thể hướng dẫn tôi không?",
    },
    status: "PUBLISHED",
    topic: "Tài khoản",
    createdAt: 1641081600000, // 2022-01-02 00:00:00
    updatedAt: 1641081600000,
  },
  {
    id: 3,
    asker: {
      fullName: "Lê Văn C",
      phoneNumber: "0123456789",
      email: "<EMAIL>",
      address: "789 Đường GHI, Phường RST, Quận 3, TP.HCM",
    },
    content: {
      title: "Làm sao để upload file lên hệ thống?",
      question:
        "Tôi cần upload các tài liệu PDF và hình ảnh. Có giới hạn về dung lượng file không?",
    },
    status: "PUBLISHED",
    topic: "Upload",
    createdAt: 1641168000000, // 2022-01-03 00:00:00
    updatedAt: 1641168000000,
  },
  {
    id: 4,
    asker: {
      fullName: "Phạm Thị D",
      phoneNumber: "0345678901",
      email: "<EMAIL>",
      address: "101 Đường JKL, Phường MNO, Quận 4, TP.HCM",
    },
    content: {
      title: "Tại sao tôi không thể truy cập một số trang?",
      question:
        "Khi tôi click vào một số menu thì hiện thông báo 'Bạn không có quyền truy cập'. Làm sao để được cấp quyền?",
    },
    status: "NEW",
    topic: "Phân quyền",
    createdAt: 1641254400000, // 2022-01-04 00:00:00
    updatedAt: 1641254400000,
  },
  {
    id: 5,
    asker: {
      fullName: "Hoàng Văn E",
      phoneNumber: "0567890123",
      email: "<EMAIL>",
      address: "202 Đường PQR, Phường STU, Quận 5, TP.HCM",
    },
    content: {
      title: "Hệ thống có hỗ trợ mobile không?",
      question:
        "Tôi muốn biết có thể sử dụng hệ thống trên điện thoại di động không? Có ứng dụng riêng không?",
    },
    status: "PUBLISHED",
    topic: "Hỗ trợ",
    createdAt: 1641340800000, // 2022-01-05 00:00:00
    updatedAt: 1641340800000,
  },
  {
    id: 6,
    asker: {
      fullName: "Vũ Thị F",
      phoneNumber: "0789012345",
      email: "<EMAIL>",
      address: "303 Đường VWX, Phường YZA, Quận 6, TP.HCM",
    },
    content: {
      title: "Câu hỏi về bảo mật tài khoản",
      question:
        "Tôi muốn hỏi về các biện pháp bảo mật tài khoản. Có nên bật xác thực 2 bước không?",
    },
    status: "DRAFT",
    topic: "Bảo mật",
    createdAt: 1641427200000, // 2022-01-06 00:00:00
    updatedAt: 1641427200000,
  },
  {
    id: 7,
    asker: {
      fullName: "Đặng Văn G",
      phoneNumber: "0901234567",
      email: "<EMAIL>",
      address: "404 Đường BCD, Phường EFG, Quận 7, TP.HCM",
    },
    content: {
      title: "Câu hỏi spam không phù hợp",
      question: "Đây là nội dung spam, quảng cáo không phù hợp...",
    },
    status: "TRASH",
    topic: "Spam",
    createdAt: 1641513600000, // 2022-01-07 00:00:00
    updatedAt: 1641513600000,
  },
  {
    id: 8,
    asker: {
      fullName: "Bùi Thị H",
      phoneNumber: "0123890456",
      email: "<EMAIL>",
      address: "505 Đường HIJ, Phường KLM, Quận 8, TP.HCM",
    },
    content: {
      title: "Hỏi về tính năng mới của hệ thống",
      question:
        "Tôi thấy có tính năng mới trong hệ thống. Bạn có thể giải thích chi tiết về cách sử dụng không?",
    },
    status: "NEW",
    topic: "Tính năng",
    createdAt: Date.now() - 86400000, // 1 day ago
    updatedAt: Date.now() - 86400000,
  },
  {
    id: 9,
    asker: {
      fullName: "Cao Văn I",
      phoneNumber: "0456789012",
      email: "<EMAIL>",
      address: "606 Đường NOP, Phường QRS, Quận 9, TP.HCM",
    },
    content: {
      title: "Thắc mắc về quy trình làm việc",
      question:
        "Tôi muốn hiểu rõ hơn về quy trình làm việc trong hệ thống. Có tài liệu hướng dẫn chi tiết không?",
    },
    status: "DRAFT",
    topic: "Quy trình",
    createdAt: Date.now() - 43200000, // 12 hours ago
    updatedAt: Date.now() - 43200000,
  },
  {
    id: 10,
    asker: {
      fullName: "Đinh Thị K",
      phoneNumber: "0678901234",
      email: "<EMAIL>",
      address: "707 Đường TUV, Phường WXY, Quận 10, TP.HCM",
    },
    content: {
      title: "Báo lỗi hệ thống không load được",
      question:
        "Hệ thống của tôi bị lỗi, không thể load trang. Mong được hỗ trợ sớm.",
    },
    status: "NEW",
    topic: "Lỗi hệ thống",
    createdAt: Date.now() - 7200000, // 2 hours ago
    updatedAt: Date.now() - 7200000,
  },
];

export const mockAnswers: Answer[] = [
  {
    id: 1,
    questionId: 1,
    content: {
      answer:
        "Để đăng nhập vào hệ thống, bạn thực hiện các bước sau:\n1. Truy cập trang đăng nhập\n2. Nhập email và mật khẩu\n3. Nhấn nút 'Đăng nhập'\n\nNếu quên mật khẩu, hãy sử dụng chức năng 'Quên mật khẩu' để reset.",
      fileList: [],
    },
  },
  {
    id: 2,
    questionId: 2,
    content: {
      answer:
        "Bạn có thể thay đổi thông tin cá nhân tại:\n1. Đăng nhập vào hệ thống\n2. Nhấp vào avatar ở góc phải màn hình\n3. Chọn 'Chỉnh sửa hồ sơ'\n4. Cập nhật thông tin và nhấn 'Lưu'",
      fileList: [],
    },
  },
  {
    id: 3,
    questionId: 3,
    content: {
      answer:
        "Để upload file:\n1. Vào trang 'Quản lý Media'\n2. Nhấn nút 'Upload'\n3. Chọn file từ máy tính\n\nGiới hạn: 10MB/file. Hỗ trợ định dạng: JPG, PNG, PDF, DOC, DOCX",
      fileList: ["https://example.com/upload-guide.pdf"],
    },
  },
  {
    id: 4,
    questionId: 5,
    content: {
      answer:
        "Hệ thống được thiết kế responsive và hoạt động tốt trên mobile. Bạn có thể:\n1. Truy cập qua trình duyệt trên điện thoại\n2. Sử dụng đầy đủ tính năng như trên desktop\n3. Giao diện tự động tối ưu cho màn hình nhỏ",
      fileList: [],
    },
  },
];

// Mock QA Configuration
export const mockQAConfig = {
  active: true,
  topics: [
    "Banking",
    "Insurance",
    "Investment",
    "Defi",
    "Trading",
    "Support",
    "Technical",
    "Account Management",
    "Card Services",
    "Loan Services",
    "Customer Service",
    "Security",
  ],
};
