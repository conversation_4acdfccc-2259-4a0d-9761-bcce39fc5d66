import { createSelector } from "@reduxjs/toolkit";
import type { RootState } from "@/store/rootReducer";
import type { HotlineState } from "./type";

// ============================================================================
// Base Selectors
// ============================================================================

/**
 * Selects the hotline state from the root state
 */
export const selectHotlineState = (state: RootState): HotlineState =>
  state.hotlineState;

// ============================================================================
// Data Selectors
// ============================================================================

/**
 * Selects hotline configuration data
 */
export const selectHotlineData = createSelector(
  [selectHotlineState],
  (hotlineState) => hotlineState.data || null
);

/**
 * Selects hotline saved data (for preview)
 */
export const selectHotlineSavedData = createSelector(
  [selectHotlineState],
  (hotlineState) => hotlineState.savedData || null
);

/**
 * Selects hotline loading state
 */
export const selectHotlineLoading = createSelector(
  [selectHotlineState],
  (hotlineState) => hotlineState.loading
);

/**
 * Selects hotline saving state
 */
export const selectHotlineSaving = createSelector(
  [selectHotlineState],
  (hotlineState) => hotlineState.saving
);

/**
 * Selects hotline error state
 */
export const selectHotlineError = createSelector(
  [selectHotlineState],
  (hotlineState) => hotlineState.error
);

/**
 * Selects whether hotline data has unsaved changes
 */
export const selectHotlineIsDirty = createSelector(
  [selectHotlineState],
  (hotlineState) => hotlineState.isDirty
);

// ============================================================================
// Utility Selectors
// ============================================================================

/**
 * Selects whether any operation is in progress
 */
export const selectHotlineIsOperating = createSelector(
  [selectHotlineLoading, selectHotlineSaving],
  (loading, saving) => loading || saving
);
