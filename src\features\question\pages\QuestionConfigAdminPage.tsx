import React, { useEffect, useState } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { LoadingPage } from "@/components/loading/LoadingPage";
import { AlertCircle, Plus, X, Save, RefreshCw } from "lucide-react";
import { useQuestion } from "../hooks/useQuestion";
import { QAConfigRequest } from "../states/types";
import { useAppDispatch } from "@/store/rootReducer";
import { fetchQAConfig as fetchQAConfigAction } from "../states/slices";

export const QuestionConfigAdminPage: React.FC = () => {
  const dispatch = useAppDispatch();
  const {
    qaConfig,
    qaConfigLoading,
    qaConfigError,
    updateQAConfig,
    clearQAConfigError,
  } = useQuestion();

  const [formData, setFormData] = useState<QAConfigRequest>({
    active: true,
    topics: [],
  });
  const [newTopic, setNewTopic] = useState("");
  const [isEditing, setIsEditing] = useState(false);
  // Load config on mount - only if not already loaded
  useEffect(() => {
    if (!qaConfig && !qaConfigLoading) {
      dispatch(fetchQAConfigAction());
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Update form when config loads - only when qaConfig changes
  useEffect(() => {
    if (qaConfig) {
      setFormData({
        active: qaConfig.active,
        topics: [...qaConfig.topics],
      });
    }
  }, [qaConfig]);

  const handleSave = () => {
    updateQAConfig(formData);
    setIsEditing(false);
  };

  const handleReset = () => {
    if (qaConfig) {
      setFormData({
        active: qaConfig.active,
        topics: [...qaConfig.topics],
      });
    }
    setIsEditing(false);
  };

  const handleAddTopic = () => {
    if (newTopic.trim() && !formData.topics.includes(newTopic.trim())) {
      setFormData((prev) => ({
        ...prev,
        topics: [...prev.topics, newTopic.trim()],
      }));
      setNewTopic("");
      setIsEditing(true);
    }
  };

  const handleRemoveTopic = (topic: string) => {
    setFormData((prev) => ({
      ...prev,
      topics: prev.topics.filter((t) => t !== topic),
    }));
    setIsEditing(true);
  };

  const handleActiveToggle = (checked: boolean) => {
    setFormData((prev) => ({
      ...prev,
      active: checked,
    }));
    setIsEditing(true);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleAddTopic();
    }
  };

  if (qaConfigLoading && !qaConfig) {
    return <LoadingPage />;
  }

  return (
    <div className="w-full h-full flex flex-col gap-6 p-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold">Cấu Hình Hỏi Đáp</h1>
          <p className="text-gray-600 mt-1">
            Quản lý cài đặt và chủ đề cho hệ thống hỏi đáp
          </p>
        </div>
        <Button
          variant="outline"
          onClick={() => dispatch(fetchQAConfigAction())}
          disabled={qaConfigLoading}
        >
          <RefreshCw
            className={`h-4 w-4 mr-2 ${qaConfigLoading ? "animate-spin" : ""}`}
          />
          Làm mới
        </Button>
      </div>

      {qaConfigError && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Lỗi</AlertTitle>
          <AlertDescription>{String(qaConfigError)}</AlertDescription>
          <Button
            variant="outline"
            size="sm"
            className="mt-2"
            onClick={clearQAConfigError}
          >
            Đóng
          </Button>
        </Alert>
      )}

      {/* Summary Card */}
      <Card>
        <CardHeader>
          <CardTitle>Tóm Tắt Cấu Hình</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">
                {formData.active ? "✅" : "❌"}
              </div>
              <p className="text-sm font-medium">Trạng thái hệ thống</p>
              <p className="text-xs text-gray-600">
                {formData.active ? "Hoạt động" : "Tạm dừng"}
              </p>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {formData.topics.length}
              </div>
              <p className="text-sm font-medium">Chủ đề khả dụng</p>
              <p className="text-xs text-gray-600">Đã cấu hình</p>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">
                {qaConfig ? "🔄" : "➖"}
              </div>
              <p className="text-sm font-medium">Đồng bộ hóa</p>
              <p className="text-xs text-gray-600">
                {qaConfig ? "Đã tải" : "Chưa tải"}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* General Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <span>Cài Đặt Chung</span>
              {isEditing && (
                <Badge variant="secondary" className="text-xs">
                  Đã chỉnh sửa
                </Badge>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-2">
              <Switch
                id="active"
                checked={formData.active}
                onCheckedChange={handleActiveToggle}
              />
              <Label htmlFor="active" className="text-sm font-medium">
                Kích hoạt hệ thống hỏi đáp
              </Label>
            </div>

            <div className="p-3 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-600">
                <strong>Trạng thái hiện tại:</strong>{" "}
                <span
                  className={
                    formData.active ? "text-green-600" : "text-red-600"
                  }
                >
                  {formData.active ? "Đang hoạt động" : "Tạm dừng"}
                </span>
              </p>
              <p className="text-xs text-gray-500 mt-1">
                Khi tắt, người dùng sẽ không thể gửi câu hỏi mới
              </p>
            </div>

            {isEditing && (
              <div className="flex gap-2 pt-2">
                <Button onClick={handleSave} disabled={qaConfigLoading}>
                  <Save className="h-4 w-4 mr-2" />
                  {qaConfigLoading ? "Đang lưu..." : "Lưu thay đổi"}
                </Button>
                <Button variant="outline" onClick={handleReset}>
                  Hủy bỏ
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Topics Management */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <span>Quản Lý Chủ Đề</span>
              {isEditing && (
                <Badge variant="secondary" className="text-xs">
                  Đã chỉnh sửa
                </Badge>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Add new topic */}
            <div className="flex gap-2">
              <Input
                placeholder="Nhập chủ đề mới..."
                value={newTopic}
                onChange={(e) => setNewTopic(e.target.value)}
                onKeyPress={handleKeyPress}
              />
              <Button
                onClick={handleAddTopic}
                disabled={
                  !newTopic.trim() || formData.topics.includes(newTopic.trim())
                }
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>

            {/* Topics list */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">
                Danh sách chủ đề ({formData.topics.length})
              </Label>
              <div className="flex flex-wrap gap-2 min-h-[100px] p-3 border rounded-md bg-gray-50">
                {formData.topics.length === 0 ? (
                  <p className="text-sm text-gray-500 w-full text-center py-4">
                    Chưa có chủ đề nào
                  </p>
                ) : (
                  formData.topics.map((topic, index) => (
                    <Badge
                      key={index}
                      variant="outline"
                      className="flex items-center gap-1 py-1 px-2"
                    >
                      {topic}
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-auto p-0 ml-1 hover:bg-red-100"
                        onClick={() => handleRemoveTopic(topic)}
                      >
                        <X className="h-3 w-3 text-red-500" />
                      </Button>
                    </Badge>
                  ))
                )}
              </div>
            </div>

            <div className="text-xs text-gray-500">
              <p>
                💡 <strong>Gợi ý:</strong> Nhập Enter để thêm chủ đề nhanh
              </p>
              <p>
                📝 Các chủ đề phổ biến: Banking, Insurance, Investment, Support,
                Technical
              </p>
            </div>

            {isEditing && (
              <div className="flex gap-2 pt-2">
                <Button onClick={handleSave} disabled={qaConfigLoading}>
                  <Save className="h-4 w-4 mr-2" />
                  {qaConfigLoading ? "Đang lưu..." : "Lưu thay đổi"}
                </Button>
                <Button variant="outline" onClick={handleReset}>
                  Hủy bỏ
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default QuestionConfigAdminPage;
