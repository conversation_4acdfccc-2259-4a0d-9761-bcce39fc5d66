import { AnyFieldApi } from "@tanstack/react-form";

export const FormError: React.FC<{
  message: React.ReactNode | string | null;
}> = ({ message }) => {
  if (message === null) return null;
  return <em className="text-red-500">{message}</em>;
};

export const FieldError = ({ fieldApi }: { fieldApi: AnyFieldApi }) => {
  return (
    <>
      {fieldApi.state.meta.isTouched && fieldApi.state.meta.errors.length ? (
        <em className="text-red-500">
          {fieldApi.state.meta.errors.map((err) => err.message).join(", ")}
        </em>
      ) : null}
    </>
  );
};
