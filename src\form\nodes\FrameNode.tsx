/**
 * FrameNode - Renders frame nodes with children
 */

import React from "react";
import { FormNode } from "../types";
import { renderNodeByType } from "./renderNodeByType";

interface FrameNodeProps {
  node: FormNode;
}

export const FrameNode: React.FC<FrameNodeProps> = ({ node }) => {
  const { id, styles, children } = node;
  const containerClass = styles?.container || "space-y-4";
  const childNodes: FormNode[] = Array.isArray(children) ? children : [];

  return (
    <div key={id} className={containerClass} data-node-id={id}>
      {childNodes.map((child) => renderNodeByType(child))}
    </div>
  );
};
