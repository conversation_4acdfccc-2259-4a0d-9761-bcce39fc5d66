import React from "react";
import { BasicInputProps } from "../registry";
import { Input } from "@/components/ui/input";

export const ImagePickerInput = React.forwardRef<
  HTMLInputElement,
  BasicInputProps<"text">
>(({ value, onChange, isViewMode, ...rest }, ref) => {
  const handleChange = isViewMode
    ? undefined
    : (e: React.ChangeEvent<HTMLInputElement>) => onChange(e.target.value);

  return (
    <Input
      ref={ref}
      type="text"
      value={value}
      onChange={handleChange}
      readOnly={isViewMode}
      {...rest}
    />
  );
});
