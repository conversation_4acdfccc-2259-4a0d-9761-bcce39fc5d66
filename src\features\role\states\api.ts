import { BaseResponse, restApi } from "@/api/restApi";
import { API_ENDPOINTS } from "@/api/endpoints";
import { CreateRole, RoleData, UpdateRole } from "./type";
import { AxiosResponse } from "axios";

export interface RoleQueryParams {
  page: number;
  size: number;
  keyword: string;
}

export async function fetchRoles(
  params: RoleQueryParams
): Promise<AxiosResponse<BaseResponse<RoleData[]>>> {
  const res = await restApi.get<BaseResponse<RoleData[]>>(
    API_ENDPOINTS.AUTH.ADMIN.ROLES,
    {
      params,
    }
  );
  return res;
}

export async function fetchRole(
  roleId: string
): Promise<AxiosResponse<BaseResponse<RoleData>>> {
  const res = await restApi.get<BaseResponse<RoleData>>(
    `${API_ENDPOINTS.AUTH.ADMIN.ROLES}/${roleId}`
  );
  return res;
}

export async function createRoleApi(
  role: CreateRole
): Promise<AxiosResponse<BaseResponse<RoleData>>> {
  const res = await restApi.post<BaseResponse<RoleData>>(
    API_ENDPOINTS.AUTH.ADMIN.ROLES,
    role
  );
  return res;
}

export async function updateRoleApi(
  roleId: number,
  role: UpdateRole
): Promise<AxiosResponse<BaseResponse<RoleData>>> {
  const res = await restApi.put<BaseResponse<RoleData>>(
    `${API_ENDPOINTS.AUTH.ADMIN.ROLES}/${roleId}`,
    role
  );
  return res;
}

export async function deleteRoleApi(
  roleId: number
): Promise<AxiosResponse<BaseResponse<null>>> {
  const res = await restApi.delete<BaseResponse<null>>(
    `${API_ENDPOINTS.AUTH.ADMIN.ROLES}/${roleId}`
  );
  return res;
}
