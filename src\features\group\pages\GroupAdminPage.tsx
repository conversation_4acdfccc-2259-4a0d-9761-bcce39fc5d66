import { useEffect } from "react";
import { useAppDispatch, useAppSelector } from "@/store/rootReducer";
import {
  selectGroupLoading,
  selectGroupError,
  selectCurrentTab,
  selectCurrentAction,
  selectFirstGroup,
  selectGroupSelectedId,
} from "../states/selectors";
import {
  fetchGroupsThunk,
  setTabAndAction,
  setSelectedId,
} from "../states/slices";
import { GroupTree } from "../components/GroupTree";
import { GroupTabs } from "../components/GroupTabs";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";
import { useLocation, useNavigate } from "react-router-dom";
import { GroupTab, GroupAction } from "../states/types";

const GroupAdminPage = () => {
  const dispatch = useAppDispatch();
  const location = useLocation();
  const navigate = useNavigate();
  const loading = useAppSelector(selectGroupLoading);
  const error = useAppSelector(selectGroupError);
  const currentTab = useAppSelector(selectCurrentTab);
  const currentAction = useAppSelector(selectCurrentAction);
  const firstGroup = useAppSelector(selectFirstGroup);
  const selectedGroupId = useAppSelector(selectGroupSelectedId);

  useEffect(() => {
    dispatch(fetchGroupsThunk());
  }, [dispatch]);

  // URL sync for group, tabs and actions
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const groupParam = searchParams.get("group");
    const tab = searchParams.get("tab") as GroupTab;
    const action = searchParams.get("action") as GroupAction;

    // Handle group selection
    if (!groupParam && firstGroup) {
      // Auto-select first group if no group param and we have groups
      const newSearchParams = new URLSearchParams(location.search);
      newSearchParams.set("group", firstGroup.id.toString());
      newSearchParams.set("tab", tab || "basic");
      newSearchParams.set("action", action || "view");
      navigate(`${location.pathname}?${newSearchParams.toString()}`, {
        replace: true,
      });
      dispatch(setSelectedId(firstGroup.id));
    } else if (groupParam) {
      // Set selected group from URL
      const groupId = parseInt(groupParam);
      if (groupId !== selectedGroupId) {
        // groupId = 0 means "undefined group" state
        dispatch(setSelectedId(groupId));
      }
    }

    // Handle tab and action
    if (!tab) {
      // Default tab and action
      const newSearchParams = new URLSearchParams(location.search);
      newSearchParams.set("tab", "basic");
      newSearchParams.set("action", "view");
      if (groupParam) {
        newSearchParams.set("group", groupParam);
      }
      navigate(`${location.pathname}?${newSearchParams.toString()}`, {
        replace: true,
      });
    } else {
      dispatch(
        setTabAndAction({
          tab: tab || "basic",
          action: action || "view",
        })
      );
    }
  }, [
    location.search,
    dispatch,
    navigate,
    location.pathname,
    firstGroup,
    selectedGroupId,
  ]);

  const updateURL = (tab: GroupTab, action: GroupAction) => {
    const searchParams = new URLSearchParams(location.search);
    const currentGroup =
      searchParams.get("group") || selectedGroupId.toString();
    searchParams.set("group", currentGroup);
    searchParams.set("tab", tab);
    searchParams.set("action", action);
    navigate(`${location.pathname}?${searchParams.toString()}`);
  };

  if (loading) {
    return (
      <div className="p-4 space-y-4">
        <Skeleton className="h-8 w-1/3" />
        <Skeleton className="h-96" />
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertTitle>Lỗi</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="flex flex-col gap-4">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Quản lý nhóm</h1>
      </div>

      {/* Tree + Tabs */}
      <div className="h-full flex space-x-6">
        {/* Left Panel - Group Tree */}
        <div className="min-w-80 flex-shrink-0 border rounded-md p-2">
          <div className="flex justify-between items-center mb-2">
            <CardTitle>Danh sách nhóm</CardTitle>
            <Button
              variant="default"
              onClick={() => {
                updateURL("basic", "create");
              }}
            >
              <Plus className="mr-1 h-4 w-4" /> Thêm
            </Button>
          </div>
          <GroupTree />
        </div>

        {/* Right Panel - Group Details with Tabs */}
        <div className="flex-1 border rounded-md p-2">
          <GroupTabs
            currentTab={currentTab}
            currentAction={currentAction}
            onTabChange={updateURL}
          />
        </div>
      </div>
    </div>
  );
};

export default GroupAdminPage;
