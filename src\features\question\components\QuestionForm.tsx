import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { useQuestion } from "../hooks/useQuestion";
import { CreateQuestionRequest, UpdateQuestionRequest } from "../states/types";

interface QuestionFormData {
  name: string;
  email: string;
  title: string;
  description: string;
  topic: string;
}

export const QuestionForm: React.FC = () => {
  const {
    selectedQuestion,
    createQuestion,
    updateQuestion,
    selectQuestion,
    questionsLoading,
  } = useQuestion();

  const [formData, setFormData] = useState<QuestionFormData>({
    name: "",
    email: "",
    title: "",
    description: "",
    topic: "",
  });

  // Load selected question data into form
  useEffect(() => {
    if (selectedQuestion) {
      setFormData({
        name: selectedQuestion.asker.fullName,
        email: selectedQuestion.asker.email,
        title: selectedQuestion.content.title,
        description: selectedQuestion.content.question,
        topic: selectedQuestion.topic,
      });
    }
  }, [selectedQuestion]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (selectedQuestion) {
      // Update existing question
      const updateData: UpdateQuestionRequest = {
        asker: {
          fullName: formData.name,
          phoneNumber: "", // Form không có field này
          email: formData.email,
          address: "", // Form không có field này
        },
        content: {
          title: formData.title,
          question: formData.description,
        },
        topic: formData.topic,
      };
      updateQuestion(selectedQuestion.id, updateData);
    } else {
      // Create new question
      const createData: CreateQuestionRequest = {
        asker: {
          fullName: formData.name,
          phoneNumber: "", // Form không có field này
          email: formData.email,
          address: "", // Form không có field này
        },
        content: {
          title: formData.title,
          question: formData.description,
        },
        topic: formData.topic,
      };
      createQuestion(createData);
    }

    // Reset form after submit
    handleReset();
  };

  const handleInputChange = (field: keyof QuestionFormData, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleReset = () => {
    setFormData({
      name: "",
      email: "",
      title: "",
      description: "",
      topic: "",
    });
    selectQuestion(null);
  };

  const isEditing = !!selectedQuestion;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex justify-between items-center">
          <span>{isEditing ? "Sửa Câu Hỏi" : "Thêm Câu Hỏi Mới"}</span>
          {isEditing && (
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={handleReset}
            >
              Hủy chỉnh sửa
            </Button>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="name">Tên người hỏi *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleInputChange("name", e.target.value)}
                placeholder="Nhập tên người hỏi..."
                required
              />
            </div>
            <div>
              <Label htmlFor="email">Email *</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange("email", e.target.value)}
                placeholder="Nhập email..."
                required
              />
            </div>
          </div>

          <div>
            <Label htmlFor="title">Tiêu đề câu hỏi *</Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) => handleInputChange("title", e.target.value)}
              placeholder="Nhập tiêu đề câu hỏi..."
              required
            />
          </div>

          <div>
            <Label htmlFor="description">Mô tả chi tiết *</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange("description", e.target.value)}
              placeholder="Nhập mô tả chi tiết câu hỏi..."
              rows={4}
              required
            />
          </div>

          <div>
            <Label htmlFor="topic">Chủ đề *</Label>
            <Input
              id="topic"
              value={formData.topic}
              onChange={(e) => handleInputChange("topic", e.target.value)}
              placeholder="Nhập chủ đề (VD: Đăng nhập, Tài khoản, Upload...)..."
              required
            />
          </div>

          <div className="flex gap-2">
            <Button
              type="submit"
              className="flex-1"
              disabled={questionsLoading}
            >
              {questionsLoading
                ? "Đang xử lý..."
                : isEditing
                ? "Cập nhật"
                : "Tạo mới"}
            </Button>
            <Button type="button" variant="outline" onClick={handleReset}>
              Đặt lại
            </Button>
          </div>
        </form>

        {isEditing && selectedQuestion && (
          <div className="mt-4 p-3 bg-blue-50 rounded-lg">
            <div className="text-sm text-blue-800">
              <div>
                <strong>Đang sửa:</strong> {selectedQuestion.content.title}
              </div>
              <div>
                <strong>ID:</strong> {selectedQuestion.id}
              </div>
              <div>
                <strong>Trạng thái:</strong> {selectedQuestion.status}
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
