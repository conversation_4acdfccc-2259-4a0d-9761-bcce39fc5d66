import { useDynamicRouteBuilder } from "./DynamicRouteBuilder";

/**
 * 🌐 Public Routes Builder
 *
 * ✅ Features:
 * - Dynamic routes from public-menu categories
 * - Nested path structure for hierarchical content
 * - NO auth routes (moved to separate AuthLayout)
 */
export const usePublicRoutes = () => {
  const {
    routes: dynamicRoutes,
    isReady,
    categoryCount,
  } = useDynamicRouteBuilder({
    type: "public-menu",
    includeStaticRoutes: true,
    buildNestedPaths: true, // Public routes can be nested
  });

  // Public routes only (auth routes moved to AuthLayout)
  const publicRoutes = dynamicRoutes;

  return {
    routes: publicRoutes,
    isReady,
    categoryCount,
  };
};
