import { FormConfig } from "@/components/form/registry";

export const CategoryFormConfig: FormConfig = {
  code: "category-form",
  name: "Thông tin chuyên mục",
  note: "Dùng để nhập thông tin người dùng",
  config: {
    id: "root",
    type: "group",
    label: "",
    style: {
      frame: "p-4",
      label: "",
      content: "",
      error: "",
    },
    children: [
      {
        id: "",
        type: "field",
        fieldConfig: {
          id: "id",
          label: "Mã",
          data_type: "number",
          input_type: "NumberInput",
          placeholder: "Tự động",
          disabled: true,
        },
        children: [],
      },
      {
        id: "type-field",
        type: "field",
        fieldConfig: {
          id: "type",
          label: "Phân loại",
          data_type: "text",
          input_type: "DropDownTextWithSearch",
          disabled: true,
          options: ["public-menu", "admin-menu", "user-menu"],
          labels: ["<PERSON><PERSON>ng khai", "Quản trị viên", "Người dùng"],
          placeholder: "Chọn loại",
          default_value: "",
        },
        children: [],
      },
      {
        id: "name-field",
        type: "field",
        fieldConfig: {
          id: "name",
          label: "Tên",
          data_type: "text",
          input_type: "TextInput",
          placeholder: "Nhập tên chuyên mục",
          default_value: "",
          validation: {
            required: true,
            maxLength: 200,
          },
        },
        children: [],
      },
      {
        id: "name-field",
        type: "field",
        fieldConfig: {
          id: "slug",
          label: "Slug",
          data_type: "text",
          input_type: "TextInput",
          placeholder: "Nhập slug",
          default_value: "",
          validation: {
            required: true,
            maxLength: 200,
          },
        },
        children: [],
      },
      {
        id: "",
        type: "field",
        fieldConfig: {
          id: "status",
          label: "Hoạt động",
          data_type: "text",
          input_type: "ToggleInput",
          options: ["ACTIVE", "DISABLED"],
          default_value: "DISABLED",
        },
        children: [],
      },
      {
        id: "",
        type: "field",
        fieldConfig: {
          id: "priority",
          label: "Ưu tiên",
          data_type: "number",
          input_type: "NumberInput",
          default_value: 0,
          validation: {
            min: 0,
            max: 1000,
          },
        },
        children: [],
      },
      {
        id: "name-field",
        type: "field",
        fieldConfig: {
          id: "parentId",
          label: "Mục cha",
          data_type: "number",
          input_type: "PickCategoryInput",
          placeholder: "Chọn chuyên mục cha",
        },
        children: [],
      },
      {
        id: "name-field",
        type: "field",
        fieldConfig: {
          id: "description-icon",
          label: "Icon",
          data_type: "text",
          input_type: "PickIconInput",
        },
        children: [],
      },
      {
        id: "name-field",
        type: "field",
        fieldConfig: {
          id: "description-component",
          label: "Component",
          data_type: "text",
          input_type: "PickComponentInput",
        },
        children: [],
      },
    ],
  },
};
