/**
 * Properties Panel Component
 * Right panel for editing selected node properties
 */

import React, { useState } from 'react';
import { useFormBuilder } from '../context/FormBuilderContext';
import { PropertiesFormFactory, PropertyTab } from '../properties';
import { FormDataViewer } from './FormDataViewer';

/**
 * Properties Panel - Right sidebar for node properties
 */
interface FormSubmission {
  id: string;
  timestamp: number;
  data: Record<string, any>;
  isValid: boolean;
}

export const PropertiesPanel: React.FC = () => {
  const { state, updateNode, setPropertiesTab } = useFormBuilder();
  const [submissions, setSubmissions] = useState<FormSubmission[]>([]);

  // Get selected node (always fallback to root if none selected)
  const selectedNode = React.useMemo(() => {
    const nodeId = state.editor.selectedNodeId || 'form-root';
    
    // Find node in tree
    const findNodeInTree = (node: any, targetId: string): any => {
      if (node.id === targetId) return node;
      if (node.children) {
        for (const child of node.children) {
          const found = findNodeInTree(child, targetId);
          if (found) return found;
        }
      }
      return null;
    };

    const foundNode = findNodeInTree(state.formConfig.uiConfig, nodeId);
    
    // Always return root if no node found or nodeId is null
    return foundNode || state.formConfig.uiConfig;
  }, [state.formConfig.uiConfig, state.editor.selectedNodeId]);

  // Handle node updates - only in design mode
  const handleNodeUpdate = (updates: Partial<typeof selectedNode>) => {
    if (!selectedNode || state.editor.mode !== 'design') return;
    updateNode(selectedNode.id, updates);
  };

  // Get current active tab from state
  const activeTab: PropertyTab = state.editor.propertiesPanel.activeTab as PropertyTab;
  
  // Add submission handler for preview mode
  const handleFormSubmission = (data: Record<string, any>, isValid: boolean = true) => {
    const newSubmission: FormSubmission = {
      id: Date.now().toString(),
      timestamp: Date.now(),
      data,
      isValid
    };
    setSubmissions(prev => [newSubmission, ...prev]);
  };
  
  const clearSubmissions = () => {
    setSubmissions([]);
  };
  
  // Expose submission handler to parent via context if needed
  React.useEffect(() => {
    if (state.editor.mode === 'preview') {
      // Store handler in a way that FormCanvas can access it
      (window as any).formBuilderSubmissionHandler = handleFormSubmission;
    }
    
    return () => {
      delete (window as any).formBuilderSubmissionHandler;
    };
  }, [state.editor.mode]);

  return (
    <div className="w-80 bg-white border-l border-gray-200 flex flex-col h-full">
      {/* Properties Form */}
      <div className="flex-1 overflow-hidden p-4">
        {state.editor.mode === 'design' ? (
          <PropertiesFormFactory
            node={selectedNode}
            onChange={handleNodeUpdate}
            disabled={state.isLoading}
            activeTab={activeTab}
            onTabChange={setPropertiesTab}
          />
        ) : state.editor.mode === 'preview' ? (
          <FormDataViewer 
            submissions={submissions}
            onClear={clearSubmissions}
          />
        ) : (
          <div className="flex items-center justify-center h-full text-center">
            <div>
              <div className="text-2xl mb-2">👁️</div>
              <p className="text-sm text-gray-600">Code Mode</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};