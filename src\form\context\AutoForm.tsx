/**
 * AutoForm - Main form component using new form system
 */

import React, { useImperative<PERSON><PERSON>le, useMemo } from "react";
import { useForm } from "@tanstack/react-form";
import { FormNode, FormData } from "../types";
import FormRenderer from "../renderer/FormRenderer";
import { buildFormZodSchema, extractDefaultValues } from "../utils/zodHelper";
import { unflattenObjectKeys } from "../utils/objectKeyHelper";
import { FormContextProvider, FormContext } from "./FormContext";

/**
 * AutoForm component props with generic support like AutoTable
 */
export interface AutoFormProps<T extends object = FormData> {
  node: FormNode;
  viewOnly: boolean;
  onSubmit?: (data: T) => void;
  className?: string;
  initialData?: T;
  validationMode?: "onChange" | "onSubmit";
}

/**
 * AutoForm ref methods with generic support like AutoTable
 */
export interface AutoFormRef<T extends object = FormData> {
  submitForm: () => void;
  getFormData: () => T;
  resetForm: () => void;
}

/**
 * AutoForm Component with generic support like AutoTable
 */
export const AutoForm = <T extends object = FormData>(
  props: AutoFormProps<T> & { ref?: React.Ref<AutoFormRef<T>> }
) => {
  const {
    node,
    viewOnly,
    onSubmit,
    className = "",
    initialData = {} as T,
    validationMode = "onSubmit",
    ref,
  } = props;
    // Extract default values from form node structure
    const flatNodeDefaults = extractDefaultValues(node);
    
    // Convert flat defaults to nested structure using dot notation
    const nodeDefaults = useMemo(() => {
      return unflattenObjectKeys(flatNodeDefaults) as T;
    }, [flatNodeDefaults]);

    // Use initialData directly without flattening
    const defaultValues = useMemo(() => {
      return { ...nodeDefaults, ...initialData };
    }, [nodeDefaults, initialData]);

    // Build Zod schema for validation
    const validationSchema = buildFormZodSchema(node);

    // Chọn validators theo validationMode
    const validators =
      validationMode === "onChange"
        ? { onChange: validationSchema }
        : { onSubmit: validationSchema };

    // Initialize TanStack Form
    const form = useForm({
      defaultValues,
      validators,
      onSubmit: async ({ value }) => {
        if (onSubmit && !viewOnly) {
          // Pass data directly without unflattening since it's already nested
          onSubmit(value as T);
        }
      },
    });

    // Expose methods via ref
    useImperativeHandle(
      ref,
      () => ({
        submitForm: () => {
          form.handleSubmit();
        },
        getFormData: () => {
          const currentValues = form.getFieldValue("") || {};
          return currentValues as T;
        },
        resetForm: () => {
          form.reset();
        },
      }) as AutoFormRef<T>,
      [form]
    );

    const contextValue: FormContext = {
      viewOnly: viewOnly,
      form,
    };

    return (
      <FormContextProvider value={contextValue}>
        <form
          onSubmit={(e) => {
            e.preventDefault();
            form.handleSubmit();
          }}
          className={`auto-form ${className}`.trim()}
        >
          <FormRenderer node={node} />
        </form>
      </FormContextProvider>
    );
};

AutoForm.displayName = "AutoForm";

export default AutoForm;
