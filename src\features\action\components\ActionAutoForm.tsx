import React, { forwardRef, useImperativeHandle } from "react";
import { AutoForm, AutoFormRef } from "@/form/context";
import { CreateActionFormConfig } from "../states/formConfig";
import { CreateActionRequest } from "../states/types";

export interface ActionAutoFormProps {
  initialData?: CreateActionRequest;
  onSubmit: (data: CreateActionRequest) => void;
  viewOnly?: boolean;
}

export interface ActionAutoFormRef {
  submitForm: () => void;
}

export const ActionAutoForm = forwardRef<
  ActionAutoFormRef,
  ActionAutoFormProps
>(({ initialData = {} as CreateActionRequest, onSubmit, viewOnly = false }, ref) => {
  const defaultData: CreateActionRequest = {
    metadata: null,
    ...initialData,
    code: initialData.code || "",
    name: initialData.name || "",
  };

  const handleSubmit = (data: CreateActionRequest) => {
    onSubmit(data);
  };

  const formRef = React.useRef<AutoFormRef<CreateActionRequest>>(null);

  useImperativeHandle(ref, () => ({
    submitForm: () => {
      formRef.current?.submitForm();
    },
  }));

  const node = CreateActionFormConfig.config;

  return (
    <AutoForm<CreateActionRequest>
      ref={formRef}
      node={node}
      viewOnly={viewOnly}
      initialData={defaultData}
      onSubmit={handleSubmit}
      validationMode="onSubmit"
    />
  );
});
