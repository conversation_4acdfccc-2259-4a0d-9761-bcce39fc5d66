import React from "react";
import { CategoryTree } from "@/features/category/states/types";
import { MenuFolder } from "./MenuFolder";
import { MenuItem } from "./MenuItem";

interface MenuTreeProps {
  items: CategoryTree[];
  selectedId: number | null;
  onClick: (id: number) => void;
  className?: string;
}

export const MenuTree: React.FC<MenuTreeProps> = ({
  items,
  selectedId,
  onClick,
  className = "",
}) => {
  // Lấy các node gốc (parentId === null)
  const roots = items.filter((n) => n.parentId === null);

  return (
    <div className={`w-full menu-tree p-1 rounded-md ${className}`}>
      {roots.map((root) =>
        // Nếu có children thì render Folder, không thì Item
        root.children && root.children.length > 0 ? (
          <MenuFolder
            key={root.id}
            item={root}
            allItems={items}
            selectedId={selectedId}
            onClick={onClick}
          />
        ) : (
          <MenuItem
            key={root.id}
            item={root}
            onClick={onClick}
            isActive={root.id === selectedId}
          />
        )
      )}
    </div>
  );
};
