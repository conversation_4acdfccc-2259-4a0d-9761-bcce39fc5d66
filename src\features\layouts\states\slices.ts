import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { initialLayoutState } from "./types";

const layoutSlice = createSlice({
  name: "layout",
  initialState: initialLayoutState,
  reducers: {
    // Sidebar actions
    toggleSidebar: (state) => {
      if (!state.sidebarLocked) {
        state.sidebarVisible = !state.sidebarVisible;
      }
    },
    setSidebarVisible: (state, action: PayloadAction<boolean>) => {
      if (!state.sidebarLocked) {
        state.sidebarVisible = action.payload;
      }
    },
    setSidebarLocked: (state, action: PayloadAction<boolean>) => {
      state.sidebarLocked = action.payload;
      if (action.payload) {
        state.sidebarVisible = true; // Always visible when locked
      }
    },

    // Mobile menu actions
    toggleMobileMenu: (state) => {
      state.mobileMenuOpen = !state.mobileMenuOpen;
    },
    setMobileMenuOpen: (state, action: PayloadAction<boolean>) => {
      state.mobileMenuOpen = action.payload;
    },

    // User menu actions
    toggleUserMenu: (state) => {
      state.userMenuOpen = !state.userMenuOpen;
    },
    setUserMenuOpen: (state, action: PayloadAction<boolean>) => {
      state.userMenuOpen = action.payload;
    },

    // Screen size actions
    setScreenSizes: (
      state,
      action: PayloadAction<{
        isMobile: boolean;
        isTablet: boolean;
        isDesktop: boolean;
        isVeryWideScreen: boolean;
      }>
    ) => {
      const { isMobile, isTablet, isDesktop, isVeryWideScreen } =
        action.payload;

      state.isMobile = isMobile;
      state.isTablet = isTablet;
      state.isDesktop = isDesktop;
      state.isVeryWideScreen = isVeryWideScreen;

      // Control public menu visibility based on screen size
      // Show public menu only on desktop screens (1024px+)
      // iPad mini (768px) is isTablet, so it won't show public menu
      state.showPublicMenu = isDesktop;

      // Auto-lock sidebar on very wide screens
      state.sidebarLocked = isVeryWideScreen;

      // Auto-hide sidebar on mobile only if it's not locked
      if (isMobile && state.sidebarVisible && !state.sidebarLocked) {
        state.sidebarVisible = false;
      }

      // Auto-show sidebar on desktop only if it's not locked and was previously hidden due to mobile
      if (
        isDesktop &&
        !isMobile &&
        !state.sidebarVisible &&
        !state.sidebarLocked &&
        state.isMobile // Only restore if we were previously on mobile
      ) {
        state.sidebarVisible = true;
      }
    },

    // Layout type actions
    setHasSidebar: (state, action: PayloadAction<boolean>) => {
      state.hasSidebar = action.payload;
    },

    // Reset layout state
    resetLayout: () => initialLayoutState,
  },
});

export const {
  toggleSidebar,
  setSidebarVisible,
  setSidebarLocked,
  toggleMobileMenu,
  setMobileMenuOpen,
  toggleUserMenu,
  setUserMenuOpen,
  setScreenSizes,
  setHasSidebar,
  resetLayout,
} = layoutSlice.actions;

export default layoutSlice.reducer;
