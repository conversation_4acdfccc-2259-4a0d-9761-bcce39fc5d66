import React from "react";
import { CategoryTreeBase } from "@/features/category/components/CategoryTree";
import { CategoryDTO } from "@/features/category/states/types";

interface CategoryPanelProps {
  categories: CategoryDTO[];
  selectedCategoryId: number | null;
  onCategorySelect: (categoryId: number) => void;
}

export const CategoryPanel: React.FC<CategoryPanelProps> = ({
  categories,
  selectedCategoryId,
  onCategorySelect,
}) => {
  return (
    <div className="w-1/3 border-r bg-gray-50 p-4">
      <CategoryTreeBase
        allCategories={categories}
        selectedId={selectedCategoryId}
        onSelected={onCategorySelect}
      />

      {/* Legend */}
      <div className="mt-4 p-3 bg-white rounded border">
        <h3 className="text-sm font-medium mb-2">Chú thích:</h3>
        <div className="space-y-1 text-xs">
          <div className="flex items-center">
            <div className="w-4 h-4 bg-primary rounded mr-2"></div>
            <span className="text-gray-600"><PERSON><PERSON><PERSON><PERSON> mục đang chọn</span>
          </div>
        </div>
      </div>
    </div>
  );
};
