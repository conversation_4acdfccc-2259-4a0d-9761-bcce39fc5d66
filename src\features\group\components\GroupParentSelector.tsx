import React, { useState, useMemo, useEffect } from "react";
import { Group, GroupTree } from "../states/types";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Search, Check, ChevronDown, ChevronRight } from "lucide-react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";

interface GroupParentSelectorProps {
  allGroups: Group[];
  selectedParentId: number | null;
  onParentChange: (parentId: number | null) => void;
  excludeId?: number; // Exclude current group and its descendants
  placeholder?: string;
}

export const GroupParentSelector: React.FC<GroupParentSelectorProps> = ({
  allGroups,
  selectedParentId,
  onParentChange,
  excludeId,
  placeholder = "Chọn nhóm cha...",
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchKeyword, setSearchKeyword] = useState("");
  const [expandedIds, setExpandedIds] = useState<Set<number>>(new Set());

  // Build tree structure
  const groupTree = useMemo(() => {
    const groupMap = new Map<number, GroupTree>();

    // Function to get all descendants of a group
    const getAllDescendants = (groupId: number): Set<number> => {
      const descendants = new Set<number>();
      const stack = [groupId];

      while (stack.length > 0) {
        const currentId = stack.pop()!;
        descendants.add(currentId);

        // Find children of current group
        allGroups.forEach((group) => {
          if (group.parentId === currentId && !descendants.has(group.id)) {
            stack.push(group.id);
          }
        });
      }

      return descendants;
    };

    // Get all descendants to exclude
    const excludeIds = excludeId
      ? getAllDescendants(excludeId)
      : new Set<number>();

    // Convert groups to tree nodes, excluding the current group and its descendants
    const validGroups = allGroups.filter((group) => {
      const isValid = !excludeIds.has(group.id);
      return isValid;
    });

    validGroups.forEach((group) => {
      groupMap.set(group.id, { ...group, children: [] });
    });

    const rootGroups: GroupTree[] = [];

    // Build parent-child relationships
    validGroups.forEach((group) => {
      const groupNode = groupMap.get(group.id)!;

      if (group.parentId === null) {
        rootGroups.push(groupNode);
      } else {
        const parent = groupMap.get(group.parentId);
        if (parent) {
          parent.children.push(groupNode);
        } else {
          // If parent not found, treat as root
          rootGroups.push(groupNode);
        }
      }
    });

    return rootGroups;
  }, [allGroups, excludeId]);

  // Filter groups based on search keyword
  const filteredTree = useMemo(() => {
    if (!searchKeyword.trim()) return groupTree;

    const isMatchOrHasMatchingChild = (node: GroupTree): boolean => {
      const isMatch =
        node.name.toLowerCase().includes(searchKeyword.toLowerCase()) ||
        node.code.toLowerCase().includes(searchKeyword.toLowerCase());

      const hasMatchingChild = node.children.some(isMatchOrHasMatchingChild);

      return isMatch || hasMatchingChild;
    };

    const filterTree = (nodes: GroupTree[]): GroupTree[] => {
      return nodes.filter(isMatchOrHasMatchingChild).map((node) => ({
        ...node,
        children: filterTree(node.children),
      }));
    };

    return filterTree(groupTree);
  }, [groupTree, searchKeyword]);

  // Auto expand when searching
  useEffect(() => {
    if (searchKeyword.trim()) {
      const getAllIds = (nodes: GroupTree[]): number[] => {
        const ids: number[] = [];
        nodes.forEach((node) => {
          ids.push(node.id);
          ids.push(...getAllIds(node.children));
        });
        return ids;
      };
      setExpandedIds(new Set(getAllIds(filteredTree)));
    }
  }, [searchKeyword, filteredTree]);

  const toggleExpanded = (id: number) => {
    const newExpanded = new Set(expandedIds);
    if (newExpanded.has(id)) {
      newExpanded.delete(id);
    } else {
      newExpanded.add(id);
    }
    setExpandedIds(newExpanded);
  };

  const selectedGroup = allGroups.find((g) => g.id === selectedParentId);

  const handleSelect = (group: GroupTree | null) => {
    onParentChange(group?.id || null);
    setIsOpen(false);
    setSearchKeyword("");
  };

  const renderTreeNode = (node: GroupTree, level = 0): React.ReactNode => {
    const isExpanded = expandedIds.has(node.id);
    const hasChildren = node.children.length > 0;
    const isSelected = selectedParentId === node.id;

    return (
      <div key={node.id}>
        <div
          className={cn(
            "flex items-center p-2 cursor-pointer rounded hover:bg-gray-100 border border-transparent hover:border-gray-200",
            isSelected && "bg-blue-50 border border-blue-200"
          )}
          style={{ paddingLeft: level * 16 + 8 }}
          onClick={() => handleSelect(node)}
        >
          {hasChildren ? (
            <button
              type="button"
              className="w-6 h-6 p-0 mr-1 flex items-center justify-center hover:bg-gray-200 rounded"
              onClick={(e) => {
                e.stopPropagation();
                toggleExpanded(node.id);
              }}
            >
              {isExpanded ? (
                <ChevronDown className="w-4 h-4" />
              ) : (
                <ChevronRight className="w-4 h-4" />
              )}
            </button>
          ) : (
            <div className="w-6 h-6 mr-1" />
          )}

          <div className="flex-1 min-w-0">
            <div className="font-medium truncate text-gray-900">
              {node.name}
            </div>
            <div className="text-sm text-gray-600 truncate">{node.code}</div>
          </div>

          {isSelected && <Check className="w-4 h-4 text-blue-600 ml-2" />}
        </div>

        {hasChildren && isExpanded && (
          <div>
            {node.children.map((child) => renderTreeNode(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className="w-full justify-between text-left font-normal"
        >
          <span className="truncate">
            {selectedGroup ? selectedGroup.name : placeholder}
          </span>
          <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>

      <PopoverContent className="w-80 p-0 bg-white border border-gray-200 shadow-lg">
        {/* Search */}
        <div className="p-3 border-b border-gray-100 bg-white">
          <div className="flex items-center space-x-2">
            <Search className="h-4 w-4 text-gray-400" />
            <Input
              placeholder="Tìm kiếm nhóm..."
              value={searchKeyword}
              onChange={(e) => setSearchKeyword(e.target.value)}
              className="border-0 shadow-none bg-white text-gray-900 placeholder-gray-500"
            />
          </div>
        </div>

        {/* None option */}
        <div className="p-1 max-h-64 overflow-y-auto bg-white">
          <div
            className={cn(
              "flex items-center p-2 cursor-pointer rounded hover:bg-gray-100 border border-transparent hover:border-gray-200",
              selectedParentId === null && "bg-blue-50 border border-blue-200"
            )}
            onClick={() => handleSelect(null)}
          >
            <div className="w-6 h-6 mr-1" />
            <div className="flex-1">
              <div className="font-medium text-gray-700">Không có nhóm cha</div>
            </div>
            {selectedParentId === null && (
              <Check className="w-4 h-4 text-blue-600 ml-2" />
            )}
          </div>

          {/* Groups tree */}
          {filteredTree.length === 0 ? (
            <div className="p-4 text-center text-gray-600">
              Không tìm thấy nhóm nào
            </div>
          ) : (
            filteredTree.map((node) => renderTreeNode(node))
          )}
        </div>
      </PopoverContent>
    </Popover>
  );
};
