import { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Plus, ArrowLeft } from "lucide-react";
import { useAppSelector, useAppDispatch } from "@/store/rootReducer";
import { selectCurrentRole, selectPageMode } from "../states/selectors";
import {
  setCurrentRole,
  setPathParams,
  fetchRoleByIdThunk,
  RolePageMode,
  RoleAction,
} from "../states/slices";
import { useSwitchMode } from "../states/hook";
import { RoleTable } from "../components/RoleTable";
import { RoleForm } from "../components/RoleForm";
import { useLocation } from "react-router-dom";

export default function RoleAdminPage() {
  const dispatch = useAppDispatch();
  const location = useLocation();
  const switchMode = useSwitchMode();

  const currentRole = useAppSelector(selectCurrentRole);
  const pageMode = useAppSelector(selectPageMode);

  const [currentAction, setCurrentAction] = useState<RoleAction>("view");

  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const mode = searchParams.get("mode") as RolePageMode;
    const actionType = searchParams.get("actionType") as RoleAction;
    const roleId = searchParams.get("role");

    if (!mode) {
      dispatch(setPathParams({ mode: "list" }));
    } else {
      dispatch(
        setPathParams({
          mode,
          actionType: actionType || "view",
        })
      );
      setCurrentAction(actionType || "view");

      // If we have a role ID and we're in detail mode, load the role
      if (mode === "detail" && roleId && roleId !== "0") {
        const id = parseInt(roleId);
        if (!isNaN(id) && (!currentRole || currentRole.id !== id)) {
          dispatch(fetchRoleByIdThunk(id));
        }
      }
    }
  }, [location.search, dispatch, currentRole]);

  const handleCreateNew = () => {
    dispatch(
      setCurrentRole({
        id: 0,
        code: "",
        name: "",
        metadata: {},
        createdAt: 0,
        updatedAt: 0,
      })
    );
    setCurrentAction("create");
    switchMode("detail", "0", "create");
  };

  const handleBackToList = () => {
    dispatch(setCurrentRole(null));
    setCurrentAction("view");
    switchMode("list");
  };

  const handleActionChange = (action: RoleAction) => {
    setCurrentAction(action);
    if (currentRole) {
      switchMode("detail", String(currentRole.id), action);
    }
  };

  if (pageMode === "detail") {
    return (
      <div className="space-y-4">
        {/* Header với nút quay lại */}
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold">
            {currentRole?.id === 0 ? "Tạo vai trò mới" : "Chi tiết vai trò"}
          </h1>
          <Button className="w-30" onClick={handleBackToList}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Quay lại
          </Button>
        </div>

        {/* Form */}
        <RoleForm
          currentAction={currentAction}
          onActionChange={handleActionChange}
        />
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Quản lý vai trò</h1>
        <Button className="w-30" onClick={handleCreateNew}>
          <Plus className="w-4 h-4 mr-2" />
          Tạo mới
        </Button>
      </div>

      {/* Table */}
      <RoleTable />
    </div>
  );
}
