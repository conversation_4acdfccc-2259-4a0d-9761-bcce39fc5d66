import { useEffect } from "react";
import { useNavigate } from "react-router-dom";

const AdminPage = () => {
  const navigate = useNavigate();

  useEffect(() => {
    // Redirect to admin overview page (route configured dynamically)
    navigate("/admin/tong-quan", { replace: true });
  }, [navigate]);

  // Show loading while redirecting
  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
        <p className="mt-4 text-gray-600">
          <PERSON><PERSON> chuyển hướng đến trang tổng quan...
        </p>
      </div>
    </div>
  );
};

export default AdminPage;
