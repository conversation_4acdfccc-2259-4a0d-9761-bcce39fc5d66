/**
 * Form Builder Commands - Optimized Command Pattern for History
 * Lightweight operations that store only deltas, not full state
 */

import { produce } from 'immer';
import { FormNode } from '@/form/types';
import { Command, FormBuilderState, HistoryEntry } from './types';
import { 
  addNodeToTree, 
  updateNodeInTree, 
  deleteNodeFromTree, 
  moveNodeInTree,
  findNode,
  createTreeContext,
  getParentNode
} from './tree';

/**
 * Generate unique command ID
 */
function generateCommandId(): string {
  return `cmd_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// ============================================================================
// NODE COMMANDS
// ============================================================================

/**
 * Add Node Command - Stores only the node and position info
 */
export class AddNodeCommand implements Command {
  readonly id: string;
  readonly type = 'ADD_NODE';
  readonly timestamp: number;
  readonly description: string;
  
  constructor(
    private readonly parentId: string,
    private readonly node: FormNode,
    private readonly index?: number
  ) {
    this.id = generateCommandId();
    this.timestamp = Date.now();
    this.description = `Add ${node.type} "${node.properties?.label || node.id}" to ${parentId}`;
  }

  execute(state: FormBuilderState): FormBuilderState {
    return produce(state, draft => {
      const result = addNodeToTree(draft.formConfig.uiConfig, this.parentId, this.node, this.index);
      if (result.success && result.data) {
        draft.formConfig.uiConfig = result.data;
      }
    });
  }

  undo(state: FormBuilderState): FormBuilderState {
    return produce(state, draft => {
      const result = deleteNodeFromTree(draft.formConfig.uiConfig, this.node.id);
      if (result.success && result.data) {
        draft.formConfig.uiConfig = result.data;
      }
    });
  }
}

/**
 * Delete Node Command - Stores node data and original position for restoration
 */
export class DeleteNodeCommand implements Command {
  readonly id: string;
  readonly type = 'DELETE_NODE';
  readonly timestamp: number;
  readonly description: string;
  
  private originalParentId: string;
  private originalIndex: number;
  private deletedNode: FormNode;

  constructor(private readonly nodeId: string, state: FormBuilderState) {
    this.id = generateCommandId();
    this.timestamp = Date.now();
    
    // Capture node info before deletion
    const context = createTreeContext(state.formConfig.uiConfig);
    const node = findNode(context, nodeId);
    const parent = getParentNode(context, nodeId);
    
    if (!node || !parent) {
      throw new Error(`Cannot delete node ${nodeId}: node or parent not found`);
    }
    
    this.deletedNode = node;
    this.originalParentId = parent.id;
    this.originalIndex = parent.children?.findIndex(child => child.id === nodeId) || 0;
    this.description = `Delete ${node.type} "${node.properties?.label || node.id}"`;
  }

  execute(state: FormBuilderState): FormBuilderState {
    return produce(state, draft => {
      const result = deleteNodeFromTree(draft.formConfig.uiConfig, this.nodeId);
      if (result.success && result.data) {
        draft.formConfig.uiConfig = result.data;
      }
    });
  }

  undo(state: FormBuilderState): FormBuilderState {
    return produce(state, draft => {
      const result = addNodeToTree(
        draft.formConfig.uiConfig, 
        this.originalParentId, 
        this.deletedNode, 
        this.originalIndex
      );
      if (result.success && result.data) {
        draft.formConfig.uiConfig = result.data;
      }
    });
  }
}

/**
 * Update Node Command - Stores only the delta changes
 */
export class UpdateNodeCommand implements Command {
  readonly id: string;
  readonly type = 'UPDATE_NODE';
  readonly timestamp: number;
  readonly description: string;
  
  private originalValues: Partial<FormNode>;

  constructor(
    private readonly nodeId: string,
    private readonly updates: Partial<FormNode>,
    state: FormBuilderState
  ) {
    this.id = generateCommandId();
    this.timestamp = Date.now();
    
    // Store only the fields that will be changed
    const context = createTreeContext(state.formConfig.uiConfig);
    const node = findNode(context, nodeId);
    if (!node) {
      throw new Error(`Cannot update node ${nodeId}: node not found`);
    }
    
    this.originalValues = {};
    Object.keys(updates).forEach(key => {
      this.originalValues[key as keyof FormNode] = (node as any)[key];
    });
    
    this.description = `Update ${node.type} "${node.properties?.label || node.id}"`;
  }

  execute(state: FormBuilderState): FormBuilderState {
    return produce(state, draft => {
      const result = updateNodeInTree(draft.formConfig.uiConfig, this.nodeId, this.updates);
      if (result.success && result.data) {
        draft.formConfig.uiConfig = result.data;
      }
    });
  }

  undo(state: FormBuilderState): FormBuilderState {
    return produce(state, draft => {
      const result = updateNodeInTree(draft.formConfig.uiConfig, this.nodeId, this.originalValues);
      if (result.success && result.data) {
        draft.formConfig.uiConfig = result.data;
      }
    });
  }
}

/**
 * Move Node Command - Stores original and new positions
 */
export class MoveNodeCommand implements Command {
  readonly id: string;
  readonly type = 'MOVE_NODE';
  readonly timestamp: number;
  readonly description: string;
  
  private originalParentId: string;
  private originalIndex: number;

  constructor(
    private readonly nodeId: string,
    private readonly newParentId: string,
    private readonly newIndex: number,
    state: FormBuilderState
  ) {
    this.id = generateCommandId();
    this.timestamp = Date.now();
    
    // Capture original position
    const context = createTreeContext(state.formConfig.uiConfig);
    const node = findNode(context, nodeId);
    const originalParent = getParentNode(context, nodeId);
    
    if (!node || !originalParent) {
      throw new Error(`Cannot move node ${nodeId}: node or parent not found`);
    }
    
    this.originalParentId = originalParent.id;
    this.originalIndex = originalParent.children?.findIndex(child => child.id === nodeId) || 0;
    this.description = `Move ${node.type} "${node.properties?.label || node.id}"`;
  }

  execute(state: FormBuilderState): FormBuilderState {
    return produce(state, draft => {
      const result = moveNodeInTree(
        draft.formConfig.uiConfig, 
        this.nodeId, 
        this.newParentId, 
        this.newIndex
      );
      if (result.success && result.data) {
        draft.formConfig.uiConfig = result.data;
      }
    });
  }

  undo(state: FormBuilderState): FormBuilderState {
    return produce(state, draft => {
      const result = moveNodeInTree(
        draft.formConfig.uiConfig, 
        this.nodeId, 
        this.originalParentId, 
        this.originalIndex
      );
      if (result.success && result.data) {
        draft.formConfig.uiConfig = result.data;
      }
    });
  }
}

// ============================================================================
// EDITOR UI COMMANDS
// ============================================================================

/**
 * Select Node Command - Lightweight UI state change
 */
export class SelectNodeCommand implements Command {
  readonly id: string;
  readonly type = 'SELECT_NODE';
  readonly timestamp: number;
  readonly description: string;
  
  constructor(
    private readonly nodeId: string | null,
    private readonly previousNodeId: string | null
  ) {
    this.id = generateCommandId();
    this.timestamp = Date.now();
    this.description = nodeId ? `Select node ${nodeId}` : 'Clear selection';
  }

  execute(state: FormBuilderState): FormBuilderState {
    return produce(state, draft => {
      draft.editor.selectedNodeId = this.nodeId;
    });
  }

  undo(state: FormBuilderState): FormBuilderState {
    return produce(state, draft => {
      draft.editor.selectedNodeId = this.previousNodeId;
    });
  }
}

/**
 * Batch Command - Groups multiple commands together
 */
export class BatchCommand implements Command {
  readonly id: string;
  readonly type = 'BATCH';
  readonly timestamp: number;
  readonly description: string;

  constructor(
    private readonly commands: Command[],
    description?: string
  ) {
    this.id = generateCommandId();
    this.timestamp = Date.now();
    this.description = description || `Batch: ${commands.length} operations`;
  }

  execute(state: FormBuilderState): FormBuilderState {
    return this.commands.reduce(
      (currentState, command) => command.execute(currentState),
      state
    );
  }

  undo(state: FormBuilderState): FormBuilderState {
    // Undo in reverse order
    return this.commands.reduceRight(
      (currentState, command) => command.undo(currentState),
      state
    );
  }
}

// ============================================================================
// COMMAND FACTORY
// ============================================================================

export class CommandFactory {
  static addNode(parentId: string, node: FormNode, index?: number): AddNodeCommand {
    return new AddNodeCommand(parentId, node, index);
  }

  static deleteNode(nodeId: string, state: FormBuilderState): DeleteNodeCommand {
    return new DeleteNodeCommand(nodeId, state);
  }

  static updateNode(
    nodeId: string, 
    updates: Partial<FormNode>, 
    state: FormBuilderState
  ): UpdateNodeCommand {
    return new UpdateNodeCommand(nodeId, updates, state);
  }

  static moveNode(
    nodeId: string, 
    newParentId: string, 
    newIndex: number, 
    state: FormBuilderState
  ): MoveNodeCommand {
    return new MoveNodeCommand(nodeId, newParentId, newIndex, state);
  }

  static selectNode(nodeId: string | null, previousNodeId: string | null): SelectNodeCommand {
    return new SelectNodeCommand(nodeId, previousNodeId);
  }

  static batch(commands: Command[], description?: string): BatchCommand {
    return new BatchCommand(commands, description);
  }
}

// ============================================================================
// HISTORY MANAGER
// ============================================================================

export class HistoryManager {
  private static readonly MAX_HISTORY_SIZE = 50;

  static addCommand(state: FormBuilderState, command: Command): FormBuilderState {
    return produce(state, draft => {
      // Execute the command
      const newState = command.execute(draft);
      Object.assign(draft, newState);

      // Add to history
      const historyEntry: HistoryEntry = {
        command,
        timestamp: command.timestamp
      };

      // Clear future if we're not at the end
      draft.history.future = [];
      
      // Add to past
      draft.history.past.push(historyEntry);
      
      // Limit history size
      if (draft.history.past.length > this.MAX_HISTORY_SIZE) {
        draft.history.past.shift();
      }

      draft.isDirty = true;
      draft.lastAction = command.description;
    });
  }

  static undo(state: FormBuilderState): FormBuilderState {
    if (state.history.past.length === 0) return state;

    return produce(state, draft => {
      const lastEntry = draft.history.past.pop()!;
      
      // Execute undo
      const newState = lastEntry.command.undo(draft);
      Object.assign(draft, newState);
      
      // Move to future
      draft.history.future.unshift(lastEntry);
      
      draft.lastAction = `Undo: ${lastEntry.command.description}`;
    });
  }

  static redo(state: FormBuilderState): FormBuilderState {
    if (state.history.future.length === 0) return state;

    return produce(state, draft => {
      const nextEntry = draft.history.future.shift()!;
      
      // Execute redo
      const newState = nextEntry.command.execute(draft);
      Object.assign(draft, newState);
      
      // Move to past
      draft.history.past.push(nextEntry);
      
      draft.lastAction = `Redo: ${nextEntry.command.description}`;
    });
  }

  static canUndo(state: FormBuilderState): boolean {
    return state.history.past.length > 0;
  }

  static canRedo(state: FormBuilderState): boolean {
    return state.history.future.length > 0;
  }

  static clear(state: FormBuilderState): FormBuilderState {
    return produce(state, draft => {
      draft.history.past = [];
      draft.history.future = [];
      draft.isDirty = false;
      draft.lastAction = null;
    });
  }
}