import { restApi, BaseResponse } from "@/api/restApi";
import { API_ENDPOINTS } from "@/api/endpoints";
import { RolePermission, UpdateRolePermissionsRequest } from "./types";

export const fetchRolePermissions = async (
  roleId: number
): Promise<BaseResponse<RolePermission[]>> => {
  const response = await restApi.get(
    `${API_ENDPOINTS.AUTH.ADMIN.PERMISSIONS}/role/${roleId}`
  );

  // Server trả về array trực tiếp, không phải BaseResponse
  // Axios response.data chứa array của permissions
  // Wrap thành BaseResponse cho consistency với type system
  return {
    code: 200,
    message: "Success",
    data: response.data as RolePermission[],
  };
};

export const updateRolePermissions = async (
  data: UpdateRolePermissionsRequest
): Promise<BaseResponse<null>> => {
  const response = await restApi.put(
    `${API_ENDPOINTS.AUTH.ADMIN.PERMISSIONS}/role`,
    data
  );
  return response.data;
};
