import React, { useState, useEffect, useCallback } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Plus, Trash2 } from "lucide-react";
import { z } from "zod";

import type { HotlineConfig, HotlineMetadata, HotlineField, EditableHotlineItem } from "../states/type";
import { hotlineItemSchema } from "../states/validation";
import { FieldError } from "../../shared/components/FieldError";
import { ValidationSummary } from "../../shared/components/ValidationSummary";

/**
 * Props for the HotlineTable component
 */
interface HotlineTableProps {
  metadata: HotlineMetadata;
  data: HotlineConfig | null;
  loading: boolean;
  onDataChange: (newData: HotlineConfig) => void;
  onValidationChange?: (isValid: boolean) => void;
  onValidationRequest?: React.MutableRefObject<(() => boolean) | undefined>;
}

export const HotlineTable: React.FC<HotlineTableProps> = ({
  metadata,
  data,
  loading,
  onDataChange,
  onValidationChange,
  onValidationRequest,
}) => {
  const [editableItems, setEditableItems] = useState<EditableHotlineItem[]>([]);
  const [validationErrors, setValidationErrors] = useState<Record<string, Record<string, string>>>({});
  const [showValidationSummary, setShowValidationSummary] = useState(false);
  const [hasShownValidationErrors, setHasShownValidationErrors] = useState(false);

  useEffect(() => {
    if (!data) {
      setEditableItems([]);
      return;
    }

    const arrayKey = metadata.arrayKey || 'hotlines';
    const arrayData = data[arrayKey as keyof HotlineConfig] || [];

    const items = arrayData.map((item, index: number) => ({
      id: `item-${index}`,
      data: { ...item },
    }));

    setEditableItems(items);
  }, [data, metadata.arrayKey]);

  // ============================================================================
  // Validation Functions
  // ============================================================================

  const validateItem = useCallback((itemData: Record<string, string>) => {
    try {
      hotlineItemSchema.parse(itemData);
      return {};
    } catch (error) {
      if (error instanceof z.ZodError) {
        const fieldErrors: Record<string, string> = {};
        error.errors.forEach((err) => {
          if (err.path.length > 0) {
            fieldErrors[err.path[0] as string] = err.message;
          }
        });
        return fieldErrors;
      }
      return {};
    }
  }, []);

  const validateAllItems = useCallback(() => {
    const newValidationErrors: Record<string, Record<string, string>> = {};
    let hasErrors = false;

    editableItems.forEach((item) => {
      const itemErrors = validateItem(item.data);
      if (Object.keys(itemErrors).length > 0) {
        newValidationErrors[item.id] = itemErrors;
        hasErrors = true;
      }
    });

    setValidationErrors(newValidationErrors);

    // Mark that validation errors have been shown
    if (hasErrors) {
      setHasShownValidationErrors(true);
    }

    if (onValidationChange) {
      onValidationChange(!hasErrors);
    }

    return !hasErrors;
  }, [editableItems, validateItem, onValidationChange]);

  // Expose validation function to parent component
  useEffect(() => {
    if (onValidationRequest) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (onValidationRequest as any).current = validateAllItems;
    }
  }, [validateAllItems, onValidationRequest]);

  const updateConfigData = useCallback((items: EditableHotlineItem[]) => {
    const arrayKey = metadata.arrayKey || 'hotlines';
    const arrayData = items.map(item => item.data);
    const newData = { [arrayKey]: arrayData } as unknown as HotlineConfig;
    onDataChange(newData);
  }, [metadata.arrayKey, onDataChange]);

  // ============================================================================
  // Event Handlers
  // ============================================================================


  const revalidateAllItems = (newItems: EditableHotlineItem[]) => {
    if (hasShownValidationErrors) {
      setTimeout(() => {
        const newValidationErrors: Record<string, Record<string, string>> = {};
        let hasErrors = false;

        newItems.forEach((item) => {
          const itemErrors = validateItem(item.data);
          if (Object.keys(itemErrors).length > 0) {
            newValidationErrors[item.id] = itemErrors;
            hasErrors = true;
          }
        });

        setValidationErrors(newValidationErrors);

        // Notify parent component of validation state change
        if (onValidationChange) {
          onValidationChange(!hasErrors);
        }
      }, 0);
    }
  };

  /**
   * Adds a new hotline item with default values
   */
  const handleAddItem = () => {
    const newItem: EditableHotlineItem = {
      id: `new-${Date.now()}`,
      isNew: true,
      data: metadata.fields.reduce((acc, field) => {
        acc[field.key] = '';
        return acc;
      }, {} as Record<string, string>),
    };

    const newItems = [...editableItems, newItem];
    setEditableItems(newItems);
    updateConfigData(newItems);

    revalidateAllItems(newItems);
  };

  /**
   * Removes a hotline item by ID
   */
  const handleRemoveItem = (id: string) => {
    const newItems = editableItems.filter(item => item.id !== id);
    setEditableItems(newItems);
    updateConfigData(newItems);

    revalidateAllItems(newItems);
  };

  /**
   * Updates a specific field value for a hotline item
   * Includes real-time validation
   */
  const handleItemChange = (id: string, field: string, value: string) => {
    const newItems = editableItems.map(item =>
      item.id === id
        ? { ...item, data: { ...item.data, [field]: value } }
        : item
    );
    setEditableItems(newItems);
    updateConfigData(newItems);

    // If validation errors have been shown, re-validate in real-time
    if (hasShownValidationErrors) {
      // Re-validate all items to update the overall validation state
      setTimeout(() => {
        const newValidationErrors: Record<string, Record<string, string>> = {};
        let hasErrors = false;

        newItems.forEach((item) => {
          const itemErrors = validateItem(item.data);
          if (Object.keys(itemErrors).length > 0) {
            newValidationErrors[item.id] = itemErrors;
            hasErrors = true;
          }
        });

        setValidationErrors(newValidationErrors);

        // Notify parent component of validation state change
        if (onValidationChange) {
          onValidationChange(!hasErrors);
        }
      }, 0);
    }
  };

  // ============================================================================
  // Render Methods
  // ============================================================================

  /**
   * Renders an input field for a specific hotline field
   * Handles different input types with validation styling and tooltip error display
   */
  const renderFieldInput = (
    field: HotlineField,
    value: string,
    onChange: (value: string) => void,
    error?: string,
    disabled: boolean = false
  ) => {
    const hasError = !!error;
    const inputProps = {
      disabled: disabled || loading,
      placeholder: field.placeholder,
      className: `h-10 ${hasError ? 'border-red-500 focus:border-red-500' : ''}`,
    };

    const inputElement = (
      <Input
        type={field.type === 'email' ? 'email' : field.type === 'url' ? 'url' : 'text'}
        value={value || ''}
        onChange={(e) => onChange(e.target.value)}
        {...inputProps}
      />
    );

    return (
      <FieldError error={error} asTooltip={true}>
        {inputElement}
      </FieldError>
    );
  };

  // ============================================================================
  // Loading State
  // ============================================================================

  if (loading && !data) {
    return (
      <Card>
        <CardContent className="p-0 py-4">
          <div className="text-center text-muted-foreground">
            Đang tải dữ liệu...
          </div>
        </CardContent>
      </Card>
    );
  }

  // ============================================================================
  // Main Render
  // ============================================================================

  /**
   * Maps field keys to responsive column widths
   * Optimized for hotline information display
   */
  const getColumnWidth = (fieldKey: string): string => {
    switch (fieldKey) {
      case 'name':
        return "w-48"; // Name - medium width
      case 'phone':
        return "w-32"; // Phone - smaller width for phone numbers
      case 'email':
        return "w-56"; // Email - medium-large width
      default:
        return "";
    }
  };

  return (
    <Card>
      <CardContent className="p-0 pb-4">
        <div className="space-y-4 py-4">
          <div className="flex items-center justify-between px-6">
            <h4 className="text-sm font-medium">
              Danh sách {metadata.title.toLowerCase()} ({editableItems.length} mục)
            </h4>
          </div>

          <ValidationSummary
            errors={Object.values(validationErrors).flatMap(itemErrors => Object.values(itemErrors))}
            show={showValidationSummary && Object.keys(validationErrors).length > 0}
            onDismiss={() => setShowValidationSummary(false)}
            title="Có lỗi trong dữ liệu đường dây nóng"
          />

          {editableItems.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              Chưa có dữ liệu. Nhấn "+" để bắt đầu.
            </div>
          ) : null}

          <Table>
            <TableHeader>
              <TableRow>
                {metadata.fields.map((field) => (
                  <TableHead key={field.key} className={getColumnWidth(field.key)}>
                    {field.label}
                    {field.required && <span className="text-red-500 ml-1">*</span>}
                  </TableHead>
                ))}
                <TableHead className="w-12">Thao tác</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {editableItems.map((item) => (
                <TableRow key={item.id}>
                  {metadata.fields.map((field) => (
                    <TableCell key={field.key} className="align-top">
                      {renderFieldInput(
                        field,
                        item.data[field.key],
                        (value) => handleItemChange(item.id, field.key, value),
                        validationErrors[item.id]?.[field.key]
                      )}
                    </TableCell>
                  ))}
                  <TableCell>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRemoveItem(item.id)}
                      className="text-red-600 hover:text-red-800"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {/* Add Tab positioned below table - centered */}
          <div className="relative">
            <div className="absolute left-1/2 transform -translate-x-1/2 -top-px">
              <Button
                onClick={handleAddItem}
                variant="ghost"
                size="sm"
                className="h-8 px-4 rounded-t-md rounded-b-none border-2 border-b-0 border-dashed border-blue-300 hover:border-blue-500 hover:bg-blue-50 text-blue-600 hover:text-blue-700 transition-colors bg-white"
                title="Thêm mục mới"
              >
                <Plus className="h-4 w-4 mr-2" />
                Thêm mục
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
