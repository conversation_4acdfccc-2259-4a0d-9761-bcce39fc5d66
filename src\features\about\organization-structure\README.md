# Organization Structure Feature

## Overview

The Organization Structure feature displays contact information and hotline data for the organization, fetched from the configuration API. This feature follows the established codebase patterns and provides a comprehensive view of organizational contact details.

## Features

- **Contact Information Display**: Shows organization contact details including addresses, phone numbers, and emails
- **Hotline Information Display**: Displays emergency/support hotline information with prominent styling
- **Real-time Data Fetching**: Automatically fetches data from configuration API endpoints
- **Loading States**: Provides skeleton loading animations during data fetch
- **Error Handling**: Comprehensive error handling with user-friendly messages
- **Responsive Design**: Works seamlessly across all device sizes
- **Manual Refresh**: Users can manually refresh data with a button
- **Auto-refresh Logic**: Automatically refreshes stale data (older than 5 minutes)

## Architecture

### File Structure

```
src/features/about/organization-structure/
├── components/
│   ├── ContactInfo.tsx          # Contact information display component
│   └── HotlineInfo.tsx          # Hotline information display component
├── pages/
│   ├── loadable.tsx             # Lazy-loaded page component
│   └── portal-link/pages/
│       └── OrganizationStructurePage.tsx  # Main page component
└── states/
    ├── type.ts                  # TypeScript type definitions
    ├── api.ts                   # API integration functions
    ├── slices.ts                # Redux state management
    └── selectors.ts             # Redux selectors
```

### API Integration

#### Endpoints
- **Contacts**: `GET /portal/v1/public/config/contacts`
- **Hotlines**: `GET /portal/v1/public/config/hot_lines`

#### Data Structure
```typescript
// Contact Item
interface ContactItem {
  name: string;      // Contact person/department name
  phone: string;     // Phone number
  email: string;     // Email address
  address: string;   // Physical address
}

// Hotline Item
interface HotlineItem {
  name: string;      // Hotline service name
  phone: string;     // Hotline phone number
  email: string;     // Hotline email address
}
```

### State Management

#### Redux Store Structure
```typescript
organizationStructure: {
  contacts: {
    data: ContactConfig | null;
    loading: boolean;
    error: string | null;
    lastUpdated: number | null;
  },
  hotlines: {
    data: HotlineConfig | null;
    loading: boolean;
    error: string | null;
    lastUpdated: number | null;
  }
}
```

#### Available Actions
- `fetchContactAsync` - Fetch contact configuration
- `fetchHotlineAsync` - Fetch hotline configuration
- `fetchOrganizationDataAsync` - Fetch both configurations in parallel
- `clearContactError` - Clear contact error state
- `clearHotlineError` - Clear hotline error state
- `clearAllErrors` - Clear all error states
- `resetOrganizationState` - Reset entire state

#### Available Selectors
- `selectContactData` - Get contact configuration data
- `selectHotlineData` - Get hotline configuration data
- `selectIsAnyLoading` - Check if any data is loading
- `selectHasAnyErrors` - Check if any errors exist
- `selectPageReady` - Check if page is ready for display

## Components

### ContactInfo Component

Displays organization contact information with:
- Contact person/department names
- Physical addresses with map pin icons
- Clickable phone numbers (tel: links)
- Clickable email addresses (mailto: links)
- Loading skeletons during data fetch
- Error states with retry options

### HotlineInfo Component

Displays emergency hotline information with:
- Prominent red color scheme for urgency
- Large, bold phone numbers for visibility
- Hotline service names with badges
- Email contact information
- Emergency usage notice
- Loading skeletons during data fetch

### OrganizationStructurePage Component

Main page component that:
- Orchestrates data fetching for both sections
- Provides manual refresh functionality
- Handles global error states
- Displays both contact and hotline sections in responsive grid
- Manages loading states and error handling

## Usage

### Basic Usage

```tsx
import { OrganizationStructurePage } from "@/features/about/organization-structure/pages/loadable";

// In router configuration
<Route 
  path="/organization-structure" 
  element={<OrganizationStructurePage />} 
/>
```

### Using Individual Components

```tsx
import ContactInfo from "@/features/about/organization-structure/components/ContactInfo";
import HotlineInfo from "@/features/about/organization-structure/components/HotlineInfo";

// In your component
<ContactInfo 
  data={contactData}
  loading={contactLoading}
  error={contactError}
/>

<HotlineInfo 
  data={hotlineData}
  loading={hotlineLoading}
  error={hotlineError}
/>
```

### Using Redux State

```tsx
import { useAppSelector, useAppDispatch } from "@/store/rootReducer";
import { 
  selectContactData, 
  fetchOrganizationDataAsync 
} from "@/features/about/organization-structure/states";

const MyComponent = () => {
  const dispatch = useAppDispatch();
  const contactData = useAppSelector(selectContactData);
  
  useEffect(() => {
    dispatch(fetchOrganizationDataAsync());
  }, [dispatch]);
  
  return <div>{/* Your component */}</div>;
};
```

## Styling

The feature uses Tailwind CSS classes following the established design system:

- **Contact Section**: Clean, professional styling with gray color scheme
- **Hotline Section**: Urgent red color scheme with prominent phone numbers
- **Icons**: Lucide React icons for visual clarity
- **Cards**: Shadcn UI card components for consistent layout
- **Responsive**: Grid layout that adapts to screen size

## Error Handling

The feature implements comprehensive error handling:

1. **API Errors**: Network failures, server errors, invalid responses
2. **Data Validation**: Ensures received data matches expected structure
3. **User Feedback**: Toast notifications for errors and success states
4. **Graceful Degradation**: Shows error states without breaking the UI
5. **Retry Mechanisms**: Manual refresh button and automatic retry logic

## Performance Optimizations

- **Lazy Loading**: Page component is lazy-loaded to reduce initial bundle size
- **Parallel Fetching**: Contact and hotline data fetched simultaneously
- **Memoized Selectors**: Redux selectors use createSelector for performance
- **Conditional Fetching**: Only fetches data when needed or stale
- **Skeleton Loading**: Provides immediate visual feedback during loading

## Testing Considerations

When testing this feature, consider:

1. **API Response Handling**: Test with various API response scenarios
2. **Loading States**: Verify skeleton animations display correctly
3. **Error States**: Test error handling and recovery mechanisms
4. **Responsive Design**: Test on different screen sizes
5. **Accessibility**: Ensure proper keyboard navigation and screen reader support
6. **Performance**: Test lazy loading and data fetching performance

## Future Enhancements

Potential improvements for this feature:

1. **Caching**: Implement more sophisticated caching strategies
2. **Offline Support**: Add offline data storage and sync
3. **Search/Filter**: Add search functionality for large contact lists
4. **Export**: Allow users to export contact information
5. **Favorites**: Let users mark frequently used contacts
6. **Integration**: Connect with external contact management systems
