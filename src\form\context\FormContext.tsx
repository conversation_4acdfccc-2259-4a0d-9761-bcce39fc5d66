/**
 * FormContext - Form state management context
 */

import React, { createContext } from "react";

/**
 * Form render context - shared data across all nodes
 */
export interface FormContext {
  viewOnly: boolean;
  form: unknown; // TanStack Form instance - will be typed properly when integrated
  // <PERSON><PERSON> thể mở rộng thêm: validation state, form data, etc.
}

/**
 * Form Context with default values
 */
const FormContextInstance = createContext<FormContext>({
  viewOnly: false,
  form: null,
});

// Export with different name to avoid confusion
export { FormContextInstance as FormContext };

/**
 * FormContext Provider Props
 */
export interface FormContextProviderProps {
  value: FormContext;
  children: React.ReactNode;
}

/**
 * FormContext Provider Component
 */
export const FormContextProvider: React.FC<FormContextProviderProps> = ({
  value,
  children,
}) => {
  return (
    <FormContextInstance.Provider value={value}>
      {children}
    </FormContextInstance.Provider>
  );
};
