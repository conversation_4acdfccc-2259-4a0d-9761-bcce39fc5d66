import { useState, useEffect, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import { useAppDispatch } from "@/store/rootReducer";
import {
  AlbumPageMode,
  clearMediaData,
  setCurrentAlbumMedia,
  setMediaLoading,
  setMediaError,
  setMediaHasMore,
  setMediaCurrentPage,
} from "./slices";
import {
  fetchAlbumMedia,
  createMediaInAlbum,
  addExistingMediaToAlbum,
  updateMedia,
  removeMediaFromAlbum,
  updateMediaPriority,
  setCoverImage,
  deleteCoverImage,
} from "./api";
import { CreateMediaRequest } from "./types";
import { toast } from "sonner";

/**
 * URL sync hook specifically for image/album management (separate from post hook)
 * Manages URL synchronization state for album pages
 * @returns Object containing URL syncing state
 */
export const useSyncStatusWithPath = () => {
  const [isUrlSyncing, setIsUrlSyncing] = useState(false);

  useEffect(() => {
    // For image routes, we mainly handle loading states
    setIsUrlSyncing(true);

    // Short delay to show loading, then stop
    setTimeout(() => setIsUrlSyncing(false), 200);
  }, []);

  return { isUrlSyncing };
};

/**
 * Hook for switching between different page modes
 * Manages navigation between list and detail views
 * @returns Object containing switchMode function
 */
export const useSwitchMode = () => {
  const navigate = useNavigate();

  const switchMode = (mode: AlbumPageMode, albumId?: string) => {
    const params = new URLSearchParams();
    params.set("mode", mode);
    if (albumId) {
      params.set("albumid", albumId);
    }
    navigate(`?${params.toString()}`);
  };

  return { switchMode };
};

/**
 * Hook for managing album media operations
 * Provides functions for CRUD operations on album media
 * @param onAlbumUpdated - Optional callback when album is updated
 * @returns Object containing media management functions
 */
export const useAlbumMedia = (onAlbumUpdated?: () => void) => {
  const dispatch = useAppDispatch();

  const clearMedia = useCallback(() => {
    dispatch(clearMediaData());
  }, [dispatch]);

  const loadAlbumMedia = useCallback(
    async (albumId: number) => {
      try {
        dispatch(setMediaLoading(true));
        dispatch(setMediaError(null));

        const response = await fetchAlbumMedia(albumId, { page: 0, size: 20 });

        dispatch(setCurrentAlbumMedia(response.data.data || []));
        // Calculate hasMore based on returned data size
        const hasMore = (response.data.data?.length || 0) === 20;
        dispatch(setMediaHasMore(hasMore));
        dispatch(setMediaCurrentPage(0));
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
      } catch (error) {
        dispatch(setMediaError("Không thể tải phương tiện"));
        toast.error("Không thể tải danh sách phương tiện");
      } finally {
        dispatch(setMediaLoading(false));
      }
    },
    [dispatch]
  );

  const loadMoreAlbumMedia = useCallback(
    async (albumId: number, currentPage: number) => {
      try {
        dispatch(setMediaLoading(true));

        const response = await fetchAlbumMedia(albumId, {
          page: currentPage + 1,
          size: 20,
        });

        // Append to existing media list
        dispatch(setCurrentAlbumMedia(response.data.data || []));
        // Calculate hasMore based on returned data size
        const hasMore = (response.data.data?.length || 0) === 20;
        dispatch(setMediaHasMore(hasMore));
        dispatch(setMediaCurrentPage(currentPage + 1));
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
      } catch (error) {
        toast.error("Không thể tải thêm phương tiện");
      } finally {
        dispatch(setMediaLoading(false));
      }
    },
    [dispatch]
  );

  const addMediaToAlbum = useCallback(
    async (albumId: number, mediaData: Omit<CreateMediaRequest, "albumId">) => {
      try {
        // Check if album is currently empty (no media)
        const currentMediaResponse = await fetchAlbumMedia(albumId, {
          page: 0,
          size: 1,
        });
        const isAlbumEmpty =
          (currentMediaResponse.data.data?.length || 0) === 0;

        // Create the media
        const createResponse = await createMediaInAlbum(albumId, mediaData);
        toast.success("Thêm phương tiện thành công");

        // If album was empty and this is an image, set it as cover image
        if (
          isAlbumEmpty &&
          mediaData.type === "image" &&
          createResponse.data.data?.src
        ) {
          try {
            await setCoverImage(albumId, createResponse.data.data.src);
            toast.success("Đã đặt làm ảnh bìa");

            // Notify parent component to reload album data
            onAlbumUpdated?.();
          } catch (coverError) {
            console.error("coverError: ", coverError);
            // Don't show error toast for cover image failure, media was still added successfully
          }
        }

        // Reload media list
        await loadAlbumMedia(albumId);
      } catch (error) {
        console.error("Không thể thêm phương tiện: ", error);
        toast.error("Không thể thêm phương tiện");
      }
    },
    [loadAlbumMedia, onAlbumUpdated]
  );

  const addExistingMediaToAlbumHook = useCallback(
    async (albumId: number, mediaId: number) => {
      try {
        await addExistingMediaToAlbum(albumId, mediaId);
        toast.success("Thêm phương tiện có sẵn thành công");
        // Reload media list
        await loadAlbumMedia(albumId);
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
      } catch (error) {
        toast.error("Không thể thêm phương tiện có sẵn");
      }
    },
    [loadAlbumMedia]
  );

  const updateMediaDetails = useCallback(
    async (
      albumId: number,
      mediaId: number,
      currentMedia: {
        type: string;
        src: string;
        name: string;
        description?: { text: string };
      },
      updates: { name: string; description?: { text: string } }
    ) => {
      try {
        // Merge current media data with updates to satisfy API requirements
        const payload = {
          type: currentMedia.type,
          src: currentMedia.src,
          name: updates.name,
          description: updates.description ||
            currentMedia.description || { text: "" },
        };

        await updateMedia(mediaId, payload);
        toast.success("Cập nhật phương tiện thành công");
        // Reload media list
        await loadAlbumMedia(albumId);
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
      } catch (error) {
        toast.error("Không thể cập nhật phương tiện");
      }
    },
    [loadAlbumMedia]
  );

  const removeMedia = useCallback(
    async (albumId: number, mediaId: number) => {
      try {
        // Remove the media
        await removeMediaFromAlbum(albumId, mediaId);
        toast.success("Xóa phương tiện thành công");

        // Reload media list first to get updated count
        await loadAlbumMedia(albumId);

        // Check if album is now empty after removal
        const updatedMediaResponse = await fetchAlbumMedia(albumId, {
          page: 0,
          size: 1,
        });
        const isAlbumNowEmpty =
          (updatedMediaResponse.data.data?.length || 0) === 0;

        // If album is now empty, clear the cover image
        if (isAlbumNowEmpty) {
          try {
            await deleteCoverImage(albumId);
            toast.success("Đã xóa ảnh bìa");

            // Notify parent component to reload album data
            onAlbumUpdated?.();
          } catch (coverError) {
            console.error("coverError: ", coverError);
            // Don't show error toast for cover image failure, media was still removed successfully
          }
        }
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
      } catch (error) {
        toast.error("Không thể xóa phương tiện");
      }
    },
    [loadAlbumMedia, onAlbumUpdated]
  );

  const updatePriority = useCallback(
    async (albumId: number, mediaId: number, priority: number) => {
      try {
        await updateMediaPriority(albumId, mediaId, priority);
        toast.success("Cập nhật thứ tự thành công");
        // Reload media list
        await loadAlbumMedia(albumId);
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
      } catch (error) {
        toast.error("Không thể cập nhật thứ tự");
      }
    },
    [loadAlbumMedia]
  );

  const setCoverImageHook = useCallback(
    async (albumId: number, imageUrl: string) => {
      try {
        await setCoverImage(albumId, imageUrl);
        toast.success("Đặt ảnh bìa thành công");

        // Notify parent component to reload album data
        onAlbumUpdated?.();
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
      } catch (error) {
        toast.error("Không thể đặt ảnh bìa");
      }
    },
    [onAlbumUpdated]
  );

  const deleteCoverImageHook = useCallback(
    async (albumId: number) => {
      try {
        await deleteCoverImage(albumId);
        toast.success("Đã xóa ảnh bìa thành công");

        // Notify parent component to reload album data
        onAlbumUpdated?.();
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
      } catch (error) {
        toast.error("Không thể xóa ảnh bìa");
      }
    },
    [onAlbumUpdated]
  );

  return {
    clearMedia,
    loadAlbumMedia,
    loadMoreAlbumMedia,
    addMediaToAlbum,
    addExistingMediaToAlbumHook,
    updateMediaDetails,
    removeMedia,
    updatePriority,
    setCoverImageHook,
    deleteCoverImageHook,
  };
};
