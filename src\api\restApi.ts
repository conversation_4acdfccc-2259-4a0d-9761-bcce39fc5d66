// src/lib/restApi.ts
import axios, { AxiosInstance, InternalAxiosRequestConfig } from "axios";
import { TOKEN_TYPE, getTokenFromCookies } from "./cookies";

export const API_URL = import.meta.env.VITE_API_URL;

// Tạo Axios client
export const restApi: AxiosInstance = axios.create({
  baseURL: API_URL,
  timeout: 30_000,
  headers: {
    "Content-Type": "application/json",
  },
});

// Gắn Authorization header nếu có token
restApi.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    const token = getTokenFromCookies(TOKEN_TYPE.ACCESS_TOKEN);
    if (token) {
      config.headers!["Authorization"] = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

export interface Pagination {
  page: number;
  size: number;
  total: number;
}
export interface BaseResponse<T = null> {
  code: number;
  message: string;
  data?: T;
  pagination?: Pagination;
  errors?: Record<string, string>;
}
