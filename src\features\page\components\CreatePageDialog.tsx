import React, { useState, useEffect } from "react";
import { useAppDispatch, useAppSelector } from "@/store/rootReducer";
import { createPageAsync, fetchPagesSummaryAsync } from "../states/slices";
import { selectPageLoading } from "../states/selectors";
import {
  CustomDial<PERSON>,
  DialogFooter,
  DialogButton,
} from "@/components/ui/CustomDialog";
import { generateSlugFromTitle, checkSlugExists } from "../states/api";
import { CreatePageRequest } from "../states/types";
import { CategoryDTO } from "@/features/category/states/types";

interface CreatePageDialogProps {
  open: boolean;
  onClose: () => void;
  selectedCategoryId: number | null;
  categories: CategoryDTO[];
}

export const CreatePageDialog: React.FC<CreatePageDialogProps> = ({
  open,
  onClose,
  selectedCategoryId,
  categories,
}) => {
  const dispatch = useAppDispatch();
  const loading = useAppSelector(selectPageLoading);

  // Form state
  const [title, setTitle] = useState("");
  const [slug, setSlug] = useState("");
  const [slugChecking, setSlugChecking] = useState(false);
  const [slugValid, setSlugValid] = useState<boolean | null>(null);

  // Reset form when dialog opens/closes
  useEffect(() => {
    if (open) {
      setTitle("");
      setSlug("");
      setSlugValid(null);
    }
  }, [open]);

  const handleGenerateSlug = () => {
    if (!title.trim()) return;
    const generatedSlug = generateSlugFromTitle(title);
    setSlug(generatedSlug);
    checkSlugValidity(generatedSlug);
  };

  const checkSlugValidity = async (slugToCheck: string) => {
    if (!slugToCheck.trim()) {
      setSlugValid(null);
      return;
    }

    setSlugChecking(true);
    try {
      const response = await checkSlugExists(slugToCheck);
      setSlugValid(!response.data); // Valid if NOT exists
    } catch (error) {
      console.error("Error checking slug:", error);
      setSlugValid(null);
    } finally {
      setSlugChecking(false);
    }
  };

  const handleSlugChange = (value: string) => {
    setSlug(value);
    // Debounced slug checking
    const timeoutId = setTimeout(() => {
      checkSlugValidity(value);
    }, 500);

    return () => clearTimeout(timeoutId);
  };

  // Auto-generate slug when user finishes typing title
  const handleTitleBlur = () => {
    if (title.trim() && !slug.trim()) {
      handleGenerateSlug();
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedCategoryId || !title.trim() || !slug.trim()) {
      return;
    }

    // Check slug validity one more time before creating
    if (slugValid === false) {
      alert("Slug đã tồn tại, vui lòng chọn slug khác");
      return;
    }

    const selectedCategory = categories.find(
      (c) => c.id === selectedCategoryId
    );

    const request: CreatePageRequest = {
      title: title.trim(),
      slug: slug.trim(),
      categoryId: selectedCategoryId,
      excerpt: {
        image: "",
        description: `Trang cho chuyên mục ${selectedCategory?.name || ""}`,
        files: [],
      },
    };

    console.log("🚀 Creating page with request:", request);

    try {
      await dispatch(createPageAsync(request)).unwrap();
      console.log("✅ Page created successfully");

      // Refresh page summaries to update the list
      dispatch(fetchPagesSummaryAsync());

      // Close dialog and reset form
      onClose();
    } catch (error) {
      console.error("❌ Failed to create page:", error);
      alert("Không thể tạo trang. Vui lòng thử lại!");
    }
  };

  const getSlugStatusColor = () => {
    if (slugChecking) return "text-blue-600";
    if (slugValid === true) return "text-green-600";
    if (slugValid === false) return "text-red-600";
    return "text-gray-600";
  };

  const getSlugStatusText = () => {
    if (slugChecking) return "Đang kiểm tra...";
    if (slugValid === true) return "Đường dẫn khả dụng";
    if (slugValid === false) return "Đường dẫn đã tồn tại";
    return "Nhập đường dẫn để kiểm tra";
  };

  const selectedCategory = categories.find((c) => c.id === selectedCategoryId);
  const isFormValid = title.trim() && slug.trim() && slugValid === true;

  return (
    <CustomDialog
      open={open}
      onClose={onClose}
      title="Tạo trang mới"
      maxWidth="lg"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Selected Category Info */}
        {selectedCategory && (
          <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
            <h3 className="font-medium text-blue-900 mb-1">
              Chuyên mục đã chọn:
            </h3>
            <p className="text-blue-700">{selectedCategory.name}</p>
          </div>
        )}

        {/* Title Input */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Tiêu đề trang *
          </label>
          <input
            type="text"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            onBlur={handleTitleBlur}
            placeholder="Nhập tiêu đề trang..."
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            disabled={loading}
          />
        </div>

        {/* Slug Input */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <label className="block text-sm font-medium text-gray-700">
              Đường dẫn URL *
            </label>
            <button
              type="button"
              onClick={handleGenerateSlug}
              disabled={!title.trim() || loading}
              className="text-sm text-blue-600 hover:text-blue-700 disabled:text-gray-400"
            >
              Tạo từ tiêu đề
            </button>
          </div>
          <input
            type="text"
            value={slug}
            onChange={(e) => handleSlugChange(e.target.value)}
            placeholder="duong-dan-url-trang"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-mono text-sm"
            disabled={loading}
          />
          <p className={`text-sm mt-1 ${getSlugStatusColor()}`}>
            {getSlugStatusText()}
          </p>
        </div>

        {/* Form Actions */}
        <DialogFooter>
          <DialogButton onClick={onClose} disabled={loading}>
            Hủy
          </DialogButton>
          <DialogButton
            type="submit"
            variant="primary"
            disabled={!isFormValid || loading}
            loading={loading}
          >
            Tạo trang
          </DialogButton>
        </DialogFooter>
      </form>
    </CustomDialog>
  );
};
