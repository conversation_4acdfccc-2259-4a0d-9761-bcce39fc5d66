import { RootState } from "@/store/rootReducer";
import { PostQueryParams } from "./api";
import { PostStatus } from "./types";

export const selectPostStatus = (state: RootState): PostStatus =>
  state.postsFilterState.params.status;

export const selectPostLoading = (state: RootState): boolean =>
  state.postsFilterState.loading;

export const selectPostError = (state: RootState): string | null =>
  state.postsFilterState.error;

export const selectPostQueryParams = (state: RootState): PostQueryParams =>
  state.postsFilterState.params;

export const selectRefetch = (state: RootState): boolean =>
  state.postsFilterState.refetch;
