/**
 * Simple data validation function
 */

import { buildFormZodSchema } from "../utils/zodHelper";
import { registerFormMock } from "../../features/form-editor/states/mock";

// Simple validation function
export function validate(data: Record<string, unknown>) {
  console.log("Data:", data);

  try {
    const rootNode = (registerFormMock as any).node;
    const schema = buildFormZodSchema(rootNode);

    const result = schema.safeParse(data);

    if (result.success) {
      console.log("✅ Valid");
    } else {
      console.log("❌ Invalid:");
      result.error.issues.forEach((issue) => {
        console.log(`   ${issue.path.join(".")}: ${issue.message}`);
      });
    }
  } catch (error) {
    console.error("Error:", error);
  }
}

// Make function available globally
if (typeof window !== "undefined") {
  (window as any).validate = validate;
  console.log("👉 validate({...data}) function ready");
}
