/**
 * Form Canvas
 * Center panel showing WYSIWYG form preview using AutoForm directly
 */

import React, { useRef } from "react";
import { useFormBuilder } from "../context/FormBuilderContext";
import { AutoForm } from "@/form/context/AutoForm";
import { SelectionOverlay } from "./SelectionOverlay";
import { toast } from "sonner";

/**
 * Form Canvas - WYSIWYG form preview
 */
export const FormCanvas: React.FC = () => {
  const { state } = useFormBuilder();
  const canvasRef = useRef<HTMLDivElement>(null);

  const renderEmptyState = () => (
    <div className="flex-1 flex items-center justify-center p-12">
      <div className="text-center max-w-md">
        <div className="text-6xl mb-4">📝</div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          Your Form Preview
        </h3>
        <p className="text-gray-600 mb-4">
          Add components using the tree panel on the left to see your form here.
        </p>
        <div className="text-sm text-gray-500">
          This canvas shows exactly how your form will look to end users.
        </div>
      </div>
    </div>
  );

  const hasFormContent = state.formConfig.uiConfig.children && 
                        state.formConfig.uiConfig.children.length > 0;

  return (
    <div className="flex-1 bg-white relative overflow-auto">
      {/* Canvas Header */}

      {/* Canvas Content */}
      <div 
        ref={canvasRef}
        className="p-6 min-h-full relative"
      >
        {!hasFormContent ? renderEmptyState() : (
          <div className="max-w-2xl mx-auto relative">
            {/* WYSIWYG AutoForm Rendering */}
            {state.editor.mode === 'design' ? (
              /* Design Mode - Edit with overlay */
              <div className="relative">
                <AutoForm
                  node={state.formConfig.uiConfig}
                  viewOnly={true}
                  validationMode="onChange"
                />
                
                {/* Figma-style Selection Overlay - Only in design mode */}
                <SelectionOverlay canvasRef={canvasRef} />
              </div>
            ) : state.editor.mode === 'preview' ? (
              /* Preview Mode - Interactive form for testing */
              <div className="relative">
                <AutoForm
                  node={state.formConfig.uiConfig}
                  viewOnly={false}
                  validationMode="onChange"
                  onSubmit={(data) => {
                    console.log('[Preview] Form submitted:', data);
                    
                    // Send data to FormDataViewer via global handler
                    if ((window as any).formBuilderSubmissionHandler) {
                      (window as any).formBuilderSubmissionHandler(data, true);
                    }
                    
                    toast.success('Form submitted successfully!', {
                      description: 'Data captured in Properties panel',
                      duration: 4000,
                    });
                  }}
                  onValidationError={(errors) => {
                    console.log('[Preview] Validation errors:', errors);
                    
                    // Send validation errors to FormDataViewer
                    if ((window as any).formBuilderSubmissionHandler) {
                      (window as any).formBuilderSubmissionHandler({ validationErrors: errors }, false);
                    }
                    
                    toast.error('Form validation failed', {
                      description: 'Check Properties panel for details',
                      duration: 4000,
                    });
                  }}
                />
              </div>
            ) : (
              /* Code View */
              <div className="bg-gray-900 text-gray-100 p-4 rounded-lg font-mono text-sm">
                <pre className="whitespace-pre-wrap">
                  {JSON.stringify(state.formConfig.uiConfig, null, 2)}
                </pre>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};