import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  CustomDialog,
  DialogFooter,
  DialogButton,
} from "@/components/ui/CustomDialog";
import { Plus, HelpCircle, User, ArrowRight, ArrowLeft } from "lucide-react";
import { useQuestion } from "../hooks/useQuestion";
import { CreateQuestionRequest } from "../states/types";

interface CreateQuestionDialogProps {
  onSuccess?: () => void;
}

export const CreateQuestionDialog: React.FC<CreateQuestionDialogProps> = ({
  onSuccess,
}) => {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const { createQuestion, qaConfig, fetchQAConfig, questionsLoading } =
    useQuestion();

  const [formData, setFormData] = useState<CreateQuestionRequest>({
    content: {
      title: "",
      question: "",
    },
    asker: {
      fullName: "",
      email: "",
      phoneNumber: "",
      address: "",
    },
    topic: "",
  });

  // Load QA config on mount to get topics
  useEffect(() => {
    if (!qaConfig) {
      fetchQAConfig();
    }
  }, [qaConfig, fetchQAConfig]);

  // Reset form when dialog opens
  useEffect(() => {
    if (open) {
      setCurrentStep(1);
      setFormData({
        content: {
          title: "",
          question: "",
        },
        asker: {
          fullName: "",
          email: "",
          phoneNumber: "",
          address: "",
        },
        topic: "",
      });
    }
  }, [open]);

  const handleInputChange = (field: string, value: string) => {
    if (field.startsWith("content.")) {
      const contentField = field.replace("content.", "");
      setFormData((prev) => ({
        ...prev,
        content: {
          ...prev.content,
          [contentField]: value,
        },
      }));
    } else if (field.startsWith("asker.")) {
      const askerField = field.replace("asker.", "");
      setFormData((prev) => ({
        ...prev,
        asker: {
          ...prev.asker,
          [askerField]: value,
        },
      }));
    } else {
      setFormData((prev) => ({
        ...prev,
        [field]: value,
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setLoading(true);
      console.log("Creating question:", formData);

      await createQuestion(formData);

      console.log("Question created successfully");
      setOpen(false);
      onSuccess?.();
    } catch (error) {
      console.error("Error creating question:", error);
    } finally {
      setLoading(false);
    }
  };

  const isStep1Valid = () => {
    return formData.asker.fullName.trim() && formData.asker.email.trim();
  };

  const isStep2Valid = () => {
    return (
      formData.content.title.trim() &&
      formData.content.question.trim() &&
      formData.topic
    );
  };

  const handleNextStep = () => {
    if (currentStep === 1 && isStep1Valid()) {
      setCurrentStep(2);
    }
  };

  const handlePrevStep = () => {
    if (currentStep === 2) {
      setCurrentStep(1);
    }
  };

  const renderStep1 = () => (
    <div className="space-y-6">
      <div className="flex items-center gap-3 mb-6">
        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-100">
          <User className="h-4 w-4 text-gray-600" />
        </div>
        <h3 className="text-lg font-medium text-gray-900">
          Bước 1: Thông tin người hỏi
        </h3>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Full Name */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Họ và tên <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            value={formData.asker.fullName}
            onChange={(e) =>
              handleInputChange("asker.fullName", e.target.value)
            }
            placeholder="Nhập họ và tên"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            disabled={loading}
            required
          />
        </div>

        {/* Email */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Email <span className="text-red-500">*</span>
          </label>
          <input
            type="email"
            value={formData.asker.email}
            onChange={(e) => handleInputChange("asker.email", e.target.value)}
            placeholder="Nhập địa chỉ email"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            disabled={loading}
            required
          />
        </div>

        {/* Phone */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Số điện thoại
          </label>
          <input
            type="text"
            value={formData.asker.phoneNumber}
            onChange={(e) =>
              handleInputChange("asker.phoneNumber", e.target.value)
            }
            placeholder="Nhập số điện thoại"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            disabled={loading}
          />
        </div>

        {/* Address */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Địa chỉ
          </label>
          <input
            type="text"
            value={formData.asker.address}
            onChange={(e) => handleInputChange("asker.address", e.target.value)}
            placeholder="Nhập địa chỉ"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            disabled={loading}
          />
        </div>
      </div>
    </div>
  );

  const renderStep2 = () => (
    <div className="space-y-6">
      <div className="flex items-center gap-3 mb-6">
        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-100">
          <HelpCircle className="h-4 w-4 text-gray-600" />
        </div>
        <h3 className="text-lg font-medium text-gray-900">
          Bước 2: Nội dung câu hỏi
        </h3>
      </div>

      <div className="space-y-4">
        {/* Title */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Tiêu đề câu hỏi <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            value={formData.content.title}
            onChange={(e) => handleInputChange("content.title", e.target.value)}
            placeholder="Nhập tiêu đề câu hỏi"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            disabled={loading}
            required
          />
        </div>

        {/* Topic */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Chủ đề <span className="text-red-500">*</span>
          </label>
          <select
            value={formData.topic}
            onChange={(e) => handleInputChange("topic", e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            disabled={loading}
            required
          >
            <option value="">Chọn chủ đề</option>
            {qaConfig?.topics?.map((topic) => (
              <option key={topic} value={topic}>
                {topic}
              </option>
            ))}
          </select>
        </div>

        {/* Question Content */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Nội dung câu hỏi <span className="text-red-500">*</span>
          </label>
          <textarea
            value={formData.content.question}
            onChange={(e) =>
              handleInputChange("content.question", e.target.value)
            }
            placeholder="Nhập nội dung câu hỏi chi tiết"
            rows={5}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
            disabled={loading}
            required
          />
        </div>
      </div>
    </div>
  );

  return (
    <>
      <Button onClick={() => setOpen(true)}>
        <Plus className="h-4 w-4 mr-2" />
        Tạo câu hỏi mới
      </Button>

      <CustomDialog
        open={open}
        onClose={() => setOpen(false)}
        title="Tạo câu hỏi"
        maxWidth="2xl"
      >
        <div className="text-sm text-gray-600 mb-6">
          {currentStep === 1
            ? "Vui lòng nhập thông tin của bạn để tiếp tục"
            : "Nhập nội dung câu hỏi bạn muốn gửi"}
        </div>

        {/* Progress Indicator */}
        <div className="flex items-center mb-8">
          <div
            className={`flex h-8 w-8 items-center justify-center rounded-full text-sm font-medium ${
              currentStep >= 1
                ? "bg-blue-600 text-white"
                : "bg-gray-200 text-gray-600"
            }`}
          >
            1
          </div>
          <div
            className={`flex-1 h-1 mx-4 ${
              currentStep >= 2 ? "bg-blue-600" : "bg-gray-200"
            }`}
          />
          <div
            className={`flex h-8 w-8 items-center justify-center rounded-full text-sm font-medium ${
              currentStep >= 2
                ? "bg-blue-600 text-white"
                : "bg-gray-200 text-gray-600"
            }`}
          >
            2
          </div>
        </div>

        <form onSubmit={handleSubmit}>
          {currentStep === 1 ? renderStep1() : renderStep2()}

          <DialogFooter>
            {currentStep === 2 && (
              <DialogButton
                onClick={handlePrevStep}
                variant="secondary"
                disabled={loading}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Quay lại
              </DialogButton>
            )}

            <DialogButton
              onClick={() => setOpen(false)}
              variant="secondary"
              disabled={loading}
            >
              Hủy bỏ
            </DialogButton>

            {currentStep === 1 ? (
              <DialogButton
                onClick={handleNextStep}
                variant="primary"
                disabled={!isStep1Valid() || loading}
              >
                Tiếp theo
                <ArrowRight className="h-4 w-4 ml-2" />
              </DialogButton>
            ) : (
              <DialogButton
                type="submit"
                variant="primary"
                disabled={!isStep2Valid() || loading || questionsLoading}
                loading={loading}
              >
                {loading ? "Đang tạo..." : "Tạo câu hỏi"}
              </DialogButton>
            )}
          </DialogFooter>
        </form>
      </CustomDialog>
    </>
  );
};
