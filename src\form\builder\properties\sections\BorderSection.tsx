/**
 * Border Section Component
 * For border width, color, and style controls with popover UX
 */

import React from 'react';
import { FormNode } from '@/form/types';
import { PropertiesFormProps } from '../types';
import { Label } from '@/components/ui/label';
import { ColorPicker } from '../input/ColorPicker';
import { BorderPopover } from '../input/BorderPopover';

export const BorderSection: React.FC<PropertiesFormProps> = ({
  node,
  onChange,
  disabled = false
}) => {
  const currentClasses = node.styles?.container || '';

  // Extract current border properties
  const getCurrentBorderWidth = () => {
    if (currentClasses.includes('border-0')) return '0';
    if (currentClasses.includes('border-2')) return '2';
    if (currentClasses.includes('border-4')) return '4';
    if (currentClasses.includes('border-8')) return '8';
    if (currentClasses.includes('border') && !currentClasses.includes('border-')) return '1';
    return 'none';
  };

  const getCurrentBorderStyle = () => {
    if (currentClasses.includes('border-solid')) return 'solid';
    if (currentClasses.includes('border-dashed')) return 'dashed';
    if (currentClasses.includes('border-dotted')) return 'dotted';
    if (currentClasses.includes('border-double')) return 'double';
    if (currentClasses.includes('border-none')) return 'none';
    return 'solid'; // default
  };

  const getCurrentBorderColor = () => {
    const colorMatch = currentClasses.match(/border-(\w+-\d+|\w+)/);
    if (colorMatch) {
      const color = colorMatch[1];
      // Filter out non-color border classes
      if (!['solid', 'dashed', 'dotted', 'double', 'none', '0', '2', '4', '8'].includes(color)) {
        return color;
      }
    }
    return 'gray-300'; // default
  };

  const getCurrentBorderRadius = () => {
    if (currentClasses.includes('rounded-none')) return 'none';
    if (currentClasses.includes('rounded-sm')) return 'sm';
    if (currentClasses.includes('rounded-md')) return 'md';
    if (currentClasses.includes('rounded-lg')) return 'lg';
    if (currentClasses.includes('rounded-xl')) return 'xl';
    if (currentClasses.includes('rounded-2xl')) return '2xl';
    if (currentClasses.includes('rounded-3xl')) return '3xl';
    if (currentClasses.includes('rounded-full')) return 'full';
    if (currentClasses.includes('rounded')) return 'base';
    return 'none';
  };

  // Update border properties
  const updateBorderWidth = (width: string) => {
    let newClasses = currentClasses;

    // Remove existing border width classes
    newClasses = newClasses
      .replace(/\bborder-[0-8]\b/g, '')
      .replace(/\bborder\b(?!-)/g, '') // Remove standalone 'border' but not 'border-xxx'
      .replace(/\s+/g, ' ')
      .trim();

    // Add new border width class
    if (width && width !== 'none') {
      if (width === '1') {
        newClasses = `${newClasses} border`.trim();
      } else {
        newClasses = `${newClasses} border-${width}`.trim();
      }
    }

    onChange({
      styles: {
        ...node.styles,
        container: newClasses
      }
    });
  };

  const updateBorderStyle = (style: string) => {
    let newClasses = currentClasses;

    // Remove existing border style classes
    newClasses = newClasses
      .replace(/\bborder-(solid|dashed|dotted|double|none)\b/g, '')
      .replace(/\s+/g, ' ')
      .trim();

    // Add new border style class
    if (style && style !== 'solid') { // solid is default, no need to add class
      newClasses = `${newClasses} border-${style}`.trim();
    }

    onChange({
      styles: {
        ...node.styles,
        container: newClasses
      }
    });
  };

  const updateBorderColor = (color: string) => {
    let newClasses = currentClasses;

    // Remove existing border color classes (more specific pattern)
    newClasses = newClasses
      .replace(/\bborder-(?!solid|dashed|dotted|double|none|[0-8]\b)[\w-]+/g, '')
      .replace(/\s+/g, ' ')
      .trim();

    // Add new border color class
    if (color && color !== 'gray-300') { // gray-300 is often default
      newClasses = `${newClasses} border-${color}`.trim();
    }

    onChange({
      styles: {
        ...node.styles,
        container: newClasses
      }
    });
  };

  const updateBorderRadius = (radius: string) => {
    let newClasses = currentClasses;
    
    // Remove existing rounded classes
    newClasses = newClasses
      .replace(/\brounded(-\w+)?\b/g, '')
      .replace(/\s+/g, ' ')
      .trim();

    // Add new rounded class
    if (radius && radius !== 'none') {
      if (radius === 'base') {
        newClasses = `${newClasses} rounded`.trim();
      } else {
        newClasses = `${newClasses} rounded-${radius}`.trim();
      }
    }

    onChange({
      styles: {
        ...node.styles,
        container: newClasses
      }
    });
  };

  return (
    <div className="space-y-2">
      {/* Border Width */}
      <div className="space-y-1">
        <Label className="text-xs">Rộng</Label>
        <BorderPopover
          value={getCurrentBorderWidth()}
          onValueChange={updateBorderWidth}
          disabled={disabled}
          type="width"
          placeholder="Chọn độ rộng"
        />
      </div>

      {/* Border Style */}
      <div className="space-y-1">
        <Label className="text-xs">Kiểu</Label>
        <BorderPopover
          value={getCurrentBorderStyle()}
          onValueChange={updateBorderStyle}
          disabled={disabled}
          type="style"
          placeholder="Chọn kiểu viền"
        />
      </div>

      {/* Border Color */}
      <div className="space-y-1">
        <Label className="text-xs">Màu</Label>
        <ColorPicker
          value={getCurrentBorderColor()}
          onChange={updateBorderColor}
          disabled={disabled}
          placeholder="Chọn màu viền"
          className="w-full text-xs"
        />
      </div>

      {/* Border Radius */}
      <div className="space-y-1">
        <Label className="text-xs">Bo góc</Label>
        <BorderPopover
          value={getCurrentBorderRadius()}
          onValueChange={updateBorderRadius}
          disabled={disabled}
          type="radius"
          placeholder="Chọn bo góc"
        />
      </div>
    </div>
  );
};