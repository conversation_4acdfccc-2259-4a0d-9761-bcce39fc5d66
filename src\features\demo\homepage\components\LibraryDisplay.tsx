import React from "react";
import { cn } from "@/lib/utils";
import { LibraryPanel } from "./LibraryPanel";
import { LibraryItem } from "../data/mockData";

/**
 * Props interface for LibraryDisplay component
 */
export interface LibraryDisplayProps {
  /** Array of library items to display */
  items: LibraryItem[];
  /** Additional CSS classes */
  className?: string;
  /** Click handler for individual items */
  onItemClick?: (item: LibraryItem) => void;
}

/**
 * LibraryDisplay component contains two LibraryPanel instances for images and videos
 * 
 * @param props - Component props
 * @returns JSX element for library display
 */
export const LibraryDisplay: React.FC<LibraryDisplayProps> = ({
  items,
  className,
  onItemClick,
}) => {
  return (
    <div className={cn("grid grid-cols-1 lg:grid-cols-2 gap-6", className)}>
      {/* Image Library Panel */}
      <LibraryPanel
        type="image"
        items={items}
        title="Thư viện hình ảnh"
        onItemClick={onItemClick}
      />
      
      {/* Video Library Panel */}
      <LibraryPanel
        type="video"
        items={items}
        title="Thư viện video"
        onItemClick={onItemClick}
      />
    </div>
  );
};
