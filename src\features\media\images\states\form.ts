import { FormNode } from "@/form/types";

export const createAlbumFormNode: FormNode = {
  id: "create-album-form",
  type: "frame",
  styles: {
    container: "space-y-6",
  },
  children: [
    {
      id: "album-title",
      type: "title",
      properties: {
        label: "Thông tin album",
        element: "h3",
      },
      styles: {
        label: "text-lg font-semibold mb-4",
      },
    },
    {
      id: "album-name",
      type: "field",
      field: {
        objectKey: "name",
        component: "TextInput",
        defaultValue: "",
        dataType: "string",
      },
      properties: {
        label: "Tên album",
        placeholder: "Nhập tên album",
      },
      validation: {
        required: {
          value: true,
          error: "<PERSON>ui lòng nhập tên album",
        },
        maxLength: {
          value: 200,
          error: "Tên album không được vượt quá 200 ký tự",
        },
      },
    },
    {
      id: "album-description",
      type: "field",
      field: {
        objectKey: "description.text",
        component: "TextAreaInput",
        defaultValue: "",
        dataType: "string",
      },
      properties: {
        label: "<PERSON><PERSON> tả",
        placeholder: "Nhập mô tả cho album (tùy chọn)",
        rows: 4,
      },
      validation: {
        maxLength: {
          value: 1000,
          error: "Mô tả không được vượt quá 1000 ký tự",
        },
      },
    },
  ],
};

export const editAlbumFormNode: FormNode = {
  id: "edit-album-form",
  type: "frame",
  styles: {
    container: "space-y-6",
  },
  children: [
    {
      id: "album-title",
      type: "title",
      properties: {
        label: "Chỉnh sửa thông tin album",
        element: "h3",
      },
      styles: {
        label: "text-lg font-semibold mb-4",
      },
    },
    {
      id: "album-name",
      type: "field",
      field: {
        objectKey: "name",
        component: "TextInput",
        defaultValue: "",
        dataType: "string",
      },
      properties: {
        label: "Tên album",
        placeholder: "Nhập tên album",
      },
      validation: {
        required: {
          value: true,
          error: "Vui lòng nhập tên album",
        },
        maxLength: {
          value: 200,
          error: "Tên album không được vượt quá 200 ký tự",
        },
      },
    },
    {
      id: "album-description",
      type: "field",
      field: {
        objectKey: "description.text",
        component: "TextAreaInput",
        defaultValue: "",
        dataType: "string",
      },
      properties: {
        label: "Mô tả",
        placeholder: "Nhập mô tả cho album (tùy chọn)",
        rows: 4,
      },
      validation: {
        maxLength: {
          value: 1000,
          error: "Mô tả không được vượt quá 1000 ký tự",
        },
      },
    },
  ],
};
