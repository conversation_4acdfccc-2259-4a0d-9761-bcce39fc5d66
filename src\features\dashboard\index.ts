// Pages
export { DashboardPage } from "./pages/loadable";

// Components
export { StatsCard } from "./components/StatsCard";
export { RecentActivities } from "./components/RecentActivities";
export { SimpleChart } from "./components/SimpleChart";
export { RecordStatsCard } from "./components/RecordStatsCard";
export { AlertsPanel } from "./components/AlertsPanel";
export { SimpleCalendar } from "./components/SimpleCalendar";
export { InformationPortalTab } from "./components/InformationPortalTab";
export { DataWarehouseTab } from "./components/DataWarehouseTab";

// Data & Types
export type {
  DashboardData,
  DashboardStats,
  ChartData,
  RecentActivity,
  RecordStats,
  ProcessingTrendData,
  RecordStatusData,
  RecordTypeData,
  AlertNotification,
  CalendarEvent,
  DataWarehouseData,
} from "./data/mockData";
export { generateMockDashboardData } from "./data/mockData";
