/**
 * Form System - Core Types
 */

// ============================================================================
// BASIC TYPES
// ============================================================================

/**
 * All possible field data types in our form system
 */
export enum FieldDataType {
  String = "string",
  Number = "number",
  Boolean = "boolean",
  StringArray = "string[]",
  NumberArray = "number[]",
  BooleanArray = "boolean[]",
  Null = "null",
  Undefined = "undefined",
}

/**
 * All possible field values in our form system
 */
export type FieldValue =
  | string
  | number
  | boolean
  | Array<string | number | boolean>
  | null
  | undefined;

/**
 * Final form data output - simple key-value pairs
 */
export type FormData = Record<string, FieldValue>;

// ============================================================================
// STYLING SYSTEM
// ============================================================================

/**
 * Styles configuration for FormNode
 */
export interface NodeStyles {
  container?: string;
  label?: string;
  content?: string;
  field?: string;
  error?: string;
}

// ============================================================================
// FORM NODE ARCHITECTURE
// ============================================================================

/**
 * All possible FormNode types - có thể mở rộng trong tương lai
 */
export type FormNodeType = "frame" | "field" | "control" | "title" | "redirect";

/**
 * Validation rules for FormNode
 */
export interface ValidationRules {
  required?: {
    value: boolean;
    error: string;
  };
  min?: {
    value: number;
    error: string;
  };
  max?: {
    value: number;
    error: string;
  };
  minLength?: {
    value: number;
    error: string;
  };
  maxLength?: {
    value: number;
    error: string;
  };
  pattern?: {
    value: string;
    error: string;
  };
  matchField?: {
    value: string; // field name to match against
    error: string;
  };
}

/**
 * Form field mapping for data binding
 */
export interface FormNodeField {
  objectKey: string; // nested object thì các key sẽ nối nhau bằng kí tự ->
  defaultValue: FieldValue;
  dataType: string;
  component: string; // Component name to render (magic của AutoForm)
}

/**
 * Base FormNode props - all nodes use this interface
 */
export interface FormNodeProps {
  id: string;
  type: FormNodeType;
  styles?: NodeStyles;
  properties?: Record<string, FieldValue>;
  validation?: ValidationRules;
  field?: FormNodeField;
  children?: FormNode[]; // optional children
}

/**
 * Union of all FormNode types - will be implemented by factories
 */
export type FormNode = FormNodeProps;

// Node-based form config type for new AutoForm system
export interface FormConfig {
  code: string;
  name: string;
  note: string;
  config: FormNode;
}
