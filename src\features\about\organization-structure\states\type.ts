// ============================================================================
// Organization Structure Types
// ============================================================================

/**
 * Contact item structure for organization contact information
 */
export interface ContactItem {
  /** Contact person or department name */
  name: string;
  /** Phone number */
  phone: string;
  /** Email address */
  email: string;
  /** Physical address */
  address: string;
}

/**
 * Contact hotline response structure
 */
export interface HotlineConfig {
  /** Array of contact items */
  hotlines: HotlineItem[];
}

/**
 * Hotline item structure for emergency/support contact information
 */
export interface HotlineItem {
  /** Hotline service name or department */
  name: string;
  /** Hotline phone number */
  phone: string;
  /** Hotline email address */
  email: string;
}

/**
 * Contact configuration response structure
 */
export interface ContactConfig {
  /** Array of contact items */
  contacts: ContactItem[];
}

// ============================================================================
// Redux State Types
// ============================================================================

/**
 * Contact state for Redux store
 */
export interface ContactState {
  /** Contact configuration data */
  data: ContactConfig | null;
  /** Loading state for contact data */
  loading: boolean;
  /** Error message for contact operations */
  error: string | null;
  /** Last updated timestamp */
  lastUpdated: number | null;
}

/**
 * Hotline state for Redux store
 */
export interface HotlineState {
  /** Hotline configuration data */
  data: HotlineConfig | null;
  /** Loading state for hotline data */
  loading: boolean;
  /** Error message for hotline operations */
  error: string | null;
  /** Last updated timestamp */
  lastUpdated: number | null;
}

/**
 * Combined organization structure state
 */
export interface OrganizationStructureState {
  /** Contact information state */
  contacts: ContactState;
  /** Hotline information state */
  hotlines: HotlineState;
}

// ============================================================================
// Component Props Types
// ============================================================================

/**
 * Props for contact information display component
 */
export interface ContactInfoProps {
  /** Contact configuration data */
  data: ContactConfig | null;
  /** Loading state */
  loading: boolean;
  /** Error message */
  error: string | null;
}

/**
 * Props for hotline information display component
 */
export interface HotlineInfoProps {
  /** Hotline configuration data */
  data: HotlineConfig | null;
  /** Loading state */
  loading: boolean;
  /** Error message */
  error: string | null;
}

/**
 * Props for organization structure page component
 */
export interface OrganizationStructurePageProps {
  /** Optional custom className for styling */
  className?: string;
}

// ============================================================================
// API Response Types
// ============================================================================

/**
 * API response type for contact configuration
 */
export interface ContactConfigResponse {
  /** Success status */
  success: boolean;
  /** Response message */
  message: string;
  /** Contact configuration data */
  data: ContactConfig;
}

/**
 * API response type for hotline configuration
 */
export interface HotlineConfigResponse {
  /** Success status */
  success: boolean;
  /** Response message */
  message: string;
  /** Hotline configuration data */
  data: HotlineConfig;
}
