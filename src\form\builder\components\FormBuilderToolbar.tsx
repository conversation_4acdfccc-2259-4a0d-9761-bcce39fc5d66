/**
 * Form Builder Toolbar
 * Top toolbar with form actions and mode controls
 */

import React, { useState } from "react";
import { useFormBuilder } from "../context/FormBuilderContext";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { 
  Save, 
  Download, 
  Upload, 
  RotateCcw, 
  RotateCw,
  Eye,
  Code,
  Edit,
  FileText,
  Edit2
} from "lucide-react";
import { FormConfigDialog } from "./FormConfigDialog";

/**
 * Form Builder Toolbar Component
 */
export const FormBuilderToolbar: React.FC = () => {
  const { 
    state, 
    saveForm, 
    newForm, 
    exportForm, 
    importForm,
    undo, 
    redo, 
    canUndo, 
    canRedo,
    setEditorMode,
    validateForm,
    updateFormConfig
  } = useFormBuilder();
  
  const [showConfigDialog, setShowConfigDialog] = useState(false);

  const handleExport = () => {
    const config = exportForm();
    const blob = new Blob([config], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${state.formConfig.code}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const handleImport = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
          const content = e.target?.result as string;
          if (content) {
            importForm(content);
          }
        };
        reader.readAsText(file);
      }
    };
    input.click();
  };

  const isFormValid = validateForm();

  return (
    <div className="bg-white border-b border-gray-200 px-6 py-3">
      <div className="flex items-center justify-between">
        {/* Left Side - Form Info */}
        <div className="flex items-center gap-4">
          <div>
            <h1 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
              <FileText className="h-5 w-5" />
              {state.formConfig.name}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowConfigDialog(true)}
                className="h-6 w-6 p-0 ml-1"
              >
                <Edit2 className="h-3 w-3" />
              </Button>
              {state.isDirty && (
                <Badge variant="secondary" className="ml-2">
                  Unsaved
                </Badge>
              )}
            </h1>
          </div>
          
          {!isFormValid && (
            <Badge variant="destructive">
              Invalid Form
            </Badge>
          )}
        </div>

        {/* Center - Mode Controls */}
        <div className="flex items-center gap-2">
          <div className="flex bg-gray-100 rounded-lg p-1">
            <Button
              variant={state.editor.mode === 'design' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setEditorMode('design')}
              className="h-8"
            >
              <Edit className="h-4 w-4 mr-1" />
              Design
            </Button>
            <Button
              variant={state.editor.mode === 'preview' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setEditorMode('preview')}
              className="h-8"
            >
              <Eye className="h-4 w-4 mr-1" />
              Preview
            </Button>
            <Button
              variant={state.editor.mode === 'code' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setEditorMode('code')}
              className="h-8"
            >
              <Code className="h-4 w-4 mr-1" />
              Code
            </Button>
          </div>
        </div>

        {/* Right Side - Actions */}
        <div className="flex items-center gap-2">
          {/* History Controls */}
          <Button
            size="sm"
            variant="outline"
            onClick={undo}
            disabled={!canUndo}
            className="h-8"
            title="Undo"
          >
            <RotateCcw className="h-4 w-4" />
          </Button>
          
          <Button
            size="sm"
            variant="outline"
            onClick={redo}
            disabled={!canRedo}
            className="h-8"
            title="Redo"
          >
            <RotateCw className="h-4 w-4" />
          </Button>
          
          <Separator orientation="vertical" className="h-6" />
          
          {/* File Operations */}
          <Button
            size="sm"
            variant="outline"
            onClick={newForm}
            className="h-8"
            title="New Form"
          >
            <FileText className="h-4 w-4 mr-1" />
            New
          </Button>
          
          <Button
            size="sm"
            variant="outline"
            onClick={handleImport}
            className="h-8"
            title="Import Form"
          >
            <Upload className="h-4 w-4 mr-1" />
            Import
          </Button>
          
          <Button
            size="sm"
            variant="outline"
            onClick={handleExport}
            className="h-8"
            title="Export Form"
          >
            <Download className="h-4 w-4 mr-1" />
            Export
          </Button>
          
          <Separator orientation="vertical" className="h-6" />
          
          {/* Save */}
          <Button
            size="sm"
            onClick={saveForm}
            disabled={!isFormValid || !state.isDirty}
            className="h-8"
          >
            <Save className="h-4 w-4 mr-1" />
            Save Form
          </Button>
        </div>
      </div>
      
      {/* Form Config Dialog */}
      <FormConfigDialog
        open={showConfigDialog}
        onOpenChange={setShowConfigDialog}
        formConfig={state.formConfig}
        onSave={updateFormConfig}
      />
    </div>
  );
};