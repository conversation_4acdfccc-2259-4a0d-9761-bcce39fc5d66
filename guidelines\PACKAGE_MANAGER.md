# 📦 Package Manager Policy

## 🎯 Rule: Yarn Only

This project uses **Yarn exclusively**. No exceptions.

## ✅ Allowed Commands

```bash
yarn install
yarn dev
yarn build
yarn lint
yarn preview
yarn check-pm
```

## ❌ Blocked Commands

```bash
npm install      # ❌ Will exit with error
npm run dev      # ❌ Will exit with error
pnpm install     # ❌ Will exit with error
```

## 🔧 How It Works

- Every script runs `check-package-manager.js` first
- Detects package manager from `npm_config_user_agent`
- Blocks non-yarn usage with clear error messages
- Checks for lockfile conflicts

## 🚨 Error Example

```
🚫 ERROR: This project uses Yarn as package manager!

Please use:
  yarn install instead of npm install
  yarn dev instead of npm run dev

To install Yarn: npm install -g yarn
```

## 🛠️ Enforcement Layers

1. **Scripts**: `preinstall`, `dev`, `build` all check package manager
2. **Engines**: `package.json` declares yarn requirement
3. **Lockfiles**: Only `yarn.lock` is tracked, others ignored
4. **NPM Config**: `.npmrc` helps prevent accidental usage

---

**For AI Assistants**: Always use `yarn` commands. This is enforced at runtime and violations will cause build failures.
