import { Post } from "@/features/post/states/types";
import { PageSummary } from "./api";

export type PageMode = "view" | "edit" | "create";

export interface PageState {
  // Record mapping postId -> Post (where PostType is PAGE)
  pages: Record<number, Post>;

  // Lightweight summaries for lists (used in management UI)
  pageSummaries: PageSummary[];

  // Search pagination state
  searchPagination: {
    currentPage: number;
    hasMore: boolean;
    loading: boolean;
  };

  // Currently selected category and its linked page
  selectedCategoryId: number | null;
  selectedPageId: number | null;

  // UI state
  mode: PageMode;
  loading: boolean;
  error: string | null;

  // Track which categories have linked pages
  categoryPageMapping: Record<number, number>; // categoryId -> postId
}

export interface CreatePageRequest {
  title: string;
  slug: string;
  categoryId: number;
  excerpt: {
    image: string;
    description: string;
    files: string[];
  };
}

export interface LinkPageToCategoryRequest {
  categoryId: number;
  pageId: number;
}
