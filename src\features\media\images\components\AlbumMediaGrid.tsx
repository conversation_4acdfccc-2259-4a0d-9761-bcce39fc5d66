import { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { But<PERSON> } from "@/components/ui/button";
import { Loader2, Image as ImageIcon, Video, Plus } from "lucide-react";
import { fetchAlbumMedia, addExistingMediaToAlbum } from "../states/api";
import { Media, Album } from "../states/types";
import { selectCurrentAlbumMedia } from "../states/selector";
import { appendAlbumMedia } from "../states/slices";
import { MediaBreadcrumb } from "@/components/other/MediaBreadcrumb";
import { toast } from "sonner";



interface AlbumMediaGridProps {
  album: Album;
  currentAlbumId: number;
  onBack: () => void;
  onMediaAdded?: () => void;
}

export function AlbumMediaGrid({
  album,
  currentAlbumId,
  onBack,
  onMediaAdded,
}: AlbumMediaGridProps) {
  const dispatch = useDispatch();
  const currentAlbumMedia = useSelector(selectCurrentAlbumMedia);

  const [media, setMedia] = useState<Media[]>([]);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(0);
  const [addingMediaIds, setAddingMediaIds] = useState<Set<number>>(new Set());

  // Get IDs of media already in current album for filtering
  const currentAlbumMediaIds = new Set(currentAlbumMedia.map((m) => m.id));

  const loadMedia = async (pageNum: number = 0, reset: boolean = false, size: number = 8) => {
    if (loading) return;

    setLoading(true);
    try {
      const response = await fetchAlbumMedia(album.id, {
        page: pageNum,
        size: size,
      });

      // Filter out media that already exists in current album
      const filteredMedia = response.data.filter(
        (mediaItem) => !currentAlbumMediaIds.has(mediaItem.id)
      );

      if (reset) {
        setMedia(filteredMedia);
      } else {
        setMedia((prev) => [...prev, ...filteredMedia]);
      }

      setHasMore(response.hasMore || false);
      setPage(pageNum);

      if (response.hasMore && (media.length === 0 && filteredMedia.length === 0)) {
        handleLoadMore(pageNum);
      } else if (response.hasMore && (filteredMedia.length === 0)) {
        handleLoadMore(pageNum);
      }

    } catch (error) {
      toast.error("Không thể tải ảnh từ thư viện: " + error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadMedia(0, true);
  }, [album.id]); // Reload when current album media changes

  const handleLoadMore = (pageNum: number) => {
    if (hasMore && !loading) {
      loadMedia(pageNum + 1, false);
    }
  };

  const handleAddMedia = async (mediaItem: Media) => {
    if (addingMediaIds.has(mediaItem.id)) return;

    setAddingMediaIds((prev) => new Set(prev).add(mediaItem.id));

    try {
      await addExistingMediaToAlbum(currentAlbumId, mediaItem.id);

      // Update Redux state to include the newly added media
      dispatch(appendAlbumMedia([mediaItem]));

      // Remove the media from the display
      setMedia((prev) => prev.filter((m) => m.id !== mediaItem.id));

      toast.success(`Đã thêm "${mediaItem.name}" vào thư viện`);

      // Notify parent component
      onMediaAdded?.();
    } catch (error) {
      toast.error(`Không thể thêm "${mediaItem.name}" vào thư viện: ` + error);
    } finally {
      setAddingMediaIds((prev) => {
        const newSet = new Set(prev);
        newSet.delete(mediaItem.id);
        return newSet;
      });
    }
  };

  let contentRender = (<></>);

  if (loading && media.length === 0) {
    contentRender = (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-2" />
          <p className="text-sm text-muted-foreground">Đang tải ảnh...</p>
        </div>
      </div>
    );
  } else if (media.length === 0 && !loading) {
    contentRender = (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="w-16 h-16 bg-muted rounded-lg flex items-center justify-center mx-auto mb-3">
            {album.type === "image" ? (
              <ImageIcon className="w-8 h-8 text-muted-foreground" />
            ) : (
              <Video className="w-8 h-8 text-muted-foreground" />
            )}
          </div>
          <p className="text-sm text-muted-foreground">
            {currentAlbumMediaIds.size > 0
              ? `Tất cả ${album.type === "image" ? "ảnh" : "video"} trong thư viện này đã được thêm vào thư viện hiện tại`
              : `Thư viện này chưa có ${album.type === "image" ? "ảnh" : "video"} nào`
            }
          </p>
        </div>
      </div>
    )
  } else {
    contentRender = (
      <div className="space-y-4">
        <div className="grid grid-cols-4 gap-2">
          {media.map((mediaItem) => {
            const isAdding = addingMediaIds.has(mediaItem.id);

            return (
              <div
                key={mediaItem.id}
                className="relative group aspect-square rounded-lg overflow-hidden bg-muted cursor-pointer"
                onClick={() => !isAdding && handleAddMedia(mediaItem)}
              >
                {/* Media Image */}
                <img
                  src={mediaItem.src}
                  alt={mediaItem.name}
                  className="w-full h-full object-cover"
                />

                {/* Overlay */}
                <div className="absolute inset-0 bg-black/0 group-hover:bg-black/50 transition-colors flex items-center justify-center">
                  {isAdding ? (
                    <Loader2 className="w-6 h-6 text-white animate-spin" />
                  ) : (
                    <Plus className="w-6 h-6 text-white opacity-0 group-hover:opacity-100 transition-opacity" />
                  )}
                </div>

                {/* Media Type Indicator */}
                {mediaItem.type === "video" && (
                  <div className="absolute top-2 right-2">
                    <Video className="w-4 h-4 text-white drop-shadow-lg" />
                  </div>
                )}
              </div>
            );
          })}
        </div>

        {/* Load More Button */}
        {
          hasMore && (
            <div className="flex justify-center">
              <Button
                variant="outline"
                onClick={() => handleLoadMore(page)}
                disabled={loading}
                size="sm"
              >
                {loading && <Loader2 className="w-4 h-4 animate-spin mr-2" />}
                Xem thêm
              </Button>
            </div>
          )
        }
      </div >
    )
  }

  return (
    <div className="space-y-4">
      {/* Breadcrumb Navigation */}
      <div className="space-y-2">
        <MediaBreadcrumb
          currentPath={album.name}
          onBack={onBack}
          backText="Tất cả thư viện"
        />
        <p className="text-sm text-muted-foreground">
          {media.length} {album.type === "image" ? "ảnh" : "video"} có sẵn
        </p>
      </div>
      {contentRender}
    </div >
  );
}
