import React, { useEffect, useState, useCallback, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import type { AppDispatch } from "@/store/store";

import { ConfigPageLayout } from "../../shared/components/layouts";
import { PortalLinkTable } from "../components/PortalLinkTable";
import { PortalLinkPreview } from "../components/PortalLinkPreview";
import type { PortalLinkConfig, PortalLinkMetadata } from "../states/type";
import {
  fetchPortalLinkAsync,
  updatePortalLinkAsync,
  setDirty,
  clearError,
} from "../states/slices";
import {
  selectPortalLinkData,
  selectPortalLinkSavedData,
  selectPortalLinkLoading,
  selectPortalLinkSaving,
  selectPortalLinkError,
  selectPortalLinkIsDirty,
  selectPortalLinkIsOperating,
} from "../states/selector";

const PORTAL_LINK_METADATA: PortalLinkMetadata = {
  title: '<PERSON><PERSON><PERSON> liên kết',
  description: '<PERSON>uản lý các cổng liên kết hiển thị trên trang chủ',
  type: 'array',
  arrayKey: 'portalLinks',
  fields: [
    {
      key: 'title',
      label: 'Tiêu đề',
      type: 'text',
      required: true,
      placeholder: 'Nhập tiêu đề liên kết'
    },
    {
      key: 'url',
      label: 'URL',
      type: 'url',
      required: true,
      placeholder: 'https://example.com'
    },
    {
      key: 'image',
      label: 'Hình ảnh',
      type: 'url',
      required: true,
      placeholder: 'https://example.com/image.jpg'
    },
  ],
};

export const PortalLinkManagementPage: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const data = useSelector(selectPortalLinkData);
  const savedData = useSelector(selectPortalLinkSavedData);
  const loading = useSelector(selectPortalLinkLoading);
  const saving = useSelector(selectPortalLinkSaving);
  const error = useSelector(selectPortalLinkError);
  const isDirty = useSelector(selectPortalLinkIsDirty);
  const isOperating = useSelector(selectPortalLinkIsOperating);

  const [currentData, setCurrentData] = useState<PortalLinkConfig | null>(null);
  const [isValid, setIsValid] = useState(true);
  const [showPreview, setShowPreview] = useState(false);
  const [showValidationWarning, setShowValidationWarning] = useState(false);
  const validateTableRef = useRef<(() => boolean) | undefined>(undefined);

  useEffect(() => {
    dispatch(fetchPortalLinkAsync());
  }, [dispatch]);

  useEffect(() => {
    if (data) {
      setCurrentData(data);
    }
  }, [data]);

  useEffect(() => {
    return () => {
      dispatch(clearError());
    };
  }, [dispatch]);

  // ============================================================================
  // Event Handlers
  // ============================================================================

  const handleDataChange = useCallback((newData: PortalLinkConfig) => {
    setCurrentData(newData);
    dispatch(setDirty(true));
  }, [dispatch]);

  const handleValidationChange = useCallback((valid: boolean) => {
    setIsValid(valid);
  }, []);

  const handleSave = useCallback(async () => {
    // Validate data before saving
    const isDataValid = validateTableRef.current ? validateTableRef.current() : true;

    if (!isDataValid) {
      setShowValidationWarning(true);
      return;
    }

    setShowValidationWarning(false);

    if (!currentData) {
      return;
    }

    try {
      await dispatch(updatePortalLinkAsync(currentData)).unwrap();
    } catch {
      // Error is handled by the async thunk
    }
  }, [dispatch, currentData, validateTableRef]);

  const handleRefresh = useCallback(() => {
    dispatch(fetchPortalLinkAsync());
  }, [dispatch]);

  const handlePreview = useCallback(() => {
    setShowPreview(!showPreview);
  }, [showPreview]);

  const saveDisabled = !isValid || isOperating || !currentData;

  return (
    <ConfigPageLayout
      title="Quản lý cổng liên kết"
      description="Cấu hình các liên kết nhanh đến các trang web quan trọng"
      loading={saving}
      error={error}
      onSave={handleSave}
      onRefresh={handleRefresh}
      onPreview={handlePreview}
      showPreview={showPreview}
      saveDisabled={saveDisabled}
      isDirty={isDirty}
      hasValidationErrors={showValidationWarning}
    >
      <div className="flex flex-col h-full space-y-6">
        <div className="flex-shrink-0">
          <PortalLinkTable
            metadata={PORTAL_LINK_METADATA}
            data={currentData}
            loading={loading}
            onDataChange={handleDataChange}
            onValidationChange={handleValidationChange}
            onValidationRequest={validateTableRef}
          />
        </div>

        {showPreview && (
          <PortalLinkPreview
            data={savedData}
            visible={showPreview}
          />
        )}
      </div>
    </ConfigPageLayout>
  );
};

export default PortalLinkManagementPage;
