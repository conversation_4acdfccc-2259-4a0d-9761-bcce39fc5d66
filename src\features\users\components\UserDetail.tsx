import { useEffect } from "react";
import { useAppSelector } from "@/store/rootReducer";
import { selectCurrentUser } from "../states/selectors";
import { useAppDispatch } from "@/store/rootReducer";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { UserCard } from "./UserCard";
import { useSwitchMode } from "../states/hook";
import { useSearchParams } from "react-router-dom";
import { setCurrentUser } from "../states/slices";
import { fetchUser } from "../states/api";
const UserDetail = () => {
  const switchMode = useSwitchMode();
  const currentUser = useAppSelector(selectCurrentUser);
  const dispatch = useAppDispatch();
  const [searchParams] = useSearchParams();
  const userId = searchParams.get("user");;

  // Chuyển dispatch vào useEffect
  useEffect(() => {
    if (!currentUser && !userId) {
      switchMode("list");
      return;
    }
    if (userId && (!currentUser || currentUser.id !== Number(userId))) {
      fetchUser(userId).then((res) => {
        if (res.status === 200 && res.data.data) {
          dispatch(setCurrentUser(res.data.data));
        } else {
          switchMode("list");
        }
      });
    }
  }, [currentUser, dispatch, switchMode, userId]);

  return (
    <div className="space-y-4 overflow-auto">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Chi tiết tài khoản</h1>
        <Button className="w-30" onClick={() => switchMode("list")}>
          <ArrowLeft className="w-4 h-4 mr-2" />
          Quay lại
        </Button>
      </div>
      {currentUser ? <UserCard /> : <div>Không tìm thấy tài khoản</div>}
    </div>
  );
};

export default UserDetail;
