import React from "react";

interface SimpleInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label: string;
  id: string;
  type?:
  | "text"
  | "number"
  | "email"
  | "password"
  | "tel"
  | "url"
  | "date"
  | "datetime-local"
  | "file";
  required?: boolean;
  variant?: "default" | "outline" | "danger" | "ghost";
  className?: string;
  direction?: "row" | "col";
}

export const SimpleInput: React.FC<SimpleInputProps> = ({
  label,
  id,
  required = false,
  type = "text",
  variant = "default",
  className = "w-full",
  ...props
}) => {
  const baseClasses =
    "w-full p-1 rounded-md transition-all duration-200 ease-in-out focus:outline-none focus:ring-1";

  const variantClasses: Record<
    NonNullable<SimpleInputProps["variant"]>,
    string
  > = {
    default:
      "text-sm bg-gray-50 border border-text-200 text-text-950 focus:ring-primary-500 focus:border-primary-500",
    outline:
      "text-sm bg-transparent border border-gray-300 text-text-950 focus:ring-primary-500 focus:border-primary-500",
    danger:
      "text-sm bg-gray-50 border border-text-200 text-text-950 focus:ring-red-500 focus:border-red-500",
    ghost:
      "text-sm bg-transparent border-transparent text-text-950 focus:ring-primary-500 focus:border-primary-500",
  };

  const disabledClasses = props.disabled ? "cursor-not-allowed" : "";

  return (
    <div className={className}>
      <label
        htmlFor={id}
        className="block mb-1 font-medium text-text-950 text-sm md:text-base"
      >
        {label}
        {required && <span className="text-red-500"> *</span>}
      </label>
      <input
        type={type}
        id={id}
        className={`${baseClasses} ${variantClasses[variant]} ${disabledClasses}`}
        {...props}
      />
    </div>
  );
};
