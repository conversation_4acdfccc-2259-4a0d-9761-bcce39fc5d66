# Bước 2: <PERSON><PERSON><PERSON> thiện Types, API và Redux

## ✅ Đã hoàn thành

### 1. **<PERSON><PERSON><PERSON> nhật Types theo API Specification**

#### Question Types

```typescript
// Thay đổi từ structure cũ
interface Question {
  id: string;
  question: string;
  answer: string;
  category?: string;
  status: "active" | "inactive";
}

// Sang structure mới theo API spec
interface Question {
  id: number;
  info: QuestionInfo;
  content: QuestionContent;
  status: QuestionStatus; // 'PENDING' | 'PUBLISHED' | 'REJECTED'
  topic: string;
  createdAt: number; // timestamp
  updatedAt: number; // timestamp
}
```

#### Answer Types (Mới)

```typescript
interface Answer {
  id: number;
  questionId: number;
  content: AnswerContent;
}

interface AnswerContent {
  answer: string;
  attachments: string[];
}
```

#### API Response Types

- `ApiResponse<T>` với pagination support
- `ApiError` cho error handling
- Request DTOs: `CreateQuestionRequest`, `UpdateQuestionRequest`, etc.
- Query parameters: `QuestionQueryParams`, `AnswerQueryParams`

### 2. **API Endpoints và Service**

#### API Endpoints trong `endpoints.ts`

```typescript
PORTAL: {
  PUBLIC: {
    QUESTIONS: "/portal/v1/public/questions",
    ANSWERS: "/portal/v1/public/answers",
  },
  PRIVATE: {
    QUESTIONS: "/portal/v1/private/questions",
  },
  ADMIN: {
    QUESTIONS: "/portal/v1/admin/questions",
    ANSWERS: "/portal/v1/admin/answers",
  },
}
```

#### API Service Classes

- **QuestionAPI**: 8 methods covering public/private/admin endpoints
  - `getPublishedQuestions()`, `createQuestion()`, `updateQuestionStatus()`, etc.
- **AnswerAPI**: 5 methods for answer management
  - `getAnswersByQuestionId()`, `createAnswer()`, `updateAnswer()`, etc.

### 3. **Redux State Management**

#### Updated State Structure

```typescript
interface QuestionState {
  // Questions
  questions: Question[];
  questionsLoading: boolean;
  questionsError: string | null;
  questionsPagination: Pagination | null;
  selectedQuestion: Question | null;

  // Answers
  answers: Answer[];
  answersLoading: boolean;
  answersError: string | null;
  answersPagination: Pagination | null;
  selectedAnswer: Answer | null;
}
```

#### Async Thunks

**Question Actions:**

- `fetchQuestions()` - Admin get all questions
- `fetchPublishedQuestions()` - Public get published questions
- `fetchQuestionById()` - Get single question
- `createQuestion()` - Create new question
- `updateQuestion()` - Update existing question
- `deleteQuestion()` - Delete question
- `publishQuestion()` - Publish question (admin)
- `updateQuestionStatus()` - Change status (admin)

**Answer Actions:**

- `fetchAnswers()` - Get all answers
- `fetchAnswersByQuestionId()` - Get answers for specific question
- `createAnswer()` - Create new answer
- `updateAnswer()` - Update existing answer

### 4. **Updated Selectors**

```typescript
// Question selectors
export const selectQuestions = (state: RootState) =>
  state.question?.questions || [];
export const selectQuestionsLoading = (state: RootState) =>
  state.question?.questionsLoading || false;
export const selectQuestionsError = (state: RootState) =>
  state.question?.questionsError || null;

// Answer selectors
export const selectAnswers = (state: RootState) =>
  state.question?.answers || [];
export const selectAnswersLoading = (state: RootState) =>
  state.question?.answersLoading || false;

// Legacy selectors for backward compatibility
export const selectQuestionItems = selectQuestions;
export const selectQuestionLoading = selectQuestionsLoading;
```

### 5. **Updated Mock Data**

Cập nhật mock data để match với API structure:

- 5 questions với structure mới
- 4 answers tương ứng
- Timestamps thực tế thay vì ISO strings
- Status values: `PENDING`, `PUBLISHED`, `REJECTED`

## 🔧 Technical Improvements

### API Integration Ready

- ✅ Full CRUD operations for Questions
- ✅ Full CRUD operations for Answers
- ✅ Proper error handling
- ✅ Pagination support
- ✅ Query parameters support
- ✅ Status management (PENDING/PUBLISHED/REJECTED)

### Type Safety

- ✅ Strict TypeScript interfaces
- ✅ Request/Response DTOs
- ✅ Error type definitions
- ✅ Query parameter types

### Redux Architecture

- ✅ Separate loading states for questions/answers
- ✅ Separate error states
- ✅ Pagination state management
- ✅ Selected item state management
- ✅ Optimistic updates

## 📝 Next Steps (Bước 3)

Sẵn sàng để:

1. **Cập nhật Components** để sử dụng structure mới
2. **Tích hợp Real API** thay thế mock data
3. **Hoàn thiện UI/UX** với features mới
4. **Testing** với real API endpoints

## 🚨 Breaking Changes

- `Question.id` từ `string` → `number`
- `Question.question` → `Question.content.title`
- `Question.answer` → separate `Answer` entity
- `Question.status` từ `'active'|'inactive'` → `'PENDING'|'PUBLISHED'|'REJECTED'`
- State structure hoàn toàn mới

Components và hooks cần được cập nhật để tương thích với structure mới.
