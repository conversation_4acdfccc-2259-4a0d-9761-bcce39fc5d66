import { Users, FileText, HelpCircle, Image } from "lucide-react";
import { StatsCard } from "./StatsCard";
import { RecentActivities } from "./RecentActivities";
import { SimpleChart } from "./SimpleChart";
import { DashboardStats, ChartData, RecentActivity } from "../data/mockData";

interface InformationPortalTabProps {
  stats: DashboardStats;
  userGrowth: ChartData[];
  contentDistribution: ChartData[];
  recentActivities: RecentActivity[];
}

export const InformationPortalTab = ({
  stats,
  userGrowth,
  contentDistribution,
  recentActivities,
}: InformationPortalTabProps) => {
  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900">Cổng thông tin</h2>
        <p className="text-gray-600 mt-1">
          Tổng quan hoạt động và thống kê hệ thống
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard
          title="Tổng người dùng"
          value={stats.totalUsers.toLocaleString()}
          icon={Users}
          description="Người dùng đã đăng ký"
          trend={{ value: 12.5, isPositive: true }}
        />
        <StatsCard
          title="Bài viết"
          value={stats.totalPosts.toLocaleString()}
          icon={FileText}
          description="Bài viết đã xuất bản"
          trend={{ value: 8.2, isPositive: true }}
        />
        <StatsCard
          title="Câu hỏi"
          value={stats.totalQuestions.toLocaleString()}
          icon={HelpCircle}
          description="Câu hỏi từ người dùng"
          trend={{ value: -2.1, isPositive: false }}
        />
        <StatsCard
          title="Hình ảnh"
          value={stats.totalMedia.toLocaleString()}
          icon={Image}
          description="Media đã tải lên"
          trend={{ value: 15.3, isPositive: true }}
        />
      </div>

      {/* Charts and Activities */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* User Growth Chart */}
        <div className="lg:col-span-1">
          <SimpleChart
            title="Người dùng theo tuần"
            data={userGrowth}
            type="bar"
          />
        </div>

        {/* Content Distribution */}
        <div className="lg:col-span-1">
          <SimpleChart
            title="Phân bố nội dung"
            data={contentDistribution}
            type="pie"
          />
        </div>

        {/* Recent Activities */}
        <div className="lg:col-span-1">
          <RecentActivities activities={recentActivities} />
        </div>
      </div>

      {/* Additional Info */}
      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
              <span className="text-white text-sm font-bold">ℹ</span>
            </div>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-green-800">
              Cổng thông tin tổng quan
            </h3>
            <div className="mt-2 text-sm text-green-700">
              <p>
                Dashboard tổng quan hiển thị các thống kê chính của hệ thống,
                hoạt động người dùng và phân tích dữ liệu để hỗ trợ quản trị
                viên theo dõi tình hình hoạt động.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
