import { useNavigate } from "react-router-dom";
export function useSwitchMode() {
  const navigate = useNavigate();
  return (mode: string, user?: string) => {
    const searchParams = new URLSearchParams(window.location.search);
    searchParams.set("mode", mode);
    if (user) {
      searchParams.set("user", user);
    } else {
      searchParams.delete("user");
    }
    navigate(`?${searchParams.toString()}`);
  };
}
