import { useCallback } from "react";
import { useAppSelector, useAppDispatch } from "@/store/rootReducer";
import {
  selectQuestions,
  selectQuestionsLoading,
  selectQuestionsError,
  selectSelectedQuestion,
  selectAnswers,
  selectAnswersLoading,
  selectAnswersError,
  selectSelectedAnswer,
  selectQuestionsPagination,
  selectAnswersPagination,
  selectQAConfig,
  selectQAConfigLoading,
  selectQAConfigError,
} from "../states/selector";
import {
  fetchQuestions,
  fetchPublishedQuestions,
  fetchQuestionById,
  createQuestion,
  updateQuestion,
  deleteQuestion,
  publishQuestion,
  updateQuestionStatus,
  fetchAnswers,
  fetchAnswersByQuestionId,
  createAnswer,
  updateAnswer,
  setSelectedQuestion,
  setSelectedAnswer,
  clearQuestionsError,
  clearAnswersError,
  fetchQAConfig,
  updateQAConfig,
  clearQAConfigError,
} from "../states/slices";
import type {
  Question,
  Answer,
  CreateQuestionRequest,
  UpdateQuestionRequest,
  CreateAnswerRequest,
  UpdateAnswerRequest,
  QuestionQueryParams,
  AnswerQueryParams,
  QuestionStatus,
  QAConfigRequest,
} from "../states/types";

export const useQuestion = () => {
  const dispatch = useAppDispatch();

  // Question selectors
  const questions = useAppSelector(selectQuestions);
  const questionsLoading = useAppSelector(selectQuestionsLoading);
  const questionsError = useAppSelector(selectQuestionsError);
  const selectedQuestion = useAppSelector(selectSelectedQuestion);
  const questionsPagination = useAppSelector(selectQuestionsPagination);

  // Answer selectors
  const answers = useAppSelector(selectAnswers);
  const answersLoading = useAppSelector(selectAnswersLoading);
  const answersError = useAppSelector(selectAnswersError);
  const selectedAnswer = useAppSelector(selectSelectedAnswer);
  const answersPagination = useAppSelector(selectAnswersPagination);

  // QA Config selectors
  const qaConfig = useAppSelector(selectQAConfig);
  const qaConfigLoading = useAppSelector(selectQAConfigLoading);
  const qaConfigError = useAppSelector(selectQAConfigError);

  // Question actions (using useCallback to prevent re-creation and loops)
  const handleFetchQuestions = useCallback(
    (params?: QuestionQueryParams) => {
      dispatch(fetchQuestions(params));
    },
    [dispatch]
  );

  const handleFetchPublishedQuestions = useCallback(
    (params?: QuestionQueryParams) => {
      dispatch(fetchPublishedQuestions(params));
    },
    [dispatch]
  );

  const handleFetchQuestionById = useCallback(
    (id: number) => {
      dispatch(fetchQuestionById(id));
    },
    [dispatch]
  );

  const handleCreateQuestion = useCallback(
    (questionData: CreateQuestionRequest) => {
      return dispatch(createQuestion(questionData));
    },
    [dispatch]
  );

  const handleUpdateQuestion = useCallback(
    (id: number, data: UpdateQuestionRequest) => {
      dispatch(updateQuestion({ id, data }));
    },
    [dispatch]
  );

  const handleDeleteQuestion = useCallback(
    (questionId: number) => {
      dispatch(deleteQuestion(questionId));
    },
    [dispatch]
  );

  const handlePublishQuestion = useCallback(
    (questionId: number) => {
      dispatch(publishQuestion(questionId));
    },
    [dispatch]
  );

  const handleUpdateQuestionStatus = useCallback(
    (id: number, status: QuestionStatus) => {
      dispatch(updateQuestionStatus({ id, status }));
    },
    [dispatch]
  );

  const handleSelectQuestion = useCallback(
    (question: Question | null) => {
      dispatch(setSelectedQuestion(question));
    },
    [dispatch]
  );

  const handleClearQuestionsError = useCallback(() => {
    dispatch(clearQuestionsError());
  }, [dispatch]);

  // Answer actions (using useCallback to prevent re-creation and loops)
  const handleFetchAnswers = useCallback(
    (params?: AnswerQueryParams) => {
      dispatch(fetchAnswers(params));
    },
    [dispatch]
  );

  const handleFetchAnswersByQuestionId = useCallback(
    (questionId: number, params?: AnswerQueryParams) => {
      dispatch(fetchAnswersByQuestionId({ questionId, params }));
    },
    [dispatch]
  );

  const handleCreateAnswer = useCallback(
    (answerData: CreateAnswerRequest) => {
      dispatch(createAnswer(answerData));
    },
    [dispatch]
  );

  const handleUpdateAnswer = useCallback(
    (id: number, data: UpdateAnswerRequest) => {
      dispatch(updateAnswer({ id, data }));
    },
    [dispatch]
  );

  const handleSelectAnswer = useCallback(
    (answer: Answer | null) => {
      dispatch(setSelectedAnswer(answer));
    },
    [dispatch]
  );

  const handleClearAnswersError = useCallback(() => {
    dispatch(clearAnswersError());
  }, [dispatch]);

  // QA Config actions (using useCallback to prevent re-creation and loops)
  const handleFetchQAConfig = useCallback(() => {
    dispatch(fetchQAConfig());
  }, [dispatch]);

  const handleUpdateQAConfig = useCallback(
    (configData: QAConfigRequest) => {
      dispatch(updateQAConfig(configData));
    },
    [dispatch]
  );

  const handleClearQAConfigError = useCallback(() => {
    dispatch(clearQAConfigError());
  }, [dispatch]);

  return {
    // Questions
    questions,
    questionsLoading,
    questionsError,
    selectedQuestion,
    questionsPagination,
    fetchQuestions: handleFetchQuestions,
    fetchPublishedQuestions: handleFetchPublishedQuestions,
    fetchQuestionById: handleFetchQuestionById,
    createQuestion: handleCreateQuestion,
    updateQuestion: handleUpdateQuestion,
    deleteQuestion: handleDeleteQuestion,
    publishQuestion: handlePublishQuestion,
    updateQuestionStatus: handleUpdateQuestionStatus,
    selectQuestion: handleSelectQuestion,
    clearQuestionsError: handleClearQuestionsError,

    // Answers
    answers,
    answersLoading,
    answersError,
    selectedAnswer,
    answersPagination,
    fetchAnswers: handleFetchAnswers,
    fetchAnswersByQuestionId: handleFetchAnswersByQuestionId,
    createAnswer: handleCreateAnswer,
    updateAnswer: handleUpdateAnswer,
    selectAnswer: handleSelectAnswer,
    clearAnswersError: handleClearAnswersError,

    // QA Config
    qaConfig,
    qaConfigLoading,
    qaConfigError,
    fetchQAConfig: handleFetchQAConfig,
    updateQAConfig: handleUpdateQAConfig,
    clearQAConfigError: handleClearQAConfigError,

    // Legacy compatibility (deprecated)
    loading: questionsLoading,
    error: questionsError,
    clearError: handleClearQuestionsError,
  };
};
