import { FormConfig } from "@/components/form/registry";

export const CreateUserFormConfig: FormConfig = {
  code: "create-user-form",
  name: "<PERSON>ạo tài khoản mới",
  note: "<PERSON>ùng để tạo tài khoản mới",
  config: {
    id: "root",
    type: "group",
    label: "",
    children: [
      {
        id: "name-field",
        type: "field",
        fieldConfig: {
          id: "userName",
          label: "Tên tài khoản",
          data_type: "text",
          input_type: "TextInput",
          placeholder: "Nhập tên tài khoản",
          default_value: "",
          validation: {
            required: true,
            maxLength: 200,
          },
        },
        children: [],
      },
      {
        id: "password-field",
        type: "field",
        fieldConfig: {
          id: "password",
          label: "<PERSON>ật khẩu",
          data_type: "text",
          input_type: "PasswordInput",
          placeholder: "<PERSON><PERSON><PERSON><PERSON> mật khẩu",
          default_value: "",
          validation: {
            required: true,
            maxLength: 200,
          },
        },
        children: [],
      },
      {
        id: "full-name-field",
        type: "field",
        fieldConfig: {
          id: "fullName",
          label: "Họ và tên",
          data_type: "text",
          input_type: "TextInput",
          placeholder: "Nhập họ và tên",
          default_value: "",
          validation: {
            required: true,
            maxLength: 200,
          },
        },
        children: [],
      },
      {
        id: "email-field",
        type: "field",
        fieldConfig: {
          id: "email",
          label: "Email",
          data_type: "text",
          input_type: "TextInput",
          placeholder: "Nhập email",
          default_value: "",
          validation: {
            required: true,
            maxLength: 200,
            pattern: "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$",
          },
        },
        children: [],
      },
      {
        id: "phone-field",
        type: "field",
        fieldConfig: {
          id: "phone",
          label: "Số điện thoại",
          data_type: "text",
          input_type: "TextInput",
          placeholder: "Nhập số điện thoại",
          default_value: "",
          validation: {
            required: true,
            pattern: "^[0-9]+$",
            maxLength: 10,
          },
        },
        children: [],
      },
    ],
  },
};
