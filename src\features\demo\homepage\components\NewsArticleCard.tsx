import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { ImageLoader } from "@/components/image/ImageLoader";
import { cn } from "@/lib/utils";
import { Clock, Share2, Star } from "lucide-react";
import { Separator } from "@radix-ui/react-separator";
import { Button } from "@/components/ui/button";

export type NewsArticleCardSize = "small" | "medium" | "large" | "stretch";

export interface NewsArticleCardProps {
  title: string;
  image?: string;
  date: string;
  size?: NewsArticleCardSize;
  className?: string;
  darkMode?: boolean;
  onClick?: () => void;
  onFavorite?: () => void;
  onShare?: () => void;
}

export const NewsArticleCard: React.FC<NewsArticleCardProps> = ({
  title,
  image,
  date,
  size = "stretch",
  className,
  darkMode = false,
  onClick,
  onFavorite,
  onShare,
}) => {
  let color;
  let secondaryColor;
  let borderColor;
  let backgroundImage;

  const sizeClasses = {
    small: {
      container: "h-64",
      image: "h-32",
      title: "text-sm font-semibold line-clamp-2",
      meta: "text-xs",
    },
    medium: {
      container: "h-80",
      image: "h-40",
      title: "text-base font-semibold line-clamp-2",
      meta: "text-xs",
    },
    large: {
      container: "h-96",
      image: "h-48",
      title: "text-lg font-semibold line-clamp-2",
      meta: "text-sm",
    },
    stretch: {
      container: "h-full w-full",
      image: "h-64",
      title: "text-lg font-semibold line-clamp-2",
      meta: "text-sm",
    }
  };

  switch (darkMode) {
    case true:
      color = "text-[#27272A]";
      secondaryColor = "text-[#757682]";
      borderColor = "bg-[#DADADD]";
      backgroundImage = `bg-[url('/your-image.jpg')]`;
      break;
    case false:
      color = "text-[#27272A]";
      secondaryColor = "text-[#757682]";
      borderColor = "bg-[#DADADD]";
      backgroundImage = ""
      break;
  }

  const classes = sizeClasses[size];

  return (
    <Card
      className={cn(
        "overflow-hidden hover:shadow-lg transition-all duration-300 cursor-pointer bg-transparent border-gray-200",
        classes.container,
        className,
        backgroundImage
      )}

      onClick={onClick}
    >
      <CardContent className="p-0 flex flex-col h-full">
        {/* Image Section */}
        <div className={cn("relative overflow-hidden", classes.image)}>
          {image ? (
            <ImageLoader
              src={image}
              alt={title}
              className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
              fallbackText={title}
            />
          ) : (
            <div className="w-full h-full bg-gray-100 flex items-center justify-center">
              <span className="text-gray-400 text-sm">Không có hình ảnh</span>
            </div>
          )}
        </div>

        {/* Content Section */}
        <div className="flex-1 flex flex-col justify-between p-4 space-y-2">
          <div className="pb-2">
            <h1 className={cn(color, classes.title)}>
              {title}
            </h1>
          </div>

          <Separator className={cn(borderColor, "w-full h-[1px]")} />

          {/* Meta Information */}
          <div className={cn("flex items-center justify-between", classes.meta, secondaryColor)}>
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4" />
              <span>{date}</span>
            </div>
            <div className="flex items-center gap-0">
              <Button onClick={onFavorite} variant="ghost">
                <Star className="h-4 w-4" />
              </Button>
              <Button onClick={onShare} variant="ghost">
                <Share2 className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
