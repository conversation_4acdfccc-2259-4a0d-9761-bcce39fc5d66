import React from "react";
import { Badge } from "@/components/ui/badge";

/**
 * Props interface for the ConfigStatusBadges component
 *
 * @interface ConfigStatusBadgesProps
 */
interface ConfigStatusBadgesProps {
  /** Whether there are unsaved changes that need to be saved */
  isDirty: boolean;
  /** Whether there are validation errors in form fields that prevent saving */
  hasValidationErrors: boolean;
}

/**
 * ConfigStatusBadges component displays status badges for configuration management pages.
 *
 * This component provides visual feedback to users about the current state of their form:
 * - Shows an orange badge when there are unsaved changes
 * - Shows a red badge when there are validation errors
 * - Both badges can be displayed simultaneously
 * - Returns null when no status indicators are needed
 *
 * Used consistently across all configuration management features to maintain
 * a unified user experience.
 *
 * @param props - The component props
 * @param props.isDirty - Whether there are unsaved changes
 * @param props.hasValidationErrors - Whether there are validation errors
 * @returns JSX element containing status badges or null if no status to show
 *
 * @example
 * ```tsx
 * <ConfigStatusBadges
 *   isDirty={true}
 *   hasValidationErrors={false}
 * />
 * ```
 */
export const ConfigStatusBadges: React.FC<ConfigStatusBadgesProps> = ({
  isDirty,
  hasValidationErrors,
}) => {
  // Early return if no status indicators are needed
  if (!isDirty && !hasValidationErrors) {
    return null;
  }

  return (
    <div className="flex items-center gap-3">
      {isDirty && (
        <Badge variant="secondary" className="bg-orange-100 text-orange-800">
          Có thay đổi chưa lưu
        </Badge>
      )}

      {hasValidationErrors && (
        <Badge variant="secondary" className="bg-red-100 text-red-800">
          Có lỗi xác thực
        </Badge>
      )}
    </div>
  );
};
