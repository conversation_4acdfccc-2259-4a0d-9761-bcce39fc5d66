import { useEffect, useState } from "react";
import { AutoTable } from "@/components/table/AutoTable";
import { post_columns } from "../states/table";
import { Post } from "../states/types";
import { useAppDispatch, useAppSelector } from "@/store/rootReducer";
import {
  selectPostQueryParams,
  selectRefetch,
  selectPostLoading,
  selectPostError,
} from "../states/selector";
import { fetchPosts } from "../states/api";
import { setFilters } from "../states/slices";
import { CreatePostDialog } from "./CreatePostDialog";
import { LoadingPage } from "@/components/loading/LoadingPage";

interface PostTableProps {
  isUrlSyncing?: boolean;
}

export const PostTable: React.FC<PostTableProps> = ({
  isUrlSyncing = false,
}) => {
  const [data, setData] = useState<Post[]>([]);
  const params = useAppSelector(selectPostQueryParams);
  const refetch = useAppSelector(selectRefetch);
  const dispatch = useAppDispatch();
  const loading = useAppSelector(selectPostLoading);
  const error = useAppSelector(selectPostError);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const res = await fetchPosts(params);
        setData(res.data || []);
      } catch (error) {
        console.error("Error in fetchPosts:", error);
      }
    };
    fetchData();
  }, [params, refetch]);

  // Show URL sync loading - this takes priority
  if (isUrlSyncing) {
    return (
      <div className="p-4">
        <div className="flex justify-between items-center mb-4">
          <h1 className="text-xl font-semibold">Danh sách bài viết</h1>
          <CreatePostDialog />
        </div>
        <div className="flex items-center justify-center min-h-[200px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
            <p className="text-sm text-muted-foreground">
              Đang đồng bộ trạng thái...
            </p>
          </div>
        </div>
      </div>
    );
  }

  // Show API loading only if not URL syncing
  if (loading) return <LoadingPage />;
  if (error) return <div>{error}</div>;

  const handlePageChange = (index: number) => {
    dispatch(setFilters({ page: index }));
  };

  const handlePageSizeChange = (size: number) => {
    dispatch(setFilters({ size: size }));
  };

  const pagination = {
    pageIndex: params.page ? params.page : 0,
    pageSize: params.size || 10,
    totalCount: 0,
    onPageChange: handlePageChange,
    onPageSizeChange: handlePageSizeChange,
  };

  return (
    <div className="p-4 space-y-4">
      <div className="flex justify-between items-center">
        <h1 className="text-xl font-semibold">Danh sách bài viết</h1>
        <CreatePostDialog />
      </div>
      {data?.length === 0 ? (
        <div className="text-center text-muted-foreground py-10">
          Không có bài viết nào
        </div>
      ) : (
        <AutoTable<Post>
          columns={post_columns}
          data={data}
          pagination={pagination}
        />
      )}
    </div>
  );
};
