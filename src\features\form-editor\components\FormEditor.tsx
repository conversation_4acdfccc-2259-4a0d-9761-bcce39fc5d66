/**
 * FormEditor - JSON Editor Component
 */

import React from "react";

interface FormEditorProps {
  jsonText: string;
  jsonError: string;
  onJsonChange: (newJsonText: string) => void;
  onReset: () => void;
}

export const FormEditor: React.FC<FormEditorProps> = ({
  jsonText,
  jsonError,
  onJsonChange,
  onReset,
}) => {
  return (
    <div className="bg-white rounded-lg shadow-lg overflow-hidden flex-1">
      {/* Header */}
      <div className="p-4 bg-gray-50 border-b">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold">📝 JSON Editor</h2>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2 text-sm">
              <span className="text-gray-500">Update: 300ms</span>
            </div>
            <button
              onClick={onReset}
              className="px-3 py-1 text-sm bg-gray-600 text-white rounded hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition duration-200"
            >
              🔄 Reset to Mock
            </button>
          </div>
        </div>
        {jsonError && (
          <div className="mt-2 p-2 bg-red-100 border border-red-300 rounded text-red-700 text-sm">
            {jsonError}
          </div>
        )}
      </div>

      {/* JSON Textarea */}
      <textarea
        value={jsonText}
        onChange={(e) => onJsonChange(e.target.value)}
        className="w-full h-[600px] p-4 font-mono text-sm bg-gray-900 text-green-400 border-none outline-none resize-none"
        placeholder="Enter JSON configuration..."
        spellCheck={false}
      />
    </div>
  );
};
