/**
 * RedirectNode - Renders redirect/link nodes
 */

import React from "react";
import { FormNode } from "../types";
import { cn } from "@/lib/utils";
import { useNavigate } from "react-router-dom";

interface RedirectNodeProps {
  node: FormNode;
}

export const RedirectNode: React.FC<RedirectNodeProps> = ({ node }) => {
  const navigate = useNavigate();
  const { id, styles, properties } = node;
  const containerClass = cn("text-xs flex items-center", styles?.container);
  const contentClass = cn(
    "underline text-blue-600 hover:text-blue-800 font-semibold ml-1 cursor-pointer",
    styles?.content
  );

  const url = (properties?.url as string) || "#";
  const target = (properties?.target as string) || "_self";
  const prefix = (properties?.prefix as string) || "";
  const suffix = (properties?.suffix as string) || "";

  return (
    <div key={id} className={containerClass}>
      <span>{prefix}</span>
      <span className={contentClass}>
        <a target={target} onClick={() => navigate(url)}>
          {suffix}
        </a>
      </span>
    </div>
  );
};
