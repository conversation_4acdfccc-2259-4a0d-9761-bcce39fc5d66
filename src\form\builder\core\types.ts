/**
 * Form Builder Core Types
 * Strictly follows AutoForm conventions
 */

import { FormNode } from '@/form/types';

/**
 * Form configuration - Core data structure
 */
export interface FormConfig {
  code: string;
  name: string;
  description: string;
  
  dataSchema: object; // Schema data của form (infer từ UI)
  uiConfig: FormNode; // Root node luôn là Frame type
}

/**
 * Editor UI State - Tracked in history
 */
export interface EditorUIState {
  selectedNodeId: string | null;
  expandedNodes: Set<string>;
  
  // Component selector modal
  showComponentSelector: boolean;
  componentSelectorParentId: string | null;
  
  // Properties panel state
  propertiesPanel: {
    activeTab: 'attributes' | 'display';
    scrollPosition: number;
  };
  
  // Editor mode
  mode: 'design' | 'preview' | 'code';
}

/**
 * Form Builder State
 */
export interface FormBuilderState {
  // Core form configuration
  formConfig: FormConfig;
  
  // UI Editor state
  editor: EditorUIState;
  
  // History management
  history: {
    past: HistoryEntry[];
    future: HistoryEntry[];
  };
  
  // Operation flags
  isDirty: boolean;
  isLoading: boolean;
  lastAction: string | null;
}

/**
 * Command interface for undo/redo operations
 */
export interface Command {
  readonly id: string;
  readonly type: string;
  readonly timestamp: number;
  execute(state: FormBuilderState): FormBuilderState;
  undo(state: FormBuilderState): FormBuilderState;
  readonly description: string;
}

/**
 * History Entry - Lightweight command-based approach
 */
export interface HistoryEntry {
  command: Command;
  timestamp: number;
}

/**
 * Component Categories
 */
export type ComponentCategory = 'layout' | 'control' | 'field-basic' | 'field-template';

/**
 * Component Definition for palette
 */
export interface ComponentDefinition {
  id: string;
  name: string;
  description: string;
  category: ComponentCategory;
  icon: string;
  
  // Factory function to create new node instance
  createNode: (id: string) => FormNode;
  
  // Validation rules
  canBeChildOf?: (parentNode: FormNode) => boolean;
  canHaveChildren?: boolean;
}

/**
 * Tree Operation Result
 */
export interface TreeOperationResult<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
}

/**
 * Node Path - Used for efficient tree operations
 */
export type NodePath = string[]; // Array of node IDs from root to target

/**
 * Tree Operation Context
 */
export interface TreeOperationContext {
  root: FormNode;
  nodeMap: Map<string, FormNode>; // For O(1) lookup
  parentMap: Map<string, string>; // Child ID -> Parent ID
  pathMap: Map<string, NodePath>; // Node ID -> Path
}

/**
 * Form Builder Actions
 */
export type FormBuilderAction = 
  // Form config actions
  | { type: 'FORM/SET_CONFIG'; payload: FormConfig }
  | { type: 'FORM/UPDATE_METADATA'; payload: Partial<Pick<FormConfig, 'code' | 'name' | 'description'>> }
  
  // Node operations
  | { type: 'NODE/ADD'; payload: { parentId: string; node: FormNode; index?: number } }
  | { type: 'NODE/UPDATE'; payload: { nodeId: string; updates: Partial<FormNode> } }
  | { type: 'NODE/DELETE'; payload: { nodeId: string } }
  | { type: 'NODE/MOVE'; payload: { nodeId: string; newParentId: string; index: number } }
  | { type: 'NODE/DUPLICATE'; payload: { nodeId: string } }
  
  // Editor UI actions
  | { type: 'EDITOR/SELECT_NODE'; payload: { nodeId: string | null } }
  | { type: 'EDITOR/TOGGLE_NODE_EXPAND'; payload: { nodeId: string } }
  | { type: 'EDITOR/SET_MODE'; payload: EditorUIState['mode'] }
  | { type: 'EDITOR/SHOW_COMPONENT_SELECTOR'; payload: { parentId: string } }
  | { type: 'EDITOR/HIDE_COMPONENT_SELECTOR' }
  | { type: 'EDITOR/SET_PROPERTIES_TAB'; payload: EditorUIState['propertiesPanel']['activeTab'] }
  
  // History actions
  | { type: 'HISTORY/UNDO' }
  | { type: 'HISTORY/REDO' }
  | { type: 'HISTORY/RESET' };