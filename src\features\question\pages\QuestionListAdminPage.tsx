import React, { useState, useEffect } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Search, RefreshCw, AlertCircle } from "lucide-react";

import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { useQuestionFilter } from "../hooks/useQuestionFilter";
import { fetchQuestionsList } from "../states/questionListService";
import { QuestionStatus, Question } from "../states/types";
import { QuestionDetailView } from "../components/QuestionDetailView";
import { CreateQuestionDialog } from "../components/CreateQuestionDialog";
import { AutoTable, PaginationProps } from "@/components/table/AutoTable";
import { QUESTION_COLUMNS } from "../states/table";

// Tab configuration for question status
const STATUS_TABS: Array<{
  value: QuestionStatus;
  label: string;
  description: string;
  variant: "default" | "secondary" | "destructive" | "outline";
}> = [
  {
    value: "NEW",
    label: "Chưa trả lời",
    description: "Câu hỏi vừa được gửi",
    variant: "outline",
  },
  {
    value: "DRAFT",
    label: "Đã trả lời",
    description: "Đã có câu trả lời",
    variant: "secondary",
  },
  {
    value: "PUBLISHED",
    label: "Đã xuất bản",
    description: "Đã được công khai",
    variant: "default",
  },
  {
    value: "TRASH",
    label: "Thùng rác",
    description: "Đã bị xóa",
    variant: "destructive",
  },
];

const QuestionListAdminPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  // Get state from URL - Single Source of Truth
  const currentStatus = (searchParams.get("status") as QuestionStatus) || "NEW";
  const currentSearch = searchParams.get("search") || "";
  const currentPage = parseInt(searchParams.get("page") || "0");
  const currentSize = parseInt(searchParams.get("size") || "20");
  const questionId = searchParams.get("questionId");

  // Local state for form inputs and data
  const [searchInput, setSearchInput] = useState(currentSearch);
  const [questions, setQuestions] = useState<Question[]>([]);

  // Question filter hook
  const {
    params,
    refetch,
    loading,
    error,
    setFilters,
    setLoading,
    setError,
    triggerRefetch,
  } = useQuestionFilter();

  // Helper function to update URL
  const updateURL = (params: {
    status?: QuestionStatus;
    search?: string;
    page?: number;
    size?: number;
    questionId?: string;
  }) => {
    const urlParams = new URLSearchParams();

    urlParams.set("status", params.status || currentStatus);

    if (params.search !== undefined) {
      if (params.search) {
        urlParams.set("search", params.search);
      }
    } else if (currentSearch) {
      urlParams.set("search", currentSearch);
    }

    if (params.page !== undefined) {
      if (params.page > 0) {
        urlParams.set("page", String(params.page));
      }
    } else if (currentPage > 0) {
      urlParams.set("page", String(currentPage));
    }

    if (params.size !== undefined) {
      if (params.size !== 20) {
        urlParams.set("size", String(params.size));
      }
    } else if (currentSize !== 20) {
      urlParams.set("size", String(currentSize));
    }

    if (params.questionId !== undefined) {
      if (params.questionId) {
        urlParams.set("questionId", params.questionId);
      }
    } else if (questionId) {
      urlParams.set("questionId", questionId);
    }

    navigate(`?${urlParams.toString()}`, { replace: true });
  };

  // Sync URL params with Redux filter state
  useEffect(() => {
    const urlParams = {
      status: currentStatus,
      keyword: currentSearch || undefined,
      page: currentPage,
      size: currentSize,
    };

    // Only update if params changed
    if (JSON.stringify(params) !== JSON.stringify(urlParams)) {
      console.log("🔄 Syncing URL to Redux:", urlParams);
      setFilters(urlParams);
    }
  }, [
    currentStatus,
    currentSearch,
    currentPage,
    currentSize,
    params,
    setFilters,
  ]);

  // Fetch questions when filter params or refetch changes
  useEffect(() => {
    if (!params) return;

    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);
        console.log("🚀 Fetching questions with params:", params);

        const data = await fetchQuestionsList(params);
        setQuestions(data);

        console.log("✅ Questions fetched:", data.length);
      } catch (err) {
        console.error("❌ Error fetching questions:", err);
        setError(String(err));
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [params, refetch, setLoading, setError]);

  // Sync search input with URL
  useEffect(() => {
    setSearchInput(currentSearch);
  }, [currentSearch]);

  // Handle status tab change
  const handleStatusChange = (status: QuestionStatus) => {
    updateURL({ status, page: 0 });
  };

  // Handle search
  const handleSearch = () => {
    updateURL({ search: searchInput.trim(), page: 0 });
  };

  // Handle search key press
  const handleSearchKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSearch();
    }
  };

  // Handle clear search
  const handleClearSearch = () => {
    setSearchInput("");
    updateURL({ search: "", page: 0 });
  };

  // Handle table actions
  const handleTableAction = (action: string, row: Question) => {
    if (action === "view") {
      updateURL({ questionId: String(row.id) });
    } else if (action === "edit") {
      // TODO: Implement edit functionality
      console.log("Edit question:", row.id);
    }
  };

  // Handle pagination
  const paginationProps: PaginationProps = {
    pageIndex: currentPage,
    pageSize: currentSize,
    totalCount: questions.length, // TODO: Get actual total from API
    onPageChange: (newIndex: number) => {
      updateURL({ page: newIndex });
    },
    onPageSizeChange: (newSize: number) => {
      updateURL({ size: newSize, page: 0 });
    },
  };

  // Handle detail view with search params instead of separate route
  if (questionId) {
    // Find the question to display
    const selectedQuestion = questions.find(
      (q) => q.id === parseInt(questionId)
    );

    return (
      <QuestionDetailView
        question={selectedQuestion || null}
        onBackToList={() => {
          const urlParams = new URLSearchParams();
          urlParams.set("status", currentStatus);
          if (currentSearch) urlParams.set("search", currentSearch);
          if (currentPage > 0) urlParams.set("page", String(currentPage));
          if (currentSize !== 20) urlParams.set("size", String(currentSize));
          navigate(`?${urlParams.toString()}`, { replace: true });
        }}
      />
    );
  }

  return (
    <div className="w-full h-full flex flex-col gap-6 p-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold">Quản Lý Câu Hỏi</h1>
          <p className="text-gray-600 mt-1">
            Xem và quản lý các câu hỏi từ người dùng
          </p>
        </div>
        <CreateQuestionDialog
          onSuccess={() => {
            // Refresh questions after creating new one
            triggerRefetch();
          }}
        />
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Lỗi</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
          <Button
            variant="outline"
            size="sm"
            className="mt-2"
            onClick={() => setError(null)}
          >
            Đóng
          </Button>
        </Alert>
      )}

      {/* Search Bar */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex gap-2">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Tìm kiếm theo tiêu đề, nội dung, email..."
                value={searchInput}
                onChange={(e) => setSearchInput(e.target.value)}
                onKeyPress={handleSearchKeyPress}
                className="pl-10"
              />
            </div>
            <Button onClick={handleSearch} disabled={loading}>
              Tìm kiếm
            </Button>
            <Button
              variant="outline"
              onClick={triggerRefetch}
              disabled={loading}
              title="Làm mới danh sách"
            >
              <RefreshCw className="h-4 w-4" />
            </Button>
            {currentSearch && (
              <Button variant="outline" onClick={handleClearSearch}>
                Xóa bộ lọc
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Status Tabs */}
      <Card>
        <CardContent className="pt-6">
          <Tabs
            value={currentStatus}
            onValueChange={(value) =>
              handleStatusChange(value as QuestionStatus)
            }
          >
            <TabsList className="grid w-full grid-cols-4">
              {STATUS_TABS.map((tab) => (
                <TabsTrigger key={tab.value} value={tab.value}>
                  <div className="flex items-center gap-2">
                    <span>{tab.label}</span>
                  </div>
                </TabsTrigger>
              ))}
            </TabsList>

            {STATUS_TABS.map((tab) => (
              <TabsContent key={tab.value} value={tab.value} className="mt-4">
                <div className="text-sm text-gray-600 mb-4">
                  {tab.description}
                </div>

                {/* Questions Table */}
                <AutoTable
                  columns={QUESTION_COLUMNS}
                  data={questions}
                  loading={loading}
                  pagination={paginationProps}
                  onAction={handleTableAction}
                  emptyStateMessage="Không có câu hỏi nào"
                />
              </TabsContent>
            ))}
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default QuestionListAdminPage;
