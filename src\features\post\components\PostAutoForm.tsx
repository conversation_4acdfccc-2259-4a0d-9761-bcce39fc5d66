import { AutoForm } from "@/form/context";
import { CreatePostFormConfig } from "../states/formConfig";
import { CreatePost } from "../states/types";

interface PostAutoFormProps {
  initialData?: CreatePost;
  onSubmit: (data: CreatePost) => void;
  viewOnly?: boolean;
}

export const PostAutoForm: React.FC<PostAutoFormProps> = ({
  initialData = {},
  onSubmit,
  viewOnly = false,
}) => {
  const defaultData: CreatePost = {
    title: "",
    slug: "",
    postType: "ARTICLE",
    authorId: "admin",
    excerpt: {
      image: "",
      description: "",
      files: [],
    },
    ...initialData,
  };

  const handleSubmit = (data: CreatePost) => {
    onSubmit(data);
  };

  const node = CreatePostFormConfig.config;

  return (
    <AutoForm<CreatePost>
      node={node}
      viewOnly={viewOnly}
      initialData={defaultData}
      onSubmit={handleSubmit}
      validationMode="onSubmit"
    />
  );
};
