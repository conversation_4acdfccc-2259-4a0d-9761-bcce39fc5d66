import i18n from "i18next";
import { initReactI18next } from "react-i18next";

import vi_common from "./locales/vi/common.json";

import vi_autoform from "./locales/vi/autoform.json";
import vi_login from "./locales/vi/login.json";
import vi_user from "./locales/vi/user.json";

export const defaultNS = "common";

const savedLanguage = localStorage.getItem("language") || "vi";

i18n.use(initReactI18next).init({
  lng: savedLanguage,
  fallbackLng: "vi",
  defaultNS,
  interpolation: {
    escapeValue: false,
  },
  resources: {
    vi: {
      common: vi_common,
      autoform: vi_autoform,
      login: vi_login,
      user: vi_user,
    },
  },
});

export default i18n;
