# Category State Management - Hướng dẫn sử dụng

## Tổng quan về cải tiến

Hệ thống quản lý category state đã được cải tiến để giải quyết vấn đề API bị gọi nhiều lần và cung cấp kiểm soát loading tốt hơn.

### C<PERSON><PERSON> cải tiến chính:

1. **Loading state riêng biệt cho từng loại category**
2. **Caching thông minh với timestamp**
3. **Hooks tùy chỉnh để tránh duplicate API calls**
4. **Error handling tốt hơn**
5. **Loading states cho operations (create, update, delete)**

## Cấu trúc State mới

```typescript
interface CategoryState {
  types: {
    "public-menu": {
      data: CategoryDTO[];
      loading: boolean;
      error: string | null;
      lastFetched: number | null;
    },
    "admin-menu": { ... },
    "user-menu": { ... }
  };
  selectedId: number;
  selectedType: CategoryType;
  mode: CategoryMode;
  // Global operation states
  creating: boolean;
  updating: boolean;
  deleting: boolean;
  globalError: string | null;
}
```

## Sử dụng Hooks

### 1. Hook đơn giản - `useCategory`

```typescript
import { useCategory } from "@/features/category/hooks/useCategoryData";

function MyComponent() {
  const { data, loading, error } = useCategory("public-menu");

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div>
      {data.map((category) => (
        <div key={category.id}>{category.name}</div>
      ))}
    </div>
  );
}
```

### 2. Hook nâng cao - `useCategoryData`

```typescript
import { useCategoryData } from "@/features/category/hooks/useCategoryData";

function MyComponent() {
  const {
    data,
    loading,
    error,
    hasData,
    isFresh,
    needsRefresh,
    fetchData,
    refreshData,
  } = useCategoryData({
    type: "public-menu",
    autoFetch: true, // Tự động fetch nếu cần (default: true)
    forceRefresh: false, // Bỏ qua cache (default: false)
    deps: [], // Dependencies để trigger re-fetch
  });

  const handleRefresh = () => {
    refreshData(); // Force refresh
  };

  const handleFetch = () => {
    fetchData(true); // Fetch với force = true
  };

  return (
    <div>
      <button onClick={handleRefresh}>Refresh</button>
      <button onClick={handleFetch}>Force Fetch</button>

      {loading && <div>Loading...</div>}
      {error && <div>Error: {error}</div>}

      <div>
        Has data: {hasData ? "Yes" : "No"}
        <br />
        Is fresh: {isFresh ? "Yes" : "No"}
        <br />
        Needs refresh: {needsRefresh ? "Yes" : "No"}
      </div>

      {data.map((category) => (
        <div key={category.id}>{category.name}</div>
      ))}
    </div>
  );
}
```

### 3. Hook cho nhiều loại - `useMultipleCategories`

```typescript
import { useMultipleCategories } from "@/features/category/hooks/useCategoryData";

function MyComponent() {
  const { data, loading, errors, allLoaded, refreshAll } =
    useMultipleCategories(["public-menu", "admin-menu"]);

  if (loading) return <div>Loading categories...</div>;
  if (!allLoaded) return <div>Not all categories loaded</div>;

  return (
    <div>
      <button onClick={refreshAll}>Refresh All</button>

      <h3>Public Menu</h3>
      {data["public-menu"].map((cat) => (
        <div key={cat.id}>{cat.name}</div>
      ))}

      <h3>Admin Menu</h3>
      {data["admin-menu"].map((cat) => (
        <div key={cat.id}>{cat.name}</div>
      ))}

      {/* Show errors */}
      {Object.entries(errors).map(([type, error]) => (
        <div key={type}>
          Error in {type}: {error}
        </div>
      ))}
    </div>
  );
}
```

## Sử dụng Selectors mới

### Loading states riêng biệt

```typescript
import {
  selectCategoryLoadingByType,
  selectCategoryCreating,
  selectCategoryUpdating,
  selectCategoryDeleting,
} from "@/features/category/states/selector";

function MyComponent() {
  const publicLoading = useAppSelector((state) =>
    selectCategoryLoadingByType(state, "public-menu")
  );
  const adminLoading = useAppSelector((state) =>
    selectCategoryLoadingByType(state, "admin-menu")
  );

  const isCreating = useAppSelector(selectCategoryCreating);
  const isUpdating = useAppSelector(selectCategoryUpdating);
  const isDeleting = useAppSelector(selectCategoryDeleting);

  return (
    <div>
      <div>Public loading: {publicLoading ? "Yes" : "No"}</div>
      <div>Admin loading: {adminLoading ? "Yes" : "No"}</div>
      <div>Creating: {isCreating ? "Yes" : "No"}</div>
      <div>Updating: {isUpdating ? "Yes" : "No"}</div>
      <div>Deleting: {isDeleting ? "Yes" : "No"}</div>
    </div>
  );
}
```

### Utility selectors

```typescript
import {
  selectCategoryHasDataByType,
  selectCategoryIsFreshByType,
  selectCategoryNeedsRefreshByType,
  selectCategoryById,
} from "@/features/category/states/selector";

function MyComponent() {
  const hasPublicData = useAppSelector((state) =>
    selectCategoryHasDataByType(state, "public-menu")
  );
  const isPublicFresh = useAppSelector((state) =>
    selectCategoryIsFreshByType(state, "public-menu")
  );
  const needsPublicRefresh = useAppSelector((state) =>
    selectCategoryNeedsRefreshByType(state, "public-menu")
  );

  const category = useAppSelector((state) => selectCategoryById(state, 123));

  return (
    <div>
      <div>Has data: {hasPublicData ? "Yes" : "No"}</div>
      <div>Is fresh: {isPublicFresh ? "Yes" : "No"}</div>
      <div>Needs refresh: {needsPublicRefresh ? "Yes" : "No"}</div>
      <div>Category 123: {category?.name || "Not found"}</div>
    </div>
  );
}
```

## Caching Logic

- **Cache duration**: 5 phút
- **Auto-skip**: Nếu dữ liệu đã có và còn fresh, API sẽ không được gọi
- **Force refresh**: Set `forceRefresh: true` để bỏ qua cache
- **Prevent duplicate**: Nếu đang loading, các request tiếp theo sẽ bị skip

## Thunk Actions mới

### Fetch với options

```typescript
// Cách cũ
dispatch(fetchCategoryByType("public-menu"));

// Cách mới
dispatch(
  fetchCategoryByType({
    type: "public-menu",
    forceRefresh: false,
  })
);

// Force refresh
dispatch(
  fetchCategoryByType({
    type: "public-menu",
    forceRefresh: true,
  })
);
```

## Migration từ code cũ

### Thay thế useEffect patterns

```typescript
// Cũ
useEffect(() => {
  if (categories.length === 0) {
    dispatch(fetchCategoryByType(type));
  }
}, [categories.length, dispatch, type]);

// Mới
const { data: categories } = useCategory(type);
```

### Thay thế manual loading checks

```typescript
// Cũ
const loading = useAppSelector(selectCategoryLoading);
const categories = useAppSelector((state) => selectCategoryByType(state, type));

// Mới
const { data: categories, loading } = useCategory(type);
```

## Best Practices

1. **Sử dụng hooks thay vì direct selectors + useEffect**
2. **Chỉ force refresh khi thực sự cần thiết**
3. **Sử dụng loading states riêng biệt cho UI feedback**
4. **Kiểm tra error states và hiển thị thông báo phù hợp**
5. **Sử dụng `useMultipleCategories` khi cần nhiều loại cùng lúc**

## Troubleshooting

### API vẫn bị gọi nhiều lần?

- Kiểm tra xem có sử dụng `useCategory` hook chưa
- Đảm bảo không có multiple useEffect gọi fetch
- Kiểm tra deps array trong useCategoryData

### Loading state không chính xác?

- Sử dụng loading states riêng biệt: `selectCategoryLoadingByType`
- Kiểm tra global operation states: `selectCategoryCreating`, etc.

### Cache không hoạt động?

- Kiểm tra `lastFetched` timestamp
- Đảm bảo không có `forceRefresh: true` không cần thiết
- Cache duration là 5 phút, có thể cần điều chỉnh
