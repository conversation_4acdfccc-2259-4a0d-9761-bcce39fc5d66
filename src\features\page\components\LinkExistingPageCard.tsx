import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Search, Link } from "lucide-react";
import { PageSummary } from "../states/api";

interface LinkExistingPageCardProps {
  hasLinkedPage: boolean;
  availablePagesCount: number;
  searchQuery: string;
  searchedPages: PageSummary[];
  searchPagination: {
    currentPage: number;
    hasMore: boolean;
    loading: boolean;
  };
  onSearchChange: (value: string) => void;
  onLinkPage: (pageId: number) => void;
  onLoadMore: () => void;
}

export const LinkExistingPageCard: React.FC<LinkExistingPageCardProps> = ({
  hasLinkedPage,
  availablePagesCount,
  searchQuery,
  searchedPages,
  searchPagination,
  onSearchChange,
  onLinkPage,
  onLoadMore,
}) => {
  // Debug: Log received props
  console.log("🔗 LinkExistingPageCard received props:", {
    hasLinkedPage,
    availablePagesCount,
    searchQuery,
    searchedPages,
    searchedPagesLength: searchedPages?.length || 0,
    searchPagination,
  });

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>
            {hasLinkedPage
              ? "Liên kết thêm trang khác"
              : "Liên kết trang có sẵn"}
          </span>
          <Badge variant="outline" className="text-xs">
            {availablePagesCount} trang
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {/* Search Input */}
        <div className="mb-4">
          <div className="relative">
            <Search
              className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
              size={16}
            />
            <Input
              value={searchQuery}
              onChange={(e) => onSearchChange(e.target.value)}
              placeholder="Tìm kiếm theo tiêu đề hoặc slug..."
              className="pl-10"
            />
          </div>
        </div>

        {/* Pages List */}
        <div className="space-y-2 max-h-96 overflow-y-auto">
          {searchedPages.length > 0 ? (
            <>
              {searchedPages.map((page) => (
                <div
                  key={page.id}
                  className="flex items-center justify-between p-3 border rounded hover:bg-gray-50"
                >
                  <div className="flex-1">
                    <p className="font-medium">{page.title}</p>
                    <p className="text-sm text-gray-500">
                      ID: {page.id} | {page.slug}
                    </p>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onLinkPage(page.id)}
                  >
                    <Link size={16} className="mr-1" />
                    Liên kết
                  </Button>
                </div>
              ))}

              {/* Load More Button */}
              {searchPagination.hasMore && (
                <div className="pt-3 border-t">
                  <Button
                    variant="outline"
                    onClick={onLoadMore}
                    disabled={searchPagination.loading}
                    className="w-full"
                  >
                    {searchPagination.loading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2" />
                        Đang tải...
                      </>
                    ) : (
                      "Xem thêm"
                    )}
                  </Button>
                </div>
              )}
            </>
          ) : (
            <div className="text-center py-8 text-gray-500">
              {searchQuery
                ? `Không tìm thấy trang nào với từ khóa "${searchQuery}"`
                : "Nhập từ khóa để tìm kiếm trang"}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
