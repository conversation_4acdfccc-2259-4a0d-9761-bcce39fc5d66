import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Trash2,
  Plus,
  ArrowLeft,
  Loader2,
  GripVertical,
  Eye,
  X,
} from "lucide-react";
import { useEffect, useState } from "react";
import { useAppSelector } from "@/store/rootReducer";
import { Album, Media, CreateMediaRequest } from "../states/types";
import { AddMediaModal } from "./AddMediaModal";
import { MediaPreviewModal } from "./MediaPreviewModal";
import { DeleteConfirmDialog } from "./DeleteConfirmDialog";
import { EditAlbumDialog } from "./EditAlbumDialog";
import { useSwitchMode, useAlbumMedia } from "../states/hooks";
import {
  selectCurrentAlbumMedia,
  selectMediaLoading,
  selectMediaError,
  selectMediaHasMore,
  selectMediaCurrentPage,
} from "../states/selector";

interface AlbumDetailProps {
  album: Album;
  onAlbumUpdated?: () => void | Promise<void>;
}

export function AlbumDetail({ album, onAlbumUpdated }: AlbumDetailProps) {
  const { switchMode } = useSwitchMode();
  const {
    loadAlbumMedia,
    loadMoreAlbumMedia,
    addMediaToAlbum,
    updateMediaDetails,
    removeMedia,
    updatePriority,
    setCoverImageHook,
    deleteCoverImageHook,
  } = useAlbumMedia(onAlbumUpdated);

  const albumMedia = useAppSelector(selectCurrentAlbumMedia);
  const mediaLoading = useAppSelector(selectMediaLoading);
  const mediaError = useAppSelector(selectMediaError);
  const mediaHasMore = useAppSelector(selectMediaHasMore);
  const mediaCurrentPage = useAppSelector(selectMediaCurrentPage);

  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [previewMedia, setPreviewMedia] = useState<Media | null>(null);
  const [draggedItem, setDraggedItem] = useState<Media | null>(null);
  const [deleteTarget, setDeleteTarget] = useState<Media | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  useEffect(() => {
    if (album?.id) {
      loadAlbumMedia(album.id);
    }
  }, [album?.id, loadAlbumMedia]);

  const handleBackToList = () => {
    switchMode("list");
  };

  const handleAddMedia = async (
    mediaData: Omit<CreateMediaRequest, "albumId">
  ) => {
    if (album?.id) {
      await addMediaToAlbum(album.id, mediaData);
    }
  };



  const handleEditFromPreview = async (
    media: Media,
    updates: { name: string; description?: { text: string } }
  ) => {
    if (album?.id) {
      await updateMediaDetails(album.id, media.id, media, updates);
    }
  };

  const handleDeleteMedia = async (mediaId: number) => {
    if (album?.id) {
      await removeMedia(album.id, mediaId);
      setShowDeleteConfirm(false);
      setDeleteTarget(null);
    }
  };

  const handleDeleteClick = (media: Media) => {
    setDeleteTarget(media);
    setShowDeleteConfirm(true);
  };

  const handleDeleteFromPreview = async (mediaId: number) => {
    if (album?.id) {
      await removeMedia(album.id, mediaId);
      setPreviewMedia(null); // Close preview after delete
    }
  };

  const handleSetCoverImage = async (imageUrl: string) => {
    if (album?.id) {
      await setCoverImageHook(album.id, imageUrl);
      // Optionally show success message or toast
    }
  };

  const handleDeleteCoverImage = async () => {
    if (album?.id) {
      await deleteCoverImageHook(album.id);
    }
  };

  const handleLoadMore = async () => {
    if (album?.id && mediaHasMore && !mediaLoading) {
      await loadMoreAlbumMedia(album.id, mediaCurrentPage);
    }
  };

  const handleMediaClick = (media: Media) => {
    // Action buttons handle their own stopPropagation, so any click here should open preview
    setPreviewMedia(media);
  };

  const handleDragStart = (e: React.DragEvent, media: Media) => {
    setDraggedItem(media);
    e.dataTransfer.effectAllowed = "move";
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = "move";

    // Add visual feedback for drop target
    const target = e.currentTarget as HTMLElement;
    target.classList.add("border-primary/50", "bg-primary/5");
  };

  const handleDragLeave = (e: React.DragEvent) => {
    // Remove visual feedback when leaving drop target
    const target = e.currentTarget as HTMLElement;
    target.classList.remove("border-primary/50", "bg-primary/5");
  };

  const handleDrop = async (e: React.DragEvent, targetMedia: Media) => {
    e.preventDefault();

    // Clean up visual feedback
    const target = e.currentTarget as HTMLElement;
    target.classList.remove("border-primary/50", "bg-primary/5");

    if (!draggedItem || !album?.id || draggedItem.id === targetMedia.id) {
      setDraggedItem(null);
      return;
    }

    // Calculate new priority based on target position
    const targetIndex = albumMedia.findIndex((m) => m.id === targetMedia.id);
    const newPriority = targetIndex + 1;

    await updatePriority(album.id, draggedItem.id, newPriority);
    setDraggedItem(null);
  };

  return (
    <div className="p-4 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-semibold">Tên thư viện: {album.name}</h1>
        <Button onClick={handleBackToList} className="w-30">
          <ArrowLeft className="w-4 h-4 mr-2" />
          Quay lại
        </Button>
      </div>

      {/* Album Info */}
      <Card>
        <CardHeader>
          <div className="flex items-start justify-between p-4">
            <div className="flex-1">
              <div className="flex items-center justify-between mb-3">
                <CardTitle className="text-lg">{album.name}</CardTitle>
                <EditAlbumDialog album={album} onAlbumUpdated={onAlbumUpdated} />
              </div>
              <div className="text-sm text-muted-foreground">
                <p>
                  <strong>Mô tả:</strong>{" "}
                  {album.description?.text || "Chưa có mô tả"}
                </p>
                <div className="grid grid-cols-2 gap-4 mt-3">
                  <p>
                    <strong>ID:</strong> {album.id}
                  </p>
                  <p>
                    <strong>Loại:</strong> {album.type}
                  </p>
                  <p>
                    <strong>Tạo lúc:</strong>{" "}
                    {album.createdAt
                      ? new Date(album.createdAt).toLocaleString("vi-VN")
                      : "Chưa có thông tin"}
                  </p>
                  <p>
                    <strong>Cập nhật:</strong>{" "}
                    {album.updatedAt
                      ? new Date(album.updatedAt).toLocaleString("vi-VN")
                      : "Chưa có thông tin"}
                  </p>
                </div>
              </div>
            </div>
            {album.coverImage && (
              <div className="ml-4 relative group">
                <img
                  src={album.coverImage}
                  alt={album.name}
                  className="w-24 h-24 object-cover rounded-lg border"
                />
                {/* Delete cover image button */}
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={handleDeleteCoverImage}
                  className="absolute -top-2 -right-2 h-6 w-6 bg-red-500 hover:bg-red-600 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity shadow-lg"
                  title="Xóa ảnh bìa"
                >
                  <X className="w-3 h-3" />
                </Button>
              </div>
            )}
          </div>
        </CardHeader>
      </Card>

      {/* Gallery Section */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between p-4">
            <CardTitle className="text-lg">
              Danh sách ảnh trong thư viện
              {mediaLoading && (
                <Loader2 className="w-4 h-4 ml-2 animate-spin inline" />
              )}
            </CardTitle>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsAddModalOpen(true)}
              >
                <Plus className="w-4 h-4 mr-2" />
                Thêm ảnh
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          {mediaError && (
            <div className="text-center text-red-500 text-sm mb-4 p-3 bg-red-50 rounded-lg">
              ⚠️ Lỗi tải ảnh: {mediaError}
            </div>
          )}

          {/* Gallery Grid - Improved layout */}
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 p-4 pt-0">
            {/* Actual Media from API */}
            {albumMedia.map((media) => (
              <div
                key={media.id}
                className="relative group cursor-pointer bg-gray-50 rounded-lg overflow-hidden border hover:border-primary/50 transition-all duration-200"
                draggable
                onDragStart={(e) => handleDragStart(e, media)}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={(e) => handleDrop(e, media)}
                onClick={() => handleMediaClick(media)}
              >
                {/* Drag handle */}
                <div className="absolute top-2 left-2 z-20 opacity-0 group-hover:opacity-100 transition-opacity">
                  <div className="bg-black/70 rounded p-1.5 cursor-move">
                    <GripVertical className="w-3 h-3 text-white" />
                  </div>
                </div>

                {/* Image Container - Fixed aspect ratio with full image display */}
                <div className="aspect-square w-full relative overflow-hidden">
                  <img
                    src={media.src}
                    alt={media.name}
                    className="w-full h-full object-contain bg-white"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.style.display = "none";
                      target.nextElementSibling?.setAttribute(
                        "style",
                        "display: flex"
                      );
                    }}
                  />
                  {/* Fallback content */}
                  <div
                    className="w-full h-full bg-muted flex items-center justify-center absolute inset-0"
                    style={{ display: "none" }}
                  >
                    <div className="text-center text-xs text-muted-foreground p-2">
                      <div className="font-medium">{media.name}</div>
                      <div>{media.type === "image" ? "Hình ảnh" : "Video"}</div>
                    </div>
                  </div>
                </div>

                {/* Media info */}
                <div className="p-3 bg-white">
                  <h4 className="font-medium text-sm truncate mb-1">
                    {media.name}
                  </h4>
                  <p className="text-xs text-muted-foreground">
                    {media.type === "image" ? "Hình ảnh" : "Video"}
                  </p>
                </div>

                {/* Action buttons - Top right */}
                <div className="media-actions absolute top-2 right-2 z-20 opacity-0 group-hover:opacity-100 transition-opacity">
                  <div className="flex items-center gap-2">
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8 bg-black/70 hover:bg-black/80 text-white"
                      onClick={(e) => {
                        e.stopPropagation();
                        setPreviewMedia(media);
                      }}
                    >
                      <Eye className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8 bg-black/70 hover:bg-black/80 text-white"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteClick(media);
                      }}
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>

                {/* Drag indicator */}
                {draggedItem?.id === media.id && (
                  <div className="absolute inset-0 bg-primary/20 border-2 border-primary border-dashed rounded-lg flex items-center justify-center">
                    <div className="bg-primary text-primary-foreground px-2 py-1 rounded text-xs font-medium">
                      Đang kéo...
                    </div>
                  </div>
                )}

                {/* Drop target indicator */}
                <div className="absolute inset-0 border-2 border-transparent rounded-lg transition-colors drop-target opacity-0"></div>
              </div>
            ))}

            {/* Loading placeholders */}
            {mediaLoading && albumMedia.length === 0 && (
              <>
                {[1, 2, 3, 4, 5].map((index) => (
                  <div
                    key={`loading-${index}`}
                    className="aspect-square relative bg-gray-50 rounded-lg overflow-hidden"
                  >
                    <div className="w-full h-full bg-muted flex items-center justify-center animate-pulse">
                      <Loader2 className="w-6 h-6 text-muted-foreground animate-spin" />
                    </div>
                  </div>
                ))}
              </>
            )}
          </div>

          {/* Load More Button */}
          {mediaHasMore && albumMedia.length > 0 && (
            <div className="flex justify-center mt-6">
              <Button
                variant="outline"
                onClick={handleLoadMore}
                disabled={mediaLoading}
                className="px-8"
              >
                {mediaLoading && (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                )}
                Xem thêm ảnh
              </Button>
            </div>
          )}

          {/* Empty state */}
          {!mediaLoading && albumMedia.length === 0 && !mediaError && (
            <div className="text-center text-muted-foreground text-sm mt-6 p-8 bg-muted/30 rounded-lg">
              <Plus className="w-12 h-12 mx-auto mb-3 text-muted-foreground/50" />
              <p className="text-base font-medium mb-2">Thư viện trống</p>
              <p className="mb-4">
                Chưa có hình ảnh nào trong thư viện này. Hãy thêm hình ảnh để
                bắt đầu!
              </p>
              <Button onClick={() => setIsAddModalOpen(true)}>
                <Plus className="w-4 h-4 mr-2" />
                Thêm ảnh đầu tiên
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Media Preview Modal */}
      <MediaPreviewModal
        media={previewMedia}
        isOpen={!!previewMedia}
        onClose={() => setPreviewMedia(null)}
        onEdit={handleEditFromPreview}
        onDelete={handleDeleteFromPreview}
        onSetCoverImage={handleSetCoverImage}
        albumId={album.id}
      />

      {/* Other Modals */}
      <AddMediaModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onSubmitNewMedia={handleAddMedia}
        isLoading={mediaLoading}
        albumType={album.type}
        currentAlbumId={album.id}
      />

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmDialog
        isOpen={showDeleteConfirm}
        onClose={() => setShowDeleteConfirm(false)}
        onConfirm={() => handleDeleteMedia(deleteTarget?.id || 0)}
        media={deleteTarget}
      />
    </div>
  );
}
