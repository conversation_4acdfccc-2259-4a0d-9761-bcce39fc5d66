/**
 * FieldNode - Renders input field nodes with TanStack Form binding
 */

import React from "react";
import { AnyFieldApi } from "@tanstack/react-form";
import { FormNode } from "../types";
import { useFormContext } from "../context";
import { FieldFactory, FieldType } from "../field/FieldFactory";
import { cn } from "@/lib/utils";

interface FieldNodeProps {
  node: FormNode;
}

export const FieldNode: React.FC<FieldNodeProps> = ({ node }) => {
  const { form, viewOnly } = useFormContext();
  const { id, styles, properties, field } = node;

  // Tạo id duy nhất cho input để label for đúng - phải ở top level
  const genId = React.useMemo(
    () =>
      `field-${field?.objectKey || "unknown"}-${id}-${Math.random().toString(
        6
      )}`,
    [field?.objectKey, id]
  );

  // If no field mapping, render as static content
  if (!field?.objectKey) {
    return (
      <div
        key={id}
        className={`${
          styles?.container ?? ""
        } p-3 border border-orange-300 bg-orange-50 text-orange-700 rounded`}
        data-node-id={id}
      >
        <div className="font-medium">⚠️ Field Configuration Error</div>
        <div className="text-sm">
          Field node '{id}' is missing objectKey mapping
        </div>
        <div className="text-xs mt-1">
          Field object: {field ? JSON.stringify(field, null, 2) : "null"}
        </div>
      </div>
    );
  }

  const fieldName = field.objectKey;
  const fieldType = (field.component as FieldType) || "TextInput";

  const containerClass = cn(
    styles?.container,
    "grid !grid-cols-1 gap-y-2 lg:!grid-cols-[100px_1fr] lg:items-start lg:gap-x-4 lg:gap-y-0"
  );
  const labelClass = cn(
    "font-semibold text-sm lg:text-xs flex items-center min-h-8",
    styles?.label
  );
  const contentClass = cn("flex flex-col gap-y-1 w-full", styles?.content);
  const fieldClass = cn("w-full", styles?.field);
  const errorClass = cn("text-red-500 text-xs mt-1", styles?.error);

  const label = (properties?.label as string) || "";
  const placeholder = (properties?.placeholder as string) || "";
  const options = (properties?.options as (string | number | boolean)[]) || [];
  const labels = (properties?.labels as string[]) || [];
  const isRequired = node.validation?.required?.value === true;

  // If no form context (shouldn't happen in AutoForm), fallback to static
  if (!form) {
    return (
      <div key={id} className={containerClass} data-node-id={id}>
        {label && <label className={labelClass}>{label}</label>}
        <span>No form context available</span>
      </div>
    );
  }

  // Use TanStack Form field binding - giống ValidationAutoForm
  const formInstance = form as {
    Field: React.ComponentType<{
      name: string;
      children: (fieldApi: AnyFieldApi) => React.ReactNode;
    }>;
  };

  return (
    <formInstance.Field key={id} name={fieldName}>
      {(fieldApi) => (
        <div className={containerClass} data-node-id={id}>
          {label && (
            <label htmlFor={genId} className={labelClass}>
              {label}
              {isRequired && <span className="text-red-500 ml-1">*</span>}
            </label>
          )}
          <div className={contentClass}>
            <FieldFactory
              type={fieldType}
              value={fieldApi.state.value}
              onChange={fieldApi.handleChange}
              onBlur={fieldApi.handleBlur}
              disabled={viewOnly || (properties?.disabled as boolean)}
              placeholder={placeholder}
              className={fieldClass}
              id={genId}
              options={options}
              labels={labels}
            />
            {fieldApi.state.meta.errors.length > 0 && (
              <div className={errorClass}>
                {(() => {
                  // Ưu tiên lỗi required nếu có
                  const requiredError = fieldApi.state.meta.errors.find(
                    (err) =>
                      (typeof err === "string" &&
                        err.toLowerCase().includes("bắt buộc")) ||
                      (err &&
                        typeof err.message === "string" &&
                        err.message.toLowerCase().includes("bắt buộc"))
                  );
                  const firstError =
                    requiredError || fieldApi.state.meta.errors[0];
                  return typeof firstError === "string"
                    ? firstError
                    : firstError && typeof firstError.message === "string"
                    ? firstError.message
                    : JSON.stringify(firstError);
                })()}
              </div>
            )}
          </div>
        </div>
      )}
    </formInstance.Field>
  );
};
