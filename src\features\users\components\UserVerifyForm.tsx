import { Button } from "@/components/ui/button";
import React, { useState } from "react";
import { UserData } from "../states/type";
import { toast } from "sonner";
import { lockUserAsync, setCurrentUser, unverifyUserEmailAsync, unverifyUserPhoneAsync, verifyUserEmailAsync, verifyUserPhoneAsync } from "../states/slices";
import { useAppDispatch } from "@/store/rootReducer";
import { Checkbox } from "@/components/ui/checkbox";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon, Save, X } from "lucide-react";
import { cn } from "@/lib/utils";
import { format } from "date-fns"
import { Calendar } from "@/components/ui/calendar";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { vi } from 'date-fns/locale';

interface UserVerifyFormProps {
    user: UserData;
    type: string;
    onCancel: () => void;
}

export const UserVerifyForm: React.FC<UserVerifyFormProps> = ({
    user,
    type,
    onCancel,
}) => {
    const [lockDate, setLockDate] = useState<Date>();
    const [error, setError] = useState("");
    const [checkPermanentLock, setCheckPermanentLock] = useState(true);
    const dispatch = useAppDispatch();

    const verifyPhone = () => {
        dispatch(verifyUserPhoneAsync(user)).unwrap().then(() => {
            toast.success("Xác thực số điện thoại thành công");
            const updatedUser: UserData = {
                ...user,
                phoneVerified: true
            };
            dispatch(setCurrentUser(updatedUser));
            onCancel()
        });
    };

    const verifyEmail = () => {
        dispatch(verifyUserEmailAsync(user)).unwrap().then(() => {
            toast.success("Xác thực email thành công");
            const updatedUser: UserData = {
                ...user,
                emailVerified: true
            };
            dispatch(setCurrentUser(updatedUser));
            onCancel()
        });
    };

    const unverifyPhone = () => {
        dispatch(unverifyUserPhoneAsync(user)).unwrap().then(() => {
            toast.success("Hủy xác thực số điện thoại thành công");
            const updatedUser: UserData = {
                ...user,
                phoneVerified: false
            };
            dispatch(setCurrentUser(updatedUser));
            onCancel()
        });
    };

    const unverifyEmail = () => {
        dispatch(unverifyUserEmailAsync(user)).unwrap().then(() => {
            toast.success("Hủy xác thực email thành công");
            const updatedUser: UserData = {
                ...user,
                emailVerified: false
            };
            dispatch(setCurrentUser(updatedUser));
            onCancel()
        });
    };

    const lockAccount = () => {
        let dateNumber = 0
        if (lockDate) {
            dateNumber = Math.floor(lockDate.getTime() / 1000);
        }

        dispatch(lockUserAsync({ user, dateNumber, locked: true })).unwrap().then(() => {
            toast.success("Khóa tài khoản thành công");
            const updatedUser: UserData = {
                ...user,
                locked: true,
                lockedUntil: dateNumber
            };
            dispatch(setCurrentUser(updatedUser));
            onCancel()
        });
    };

    const unlockAccount = () => {
        const dateNumber = 0

        dispatch(lockUserAsync({ user, dateNumber, locked: false })).unwrap().then(() => {
            toast.success("Mở khóa tài khoản thành công");
            const updatedUser: UserData = {
                ...user,
                locked: false,
                lockedUntil: 0
            };
            dispatch(setCurrentUser(updatedUser));
            onCancel()
        });
    };

    return (
        <form
            onSubmit={(e) => {
                e.preventDefault();
                switch (type) {
                    case "email":
                        verifyEmail();
                        break;
                    case "phone":
                        verifyPhone();
                        break;
                    case "lock":
                        lockAccount();
                        break;
                    case "email_unverify":
                        unverifyEmail();
                        break;
                    case "phone_unverify":
                        unverifyPhone();
                        break;
                    case "unlock":
                        unlockAccount();
                        break;
                    default:
                        console.error("Unknown verification type:", type);
                }
            }}
            className="space-y-4 border rounded-lg bg-white shadow-sm max-w-80 mx-auto"
        >
            <div className=" p-4 space-y-2 pb-0">
                <h1 className="text-2xl">
                    {type === "email" ? (
                        <>Xác thực email</>
                    ) : type === "phone" ? (
                        <>Xác thực số điện thoại</>
                    ) : type === "lock" ? (
                        <>Khóa tài khoản</>
                    ) : type === "unlock" ? (
                        <>Mở khóa tài khoản</>
                    ) : type === "email_unverify" ? (
                        <>Hủy xác thực email</>
                    ) : type === "phone_unverify" && (
                        <>Hủy xác thực số điện thoại</>
                    )}
                </h1>
                <div className="max-w-3xs">
                    {type === "email" ? (
                        <>Bạn có muốn xác thực email <strong>{user.email}</strong> không?</>
                    ) : type === "phone" ? (
                        <>Bạn có muốn xác thực số điện thoại <strong>{user.phone}</strong> không?</>
                    ) : type === "lock" ? (
                        <>Bạn có muốn khoá tài khoản <strong>{user.userName}</strong> hay không?</>
                    ) : type === "unlock" ? (
                        <>Bạn có muốn mở khoá tài khoản <strong>{user.userName}</strong> hay không?</>
                    ) : type === "email_unverify" ? (
                        <>Bạn có muốn hủy xác thực email <strong>{user.email}</strong> không?</>
                    ) : type === "phone_unverify" && (
                        <>Bạn có muốn hủy xác thực số điện thoại <strong>{user.phone}</strong> không?</>
                    )}
                </div>
            </div>
            {
                type === "lock" && (
                    <>
                        <Separator />
                        <div className=" px-4 space-y-1">
                            <div className="flex flex-row gap-4 items-center">
                                <label>Vĩnh viễn?</label>
                                <Checkbox
                                    checked={checkPermanentLock}
                                    onClick={() => {
                                        if (!checkPermanentLock) {
                                            setLockDate(undefined); // nếu chọn vĩnh viễn thì không cần chọn ngày
                                            setError(""); // reset lỗi
                                        } else {
                                            const nextDay = new Date();
                                            nextDay.setDate(nextDay.getDate() + 1);
                                            setLockDate(nextDay); // khoa tai khoan den // thoi diem hien tai + 1 ngay
                                        }
                                        setCheckPermanentLock(!checkPermanentLock)
                                    }}>
                                </Checkbox>
                            </div>
                            {!checkPermanentLock && (
                                <div className="flex flex-col">
                                    <label>Thời hạn đến: </label>
                                    <Popover>
                                        <PopoverTrigger asChild>
                                            <Button
                                                variant={"secondary"}
                                                className={cn(
                                                    "justify-start text-left font-normal gap-4",
                                                    !lockDate && "text-muted-foreground"
                                                )}
                                            >
                                                <CalendarIcon size={16} />
                                                {lockDate ? format(lockDate, "PPPpp", { locale: vi }) : <span>Chọn ngày</span>}
                                            </Button>
                                        </PopoverTrigger>
                                        <PopoverContent className="w-auto p-0" align="start">
                                            <Calendar
                                                mode="single"
                                                selected={lockDate}
                                                onSelect={(newDate) => {
                                                    if (newDate) {
                                                        const currentDate = Math.floor(new Date().getTime());
                                                        if (newDate.getTime() > currentDate) {
                                                            setLockDate(newDate);
                                                            setError("");
                                                        } else {
                                                            const error = `Vui lòng chọn ngày sau ngày hiện tại! (${new Date().toLocaleString("vi-VN")})`;
                                                            setError(error);
                                                        }
                                                    }
                                                }}
                                                locale={vi}
                                            />
                                            <div className="flex gap-2 p-4 justify-around">
                                                <div className="flex flex-col">
                                                    <label>Giờ:</label>
                                                    <Input
                                                        type="number"
                                                        min="0"
                                                        max="23"
                                                        value={lockDate && lockDate.getHours()}
                                                        onChange={(e) => {
                                                            const dateNum = lockDate?.setHours(Number(e.target.value))
                                                            if (dateNum) {
                                                                setLockDate(new Date(dateNum))
                                                            }
                                                        }}
                                                        className="w-12 border rounded px-2 h-[24px]"
                                                    />
                                                </div>
                                                <div className="flex flex-col">
                                                    <label>Phút:</label>
                                                    <Input
                                                        type="number"
                                                        min="0"
                                                        max="59"
                                                        value={lockDate && lockDate.getMinutes()}
                                                        onChange={(e) => {
                                                            const dateNum = lockDate?.setMinutes(Number(e.target.value))
                                                            if (dateNum) {
                                                                setLockDate(new Date(dateNum))
                                                            }
                                                        }}
                                                        className="w-12 border rounded px-2 h-[24px]"
                                                    />
                                                </div>
                                                <div className="flex flex-col">
                                                    <label>Giây:</label>
                                                    <Input
                                                        type="number"
                                                        min="0"
                                                        max="59"
                                                        value={lockDate && lockDate.getSeconds()}
                                                        onChange={(e) => {
                                                            const dateNum = lockDate?.setSeconds(Number(e.target.value))
                                                            if (dateNum) {
                                                                setLockDate(new Date(dateNum))
                                                            }
                                                        }}
                                                        className="w-12 border rounded px-2 h-[24px]"
                                                    />
                                                </div>
                                            </div>
                                        </PopoverContent>
                                    </Popover>
                                </div>
                            )}
                            <em className="text-red-500">
                                {error}
                            </em>
                        </div>
                        <Separator />
                    </>
                )
            }
            <div className="flex justify-end  p-4 pt-0">
                <div className="grid grid-cols-2 gap-3">
                    <Button
                        type="button"
                        variant="secondary"
                        className="w-24 cursor-pointer"
                        size="icon"
                        onClick={onCancel}
                    >
                        <X className="mr-2 h-4 w-4" />
                        Hủy bỏ
                    </Button>
                    <Button
                        type="submit"
                        variant="default"
                        className="w-24 cursor-pointer"
                        size="icon"
                    >
                        <Save className="mr-2 h-4 w-4" />
                        Đồng ý
                    </Button>
                </div>
            </div>
        </form>
    );
};

export default UserVerifyForm;
