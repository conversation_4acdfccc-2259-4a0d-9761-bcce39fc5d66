/**
 * Size Popover Component
 * Popover for width and height selection with visual grid
 */

import React, { useState } from 'react';
import { cn } from '@/lib/utils';
import { ChevronDown, Check } from 'lucide-react';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Button } from '@/components/ui/button';

interface SizeOption {
  value: string;
  label: string;
  description: string;
  category: 'auto' | 'relative' | 'fixed';
}

interface SizePopoverProps {
  value?: string;
  onValueChange?: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  type: 'width' | 'height';
}

// Size options organized by category
const WIDTH_OPTIONS: SizeOption[] = [
  // Auto category
  { value: 'auto', label: 'Auto', description: 'Tự động', category: 'auto' },
  { value: 'min', label: 'Min', description: 'min-content', category: 'auto' },
  { value: 'max', label: 'Max', description: 'max-content', category: 'auto' },
  { value: 'fit', label: 'Fit', description: 'fit-content', category: 'auto' },
  
  // Relative category
  { value: 'full', label: 'Full', description: '100%', category: 'relative' },
  { value: 'screen', label: 'Screen', description: '100vw', category: 'relative' },
  { value: '1/2', label: '1/2', description: '50%', category: 'relative' },
  { value: '1/3', label: '1/3', description: '33%', category: 'relative' },
  { value: '2/3', label: '2/3', description: '67%', category: 'relative' },
  { value: '1/4', label: '1/4', description: '25%', category: 'relative' },
  { value: '3/4', label: '3/4', description: '75%', category: 'relative' },
  
  // Fixed category
  { value: '96', label: '96', description: '24rem', category: 'fixed' },
  { value: '80', label: '80', description: '20rem', category: 'fixed' },
  { value: '64', label: '64', description: '16rem', category: 'fixed' },
  { value: '48', label: '48', description: '12rem', category: 'fixed' },
  { value: '32', label: '32', description: '8rem', category: 'fixed' },
  { value: '24', label: '24', description: '6rem', category: 'fixed' },
  { value: '16', label: '16', description: '4rem', category: 'fixed' },
  { value: '12', label: '12', description: '3rem', category: 'fixed' }
];

const HEIGHT_OPTIONS: SizeOption[] = [
  // Auto category
  { value: 'auto', label: 'Auto', description: 'Tự động', category: 'auto' },
  { value: 'min', label: 'Min', description: 'min-content', category: 'auto' },
  { value: 'max', label: 'Max', description: 'max-content', category: 'auto' },
  { value: 'fit', label: 'Fit', description: 'fit-content', category: 'auto' },
  
  // Relative category
  { value: 'full', label: 'Full', description: '100%', category: 'relative' },
  { value: 'screen', label: 'Screen', description: '100vh', category: 'relative' },
  
  // Fixed category
  { value: '96', label: '96', description: '24rem', category: 'fixed' },
  { value: '80', label: '80', description: '20rem', category: 'fixed' },
  { value: '64', label: '64', description: '16rem', category: 'fixed' },
  { value: '48', label: '48', description: '12rem', category: 'fixed' },
  { value: '32', label: '32', description: '8rem', category: 'fixed' },
  { value: '24', label: '24', description: '6rem', category: 'fixed' },
  { value: '16', label: '16', description: '4rem', category: 'fixed' },
  { value: '12', label: '12', description: '3rem', category: 'fixed' }
];

export const SizePopover: React.FC<SizePopoverProps> = ({
  value = 'auto',
  onValueChange,
  placeholder = 'Chọn kích thước',
  disabled = false,
  type
}) => {
  const [open, setOpen] = useState(false);
  
  const options = type === 'width' ? WIDTH_OPTIONS : HEIGHT_OPTIONS;
  const currentOption = options.find(opt => opt.value === value) || options[0];

  const handleValueSelect = (newValue: string) => {
    onValueChange?.(newValue);
    setOpen(false);
  };

  // Group options by category
  const groupedOptions = options.reduce((acc, option) => {
    if (!acc[option.category]) {
      acc[option.category] = [];
    }
    acc[option.category].push(option);
    return acc;
  }, {} as Record<string, SizeOption[]>);

  const categoryLabels = {
    auto: 'Tự động',
    relative: 'Tương đối',
    fixed: 'Cố định'
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            "h-7 justify-between text-xs",
            !currentOption && "text-muted-foreground",
            disabled && "opacity-50 cursor-not-allowed"
          )}
          disabled={disabled}
        >
          <span className="truncate">
            {currentOption?.label || placeholder}
          </span>
          <ChevronDown className="ml-2 h-3 w-3 opacity-50 flex-shrink-0" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-72 p-2" align="start">
        <div className="space-y-3">
          {Object.entries(groupedOptions).map(([category, categoryOptions]) => (
            <div key={category}>
              <div className="text-xs font-medium text-gray-600 mb-2 px-1">
                {categoryLabels[category as keyof typeof categoryLabels]}
              </div>
              <div className="grid grid-cols-4 gap-1">
                {categoryOptions.map((option) => (
                  <button
                    key={option.value}
                    onClick={() => handleValueSelect(option.value)}
                    className={cn(
                      "flex flex-col items-center p-2 text-xs rounded-lg hover:bg-gray-100 transition-colors relative border border-gray-200",
                      value === option.value && "bg-blue-100 text-blue-900 ring-1 ring-blue-500 border-blue-200"
                    )}
                    title={`${option.label} - ${option.description}`}
                  >
                    <span className="font-medium text-center mb-1">
                      {option.label}
                    </span>
                    <span className="text-gray-500 text-xs text-center leading-tight">
                      {option.description}
                    </span>
                    
                    {value === option.value && (
                      <Check className="h-3 w-3 absolute top-1 right-1 text-blue-600" />
                    )}
                  </button>
                ))}
              </div>
            </div>
          ))}
        </div>
      </PopoverContent>
    </Popover>
  );
};