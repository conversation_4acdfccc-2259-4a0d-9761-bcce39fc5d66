import { BaseResponse, restApi } from "@/api/restApi";
import { API_ENDPOINTS } from "@/api/endpoints";
import { CreateUser, UserData } from "./type";
import { AxiosResponse } from "axios";

export interface UserQueryParams {
  page: number;
  size: number;
  keyword: string;
}

export async function fetchUsers(
  params: UserQueryParams
): Promise<AxiosResponse<BaseResponse<UserData[]>>> {
  const res = await restApi.get<BaseResponse<UserData[]>>(
    API_ENDPOINTS.AUTH.ADMIN.USERS,
    {
      params,
    }
  );
  return res;
}

export async function fetchUser(
  userId: string
): Promise<AxiosResponse<BaseResponse<UserData>>> {
  const res = await restApi.get<BaseResponse<UserData>>(
    `${API_ENDPOINTS.AUTH.ADMIN.USERS}/${userId}`
  );
  return res;
}

export async function createUserApi(
  user: CreateUser
): Promise<AxiosResponse<BaseResponse<UserData>>> {
  const res = await restApi.post<BaseResponse<UserData>>(
    API_ENDPOINTS.AUTH.ADMIN.USERS,
    user
  );
  return res;
}

export async function lockUserApi(
  user: UserData,
  payload: { isLocked?: boolean; lockedUntil?: number }
): Promise<AxiosResponse<BaseResponse<UserData>>> {
  const res = await restApi.put<BaseResponse<UserData>>(
    `${API_ENDPOINTS.AUTH.ADMIN.USERS}/${user.id}/lock`,
    payload
  );
  return res;
}

export async function verifyPhoneApi(
  user: UserData
): Promise<AxiosResponse<BaseResponse<UserData>>> {
  const res = await restApi.put<BaseResponse<UserData>>(
    `${API_ENDPOINTS.AUTH.ADMIN.USERS}/${user.id}/verify-phone/true`
  );
  return res;
}

export async function unverifyPhoneApi(
  user: UserData
): Promise<AxiosResponse<BaseResponse<UserData>>> {
  const res = await restApi.put<BaseResponse<UserData>>(
    `${API_ENDPOINTS.AUTH.ADMIN.USERS}/${user.id}/verify-phone/false`
  );
  return res;
}

export async function verifyEmailApi(
  user: UserData
): Promise<AxiosResponse<BaseResponse<UserData>>> {
  const res = await restApi.put<BaseResponse<UserData>>(
    `${API_ENDPOINTS.AUTH.ADMIN.USERS}/${user.id}/verify-email/true`
  );
  return res;
}

export async function unverifyEmailApi(
  user: UserData
): Promise<AxiosResponse<BaseResponse<UserData>>> {
  const res = await restApi.put<BaseResponse<UserData>>(
    `${API_ENDPOINTS.AUTH.ADMIN.USERS}/${user.id}/verify-email/false`
  );
  return res;
}

// Search users API for public access (used in group management)
export interface SearchUsersParams {
  keyword?: string;
  page?: number;
  size?: number;
}

export interface SearchUsersResponse {
  data: UserData[];
  pagination: {
    page: number;
    size: number;
    total: number;
  };
}

export async function searchUsers(
  params: SearchUsersParams
): Promise<AxiosResponse<SearchUsersResponse>> {
  const cleanKeyword = params.keyword?.trim() || "";
  const res = await restApi.get<SearchUsersResponse>(
    API_ENDPOINTS.AUTH.PUBLIC.USERS.FILTER,
    {
      params: {
        keyword: cleanKeyword,
        page: params.page || 0,
        size: params.size || 10,
      },
    }
  );

  return res;
}
