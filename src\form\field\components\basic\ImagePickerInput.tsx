/**
 * ImagePickerInput - Image picker component for AutoForm
 */

import React from "react";
import { FieldComponentProps } from "../../FieldFactory";
import { Input } from "@/components/ui/input";

export const ImagePickerInput: React.FC<FieldComponentProps> = ({
  value,
  onChange,
  onBlur,
  disabled = false,
  placeholder = "Chọn ảnh...",
  id,
  className = "",
}) => {
  const stringValue = typeof value === "string" ? value : "";

  const handleChange = disabled
    ? undefined
    : (e: React.ChangeEvent<HTMLInputElement>) => onChange(e.target.value);

  return (
    <Input
      type="text"
      id={id}
      value={stringValue}
      onChange={handleChange}
      onBlur={onBlur}
      placeholder={placeholder}
      disabled={disabled}
      className={className}
    />
  );
};
