import React, { useState, useRef } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Shield, X } from "lucide-react";
import { ActionAutoForm, ActionAutoFormRef } from "./ActionAutoForm";
import { CreateActionRequest, Action } from "../states/types";
import { createActionApi } from "../states/api";
import { useAppDispatch } from "@/store/rootReducer";
import { setCurrentAction } from "../states/slices";
import { useSwitchMode } from "../states/hooks";
import { toast } from "sonner";

interface CreateActionDialogProps {
  triggerText?: string;
  onActionCreated?: (action: Action) => void;
}

export const CreateActionDialog: React.FC<CreateActionDialogProps> = ({
  triggerText = "Tạo quyền",
  onActionCreated,
}) => {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const formRef = useRef<ActionAutoFormRef>(null);
  const dispatch = useAppDispatch();
  const switchMode = useSwitchMode();

  const handleSubmit = async (data: CreateActionRequest) => {
    try {
      setLoading(true);

      console.log("Creating action with data:", data);

      const response = await createActionApi(data);

      if (response.status === 200 && response.data.data) {
        const newAction = response.data.data as Action;

        // Update Redux state
        dispatch(setCurrentAction(newAction));

        // Navigate to detail view
        switchMode("detail", String(newAction.id));

        // Notify parent component
        onActionCreated?.(newAction);

        // Close dialog and show success message
        setOpen(false);
        toast.success("Tạo quyền thành công!");
      } else {
        throw new Error("Failed to create action");
      }
    } catch (error) {
      console.error("Error creating action:", error);
      toast.error("Có lỗi xảy ra khi tạo quyền");
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    if (!loading) {
      setOpen(false);
    }
  };

  const handleFormSubmit = () => {
    if (formRef.current) {
      formRef.current.submitForm();
    }
  };

  if (!open) {
    return (
      <Button onClick={() => setOpen(true)}>
        <Shield className="w-4 h-4 mr-2" />
        {triggerText}
      </Button>
    );
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <div className="relative bg-white rounded-lg shadow-lg max-w-lg w-full mx-4">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b">
          <h2 className="text-lg font-semibold">Tạo quyền mới</h2>
          <Button
            variant="ghost"
            size="icon"
            onClick={handleCancel}
            disabled={loading}
          >
            <X className="w-4 h-4" />
          </Button>
        </div>

        {/* Content */}
        <div className="p-4">
          <ActionAutoForm
            ref={formRef}
            onSubmit={handleSubmit}
            viewOnly={loading}
          />
        </div>

        {/* Footer Buttons */}
        <div className="flex justify-end gap-2 p-4 border-t bg-gray-50">
          <Button
            className="w-24"
            variant="outline"
            size="sm"
            onClick={handleCancel}
            disabled={loading}
          >
            Hủy
          </Button>
          <Button
            className="w-24"
            variant="default"
            size="sm"
            onClick={handleFormSubmit}
            disabled={loading}
          >
            {loading ? "Đang tạo..." : "Tạo"}
          </Button>
        </div>

        {/* Loading overlay */}
        {loading && (
          <div className="absolute inset-0 bg-white/80 flex items-center justify-center rounded-lg">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
              <p className="text-sm text-muted-foreground">Đang tạo quyền...</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
