import { Question<PERSON><PERSON> } from "./api";
import { Question, QuestionQueryParams } from "./types";
import { mockQuestions } from "../data/mockData";

/**
 * Fetch questions with query params (following post pattern)
 * Returns array of questions based on filter criteria
 */
export async function fetchQuestionsList(
  params: QuestionQueryParams
): Promise<Question[]> {
  try {
    console.log("🚀 fetchQuestionsList called with params:", params);

    // Try real API first
    const response = await QuestionAPI.getAllQuestions(params);
    console.log("✅ Real API response:", response.data);

    // Handle different response formats
    let questions: Question[] = [];
    if (Array.isArray(response.data)) {
      questions = response.data;
    } else if (response.data && Array.isArray((response.data as any).data)) {
      questions = (response.data as any).data;
    }

    // Filter by status if provided (in case backend doesn't filter)
    if (params.status && questions.length > 0) {
      questions = questions.filter((q) => q.status === params.status);
    }

    return questions;
  } catch (error) {
    console.warn("⚠️ Real API failed, using mock data:", error);

    // Fallback to mock data with filtering
    let filteredQuestions = [...mockQuestions];

    // Filter by status
    if (params.status) {
      filteredQuestions = filteredQuestions.filter(
        (q) => q.status === params.status
      );
    }

    // Filter by keyword
    if (params.keyword) {
      const keyword = params.keyword.toLowerCase();
      filteredQuestions = filteredQuestions.filter(
        (q) =>
          q.content.title.toLowerCase().includes(keyword) ||
          q.content.question.toLowerCase().includes(keyword) ||
          q.asker.fullName.toLowerCase().includes(keyword) ||
          q.asker.email.toLowerCase().includes(keyword) ||
          q.topic.toLowerCase().includes(keyword)
      );
    }

    // Handle pagination
    const page = params.page || 0;
    const size = params.size || 20;
    const startIndex = page * size;
    const endIndex = startIndex + size;

    const paginatedQuestions = filteredQuestions.slice(startIndex, endIndex);

    console.log("📊 Mock data filtered:", {
      total: filteredQuestions.length,
      page,
      size,
      returned: paginatedQuestions.length,
      status: params.status,
      keyword: params.keyword,
    });

    return paginatedQuestions;
  }
}
