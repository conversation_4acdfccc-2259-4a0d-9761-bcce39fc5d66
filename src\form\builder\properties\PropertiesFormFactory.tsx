/**
 * Properties Form Factory
 * Simplified UI for node properties
 */

import React from 'react';
import { FormNode } from '@/form/types';
import { PropertyTabs } from './components/PropertyTabs';
import { PropertyTab } from './types';

interface PropertiesFormFactoryProps {
  node: FormNode | null;
  onChange: (updates: Partial<FormNode>) => void;
  disabled?: boolean;
  activeTab: PropertyTab;
  onTabChange: (tab: PropertyTab) => void;
}

/**
 * Simplified properties form without header clutter
 */
export const PropertiesFormFactory: React.FC<PropertiesFormFactoryProps> = ({
  node,
  onChange,
  disabled = false,
  activeTab,
  onTabChange
}) => {
  // Luôn có node được chọn (không bao giờ null)
  if (!node) {
    return (
      <div className="flex items-center justify-center h-full text-center">
        <div className="text-gray-500 text-sm">
          No node selected
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* Simplified Properties Form Content */}
      <div className="flex-1 overflow-auto p-3">
        <PropertyTabs
          node={node}
          onChange={onChange}
          disabled={disabled}
          activeTab={activeTab}
          onTabChange={onTabChange}
        />
      </div>
    </div>
  );
};