/**
 * PickIconInput - Icon picker component for AutoForm
 */

import React from "react";
import { FieldComponentProps } from "../../FieldFactory";
import { Button } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { IconOptions } from "@/components/other/DynamicIcon";

export const PickIconInput: React.FC<FieldComponentProps> = ({
  value = "Dot",
  onChange,
  onBlur,
  disabled = false,
  id,
  className = "",
}) => {
  const [open, setOpen] = React.useState(false);
  const selected = IconOptions.find((opt) => opt.value === value);

  const handleIconSelect = (iconValue: string) => {
    onChange(iconValue);
    setOpen(false);
  };

  return (
    <div className={cn("flex items-center gap-2", className)} onBlur={onBlur}>
      <div className="h-8 w-8 flex items-center justify-center border rounded-md">
        {selected?.label ?? "?"}
      </div>
      <Popover
        open={open}
        onOpenChange={(v) => !disabled && setOpen(v as boolean)}
      >
        <PopoverTrigger asChild>
          <button
            id={id}
            type="button"
            disabled={disabled}
            className={cn(
              "text-xs underline bg-transparent border-none p-0",
              disabled
                ? "cursor-not-allowed text-muted-foreground opacity-50"
                : "cursor-pointer text-blue-500"
            )}
          >
            Sửa
          </button>
        </PopoverTrigger>
        <PopoverContent className="grid grid-cols-6 gap-2 max-w-xs">
          {IconOptions.map((icon) => (
            <Button
              key={icon.value}
              variant={icon.value === value ? "default" : "ghost"}
              className="h-8 w-8 p-2"
              disabled={disabled}
              onClick={() => handleIconSelect(icon.value)}
            >
              {icon.label}
            </Button>
          ))}
        </PopoverContent>
      </Popover>
    </div>
  );
};
