/**
 * Shadow Popover Component
 * Popover with visual shadow options
 */

import React, { useState } from 'react';
import { cn } from '@/lib/utils';
import { ChevronDown } from 'lucide-react';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Button } from '@/components/ui/button';

interface ShadowOption {
  value: string;
  label: string;
  className: string;
}

interface ShadowPopoverProps {
  value?: string;
  onValueChange?: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
}

const SHADOW_OPTIONS: ShadowOption[] = [
  { value: 'none', label: 'None', className: 'shadow-none' },
  { value: 'sm', label: 'Small', className: 'shadow-sm' },
  { value: 'default', label: 'Default', className: 'shadow' },
  { value: 'md', label: 'Medium', className: 'shadow-md' },
  { value: 'lg', label: 'Large', className: 'shadow-lg' },
  { value: 'xl', label: 'X-Large', className: 'shadow-xl' },
  { value: '2xl', label: '2X-Large', className: 'shadow-2xl' },
  { value: 'inner', label: 'Inner', className: 'shadow-inner' }
];

export const ShadowPopover: React.FC<ShadowPopoverProps> = ({
  value = 'none',
  onValueChange,
  placeholder = 'Choose shadow',
  disabled = false
}) => {
  const [open, setOpen] = useState(false);
  
  const currentOption = SHADOW_OPTIONS.find(opt => opt.value === value) || SHADOW_OPTIONS[0];

  const handleValueSelect = (newValue: string) => {
    onValueChange?.(newValue);
    setOpen(false);
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            "h-7 justify-between text-xs",
            disabled && "opacity-50 cursor-not-allowed"
          )}
          disabled={disabled}
        >
          <span>{currentOption.label}</span>
          <ChevronDown className="ml-2 h-3 w-3 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-56 p-2" align="start">
        <div className="grid grid-cols-2 gap-1">
          {SHADOW_OPTIONS.map((option) => (
            <button
              key={option.value}
              onClick={() => handleValueSelect(option.value)}
              className={cn(
                "flex items-center justify-center p-3 text-xs rounded hover:bg-gray-100 transition-colors relative",
                value === option.value && "bg-blue-100 text-blue-900 ring-1 ring-blue-500"
              )}
            >
              <div 
                className={cn(
                  "w-8 h-8 bg-white rounded",
                  option.className
                )}
              />
              <span className="absolute bottom-1 text-xs">
                {option.label}
              </span>
            </button>
          ))}
        </div>
      </PopoverContent>
    </Popover>
  );
};