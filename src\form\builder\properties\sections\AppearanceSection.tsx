/**
 * Appearance Section Component
 * For background color and corner radius controls
 */

import React from 'react';
import { FormNode } from '@/form/types';
import { PropertiesFormProps } from '../types';
import { Label } from '@/components/ui/label';
import { ColorPicker } from '../input/ColorPicker';
import { SelectPopover } from '../input/SelectPopover';

export const AppearanceSection: React.FC<PropertiesFormProps> = ({
  node,
  onChange,
  disabled = false
}) => {
  const currentClasses = node.styles?.container || '';

  // Extract current background color
  const getCurrentBackgroundColor = () => {
    const bgMatch = currentClasses.match(/bg-(\w+-\d+|\w+)/);
    if (bgMatch) {
      return bgMatch[1];
    }
    return 'transparent';
  };

  // Extract current text color
  const getCurrentTextColor = () => {
    const textMatch = currentClasses.match(/text-(\w+-\d+|\w+)/);
    if (textMatch) {
      const color = textMatch[1];
      // Filter out text size classes
      if (!['xs', 'sm', 'base', 'lg', 'xl', '2xl', '3xl', '4xl', '5xl', '6xl', '7xl', '8xl', '9xl'].includes(color)) {
        return color;
      }
    }
    return 'gray-900';
  };

  // Extract current opacity
  const getCurrentOpacity = () => {
    const opacityMatch = currentClasses.match(/opacity-(\d+)/);
    return opacityMatch ? opacityMatch[1] : '100';
  };

  // Update background color
  const updateBackgroundColor = (color: string) => {
    let newClasses = currentClasses;

    // Remove existing background color classes
    newClasses = newClasses
      .replace(/\bbg-[\w-]+/g, '')
      .replace(/\s+/g, ' ')
      .trim();

    // Add new background color class
    if (color && color !== 'transparent') {
      newClasses = `${newClasses} bg-${color}`.trim();
    }

    onChange({
      styles: {
        ...node.styles,
        container: newClasses
      }
    });
  };

  // Update text color
  const updateTextColor = (color: string) => {
    let newClasses = currentClasses;

    // Remove existing text color classes (preserve text size classes)
    newClasses = newClasses
      .replace(/\btext-(?!xs|sm|base|lg|xl|2xl|3xl|4xl|5xl|6xl|7xl|8xl|9xl\b)[\w-]+/g, '')
      .replace(/\s+/g, ' ')
      .trim();

    // Add new text color class
    if (color && color !== 'gray-900') {
      newClasses = `${newClasses} text-${color}`.trim();
    }

    onChange({
      styles: {
        ...node.styles,
        container: newClasses
      }
    });
  };

  // Update opacity
  const updateOpacity = (opacity: string) => {
    let newClasses = currentClasses;

    // Remove existing opacity classes
    newClasses = newClasses
      .replace(/\bopacity-\d+/g, '')
      .replace(/\s+/g, ' ')
      .trim();

    // Add new opacity class
    if (opacity && opacity !== '100') {
      newClasses = `${newClasses} opacity-${opacity}`.trim();
    }

    onChange({
      styles: {
        ...node.styles,
        container: newClasses
      }
    });
  };

  const opacityOptions = [
    { value: '0', label: '0% (Trong suốt)' },
    { value: '5', label: '5%' },
    { value: '10', label: '10%' },
    { value: '20', label: '20%' },
    { value: '25', label: '25%' },
    { value: '30', label: '30%' },
    { value: '40', label: '40%' },
    { value: '50', label: '50%' },
    { value: '60', label: '60%' },
    { value: '70', label: '70%' },
    { value: '75', label: '75%' },
    { value: '80', label: '80%' },
    { value: '90', label: '90%' },
    { value: '95', label: '95%' },
    { value: '100', label: '100% (Mặc định)' }
  ];

  return (
    <div className="space-y-2">
      {/* Background Color */}
      <div className="space-y-1">
        <Label className="text-xs">Nền</Label>
        <ColorPicker
          value={getCurrentBackgroundColor()}
          onChange={updateBackgroundColor}
          disabled={disabled}
          placeholder="Chọn màu nền"
          className="w-full text-xs"
        />
      </div>

      {/* Text Color */}
      <div className="space-y-1">
        <Label className="text-xs">Chữ</Label>
        <ColorPicker
          value={getCurrentTextColor()}
          onChange={updateTextColor}
          disabled={disabled}
          placeholder="Chọn màu chữ"
          className="w-full text-xs"
        />
      </div>

      {/* Opacity */}
      <div className="space-y-1">
        <Label className="text-xs">Opacity</Label>
        <SelectPopover
          value={getCurrentOpacity()}
          onValueChange={updateOpacity}
          disabled={disabled}
          options={opacityOptions}
          placeholder="Choose opacity"
        />
      </div>

      {/* Font Size - for text nodes */}
      {(node.type === 'title' || node.type === 'field') && (
        <div className="space-y-1">
          <Label className="text-xs">Size</Label>
          <SelectPopover
            value={(() => {
              const sizeMatch = currentClasses.match(/text-(xs|sm|base|lg|xl|2xl|3xl|4xl|5xl|6xl|7xl|8xl|9xl)/);
              return sizeMatch ? sizeMatch[1] : 'base';
            })()}
            onValueChange={(size) => {
              let newClasses = currentClasses;
              
              // Remove existing text size classes
              newClasses = newClasses
                .replace(/\btext-(xs|sm|base|lg|xl|2xl|3xl|4xl|5xl|6xl|7xl|8xl|9xl)\b/g, '')
                .replace(/\s+/g, ' ')
                .trim();

              // Add new text size class
              if (size && size !== 'base') {
                newClasses = `${newClasses} text-${size}`.trim();
              }

              onChange({
                styles: {
                  ...node.styles,
                  container: newClasses
                }
              });
            }}
            disabled={disabled}
            options={[
              { value: 'xs', label: 'Extra Small', description: '12px' },
              { value: 'sm', label: 'Small', description: '14px' },
              { value: 'base', label: 'Base', description: '16px' },
              { value: 'lg', label: 'Large', description: '18px' },
              { value: 'xl', label: 'Extra Large', description: '20px' },
              { value: '2xl', label: '2X Large', description: '24px' },
              { value: '3xl', label: '3X Large', description: '30px' },
              { value: '4xl', label: '4X Large', description: '36px' },
              { value: '5xl', label: '5X Large', description: '48px' }
            ]}
            placeholder="Choose size"
          />
        </div>
      )}

      {/* Font Weight - for text nodes */}
      {(node.type === 'title' || node.type === 'field') && (
        <div className="space-y-1">
          <Label className="text-xs">Weight</Label>
          <SelectPopover
            value={(() => {
              const weightMatch = currentClasses.match(/font-(thin|extralight|light|normal|medium|semibold|bold|extrabold|black)/);
              return weightMatch ? weightMatch[1] : 'normal';
            })()}
            onValueChange={(weight) => {
              let newClasses = currentClasses;
              
              // Remove existing font weight classes
              newClasses = newClasses
                .replace(/\bfont-(thin|extralight|light|normal|medium|semibold|bold|extrabold|black)\b/g, '')
                .replace(/\s+/g, ' ')
                .trim();

              // Add new font weight class
              if (weight && weight !== 'normal') {
                newClasses = `${newClasses} font-${weight}`.trim();
              }

              onChange({
                styles: {
                  ...node.styles,
                  container: newClasses
                }
              });
            }}
            disabled={disabled}
            options={[
              { value: 'thin', label: 'Thin', description: '100' },
              { value: 'extralight', label: 'Extra Light', description: '200' },
              { value: 'light', label: 'Light', description: '300' },
              { value: 'normal', label: 'Normal', description: '400' },
              { value: 'medium', label: 'Medium', description: '500' },
              { value: 'semibold', label: 'Semibold', description: '600' },
              { value: 'bold', label: 'Bold', description: '700' },
              { value: 'extrabold', label: 'Extra Bold', description: '800' },
              { value: 'black', label: 'Black', description: '900' }
            ]}
            placeholder="Choose weight"
          />
        </div>
      )}
    </div>
  );
};