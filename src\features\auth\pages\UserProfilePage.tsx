import React from "react";
import { useSelector } from "react-redux";
import { RootState, useAppDispatch } from "@/store/rootReducer";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  User,
  Mail,
  Phone,
  Calendar,
  Shield,
  LogOut,
  Key,
  Settings,
} from "lucide-react";
import { logoutAsync } from "../states/slices";
import { useNavigate } from "react-router-dom";

const UserProfilePage: React.FC = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { account, is_authenticated, loading } = useSelector(
    (state: RootState) => state.auth
  );

  if (!is_authenticated || !account) {
    return (
      <div className="flex items-center justify-center h-full">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6">
            <div className="text-center">
              <Shield className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <h2 className="text-xl font-semibold text-gray-600 mb-2">
                Chưa đăng nhập
              </h2>
              <p className="text-gray-500">
                Vui lòng đăng nhập để xem thông tin cá nhân
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  const handleLogout = async () => {
    try {
      await dispatch(logoutAsync()).unwrap();
      navigate("/");
    } catch (error) {
      console.error("Logout failed:", error);
    }
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString("vi-VN", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header Section */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Thông tin cá nhân
          </h1>
          <p className="text-muted-foreground">
            Quản lý thông tin tài khoản và cài đặt cá nhân
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            onClick={handleLogout}
            variant="destructive"
            size="sm"
            disabled={loading}
          >
            <LogOut className="w-4 h-4 mr-2" />
            {loading ? "Đang đăng xuất..." : "Đăng xuất"}
          </Button>
        </div>
      </div>

      <Tabs defaultValue="profile" className="space-y-4">
        <TabsList>
          <TabsTrigger value="profile">Hồ sơ</TabsTrigger>
          <TabsTrigger value="security">Bảo mật</TabsTrigger>
          <TabsTrigger value="settings">Cài đặt</TabsTrigger>
        </TabsList>

        <TabsContent value="profile" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Thông tin cơ bản</CardTitle>
              <CardDescription>
                Thông tin cá nhân và liên hệ của bạn
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Avatar Section */}
              <div className="flex items-center gap-4">
                <Avatar className="w-20 h-20">
                  <AvatarFallback className="text-lg">
                    {account.userName.charAt(0).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div className="space-y-1">
                  <h3 className="text-xl font-semibold">{account.userName}</h3>
                  <p className="text-sm text-muted-foreground">
                    {account.email}
                  </p>
                  <Badge variant="secondary" className="text-xs">
                    Tài khoản đã xác thực
                  </Badge>
                </div>
              </div>

              <Separator />

              {/* Profile Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="userName" className="flex items-center gap-2">
                    <User className="w-4 h-4" />
                    Tên đăng nhập
                  </Label>
                  <div className="p-3 bg-muted rounded-md">
                    {account.userName}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email" className="flex items-center gap-2">
                    <Mail className="w-4 h-4" />
                    Email
                  </Label>
                  <div className="p-3 bg-muted rounded-md">{account.email}</div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="phone" className="flex items-center gap-2">
                    <Phone className="w-4 h-4" />
                    Số điện thoại
                  </Label>
                  <div className="p-3 bg-muted rounded-md">
                    {account.phone || "Chưa cập nhật"}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label className="flex items-center gap-2">
                    <Calendar className="w-4 h-4" />
                    Ngày tạo tài khoản
                  </Label>
                  <div className="p-3 bg-muted rounded-md">
                    {account.createdAt ? formatDate(account.createdAt) : "N/A"}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="security" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Key className="w-5 h-5" />
                Bảo mật tài khoản
              </CardTitle>
              <CardDescription>
                Quản lý mật khẩu và cài đặt bảo mật
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="flex justify-between items-center p-4 border rounded-lg">
                  <div>
                    <h4 className="font-medium">Đổi mật khẩu</h4>
                    <p className="text-sm text-muted-foreground">
                      Cập nhật mật khẩu để bảo vệ tài khoản
                    </p>
                  </div>
                  <Button variant="outline" size="sm">
                    Đổi mật khẩu
                  </Button>
                </div>

                <div className="flex justify-between items-center p-4 border rounded-lg">
                  <div>
                    <h4 className="font-medium">Xác thực hai yếu tố</h4>
                    <p className="text-sm text-muted-foreground">
                      Tăng cường bảo mật với xác thực 2FA
                    </p>
                  </div>
                  <Badge variant="outline">Chưa kích hoạt</Badge>
                </div>

                <div className="flex justify-between items-center p-4 border rounded-lg">
                  <div>
                    <h4 className="font-medium">Phiên đăng nhập</h4>
                    <p className="text-sm text-muted-foreground">
                      Quản lý các thiết bị đã đăng nhập
                    </p>
                  </div>
                  <Button variant="outline" size="sm">
                    Xem chi tiết
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="w-5 h-5" />
                Cài đặt ứng dụng
              </CardTitle>
              <CardDescription>
                Tùy chỉnh trải nghiệm sử dụng ứng dụng
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="flex justify-between items-center p-4 border rounded-lg">
                  <div>
                    <h4 className="font-medium">Ngôn ngữ</h4>
                    <p className="text-sm text-muted-foreground">
                      Chọn ngôn ngữ hiển thị
                    </p>
                  </div>
                  <Badge variant="secondary">Tiếng Việt</Badge>
                </div>

                <div className="flex justify-between items-center p-4 border rounded-lg">
                  <div>
                    <h4 className="font-medium">Chế độ hiển thị</h4>
                    <p className="text-sm text-muted-foreground">
                      Chọn giao diện sáng hoặc tối
                    </p>
                  </div>
                  <Badge variant="secondary">Hệ thống</Badge>
                </div>

                <div className="flex justify-between items-center p-4 border rounded-lg">
                  <div>
                    <h4 className="font-medium">Thông báo</h4>
                    <p className="text-sm text-muted-foreground">
                      Cài đặt thông báo qua email và trình duyệt
                    </p>
                  </div>
                  <Button variant="outline" size="sm">
                    Cài đặt
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default UserProfilePage;
