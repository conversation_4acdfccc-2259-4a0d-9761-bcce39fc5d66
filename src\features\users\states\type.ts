import { FieldValue } from "@/components/form/registry";

export interface UserResponse {
  data: UserData;
}

export interface UserData {
  id: number;
  userName: string;
  fullName: string;
  email: string;
  emailVerified: boolean;
  phone: string;
  phoneVerified: boolean;
  locked: boolean;
  lockedUntil: number | null;
  metadata: Record<string, FieldValue>;
  createdAt: number;
  updatedAt: number;
}

export interface CreateUser {
  userName: string;
  password: string;
  fullName: string;
  email: string;
  phone: string;
}
