import React from "react";
import { ActionAutoForm } from "../components/ActionAutoForm";
import { CreateActionRequest } from "../states/types";

export const ActionCreatePage: React.FC = () => {
  const handleSubmit = (data: CreateActionRequest) => {
    console.log("Action create data:", data);
  };

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6"><PERSON><PERSON><PERSON> quyền mới</h1>
      <ActionAutoForm onSubmit={handleSubmit} />
    </div>
  );
};
