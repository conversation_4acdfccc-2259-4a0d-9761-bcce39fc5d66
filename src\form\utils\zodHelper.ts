import { z, ZodTypeAny } from "zod";
import { FormNode, ValidationRules, FieldValue } from "../types";

// Việt hóa lỗi Zod
z.setErrorMap((issue, ctx) => {
  switch (issue.code) {
    case "invalid_type":
      if (issue.expected === "number" && issue.received === "string") {
        return { message: "Trường này phải là số" };
      }
      if (issue.expected === "string" && issue.received === "number") {
        return { message: "Trường này phải là chuỗi ký tự" };
      }
      if (issue.expected === "boolean") {
        return { message: "Trường này phải là đúng/sai" };
      }
      return { message: "Sai kiểu dữ liệu" };
    case "too_small":
      return { message: `<PERSON>i<PERSON> trị quá nhỏ (tối thiểu ${issue.minimum})` };
    case "too_big":
      return { message: `<PERSON><PERSON><PERSON> trị quá lớn (tối đa ${issue.maximum})` };
    case "invalid_string":
      return { message: "Đị<PERSON> dạng chuỗi không hợp lệ" };
    case "invalid_enum_value":
      return { message: "Gi<PERSON> trị không hợp lệ" };
    case "invalid_literal":
      return { message: "Giá trị không hợp lệ" };
    case "unrecognized_keys":
      return { message: "Có trường không hợp lệ trong dữ liệu" };
    case "invalid_union":
      return { message: "Dữ liệu không hợp lệ" };
    case "invalid_arguments":
      return { message: "Tham số không hợp lệ" };
    case "invalid_date":
      return { message: "Ngày không hợp lệ" };
    case "custom":
      return { message: issue.message || "Dữ liệu không hợp lệ" };
    default:
      return { message: ctx.defaultError };
  }
});

// Default error messages
const DEFAULT_MESSAGES = {
  REQUIRED: "Nội dung này là bắt buộc",
  MIN_LENGTH: (min: number) => `Độ dài tối thiểu là ${min} ký tự`,
  MAX_LENGTH: (max: number) => `Độ dài tối đa là ${max} ký tự`,
  MIN_VALUE: (min: number) => `Giá trị tối thiểu là ${min}`,
  MAX_VALUE: (max: number) => `Giá trị tối đa là ${max}`,
  PATTERN: "Nội dung không đúng định dạng",
};

/**
 * Convert form validation rules to Zod schema for a field
 */
export function validationRulesToZod(
  fieldType: string,
  validation?: ValidationRules,
  defaultValue?: FieldValue
): ZodTypeAny {
  const v = validation ?? {};
  const isRequired = v.required?.value === true;
  const requiredMessage = v.required?.error || DEFAULT_MESSAGES.REQUIRED;

  // Check if field can be null (defaultValue is null or not required)
  const canBeNull = defaultValue === null && !isRequired;

  switch (fieldType.toLowerCase()) {
    case "string":
    case "text":
    case "email":
    case "url":
    case "password": {
      let schema = z.string();
      if (v.minLength) {
        const message =
          v.minLength.error || DEFAULT_MESSAGES.MIN_LENGTH(v.minLength.value);
        schema = schema.min(v.minLength.value, { message });
      }
      if (v.maxLength) {
        const message =
          v.maxLength.error || DEFAULT_MESSAGES.MAX_LENGTH(v.maxLength.value);
        schema = schema.max(v.maxLength.value, { message });
      }
      if (v.pattern) {
        const message = v.pattern.error || DEFAULT_MESSAGES.PATTERN;
        schema = schema.regex(new RegExp(v.pattern.value), { message });
      }
      return isRequired ? schema.nonempty(requiredMessage) : schema.optional();
    }
    case "number": {
      let schema = z.number({ required_error: requiredMessage });
      if (v.min) {
        const message = v.min.error || DEFAULT_MESSAGES.MIN_VALUE(v.min.value);
        schema = schema.min(v.min.value, { message });
      }
      if (v.max) {
        const message = v.max.error || DEFAULT_MESSAGES.MAX_VALUE(v.max.value);
        schema = schema.max(v.max.value, { message });
      }

      // Handle nullable numbers
      if (canBeNull) {
        return z.union([schema, z.null()]).optional();
      }

      return isRequired ? schema : schema.optional();
    }
    case "boolean": {
      const schema = z.boolean({ required_error: requiredMessage });
      return isRequired ? schema : schema.optional();
    }
    case "string[]": {
      const schema = z.array(z.string());
      return isRequired ? schema.nonempty(requiredMessage) : schema.optional();
    }
    case "number[]": {
      const schema = z.array(z.number());
      return isRequired ? schema.nonempty(requiredMessage) : schema.optional();
    }
    case "boolean[]": {
      const schema = z.array(z.boolean());
      return isRequired ? schema.nonempty(requiredMessage) : schema.optional();
    }
    case "select":
    case "radio": {
      const schema = z.string();
      return isRequired ? schema.nonempty(requiredMessage) : schema.optional();
    }
    case "checkbox": {
      const schema = z.boolean({ required_error: requiredMessage });
      return isRequired ? schema : schema.optional();
    }
    case "multi-checkbox": {
      const schema = z.array(z.boolean());
      return isRequired ? schema.nonempty(requiredMessage) : schema.optional();
    }
    case "multi-select": {
      const schema = z.array(z.string());
      return isRequired ? schema.nonempty(requiredMessage) : schema.optional();
    }
    case "date":
    case "time":
    case "datetime-local":
    case "file": {
      const schema = z.string();
      return isRequired ? schema.nonempty(requiredMessage) : schema.optional();
    }
    case "null": {
      return z.null();
    }
    case "undefined": {
      return z.undefined();
    }
    default:
      return z.any();
  }
}

/**
 * Build Zod schema from form nodes
 */
export function buildFormZodSchema(rootNode: FormNode): ZodTypeAny {
  const shape: Record<string, ZodTypeAny> = {};
  const crossFieldValidations: Array<{
    field: string;
    matchField: string;
    error: string;
  }> = [];

  const processNode = (node: FormNode): void => {
    // Only process field nodes that have field mapping
    if (node.type === "field" && node.field?.objectKey) {
      const objectKey = node.field.objectKey;
      // Ưu tiên lấy dataType từ node.field, fallback sang properties.type
      const fieldType =
        (node.field.dataType as string) ||
        (node.properties?.type as string) ||
        "text";
      const defaultValue = node.field.defaultValue;

      shape[objectKey] = validationRulesToZod(
        fieldType,
        node.validation,
        defaultValue
      );

      // Collect cross-field validations
      if (node.validation?.matchField) {
        crossFieldValidations.push({
          field: objectKey,
          matchField: node.validation.matchField.value,
          error:
            node.validation.matchField.error || "Các trường không khớp nhau",
        });
      }
    }

    // Duyệt children: chỉ node.children (không cần properties.children)
    const children = Array.isArray(node.children) ? node.children : [];
    (children as FormNode[]).forEach(processNode);
  };

  processNode(rootNode);

  // Create base schema
  let schema = z.object(shape) as ZodTypeAny;

  // Apply cross-field validations
  crossFieldValidations.forEach(({ field, matchField, error }) => {
    schema = schema.refine((data) => data[field] === data[matchField], {
      message: error,
      path: [field], // Error will be attached to the field being validated
    });
  });

  return schema;
}

/**
 * Extract default values from form node for TanStack Form
 */
export function extractDefaultValues(
  rootNode: FormNode
): Record<string, FieldValue> {
  const defaults: Record<string, FieldValue> = {};

  const processNode = (node: FormNode): void => {
    // Only process field nodes that have field mapping
    if (node.type === "field" && node.field?.objectKey) {
      const objectKey = node.field.objectKey;
      const defaultValue = node.field.defaultValue;

      if (defaultValue !== undefined && defaultValue !== null) {
        defaults[objectKey] = defaultValue;
      }
    }
    const children = Array.isArray(node.children) ? node.children : [];
    (children as FormNode[]).forEach(processNode);
  };

  processNode(rootNode);
  return defaults;
}
