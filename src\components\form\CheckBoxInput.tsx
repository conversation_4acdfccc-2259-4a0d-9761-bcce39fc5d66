import React from "react";
import { BasicInputProps } from "./registry";

export const CheckBoxInput = React.forwardRef<
  HTMLInputElement,
  BasicInputProps<"boolean">
>(function CheckBoxInput(
  { value, onChange, isViewMode, default_value, ...rest },
  ref
) {
  const resolvedValue =
    typeof value === "boolean"
      ? value
      : typeof default_value === "boolean"
      ? default_value
      : false;

  return (
    <input
      className="w-4 h-4 accent-blue-500 cursor-pointer disabled:cursor-not-allowed"
      ref={ref}
      type="checkbox"
      checked={resolvedValue}
      onChange={
        isViewMode
          ? undefined
          : (e: React.ChangeEvent<HTMLInputElement>) =>
              onChange(e.target.checked)
      }
      disabled={isViewMode}
      {...rest}
    />
  );
});

CheckBoxInput.displayName = "CheckBoxInput";
