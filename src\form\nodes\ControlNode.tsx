/**
 * ControlNode - Renders control/button nodes
 */

import React from "react";
import { FormNode } from "../types";
import { useFormContext } from "../context";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { ButtonProps } from "react-day-picker";
import { DynamicIcon } from "@/components/other/DynamicIcon";

interface ControlNodeProps {
  node: FormNode;
}

export const ControlNode: React.FC<ControlNodeProps> = ({ node }) => {
  const { form } = useFormContext();
  const { id, styles, properties } = node;
  const containerClass = cn("w-full", styles?.container);
  const contentClass = cn("w-full", styles?.content);

  const controlType = (properties?.controlType as string) || "submit";
  const buttonText = (properties?.text as string) || "Submit";
  const buttonVariant =
    (properties?.variant as ButtonProps["variant"]) || "default";
  const icon = (properties?.icon as string) || null;
  const buttonSize = (properties?.size as ButtonProps["size"]) || "default";

  const handleClick = () => {
    if (controlType === "reset" && form) {
      // Reset form to default values
      (form as { reset: () => void }).reset();
      console.log("🔄 Form reset to default values");
    }
  };

  return (
    <div key={id} className={containerClass} data-node-id={id}>
      <Button
        type={controlType === "submit" ? "submit" : "button"}
        className={contentClass}
        onClick={controlType === "reset" ? handleClick : undefined}
        variant={buttonVariant}
        size={buttonSize}
      >
        {icon && <DynamicIcon name={icon} icon={icon} className="w-4 h-4" />}
        {buttonText}
      </Button>
    </div>
  );
};
