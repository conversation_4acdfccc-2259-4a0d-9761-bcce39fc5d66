import React from "react";
import { Switch } from "@/components/ui/switch";
import { BasicInputProps } from "../registry";

type ToggleInputProps = BasicInputProps<"text"> & {};

export const ToggleInput = React.forwardRef<
  HTMLButtonElement,
  ToggleInputProps
>(
  (
    {
      isViewMode,
      value,
      onChange,
      options = ["ACTIVE", "DISABLED"],
      disabled,
      ...rest
    },
    ref
  ) => {
    const checked = value === options[0];
    return (
      <Switch
        ref={ref}
        checked={checked}
        onCheckedChange={(checked) =>
          !isViewMode &&
          !disabled &&
          onChange(checked ? options[0] : options[1])
        }
        {...rest}
      />
    );
  }
);

ToggleInput.displayName = "ToggleInput";
