# Record Register Feature

## 📋 Overview

Record Register feature quản lý và theo dõi các yêu cầu đăng ký lưu trữ hồ sơ tài liệu. Feature này bao gồm đầy đủ chức năng CRUD, filter, pagination và bulk operations.

## 🏗️ Structure

```
src/features/record-register/
├── components/          # Record register components
│   ├── RecordFilters.tsx    # Bộ lọc tìm kiếm
│   ├── RecordTable.tsx      # Bảng hiển thị dữ liệu
│   └── RecordPagination.tsx # Component phân trang
├── data/               # Mock data
│   └── mockData.ts     # Dữ liệu mô phỏng và logic
├── pages/              # Record register pages
│   ├── RecordRegisterAdminPage.tsx # Trang chính quản lý
│   └── loadable.tsx    # Lazy loading components
├── states/             # States (sẵn sàng cho future)
└── index.ts            # Feature exports
```

## 🎯 Features

### 📊 Table Display

- Hiển thị đầy đủ thông tin hồ sơ đăng ký
- Support responsive design
- Sorting và selection
- Action buttons theo trạng thái

### 🔍 Advanced Filtering

- **Keyword Search**: T<PERSON><PERSON> kiếm theo mã yêu cầu, n<PERSON><PERSON> dung, cơ quan
- **Status Filter**: Lọc theo trạng thái (Tạo mới, Chờ duyệt, Đã duyệt, Từ chối)
- **Year Filter**: Lọc theo năm
- **Reset Function**: Reset tất cả bộ lọc

### 📄 Pagination

- Phân trang có thể điều chỉnh page size
- Navigate: First, Previous, Next, Last
- Hiển thị thông tin trang hiện tại
- Tự động reset về trang đầu khi filter

### ⚡ Actions

- **View**: Xem chi tiết hồ sơ
- **Edit**: Chỉnh sửa (chỉ với trạng thái New/Rejected)
- **Delete**: Xóa hồ sơ (chỉ với trạng thái New/Rejected)
- **Submit**: Gửi duyệt (chỉ với trạng thái New)
- **Bulk Operations**: Export/Import multiple records

## 📊 Data Structure

### RecordRegister Interface

```typescript
interface RecordRegister {
  id: number;
  requestCode: string; // Mã yêu cầu (NLLS01, NLLS02...)
  requestContent: string; // Nội dung yêu cầu
  organizationName: string; // Tên cơ quan
  storageOffice: string; // Phòng lưu trữ
  totalDocuments: number; // Tổng số tài liệu
  submissionCount: number; // Số lần nộp
  sentDate: string; // Ngày gửi
  status: "new" | "pending" | "approved" | "rejected";
  year: number; // Năm
  createdAt: string;
  updatedAt: string;
}
```

## 🎨 UI/UX Features

### Status Management

- **Tạo mới** (new): Màu xanh dương - Có thể edit, delete, submit
- **Chờ duyệt** (pending): Màu vàng - Chỉ có thể view
- **Đã duyệt** (approved): Màu xanh lá - Chỉ có thể view
- **Từ chối** (rejected): Màu đỏ - Có thể edit, delete

### Responsive Design

- **Desktop**: Full table với tất cả columns
- **Tablet**: Hidden một số columns không quan trọng
- **Mobile**: Stacked layout với summary view

### Loading States

- Skeleton loading khi tải dữ liệu
- Button loading states
- Table loading states

## 🔧 Components

### RecordFilters

```tsx
<RecordFilters
  filters={filters}
  onFiltersChange={handleFiltersChange}
  onSearch={handleSearch}
  onReset={handleReset}
/>
```

### RecordTable

```tsx
<RecordTable
  records={records}
  onView={handleView}
  onEdit={handleEdit}
  onDelete={handleDelete}
  onSubmit={handleSubmit}
  onSelectionChange={handleSelectionChange}
/>
```

### RecordPagination

```tsx
<RecordPagination
  pagination={paginationInfo}
  onPageChange={handlePageChange}
  onPageSizeChange={handlePageSizeChange}
/>
```

## 📊 Mock Data

Feature sử dụng 100 records mock data với:

- 8 organizations khác nhau
- 5 storage offices
- 7 loại request content
- 4 status types
- Random distribution across years 2023-2025

## 🚀 Usage

RecordRegisterAdminPage có thể được sử dụng:

1. **Component Picker**: Chọn "Quản lý đăng ký lưu trữ"
2. **Direct Import**: `import { RecordRegisterAdminPage } from '@/features/record-register'`
3. **Route Configuration**: `/admin/record-register`

## 🔄 Future Enhancements

- [ ] Kết nối với API thực tế
- [ ] Export to Excel/PDF
- [ ] Bulk edit operations
- [ ] Advanced search with date range
- [ ] File upload functionality
- [ ] Email notifications
- [ ] Audit trail
- [ ] Print functionality
- [ ] Mobile app support

## 📝 Notes

- Tất cả data hiện tại là mock data
- Action handlers chỉ log console (TODO: implement)
- Ready for API integration
- Fully responsive và accessible
- Follows project patterns và standards
