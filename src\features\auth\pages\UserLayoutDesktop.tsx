import { useMemo, useEffect } from "react";
import { Outlet } from "react-router";
import { X } from "lucide-react";
import { SharedHeader } from "@/features/layouts/components/header/SharedHeader";
import { TreeSidebar } from "@/features/layouts/components/menu";
import {
  buildCategoryTree,
  CategoryTree,
} from "@/features/category/states/types";
import { LoadingPage } from "@/components/loading/LoadingPage";
import { useCategory } from "@/features/category/hooks/useCategoryData";
import { ProtectedRoute } from "@/components/auth/ProtectedRoute";
import { useAppSelector, useAppDispatch } from "@/store/rootReducer";
import { selectAuthState } from "@/features/auth/states/selector";
import { useLayoutManager } from "@/features/layouts/hooks/useLayoutManager";
import { toggleSidebar, setHasSidebar } from "@/features/layouts/states/slices";

const UserLayoutDesktop: React.FC = () => {
  const dispatch = useAppDispatch();
  const { data: flatMenuList, loading } = useCategory("user-menu");
  const { account } = useAppSelector(selectAuthState);

  // Set hasSidebar when component mounts
  useEffect(() => {
    dispatch(setHasSidebar(true));
    return () => {
      dispatch(setHasSidebar(false));
    };
  }, [dispatch]);

  // Use layout manager
  const { sidebarVisible, sidebarLocked, headerHeight } = useLayoutManager();

  const treeMenuList: CategoryTree[] = useMemo(
    () => buildCategoryTree(flatMenuList),
    [flatMenuList]
  );

  const handleToggle = () => {
    dispatch(toggleSidebar());
  };

  const onMenuClick = () => {
    // Desktop không cần auto-close
  };

  if (loading || !treeMenuList.length) return <LoadingPage />;

  return (
    <ProtectedRoute>
      <div className="w-full h-full min-h-screen bg-primary-foreground">
        {/* Shared Header - Public menu always shows + User info when authenticated */}
        <SharedHeader />

        {/* Main Content Area */}
        <div
          className="w-full flex"
          style={{
            marginTop: `${headerHeight}px`,
            minHeight: `calc(100vh - ${headerHeight}px)`,
          }}
        >
          {/* User Sidebar (when expanded) */}
          {sidebarVisible && (
            <div className="w-80 bg-white border-r flex flex-col shadow-sm">
              {/* Avatar Section */}
              <div className="p-4 border-b bg-gradient-to-r from-primary-50 to-primary-100">
                <div className="flex items-center gap-3">
                  {account?.metadata?.avatar ? (
                    <img
                      src={account.metadata.avatar as string}
                      alt={account.fullName || "User"}
                      className="w-12 h-12 rounded-full object-cover border-2 border-white shadow-sm"
                    />
                  ) : (
                    <div className="w-12 h-12 rounded-full bg-primary-500 text-white flex items-center justify-center font-semibold text-lg">
                      {(account?.fullName || "U").charAt(0).toUpperCase()}
                    </div>
                  )}

                  <div className="flex-1 min-w-0">
                    <p className="font-semibold text-sm text-gray-900 truncate">
                      {account?.fullName || "Người dùng"}
                    </p>
                    <p className="text-xs text-gray-500 truncate">
                      {account?.email || ""}
                    </p>
                  </div>

                  {/* Close Button - Only show if toggle is allowed */}
                  {!sidebarLocked && (
                    <button
                      onClick={handleToggle}
                      className="p-1 rounded-md hover:bg-white/50 transition-colors"
                      aria-label="Đóng menu"
                    >
                      <X className="w-5 h-5 text-gray-600" />
                    </button>
                  )}
                </div>
              </div>

              {/* User Menu Sidebar */}
              <div className="flex-1 p-2 overflow-y-auto">
                <TreeSidebar
                  menuList={treeMenuList}
                  onMenuClick={onMenuClick}
                  pathPrefix="/me"
                />
              </div>
            </div>
          )}

          {/* Main Content */}
          <div className="flex-1 bg-white p-4 overflow-y-auto">
            <Outlet />
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
};

export default UserLayoutDesktop;
