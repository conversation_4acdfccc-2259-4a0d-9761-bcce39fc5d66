import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { ChevronDown, ChevronRight } from "lucide-react";
import { CategoryTree } from "@/features/category/states/types";

interface UserMenuVerticalProps {
  menuList: CategoryTree[];
  pathPrefix: string;
  onMenuClick: () => void;
}

export const UserMenuVertical: React.FC<UserMenuVerticalProps> = ({
  menuList,
  pathPrefix,
  onMenuClick,
}) => {
  const [expandedItems, setExpandedItems] = useState<Set<number>>(new Set());
  const navigate = useNavigate();

  const toggleExpanded = (id: number) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(id)) {
      newExpanded.delete(id);
    } else {
      newExpanded.add(id);
    }
    setExpandedItems(newExpanded);
  };

  const handleItemClick = (item: CategoryTree) => {
    if (item.description.component) {
      const path = item.slug ? `${pathPrefix}/${item.slug}` : pathPrefix;
      navigate(path);
      onMenuClick();
    } else if (item.children.length > 0) {
      toggleExpanded(item.id);
    }
  };

  const renderMenuItem = (item: CategoryTree, level: number = 0) => {
    const hasChildren = item.children.length > 0;
    const isExpanded = expandedItems.has(item.id);

    return (
      <div key={item.id} className="w-full">
        <button
          onClick={() => handleItemClick(item)}
          className={`w-full flex items-center justify-between p-3 text-left hover:bg-gray-50 transition-colors ${
            level > 0 ? "ml-4 border-l-2 border-gray-100" : ""
          }`}
        >
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center">
              <span className="text-xs font-semibold text-primary-700">
                {item.name.charAt(0).toUpperCase()}
              </span>
            </div>
            <div>
              <p className="font-medium text-gray-900">{item.name}</p>
              {item.slug && (
                <p className="text-xs text-gray-500 truncate">{item.slug}</p>
              )}
            </div>
          </div>

          {hasChildren && (
            <div className="flex-shrink-0">
              {isExpanded ? (
                <ChevronDown className="w-4 h-4 text-gray-400" />
              ) : (
                <ChevronRight className="w-4 h-4 text-gray-400" />
              )}
            </div>
          )}
        </button>

        {/* Children */}
        {hasChildren && isExpanded && (
          <div className="bg-gray-50">
            {item.children.map((child) => renderMenuItem(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="w-full">{menuList.map((item) => renderMenuItem(item))}</div>
  );
};
