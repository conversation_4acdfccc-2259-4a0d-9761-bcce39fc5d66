import React from "react";
import { AutoTable } from "@/components/table/AutoTable";
import { CategoryColumns, CategoryDTO } from "../states/types";

type Props = {
  data: CategoryDTO[];
  onAction: (action: string, row: CategoryDTO) => void;
};

export const CategoryTable: React.FC<Props> = ({ data, onAction }) => {
  return (
    <AutoTable<CategoryDTO>
      columns={CategoryColumns}
      data={data}
      onAction={onAction}
    />
  );
};
