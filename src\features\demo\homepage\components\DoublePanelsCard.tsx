import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { ImageLoader } from "@/components/image/ImageLoader";
import { cn } from "@/lib/utils";

/**
 * Content type for each panel - can be either image URL or text content
 */
export type PanelContent = {
  type: string;
  content: string;
  title?: string;
};

/**
 * Props interface for DoublePanelsCard component
 */
export interface DoublePanelsCardProps {
  /** Content for the left panel */
  leftPanel: PanelContent;
  /** Content for the right panel */
  rightPanel: PanelContent;
  /** Additional CSS classes */
  className?: string;
  /** Click handler for the card */
  onClick?: () => void;
}

/**
 * DoublePanelsCard component displays two panels side by side with numbered labels
 * Each panel can contain either an image or text content
 * 
 * @param props - Component props
 * @returns JSX element for double panels card
 */
export const DoublePanelsCard: React.FC<DoublePanelsCardProps> = ({
  leftPanel,
  rightPanel,
  className,
  onClick,
}) => {
  /**
   * Renders panel content based on type
   */
  const renderPanelContent = (panel: PanelContent) => {

    return (
      <div className="relative flex-1 h-full">
        {/* Panel Content */}
        <div className="h-full">
          {panel.type === "image" ? (
            <ImageLoader
              src={panel.content}
              alt={panel.title}
              className="w-full h-full object-cover"
              fallbackText={panel.title}
            />
          ) : (
            <div className="h-full bg-gray-50 p-6 flex items-center justify-center">
              <div className="text-center">
                {panel.title && (
                  <h1 className="text-2xl lg:text-3xl font-medium whitespace-nowrap text-blue-500 text-center" >{panel.title}</h1>
                )}
                <p className="text-gray-700 text-sm leading-relaxed">
                  {panel.content}
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <Card
      className={cn(
        "overflow-hidden hover:shadow-lg transition-all duration-300 cursor-pointer bg-transparent border-gray-200 h-64",
        className
      )}
      onClick={onClick}
    >
      <CardContent className="p-0 h-full">
        <div className="flex h-full">
          {/* Left Panel */}
          {renderPanelContent(leftPanel)}

          {/* Divider */}
          <div className="w-px bg-gray-200" />

          {/* Right Panel */}
          {renderPanelContent(rightPanel)}
        </div>
      </CardContent>
    </Card>
  );
};
