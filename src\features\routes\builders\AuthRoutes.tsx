import React from "react";
import { AutoFormLogin } from "@/features/auth/components/AutoFormLogin";
import { AutoFormRegister } from "@/features/auth/components/AutoFormRegister";
import { AuthLayout } from "@/features/auth/layouts";
import { type RouteObject } from "react-router-dom";

/**
 * 🔐 Auth Routes Builder
 *
 * ✅ Features:
 * - Standalone auth pages (login, register)
 * - Clean layout without main site navigation
 * - Better UX for authentication flow
 * - Easier SSO integration
 */
export class AuthRoutesBuilder {
  static build(): RouteObject {
    return {
      path: "/auth",
      element: React.createElement(AuthLayout),
      children: [
        {
          path: "login",
          element: React.createElement(AutoFormLogin),
        },
        {
          path: "register",
          element: React.createElement(AutoFormRegister),
        },
      ],
    };
  }
}
