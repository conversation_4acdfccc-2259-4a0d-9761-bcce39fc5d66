import React, { useState, useEffect } from "react";
import { useAppDispatch } from "@/store/rootReducer";
import {
  CustomDial<PERSON>,
  DialogFooter,
  DialogButton,
} from "@/components/ui/CustomDialog";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import {
  generateSlugFromTitle,
  checkSlugExists,
  createPost,
} from "../states/api";
import { triggerRefetch } from "../states/slices";

interface CreatePostDialogProps {
  trigger?: React.ReactNode;
}

export const CreatePostDialog: React.FC<CreatePostDialogProps> = ({
  trigger,
}) => {
  const dispatch = useAppDispatch();
  const [open, setOpen] = useState(false);
  const [title, setTitle] = useState("");
  const [slug, setSlug] = useState("");
  const [authorName, setAuthorName] = useState("");
  const [slugChecking, setSlugChecking] = useState(false);
  const [slugValid, setSlugValid] = useState<boolean | null>(null);
  const [loading, setLoading] = useState(false);

  // Reset form when dialog opens/closes
  useEffect(() => {
    if (open) {
      setTitle("");
      setSlug("");
      setAuthorName("");
      setSlugValid(null);
      setSlugChecking(false);
    }
  }, [open]);

  const checkSlugValidity = async (slugToCheck: string) => {
    if (!slugToCheck.trim()) {
      setSlugValid(null);
      return;
    }

    setSlugChecking(true);
    try {
      const response = await checkSlugExists(slugToCheck);
      setSlugValid(!response.data); // Valid if NOT exists
    } catch (error) {
      console.error("Error checking slug:", error);
      setSlugValid(null);
    } finally {
      setSlugChecking(false);
    }
  };

  const handleSlugChange = (value: string) => {
    setSlug(value);
    // Debounced slug checking
    const timeoutId = setTimeout(() => {
      checkSlugValidity(value);
    }, 500);

    return () => clearTimeout(timeoutId);
  };

  const handleGenerateSlug = () => {
    if (!title.trim()) return;
    const generatedSlug = generateSlugFromTitle(title);
    setSlug(generatedSlug);
    checkSlugValidity(generatedSlug);
  };

  // Auto-generate slug when user finishes typing title
  const handleTitleBlur = () => {
    if (title.trim() && !slug.trim()) {
      handleGenerateSlug();
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!title.trim() || !slug.trim() || slugValid !== true) return;

    setLoading(true);
    try {
      const postData = {
        title: title.trim(),
        slug: slug.trim(),
        excerpt: {
          image: "",
          description: "",
          files: [],
        }, // User will add in editor later
        content: {
          type: "root",
          children: [],
        }, // Default empty content structure
        postType: "ARTICLE" as const,
        status: "DRAFT" as const, // Default status for new posts
        authorId: authorName.trim() || null, // Use input or null for anonymous
        parentId: null, // Posts typically don't have parents
      };

      console.log("🚀 Creating post with data:", postData);
      const result = await createPost(postData);
      console.log("✅ Post created successfully:", result);

      // Trigger refetch to update post list
      dispatch(triggerRefetch());

      // Reset form and close dialog
      setTitle("");
      setSlug("");
      setAuthorName("");
      setOpen(false);
    } catch (error) {
      console.error("❌ Failed to create post:", error);
      alert("Không thể tạo bài viết. Vui lòng thử lại!");
    } finally {
      setLoading(false);
    }
  };

  const getSlugStatusColor = () => {
    if (slugChecking) return "text-blue-600";
    if (slugValid === true) return "text-green-600";
    if (slugValid === false) return "text-red-600";
    return "text-gray-600";
  };

  const getSlugStatusText = () => {
    if (slugChecking) return "Đang kiểm tra...";
    if (slugValid === true) return "Đường dẫn khả dụng";
    if (slugValid === false) return "Đường dẫn đã tồn tại";
    return "Nhập đường dẫn để kiểm tra";
  };

  const isFormValid = title.trim() && slug.trim() && slugValid === true;

  return (
    <>
      {trigger ? (
        <div onClick={() => setOpen(true)}>{trigger}</div>
      ) : (
        <Button onClick={() => setOpen(true)}>
          <Plus className="w-4 h-4 mr-2" />
          Tạo bài viết mới
        </Button>
      )}

      <CustomDialog
        open={open}
        onClose={() => setOpen(false)}
        title="Tạo bài viết mới"
        maxWidth="lg"
      >
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Title Input */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Tiêu đề bài viết *
            </label>
            <input
              type="text"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              onBlur={handleTitleBlur}
              placeholder="Nhập tiêu đề bài viết..."
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              disabled={loading}
            />
          </div>

          {/* Slug Input */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <label className="block text-sm font-medium text-gray-700">
                Đường dẫn URL *
              </label>
              <button
                type="button"
                onClick={handleGenerateSlug}
                disabled={!title.trim() || loading}
                className="text-sm text-blue-600 hover:text-blue-700 disabled:text-gray-400"
              >
                Tạo từ tiêu đề
              </button>
            </div>
            <input
              type="text"
              value={slug}
              onChange={(e) => handleSlugChange(e.target.value)}
              placeholder="duong-dan-url-bai-viet"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-mono text-sm"
              disabled={loading}
            />
            <p className={`text-sm mt-1 ${getSlugStatusColor()}`}>
              {getSlugStatusText()}
            </p>
          </div>

          {/* Author Input */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Bút danh tác giả
            </label>
            <input
              type="text"
              value={authorName}
              onChange={(e) => setAuthorName(e.target.value)}
              placeholder="Nhập bút danh hoặc để trống để ẩn danh..."
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              disabled={loading}
            />
            <p className="text-xs text-gray-500 mt-1">
              Để trống nếu muốn đăng ẩn danh
            </p>
          </div>

          {/* Note about content */}
          <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
            <p className="text-sm text-blue-700">
              💡 Nội dung, ảnh bìa và mô tả sẽ được thêm sau khi tạo bài viết
            </p>
          </div>

          {/* Form Actions */}
          <DialogFooter>
            <DialogButton onClick={() => setOpen(false)} disabled={loading}>
              Hủy
            </DialogButton>
            <DialogButton
              type="submit"
              variant="primary"
              disabled={!isFormValid || loading}
              loading={loading}
            >
              Tạo bài viết
            </DialogButton>
          </DialogFooter>
        </form>
      </CustomDialog>
    </>
  );
};
