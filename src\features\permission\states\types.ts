export interface RolePermission {
  id: number;
  createdAt: number;
  updatedAt: number;
  roleId: number;
  resourceId: number;
  actionId: number;
}

export interface ResourcePermissions {
  resourceId: number;
  actionIds: number[];
}

export interface UpdateRolePermissionsRequest {
  roleId: number;
  resourcePermissions: ResourcePermissions[];
}

export interface PermissionMatrix {
  [resourceId: number]: number[]; // actionIds được cấp cho resource
}

export type PermissionPageMode = "view" | "edit";

export interface PermissionState {
  // Cache permissions by roleId để tối ưu
  rolePermissions: {
    [roleId: number]: {
      data: RolePermission[];
      matrix: PermissionMatrix;
      loading: boolean;
      error: string | null;
      lastFetched: number;
    };
  };

  // Current state
  selectedRoleId: number | null;
  mode: PermissionPageMode;

  // UI state
  isRoleSelectionOpen: boolean;
  loading: boolean;
  error: string | null;
}

export interface PermissionQueryParams {
  roleId?: string;
  mode?: PermissionPageMode;
}

// Helper types for UI
export interface ResourcePermissionRow {
  resourceId: number;
  resourceCode: string;
  resourceName: string;
  availableActions: number[]; // All actions this resource supports
  grantedActions: number[]; // Actions currently granted to the role
}
