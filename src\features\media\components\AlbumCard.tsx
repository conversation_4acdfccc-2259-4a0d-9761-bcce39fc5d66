import { <PERSON><PERSON><PERSON><PERSON>, Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Trash } from "lucide-react";
import { ImageLoader } from "@/components/image/ImageLoader";

interface AlbumCardProps {
  title: string;
  subtitle: string;
  imageUrl: string;
  onDelete?: () => void;
  onView: () => void;
}

export function AlbumCard({
  title,
  subtitle,
  imageUrl,
  onView,
  onDelete,
}: AlbumCardProps) {
  return (
    <Card className="shadow-md relative overflow-hidden hover:ring-2 hover:ring-pink-300 transition group p-0">
      <CardContent className="items-center px-0">
        <div className="relative w-full flex justify-end">
          <div className="absolute top-2 right-2">
            {onDelete && (
              <Button
                variant="outline"
                size="icon"
                className="h-6 w-6 text-muted-foreground bg-white"
                onClick={onDelete}
              >
                <Trash className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
        <ImageLoader
          className="w-full object-cover block aspect-square cursor-pointer"
          containerClassName="aspect-square"
          src={imageUrl}
          alt={title}
          fallbackText={title}
          onClick={onView}
        />
        <div className="p-2">
          <p className="text-sm font-medium leading-none">{title}</p>
          <p className="text-xs text-muted-foreground">{subtitle}</p>
        </div>
      </CardContent>
    </Card>
  );
}
