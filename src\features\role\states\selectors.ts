import { RootState } from "@/store/rootReducer";
import { createSelector } from "@reduxjs/toolkit";
import { RoleData } from "./type";

const selectRoleState = (state: RootState) => state.roleState;

export const selectCurrentRole = createSelector(
  [selectRoleState],
  (roleState) => roleState.currentRole
);

export const selectPageMode = createSelector(
  [selectRoleState],
  (roleState) => roleState.pageMode
);

export const selectActionType = createSelector(
  [selectRoleState],
  (roleState) => roleState.actionType
);

export const selectLoading = createSelector(
  [selectRoleState],
  (roleState) => roleState.loading
);

export const selectError = createSelector(
  [selectRoleState],
  (roleState) => roleState.error
);

export const selectSearchTerm = createSelector(
  [selectRoleState],
  (roleState) => roleState.searchTerm
);

export const selectData = createSelector(
  [selectRoleState],
  (roleState) => roleState.data
);

export const selectRoles = createSelector(
  [selectRoleState],
  (roleState) => roleState.data as RoleData[]
);

export const selectFilteredRoles = createSelector(
  [selectData, selectSearchTerm],
  (data, searchTerm) => {
    if (!searchTerm) return data;
    return data.filter(
      (role) =>
        role.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        role.code.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }
);
