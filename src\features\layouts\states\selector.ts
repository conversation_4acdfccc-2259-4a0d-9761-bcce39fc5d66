import { RootState } from "@/store/rootReducer";

export const selectLayoutState = (state: RootState) => state.layout;

export const selectSidebarVisible = (state: RootState) =>
  state.layout.sidebarVisible;
export const selectSidebarLocked = (state: RootState) =>
  state.layout.sidebarLocked;
export const selectMobileMenuOpen = (state: RootState) =>
  state.layout.mobileMenuOpen;
export const selectUserMenuOpen = (state: RootState) =>
  state.layout.userMenuOpen;
export const selectShowPublicMenu = (state: RootState) =>
  state.layout.showPublicMenu;

export const selectIsMobile = (state: RootState) => state.layout.isMobile;
export const selectIsTablet = (state: RootState) => state.layout.isTablet;
export const selectIsDesktop = (state: RootState) => state.layout.isDesktop;
export const selectIsVeryWideScreen = (state: RootState) =>
  state.layout.isVeryWideScreen;

// Computed selectors
export const selectCanToggleSidebar = (state: RootState) =>
  !state.layout.sidebarLocked && !state.layout.isMobile;

export const selectShowSidebarToggle = (state: RootState) =>
  state.layout.hasSidebar && !state.layout.sidebarLocked && !state.layout.isMobile;

export const selectHeaderHeight = (state: RootState) =>
  state.layout.showPublicMenu ? 116 : 80;
