import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Trash2, Image } from "lucide-react";
import { toast } from "sonner";
import { BannerCarousel } from "@/components/images/BannerCarousel";

import type { BannerConfig, BannerItem } from "../states/type";

/**
 * Props for the BannerGrid component
 */
interface BannerGridProps {
  /** Current banner configuration data */
  data: BannerConfig | null;
  /** Loading state indicator */
  loading: boolean;
  /** Callback fired when data changes */
  onDataChange: (newData: BannerConfig) => void;
  /** Callback fired when validation state changes */
  onValidationChange?: (isValid: boolean) => void;
}

/**
 * Individual banner thumbnail item with delete capability and click-to-preview
 */
interface BannerGridItemProps {
  banner: BannerItem;
  index: number;
  onDelete: (index: number) => void;
  onPreview: (index: number) => void;
}

const BannerGridItem: React.FC<BannerGridItemProps> = ({
  banner,
  index,
  onDelete,
  onPreview,
}) => {
  const handleDeleteClick = () => {
    onDelete(index);
  };

  const handlePreviewClick = () => {
    onPreview(index);
  };

  return (
    <div className="relative group rounded-lg overflow-hidden bg-white border hover:border-primary/50 transition-all duration-200 cursor-pointer">
      {/* Image Panel */}
      <div className="relative aspect-video flex items-center justify-center bg-gray-50" onClick={handlePreviewClick}>
        <img
          src={banner.src}
          alt={banner.alt}
          className="w-full h-full object-contain transition-transform duration-200 group-hover:scale-105"
          onError={(e) => {
            const target = e.target as HTMLImageElement;
            target.src = "/placeholder-banner.jpg"; // Fallback image
          }}
        />

        {/* Action Buttons - Top Right */}
        <div className="absolute top-2 right-2 flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
          <Button
            variant="destructive"
            size="icon"
            className="h-8 w-8"
            onClick={(e) => {
              e.stopPropagation(); // Prevent triggering preview
              handleDeleteClick();
            }}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Banner Info */}
      <div className="p-2">
        <p className="text-xs text-gray-500 font-medium text-center">
          Banner #{index + 1}
        </p>
      </div>
    </div>
  );
};

/**
 * BannerGrid component for managing banner configuration data
 * Provides carousel preview with thumbnail grid for management
 */
export const BannerGrid: React.FC<BannerGridProps> = ({
  data,
  loading,
  onDataChange,
  onValidationChange,
}) => {
  const [banners, setBanners] = useState<BannerItem[]>([]);
  const [selectedBannerIndex, setSelectedBannerIndex] = useState<number | undefined>(undefined);

  // Initialize banners when data changes
  useEffect(() => {
    if (data?.banners) {
      setBanners(data.banners);
    } else {
      setBanners([]);
    }
  }, [data]);

  // Validate banners and notify parent
  useEffect(() => {
    const isValid = banners.every(banner =>
      banner.src && banner.src.trim() !== ""
    );
    onValidationChange?.(isValid);
  }, [banners, onValidationChange]);

  const updateBanners = (newBanners: BannerItem[]) => {
    setBanners(newBanners);
    onDataChange({ banners: newBanners });
  };

  const handleDelete = (index: number) => {
    const newBanners = banners.filter((_, i) => i !== index);
    updateBanners(newBanners);
    toast.success("Đã xóa banner");
  };

  if (loading && !data) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-muted-foreground">
            Đang tải dữ liệu...
          </div>
        </CardContent>
      </Card>
    );
  }

  if (banners.length === 0) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center py-8 text-muted-foreground">
            <div className="w-16 h-16 bg-muted rounded-lg flex items-center justify-center mx-auto mb-3">
              <Image className="w-8 h-8 text-muted-foreground" />
            </div>
            <p>Chưa có banner nào. Nhấn "Thêm banner" để bắt đầu.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const handlePreview = (index: number) => {
    setSelectedBannerIndex(index);
    // Reset after a short delay to allow for multiple clicks
    setTimeout(() => setSelectedBannerIndex(undefined), 100);
  };

  return (
    <div className="space-y-6">
      {/* Banner Carousel Preview */}
      <BannerCarousel
        banners={banners}
        autoPlayInterval={3500}
        pauseDuration={5000}
        jumpToIndex={selectedBannerIndex}
        onBannerClick={(index) => {
          // Optional: Add any additional logic when banner is clicked
          console.log(`Banner ${index + 1} clicked in carousel`);
        }}
      />

      {/* Banner Management Grid */}
      <Card>
        <CardContent className="p-6">
          <div className="space-y-4">
            <h4 className="text-sm font-medium">
              Danh sách banner ({banners.length} banner)
            </h4>
            <p className="text-xs text-muted-foreground">
              Nhấn vào banner để xem trước.
            </p>

            {/* Banner Thumbnail Grid */}
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
              {banners.map((banner, index) => (
                <BannerGridItem
                  key={index}
                  banner={banner}
                  index={index}
                  onDelete={handleDelete}
                  onPreview={handlePreview}
                />
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
