import { Outlet } from "react-router";
import { SharedHeader } from "./components/header/SharedHeader";
import Footer from "./components/Footer";
import { useLayoutManager } from "./hooks/useLayoutManager";

const MainLayout: React.FC = () => {
  const { headerHeight } = useLayoutManager();

  return (
    <div className="w-full h-full min-h-screen flex flex-col bg-primary-foreground">
      <SharedHeader />
      <main className="flex-1" style={{ marginTop: `${headerHeight}px` }}>
        <Outlet />
      </main>
      <Footer />
    </div>
  );
};

export default MainLayout;
