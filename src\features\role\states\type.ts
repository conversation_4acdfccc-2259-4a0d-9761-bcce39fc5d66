import { FieldValue } from "@/components/form/registry";

export interface RoleResponse {
  data: RoleData;
}

export interface RoleData {
  id: number;
  code: string;
  name: string;
  metadata: Record<string, FieldValue>;
  createdAt: number;
  updatedAt: number;
}

export interface CreateRole {
  code: string;
  name: string;
  metadata?: Record<string, FieldValue>;
}

export interface UpdateRole {
  code?: string;
  name?: string;
  metadata?: Record<string, FieldValue>;
}
