import { ColumnConfig } from "@/components/table/registry";

export const USER_COLLUMS: ColumnConfig[] = [
  { accessorKey: "id", header: "ID" },
  { accessorKey: "userName", header: "Tên tài kho<PERSON>n" },
  { accessorKey: "fullName", header: "Họ Tên" },
  {
    accessorKey: "email",
    header: "Email",
  },
  {
    accessorKey: "phone",
    header: "SỐ điện thoại",
    visible: true,
  },
  {
    accessorKey: "actions",
    header: "Thao tác",
    cell: {
      component: "ActionCell",
      props: {
        actions: ["edit"],
      },
    },
  },
];
