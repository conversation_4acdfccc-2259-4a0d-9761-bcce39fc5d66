import { createQuestion, fetchQuestions } from "../states/slices";
import type { CreateQuestionRequest } from "../states/types";
import store from "@/store/store";

// Sample data generator for testing with real API
export const createSampleQuestions = async () => {
  const sampleQuestions: CreateQuestionRequest[] = [
    {
      asker: {
        fullName: "Nguyễn Văn A",
        phoneNumber: "0912345678",
        email: "<EMAIL>",
        address: "123 Đường ABC, Phường XYZ, Quận 1, TP.HCM",
      },
      content: {
        title: "Làm thế nào để đăng nhập vào hệ thống?",
        question:
          "Tôi gặp khó khăn khi đăng nhập vào hệ thống. Bạn có thể hướng dẫn chi tiết các bước đăng nhập không?",
      },
      topic: "<PERSON><PERSON><PERSON> nhập",
    },
    {
      asker: {
        fullName: "Tr<PERSON><PERSON>",
        phoneNumber: "0987654321",
        email: "<EMAIL>",
        address: "456 Đường DEF, Phường UVW, Quận 2, TP.HCM",
      },
      content: {
        title: "Tôi có thể thay đổi thông tin cá nhân ở đâu?",
        question:
          "Tôi muốn cập nhật thông tin hồ sơ cá nhân như số điện thoại, địa chỉ. Có thể hướng dẫn tôi không?",
      },
      topic: "Tài khoản",
    },
    {
      asker: {
        fullName: "Lê Văn C",
        phoneNumber: "0123456789",
        email: "<EMAIL>",
        address: "789 Đường GHI, Phường RST, Quận 3, TP.HCM",
      },
      content: {
        title: "Làm sao để upload file lên hệ thống?",
        question:
          "Tôi cần upload các tài liệu PDF và hình ảnh. Có giới hạn về dung lượng file không?",
      },
      topic: "Upload",
    },
    {
      asker: {
        fullName: "Phạm Thị D",
        phoneNumber: "0345678901",
        email: "<EMAIL>",
        address: "101 Đường JKL, Phường MNO, Quận 4, TP.HCM",
      },
      content: {
        title: "Tại sao tôi không thể truy cập một số trang?",
        question:
          "Khi tôi click vào một số menu thì hiện thông báo 'Bạn không có quyền truy cập'. Làm sao để được cấp quyền?",
      },
      topic: "Phân quyền",
    },
    {
      asker: {
        fullName: "Hoàng Văn E",
        phoneNumber: "0567890123",
        email: "<EMAIL>",
        address: "202 Đường PQR, Phường STU, Quận 5, TP.HCM",
      },
      content: {
        title: "Hệ thống có hỗ trợ mobile không?",
        question:
          "Tôi muốn biết có thể sử dụng hệ thống trên điện thoại di động không? Có ứng dụng riêng không?",
      },
      topic: "Hỗ trợ",
    },
  ];

  console.log("🔄 Creating sample questions using real API...");

  try {
    for (const questionData of sampleQuestions) {
      await store.dispatch(createQuestion(questionData));
      console.log(`✅ Created question: ${questionData.content.title}`);

      // Small delay to avoid overwhelming the API
      await new Promise((resolve) => setTimeout(resolve, 200));
    }

    console.log("✅ All sample questions created successfully!");

    // Fetch the updated list
    await store.dispatch(fetchQuestions());
    console.log("✅ Questions list refreshed from API");
  } catch (error) {
    console.error("❌ Error creating sample questions:", error);
    throw error;
  }
};

// Helper to clear and recreate sample data
export const resetSampleData = async () => {
  console.log("🔄 Resetting sample data...");

  try {
    // Note: You might need to implement a delete all API endpoint for this
    // For now, we'll just create new sample questions
    await createSampleQuestions();
  } catch (error) {
    console.error("❌ Error resetting sample data:", error);
    throw error;
  }
};

// Development utility to generate test data
export const generateTestData = async () => {
  const isDevelopment = process.env.NODE_ENV === "development";

  if (!isDevelopment) {
    console.warn(
      "⚠️ Test data generation is only available in development mode"
    );
    return;
  }

  const shouldGenerate = window.confirm(
    "Do you want to generate sample questions using the real API? This will create test data in your system."
  );

  if (shouldGenerate) {
    await createSampleQuestions();
    alert(
      "Sample questions created successfully! Check the console for details."
    );
  }
};

// Export for use in development console
if (process.env.NODE_ENV === "development") {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  (window as any).questionApiHelpers = {
    createSampleQuestions,
    resetSampleData,
    generateTestData,
  };
}
