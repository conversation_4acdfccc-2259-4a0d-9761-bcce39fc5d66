/**
 * Form Builder Container
 * Main container that wraps the entire form builder with context
 */

import React from "react";
import { FormBuilderProvider } from "../context/FormBuilderContext";
import { FormBuilder } from "../components/FormBuilder";

/**
 * Container component that provides context and main UI
 */
export const FormBuilderContainer: React.FC = () => {
  return (
    <FormBuilderProvider>
      <FormBuilder />
    </FormBuilderProvider>
  );
};