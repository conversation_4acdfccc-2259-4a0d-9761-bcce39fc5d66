/**
 * TextAreaInput - Textarea input component for AutoForm
 */

import React from "react";
import { FieldComponentProps } from "../../FieldFactory";
import { Textarea } from "@/components/ui/textarea";

export const TextAreaInput: React.FC<FieldComponentProps> = ({
  value,
  onChange,
  onBlur,
  disabled = false,
  placeholder = "",
  id,
  className = "",
}) => {
  const handleChange = disabled
    ? undefined
    : (e: React.ChangeEvent<HTMLTextAreaElement>) => onChange(e.target.value);

  return (
    <Textarea
      id={id}
      value={value as string}
      onChange={handleChange}
      onBlur={onBlur}
      disabled={disabled}
      placeholder={placeholder}
      className={className}
    />
  );
};
