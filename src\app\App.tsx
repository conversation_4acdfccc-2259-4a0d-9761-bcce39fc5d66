import React, { Suspense } from "react";
import { Toaster } from "sonner";
import { AppRouter } from "@/features/routes";
import { AuthInitializer } from "@/features/auth/components/AuthInitializer";
import { LoadingPage } from "@/components/loading/LoadingPage";
import { DemoRouter } from "@/features/routes/DemoRouter";

export const App: React.FC = () => {
  console.log("🏠 App component rendering...");

  return (
    <>
      <AuthInitializer />
      <Suspense fallback={<LoadingPage />}>
        {/* <AppRouter /> */}
        <DemoRouter />
      </Suspense>
      <Toaster richColors position="bottom-right" />
    </>
  );
};

export default App;
