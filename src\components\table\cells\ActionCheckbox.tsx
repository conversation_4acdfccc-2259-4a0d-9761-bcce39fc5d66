import React, { JSX, useId } from "react";
import { Checkbox } from "@/components/ui/checkbox";

export interface ActionCheckboxProps<T> {
  value: T[keyof T];
  label: string;
  row: T;
  accessorKey: keyof T;
  onChange?: (row: T, key: keyof T, value: T[keyof T]) => void;
}

export const ActionCheckbox = React.memo(function ActionCheckbox<T>({
  value,
  label,
  row,
  accessorKey,
  onChange,
}: ActionCheckboxProps<T>) {
  const id = `checkbox-${useId()}`;

  return (
    <div className="flex items-center space-x-2">
      <Checkbox
        id={id}
        checked={value as boolean}
        onCheckedChange={() =>
          onChange?.(row, accessorKey, !value as T[keyof T])
        }
      />
      <label
        htmlFor={id}
        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
      >
        {label}
      </label>
    </div>
  );
}) as <T>(props: ActionCheckboxProps<T>) => JSX.Element;
