import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { cn } from "@/lib/utils";

/**
 * Props interface for StatCard component
 */
export interface StatCardProps {
  /** Statistical number to display */
  number: string;
  /** Name/title of the statistic */
  name: string;
  /** Description of the statistic */
  description: string;
  /** Additional CSS classes */
  className?: string;
  /** Click handler for the card */
  onClick?: () => void;
}

/**
 * StatCard component displays statistical information with proper typography hierarchy
 * 
 * @param props - Component props
 * @returns JSX element for stat card
 */
export const StatCard: React.FC<StatCardProps> = ({
  number,
  name,
  description,
  className,
  onClick,
}) => {
  return (
    <Card 
      className={cn(
        "hover:shadow-lg transition-all duration-300 cursor-pointer bg-transparent border-gray-200",
        className
      )}
      onClick={onClick}
    >
      <CardContent className="p-6 text-center">
        <div className="space-y-3">
          {/* Statistical Number */}
          <div className="text-3xl font-bold text-blue-600">
            {number}
          </div>
          
          {/* Name/Title */}
          <h3 className="text-lg font-semibold text-gray-900">
            {name}
          </h3>
          
          {/* Description */}
          <p className="text-sm text-gray-600 leading-relaxed">
            {description}
          </p>
        </div>
      </CardContent>
    </Card>
  );
};
