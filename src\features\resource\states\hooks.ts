import { useNavigate } from "react-router-dom";
import { ResourcePageMode, ResourceAction } from "./types";

export const useSwitchMode = () => {
  const navigate = useNavigate();

  return (
    mode: ResourcePageMode,
    resourceId?: string,
    actionType?: ResourceAction
  ) => {
    const params = new URLSearchParams();
    params.set("mode", mode);

    if (mode === "detail" && resourceId && resourceId !== "0") {
      params.set("resource", resourceId);
    }

    if (actionType && mode === "detail") {
      params.set("actionType", actionType);
    }

    navigate(`?${params.toString()}`);
  };
};
