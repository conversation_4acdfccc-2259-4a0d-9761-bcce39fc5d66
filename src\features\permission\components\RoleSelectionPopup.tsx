import React, { useState, useMemo } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Search, Users } from "lucide-react";
import { useAppSelector, useAppDispatch } from "@/store/rootReducer";
import { setRoleSelectionOpen } from "../states/slices";
import { RoleData } from "@/features/role/states/type";
import { selectRoleSelectionData } from "../states/selectors";
import { useSearchParams } from "react-router-dom";

interface RoleSelectionPopupProps {
  onRoleSelect?: (role: RoleData) => void;
}

export const RoleSelectionPopup: React.FC<RoleSelectionPopupProps> = ({
  onRoleSelect,
}) => {
  const dispatch = useAppDispatch();
  const [searchParams, setSearchParams] = useSearchParams();
  const [searchTerm, setSearchTerm] = useState("");

  const { isOpen, roles, loading } = useAppSelector(selectRoleSelectionData);

  // Filter roles based on search term
  const filteredRoles = useMemo(() => {
    if (!searchTerm) return roles;

    const lowercaseSearch = searchTerm.toLowerCase();
    return roles.filter(
      (role) =>
        role.name.toLowerCase().includes(lowercaseSearch) ||
        role.code.toLowerCase().includes(lowercaseSearch)
    );
  }, [roles, searchTerm]);

  const handleRoleSelect = (role: RoleData) => {
    // Update URL first - this will trigger Redux state update via usePermissionQueryParams
    const newSearchParams = new URLSearchParams(searchParams.toString());
    newSearchParams.set("roleId", role.id.toString());
    newSearchParams.delete("mode"); // Reset to view mode
    setSearchParams(newSearchParams, { replace: true });

    // Close popup
    dispatch(setRoleSelectionOpen(false));
    setSearchTerm("");
    onRoleSelect?.(role);
  };

  const handleClose = () => {
    dispatch(setRoleSelectionOpen(false));
    setSearchTerm("");
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-3xl max-h-[85vh] bg-white border-0 shadow-2xl rounded-xl overflow-hidden">
        <DialogHeader className="px-6 py-4 bg-white border-b border-gray-100">
          <DialogTitle className="text-xl font-semibold text-gray-800 flex items-center">
            <Users className="w-5 h-5 mr-2 text-blue-600" />
            Chọn vai trò để phân quyền
          </DialogTitle>
        </DialogHeader>

        <div className="p-6 space-y-5 bg-white">
          {/* Search Input */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Tìm kiếm theo tên hoặc mã vai trò..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 h-11 border-gray-200 focus:border-blue-500 focus:ring-blue-500 bg-white"
            />
          </div>

          {/* Role List */}
          <div className="max-h-96 overflow-y-auto space-y-3 pr-1">
            {loading ? (
              <div className="text-center py-12 text-gray-500">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-3"></div>
                <p>Đang tải danh sách vai trò...</p>
              </div>
            ) : filteredRoles.length === 0 ? (
              <div className="text-center py-12">
                <Users className="w-12 h-12 text-gray-300 mx-auto mb-3" />
                <p className="text-gray-500">
                  {searchTerm
                    ? "Không tìm thấy vai trò nào phù hợp"
                    : "Chưa có vai trò nào trong hệ thống"}
                </p>
                {searchTerm && (
                  <p className="text-sm text-gray-400 mt-1">
                    Thử với từ khóa khác
                  </p>
                )}
              </div>
            ) : (
              filteredRoles.map((role) => (
                <div
                  key={role.id}
                  className="group p-4 bg-white border border-gray-200 rounded-lg hover:border-blue-300 hover:shadow-md transition-all duration-200 cursor-pointer"
                  onClick={() => handleRoleSelect(role)}
                >
                  <div className="flex justify-between items-start">
                    <div className="flex-1 min-w-0">
                      <h3 className="font-semibold text-gray-900 group-hover:text-blue-700 transition-colors">
                        {role.name}
                      </h3>
                      <div className="flex items-center mt-2 space-x-4">
                        <div className="flex items-center">
                          <span className="text-sm text-gray-500 mr-1">
                            Mã:
                          </span>
                          <span className="text-sm font-mono bg-gray-100 px-2 py-1 rounded text-gray-700">
                            {role.code}
                          </span>
                        </div>
                        <div className="flex items-center">
                          <span className="text-sm text-gray-500 mr-1">
                            ID:
                          </span>
                          <span className="text-sm font-mono text-gray-600">
                            {role.id}
                          </span>
                        </div>
                      </div>
                      <p className="text-xs text-gray-400 mt-2">
                        Tạo ngày:{" "}
                        {new Date(role.createdAt).toLocaleDateString("vi-VN")}
                      </p>
                    </div>
                    <Button
                      size="sm"
                      className="ml-4 bg-blue-600 hover:bg-blue-700 text-white border-0 shadow-sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleRoleSelect(role);
                      }}
                    >
                      Chọn
                    </Button>
                  </div>
                </div>
              ))
            )}
          </div>

          {/* Footer */}
          <div className="flex justify-between items-center pt-4 border-t border-gray-100">
            <p className="text-sm text-gray-500">
              {filteredRoles.length} vai trò được tìm thấy
            </p>
            <Button
              variant="outline"
              onClick={handleClose}
              className="border-gray-300 text-gray-700 hover:bg-gray-50"
            >
              Đóng
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
