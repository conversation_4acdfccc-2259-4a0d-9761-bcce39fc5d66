import React, { useEffect, useState } from "react";
import { AutoTable } from "@/components/table/AutoTable";
import { UserData } from "../states/type";
import { USER_COLLUMS } from "../states/table";

import { CreateUserDialog } from "./CreateUserDialog";
import { useAppSelector, useAppDispatch } from "@/store/rootReducer";
import { selectRefetch } from "@/features/post/states/selector";
import { fetchUsers } from "@/features/users/states/api";
import { setCurrentUser, setFilters } from "@/features/users/states/slices";
import { selectUserQueryParams } from "../states/selectors";
import { useSwitchMode } from "../states/hook";

export const UserTable: React.FC = () => {
  const [data, setData] = useState<UserData[]>([]);
  const params = useAppSelector(selectUserQueryParams);
  const refetch = useAppSelector(selectRefetch);
  const dispatch = useAppDispatch();
  const switchMode = useSwitchMode();

  useEffect(() => {
    const fetchData = async () => {
      fetchUsers(params).then((res) => setData(res.data.data || []));
    };
    fetchData();
  }, [params, refetch]);

  const handlePageChange = (index: number) => {
    dispatch(setFilters({ page: index + 1 }));
  };

  const handlePageSizeChange = (size: number) => {
    dispatch(setFilters({ size: size }));
  };

  const pagination = {
    pageIndex: params.page ? params.page - 1 : 0,
    pageSize: params.size || 10,
    totalCount: 0,
    onPageChange: handlePageChange,
    onPageSizeChange: handlePageSizeChange,
  };

  const handleAction = (action: string, row: UserData) => {
    if (action === "edit") {
      dispatch(setCurrentUser(row));
      switchMode("detail", String(row.id));
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Danh sách người dùng</h1>
        <CreateUserDialog />
      </div>
      {data?.length === 0 ? (
        <div className="text-center text-muted-foreground py-10">
          Không có người dùng nào
        </div>
      ) : (
        <AutoTable<UserData>
          columns={USER_COLLUMS}
          data={data}
          pagination={pagination}
          onAction={handleAction}
        />
      )}
    </div>
  );
};
