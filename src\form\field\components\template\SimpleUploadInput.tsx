import React, { useRef, useState, useEffect, useId } from "react";
import {
  Upload,
  Trash2,
  <PERSON><PERSON><PERSON><PERSON>,
  AlertCircle,
  Loader2,
} from "lucide-react";
import { uploadFile, getFileViewUrl } from "@/features/demo/states/api";
import { FieldComponentProps } from "../../FieldFactory";

export const SimpleUploadInput: React.FC<FieldComponentProps> = ({
  value,
  onChange,
  disabled,
  className,
  id: propId,
}) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [uploadStatus, setUploadStatus] = useState<
    "idle" | "uploading" | "success" | "error"
  >("idle");
  const [errorMessage, setErrorMessage] = useState<string>("");
  const fileInputRef = useRef<HTMLInputElement>(null);
  const autoId = useId();
  const inputId = propId || `simple-upload-input-${autoId}`;

  // Reset state nếu value bị xóa ngoài (reset form)
  useEffect(() => {
    if (!value) {
      setSelectedFile(null);
      setUploadStatus("idle");
      setErrorMessage("");
      if (fileInputRef.current) fileInputRef.current.value = "";
    }
  }, [value]);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      setUploadStatus("idle");
      setErrorMessage("");
    }
  };

  const handleChooseFile = () => {
    if (uploadStatus === "uploading" || disabled) return;
    fileInputRef.current?.click();
  };

  const handleRemoveFile = () => {
    if (uploadStatus === "uploading" || disabled) return;
    setSelectedFile(null);
    setUploadStatus("idle");
    setErrorMessage("");
    onChange("");
    if (fileInputRef.current) fileInputRef.current.value = "";
  };

  const handleUpload = async () => {
    if ((!selectedFile && !value) || uploadStatus === "uploading" || disabled)
      return;
    setUploadStatus("uploading");
    setErrorMessage("");
    try {
      const file = selectedFile;
      if (!file) return;
      const response = await uploadFile(file);
      if (response.code === "SUCCESS") {
        const fileUrl = getFileViewUrl(response.data.name);
        setUploadStatus("success");
        onChange(fileUrl);
      } else {
        throw new Error(response.message || "Upload failed");
      }
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : "Upload failed";
      setErrorMessage(errorMsg);
      setUploadStatus("error");
    }
  };

  const isDisabled = disabled || uploadStatus === "uploading";

  // Lấy tên file từ url hoặc selectedFile
  const getFileName = () => {
    if (selectedFile) return selectedFile.name;
    if (value && typeof value === "string") {
      try {
        const name = decodeURIComponent(value.split("/").pop() || "");
        return name.length > 32
          ? name.slice(0, 16) + "..." + name.slice(-8)
          : name;
      } catch {
        return "Xem file đã tải lên";
      }
    }
    return "Chưa chọn file";
  };

  return (
    <div className={className || "w-full max-w-md"}>
      {/* Hidden file input with label for accessibility */}
      <label htmlFor={inputId} className="sr-only">
        Chọn file tải lên
      </label>
      <input
        ref={fileInputRef}
        id={inputId}
        type="file"
        onChange={handleFileSelect}
        className="hidden"
        accept="*/*"
        disabled={isDisabled}
      />
      <div className="flex items-center space-x-2">
        <div
          className={
            "flex-1 px-3 py-1.5 border border-gray-300 rounded-lg bg-white flex items-center justify-between text-gray-800 text-xs h-8" +
            (isDisabled ? " opacity-75" : "")
          }
        >
          {value ? (
            <a
              href={typeof value === "string" ? value : undefined}
              target="_blank"
              rel="noopener noreferrer"
              className="truncate mr-2 text-blue-600 underline max-w-xs text-xs"
              title={typeof value === "string" ? value : undefined}
            >
              {getFileName()}
            </a>
          ) : (
            <span className="truncate mr-2 text-gray-500 text-xs">
              {getFileName()}
            </span>
          )}
          <div className="flex items-center space-x-2">
            {uploadStatus === "success" && value && (
              <CheckCircle className="h-3.5 w-3.5 text-green-500" />
            )}
            {uploadStatus === "error" && (
              <AlertCircle className="h-3.5 w-3.5 text-red-500" />
            )}
            {uploadStatus === "uploading" && (
              <Loader2 className="h-3.5 w-3.5 animate-spin text-blue-500" />
            )}
            {(selectedFile || value) && (
              <button
                type="button"
                onClick={handleRemoveFile}
                className={
                  "h-5 w-5 p-0 hover:bg-gray-100 rounded-full" +
                  (isDisabled
                    ? " opacity-50 cursor-not-allowed hover:bg-transparent"
                    : "")
                }
                disabled={isDisabled}
              >
                <Trash2 className="h-3 w-3" />
              </button>
            )}
          </div>
        </div>
        <button
          type="button"
          onClick={selectedFile ? handleUpload : handleChooseFile}
          className={
            "px-2 h-8 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 flex items-center" +
            (isDisabled
              ? " opacity-50 cursor-not-allowed hover:bg-blue-600"
              : "")
          }
          disabled={Boolean(
            isDisabled || (uploadStatus === "success" && value && !selectedFile)
          )}
        >
          {uploadStatus === "uploading" ? (
            <>
              <Loader2 className="h-3.5 w-3.5 mr-1 animate-spin" />
              Đang tải...
            </>
          ) : selectedFile ? (
            <>
              <Upload className="h-3.5 w-3.5 mr-1" />
              Tải lên
            </>
          ) : (
            <>
              <Upload className="h-3.5 w-3.5 mr-1" />
              Chọn file
            </>
          )}
        </button>
      </div>
      {/* Error message */}
      {uploadStatus === "error" && errorMessage && (
        <div className="mt-2 text-sm text-red-600 flex items-center space-x-1">
          <AlertCircle className="h-4 w-4" />
          <span>{errorMessage}</span>
        </div>
      )}
    </div>
  );
};
