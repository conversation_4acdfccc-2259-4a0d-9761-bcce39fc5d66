export interface BannerItem {
  src: string;
  alt: string;
}

export interface BannerConfig {
  banners: BannerItem[];
}

export interface BannerState {
  data: BannerConfig | null;
  savedData: BannerConfig | null;
  loading: boolean;
  saving: boolean;
  error: string | null;
  isDirty: boolean;
}

export interface BannerField {
  key: string;
  label: string;
  type: "text" | "email" | "url" | "textarea" | "boolean" | "array";
  required?: boolean;
  placeholder?: string;
}

export interface BannerMetadata {
  title: string;
  description: string;
  type: "array" | "object";
  arrayKey?: string;
  fields: BannerField[];
}

// Edit state for tracking changes
export interface EditableBannerItem {
  id: string;
  isNew?: boolean;
  isDeleted?: boolean;
  data: Record<string, any>;
}
