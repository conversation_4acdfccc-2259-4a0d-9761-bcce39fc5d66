import { API_ENDPOINTS } from "@/api/endpoints";
import { restApi, BaseResponse } from "@/api/restApi";
import { LoginRequest, RegisterRequest, AccountResponse } from "./type";
import { getTokenFromCookies, TOKEN_TYPE } from "@/api/cookies";

// Hàm xử lý login
export const handleLogin = async (request: LoginRequest) => {
  return restApi.post<BaseResponse<AccountResponse>>(
    API_ENDPOINTS.AUTH.PUBLIC.USERS.LOGIN,
    request
  );
};

// Hàm xử lý đăng ký
export const handleRegister = async (request: RegisterRequest) => {
  return restApi.post<BaseResponse<AccountResponse>>(
    API_ENDPOINTS.AUTH.PUBLIC.USERS.REGISTER,
    request
  );
};

// Hàm xử lý quên mật khẩu
export const forgotPassword = async (email: string) => {
  return restApi.post<BaseResponse<null>>(
    API_ENDPOINTS.AUTH.PUBLIC.USERS.FORGOT_PASSWORD,
    { email }
  );
};

// Hàm lấy thông tin user hiện tại
export const getMe = async () => {
  return restApi.get<BaseResponse<AccountResponse>>(
    API_ENDPOINTS.AUTH.PRIVATE.USERS.GET_ME
  );
};

// Hàm refresh token
export const refreshToken = async () => {
  const refreshTokenValue = getTokenFromCookies(TOKEN_TYPE.REFRESH_TOKEN);
  return restApi.post<
    BaseResponse<{ accessToken: string; refreshToken: string }>
  >(API_ENDPOINTS.AUTH.PRIVATE.USERS.REFRESH_TOKEN, {
    refreshToken: refreshTokenValue,
  });
};

// Hàm xử lý logout
export const handleLogout = async () => {
  return restApi.post<BaseResponse<null>>(
    API_ENDPOINTS.AUTH.PRIVATE.USERS.LOGOUT
  );
};
