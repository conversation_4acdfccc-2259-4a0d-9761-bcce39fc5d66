/**
 * Form Builder Validation Utilities
 * Real-time validation for tree operations
 */

import { FormNode } from '@/form/types';
import { FormConfig, TreeOperationResult } from './types';
import { createTreeContext, countNodes } from './tree';

/**
 * Validate FormConfig structure
 */
export function validateFormConfig(config: FormConfig): TreeOperationResult<void> {
  try {
    // Check basic structure
    if (!config.code || typeof config.code !== 'string') {
      throw new Error('Form config must have a valid code');
    }
    
    if (!config.name || typeof config.name !== 'string') {
      throw new Error('Form config must have a valid name');
    }
    
    if (!config.uiConfig) {
      throw new Error('Form config must have uiConfig');
    }
    
    // Validate root node
    const rootValidation = validateRootNode(config.uiConfig);
    if (!rootValidation.success) {
      throw new Error(rootValidation.error);
    }
    
    // Validate tree structure
    const treeValidation = validateTreeStructure(config.uiConfig);
    if (!treeValidation.success) {
      throw new Error(treeValidation.error);
    }
    
    return { success: true };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

/**
 * Validate root node requirements
 */
export function validateRootNode(node: FormNode): TreeOperationResult<void> {
  try {
    if (node.type !== 'frame') {
      throw new Error('Root node must be frame type');
    }
    
    if (!node.id || typeof node.id !== 'string') {
      throw new Error('Root node must have valid ID');
    }
    
    if (!node.properties || typeof node.properties !== 'object') {
      throw new Error('Root node must have properties object');
    }
    
    return { success: true };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

/**
 * Validate tree structure integrity
 */
export function validateTreeStructure(root: FormNode): TreeOperationResult<void> {
  try {
    const context = createTreeContext(root);
    const nodeCount = countNodes(root);
    
    // Check for duplicate IDs
    if (context.nodeMap.size !== nodeCount) {
      throw new Error('Duplicate node IDs detected in tree');
    }
    
    // Validate each node
    const allNodes = Array.from(context.nodeMap.values());
    for (const node of allNodes) {
      const nodeValidation = validateNode(node);
      if (!nodeValidation.success) {
        throw new Error(`Node ${node.id}: ${nodeValidation.error}`);
      }
    }
    
    // Validate parent-child relationships
    context.parentMap.forEach((parentId, childId) => {
      const child = context.nodeMap.get(childId);
      const parent = context.nodeMap.get(parentId);
      
      if (!child || !parent) {
        throw new Error(`Invalid parent-child relationship: ${parentId} -> ${childId}`);
      }
      
      if (!parent.children || !parent.children.find(c => c.id === childId)) {
        throw new Error(`Parent ${parentId} does not contain child ${childId} in children array`);
      }
    });
    
    return { success: true };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

/**
 * Validate individual node structure
 */
export function validateNode(node: FormNode): TreeOperationResult<void> {
  try {
    // Basic node structure
    if (!node.id || typeof node.id !== 'string') {
      throw new Error('Node must have valid ID');
    }
    
    if (!node.type || typeof node.type !== 'string') {
      throw new Error('Node must have valid type');
    }
    
    // Validate type-specific requirements
    switch (node.type) {
      case 'frame':
        return validateFrameNode(node);
      case 'field':
        return validateFieldNode(node);
      case 'title':
        return validateTitleNode(node);
      case 'control':
        return validateControlNode(node);
      default:
        throw new Error(`Unknown node type: ${node.type}`);
    }
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

/**
 * Validate frame node
 */
function validateFrameNode(node: FormNode): TreeOperationResult<void> {
  try {
    if (node.type !== 'frame') {
      throw new Error('Not a frame node');
    }
    
    if (!node.properties || typeof node.properties !== 'object') {
      throw new Error('Frame node must have properties');
    }
    
    // Children are optional for frame nodes
    if (node.children && !Array.isArray(node.children)) {
      throw new Error('Frame children must be array if present');
    }
    
    return { success: true };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

/**
 * Validate field node
 */
function validateFieldNode(node: FormNode): TreeOperationResult<void> {
  try {
    if (node.type !== 'field') {
      throw new Error('Not a field node');
    }
    
    if (!node.field || typeof node.field !== 'object') {
      throw new Error('Field node must have field configuration');
    }
    
    const { objectKey, component, dataType } = node.field;
    
    if (!objectKey || typeof objectKey !== 'string') {
      throw new Error('Field must have valid objectKey');
    }
    
    if (!component || typeof component !== 'string') {
      throw new Error('Field must have valid component');
    }
    
    if (!dataType || typeof dataType !== 'string') {
      throw new Error('Field must have valid dataType');
    }
    
    // Field nodes cannot have children
    if (node.children && node.children.length > 0) {
      throw new Error('Field nodes cannot have children');
    }
    
    return { success: true };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

/**
 * Validate title node
 */
function validateTitleNode(node: FormNode): TreeOperationResult<void> {
  try {
    if (node.type !== 'title') {
      throw new Error('Not a title node');
    }
    
    if (!node.properties || typeof node.properties !== 'object') {
      throw new Error('Title node must have properties');
    }
    
    // Title nodes cannot have children
    if (node.children && node.children.length > 0) {
      throw new Error('Title nodes cannot have children');
    }
    
    return { success: true };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

/**
 * Validate control node
 */
function validateControlNode(node: FormNode): TreeOperationResult<void> {
  try {
    if (node.type !== 'control') {
      throw new Error('Not a control node');
    }
    
    if (!node.properties || typeof node.properties !== 'object') {
      throw new Error('Control node must have properties');
    }
    
    // Control nodes cannot have children
    if (node.children && node.children.length > 0) {
      throw new Error('Control nodes cannot have children');
    }
    
    return { success: true };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

/**
 * Validate node placement in tree
 */
export function validateNodePlacement(
  parentNode: FormNode,
  childType: FormNode['type'],
  index?: number
): TreeOperationResult<void> {
  try {
    // Check if parent can have children
    if (parentNode.type !== 'frame') {
      throw new Error(`Node type ${parentNode.type} cannot have children`);
    }
    
    // Check if child type can be placed in parent
    if (!canBeChildOfType(childType, parentNode)) {
      throw new Error(`${childType} cannot be child of ${parentNode.type}`);
    }
    
    // Validate index if provided
    if (index !== undefined) {
      const maxIndex = parentNode.children ? parentNode.children.length : 0;
      if (index < 0 || index > maxIndex) {
        throw new Error(`Invalid index ${index}. Must be between 0 and ${maxIndex}`);
      }
    }
    
    return { success: true };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

/**
 * Check if a node type can be child of another node type
 */
function canBeChildOfType(childType: FormNode['type'], parentNode: FormNode): boolean {
  // Only frame nodes can have children
  if (parentNode.type !== 'frame') return false;
  
  // All node types can be children of frame
  switch (childType) {
    case 'field':
    case 'frame':
    case 'title':
    case 'control':
      return true;
    default:
      return false;
  }
}

/**
 * Validate form config before save
 */
export function validateBeforeSave(config: FormConfig): TreeOperationResult<void> {
  try {
    // Basic config validation
    const configValidation = validateFormConfig(config);
    if (!configValidation.success) {
      return configValidation;
    }
    
    // Check if form has at least one field
    const hasFields = hasAnyField(config.uiConfig);
    if (!hasFields) {
      throw new Error('Form must contain at least one field');
    }
    
    // Validate all field objectKeys are unique
    const fieldKeys = extractFieldKeys(config.uiConfig);
    const uniqueKeys = new Set(fieldKeys);
    if (fieldKeys.length !== uniqueKeys.size) {
      throw new Error('Duplicate field objectKeys detected');
    }
    
    return { success: true };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

/**
 * Check if tree contains any field nodes
 */
function hasAnyField(node: FormNode): boolean {
  if (node.type === 'field') return true;
  
  if (node.children) {
    return node.children.some(child => hasAnyField(child));
  }
  
  return false;
}

/**
 * Extract all field objectKeys from tree
 */
function extractFieldKeys(node: FormNode): string[] {
  const keys: string[] = [];
  
  function traverse(node: FormNode) {
    if (node.type === 'field' && node.field?.objectKey) {
      keys.push(node.field.objectKey);
    }
    
    if (node.children) {
      node.children.forEach(traverse);
    }
  }
  
  traverse(node);
  return keys;
}