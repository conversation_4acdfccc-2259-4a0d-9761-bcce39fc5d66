import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import { toast } from "sonner";
import type { ContactState, ContactConfig } from "./type";
import { fetchContactConfig, updateContactConfig } from "./api";

const initialState: ContactState = {
  data: null,
  savedData: null,
  loading: false,
  saving: false,
  error: null,

  isDirty: false,
};

// Async thunks
export const fetchContactAsync = createAsyncThunk(
  "contact/fetchContact",
  async (_, { rejectWithValue }) => {
    try {
      const response = await fetchContactConfig();
      return response.data;
    } catch (error) {
      return rejectWithValue(
        error instanceof Error
          ? error.message
          : "<PERSON>hông thể tải cấu hình liên hệ"
      );
    }
  }
);

export const updateContactAsync = createAsyncThunk(
  "contact/updateContact",
  async (data: ContactConfig, { rejectWithValue }) => {
    try {
      const response = await updateContactConfig(data);
      return response.data;
    } catch (error) {
      return rejectWithValue(
        error instanceof Error
          ? error.message
          : "<PERSON>hông thể cập nhật cấu hình liên hệ"
      );
    }
  }
);

const contactSlice = createSlice({
  name: "contact",
  initialState,
  reducers: {
    setContactData: (state, action: PayloadAction<ContactConfig>) => {
      state.data = action.payload;
      state.isDirty = true;
    },

    clearError: (state) => {
      state.error = null;
    },

    resetContact: (state) => {
      state.data = null;
      state.savedData = null;
      state.isDirty = false;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch contact
      .addCase(fetchContactAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchContactAsync.fulfilled, (state, action) => {
        state.loading = false;
        const serverData = action.payload || null;
        state.data = serverData;
        state.savedData = serverData;

        state.isDirty = false;
      })
      .addCase(fetchContactAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
        toast.error(`Lỗi tải cấu hình liên hệ: ${action.payload}`);
      })

      // Update contact
      .addCase(updateContactAsync.pending, (state) => {
        state.saving = true;
        state.error = null;
      })
      .addCase(updateContactAsync.fulfilled, (state, action) => {
        state.saving = false;
        const serverData = action.payload || null;
        state.data = serverData;
        state.savedData = serverData;

        state.isDirty = false;
        toast.success("Cập nhật cấu hình liên hệ thành công");
      })
      .addCase(updateContactAsync.rejected, (state, action) => {
        state.saving = false;
        state.error = action.payload as string;
        toast.error(`Lỗi cập nhật cấu hình liên hệ: ${action.payload}`);
      });
  },
});

export const { setContactData, clearError, resetContact } =
  contactSlice.actions;

export default contactSlice.reducer;
