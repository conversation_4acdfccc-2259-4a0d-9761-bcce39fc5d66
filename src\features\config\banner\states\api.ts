import { BaseResponse, restApi } from "@/api/restApi";
import type { BannerConfig } from "./type";

// Base URLs for config API
const PUBLIC_CONFIG_URL = "/portal/v1/public/config";
const ADMIN_CONFIG_URL = "/portal/v1/admin/config";

// GET /portal/v1/public/config/banners
export async function fetchBannerConfig(): Promise<BaseResponse<BannerConfig>> {
  const res = await restApi.get<BaseResponse<BannerConfig>>(
    `${PUBLIC_CONFIG_URL}/banners`
  );
  return res.data;
}

// POST /portal/v1/admin/config/banners
export async function updateBannerConfig(
  data: BannerConfig
): Promise<BaseResponse<BannerConfig>> {
  const res = await restApi.post<BaseResponse<BannerConfig>>(
    `${ADMIN_CONFIG_URL}/banners`,
    data
  );
  return res.data;
}
