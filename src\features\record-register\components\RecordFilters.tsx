import { useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Search, RotateCcw } from "lucide-react";
import { RecordRegisterFilters, statusLabels } from "../data/mockData";

interface RecordFiltersProps {
  filters: RecordRegisterFilters;
  onFiltersChange: (filters: RecordRegisterFilters) => void;
  onSearch: () => void;
  onReset: () => void;
}

export const RecordFilters = ({
  filters,
  onFiltersChange,
  onSearch,
  onReset,
}: RecordFiltersProps) => {
  const [localFilters, setLocalFilters] =
    useState<RecordRegisterFilters>(filters);

  const handleFilterChange = (
    key: keyof RecordRegisterFilters,
    value: string
  ) => {
    const newFilters = { ...localFilters, [key]: value };
    setLocalFilters(newFilters);
    onFiltersChange(newFilters);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      onSearch();
    }
  };

  const handleReset = () => {
    const resetFilters: RecordRegisterFilters = {
      keyword: "",
      status: "",
      year: "",
    };
    setLocalFilters(resetFilters);
    onFiltersChange(resetFilters);
    onReset();
  };

  return (
    <div className="bg-white p-4 rounded-lg border shadow-sm space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
        {/* Keyword Search */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700">
            Tìm theo từ khóa
          </label>
          <Input
            placeholder="Nhập..."
            value={localFilters.keyword}
            onChange={(e) => handleFilterChange("keyword", e.target.value)}
            onKeyPress={handleKeyPress}
            className="w-full"
          />
        </div>

        {/* Status Filter */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700">
            Trạng thái
          </label>
          <Select
            value={localFilters.status || undefined}
            onValueChange={(value) => handleFilterChange("status", value || "")}
          >
            <SelectTrigger>
              <SelectValue placeholder="—Trạng thái—" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Tất cả trạng thái</SelectItem>
              <SelectItem value="new">{statusLabels.new}</SelectItem>
              <SelectItem value="pending">{statusLabels.pending}</SelectItem>
              <SelectItem value="approved">{statusLabels.approved}</SelectItem>
              <SelectItem value="rejected">{statusLabels.rejected}</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Year Filter */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700">Năm</label>
          <Select
            value={localFilters.year || undefined}
            onValueChange={(value) => handleFilterChange("year", value || "")}
          >
            <SelectTrigger>
              <SelectValue placeholder="—Năm—" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Tất cả năm</SelectItem>
              <SelectItem value="2025">2025</SelectItem>
              <SelectItem value="2024">2024</SelectItem>
              <SelectItem value="2023">2023</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Action Buttons */}
        <div className="flex space-x-2">
          <Button
            onClick={onSearch}
            className="flex items-center space-x-2"
            variant="default"
          >
            <Search className="h-4 w-4" />
            <span>Tìm kiếm</span>
          </Button>
          <Button
            onClick={handleReset}
            variant="outline"
            className="flex items-center space-x-2"
          >
            <RotateCcw className="h-4 w-4" />
            <span>Reset</span>
          </Button>
        </div>
      </div>
    </div>
  );
};
