import React, { useId } from "react";
import { Checkbox } from "@/components/ui/checkbox";

export interface SimpleCheckboxProps {
  value: boolean;
  label: string;
}

export const SimpleCheckbox = React.memo(function SimpleCheckbox({
  value,
  label,
}: SimpleCheckboxProps) {
  const id = `checkbox-${useId()}`;

  return (
    <div className="flex items-center space-x-2">
      <Checkbox id={id} checked={value} />
      <label
        htmlFor={id}
        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
      >
        {label}
      </label>
    </div>
  );
});
