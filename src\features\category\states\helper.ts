// File: src/features/category/states/helper.ts
import type { CategoryDTO } from "./types";

/**
 * <PERSON><PERSON> thập tất cả id của node v<PERSON> các descendants trong cây.
 *
 * @param list - Mảng CategoryDTO phẳng
 * @param rootId - id của node gốc cần lấy descendants
 * @returns Mảng các id (bao gồm cả rootId)
 */
export function collectDescendantIds(
  list: CategoryDTO[],
  rootId: number
): number[] {
  // Build map parentId -> children[]
  const childrenMap = new Map<number, CategoryDTO[]>();
  for (const node of list) {
    if (node.parentId != null) {
      const arr = childrenMap.get(node.parentId) ?? [];
      arr.push(node);
      childrenMap.set(node.parentId, arr);
    }
  }

  // BFS/DFS from rootId
  const result: number[] = [];
  const stack: number[] = [rootId];

  while (stack.length > 0) {
    const id = stack.pop()!;
    result.push(id);
    const children = childrenMap.get(id);
    if (children) {
      for (const child of children) {
        stack.push(child.id);
      }
    }
  }

  return result;
}
