import { useEffect, useState } from "react";
import { AutoTable } from "@/components/table/AutoTable";
import { album_columns } from "../states/table";
import { Album, Pagination } from "../states/types";
import { useAppDispatch, useAppSelector } from "@/store/rootReducer";
import {
  selectAlbumQueryParams,
  selectAlbumRefetch,
  selectAlbumLoading,
  selectAlbumError,
} from "../states/selector";
import { fetchAlbums } from "../states/api";
import { setFilters } from "../states/slices";
import { LoadingPage } from "@/components/loading/LoadingPage";
import { CreateAlbumDialog } from "./CreateAlbumDialog";

interface AlbumTableProps {
  isUrlSyncing?: boolean;
}

export const AlbumTable: React.FC<AlbumTableProps> = ({
  isUrlSyncing = false,
}) => {
  const [data, setData] = useState<Album[]>([]);
  const [pagination, setPagination] = useState<Pagination>({
    page: 0,
    size: 20,
    total: 0,
  });

  const params = useAppSelector(selectAlbumQueryParams);
  const refetch = useAppSelector(selectAlbumRefetch);
  const dispatch = useAppDispatch();
  const loading = useAppSelector(selectAlbumLoading);
  const error = useAppSelector(selectAlbumError);

  useEffect(() => {
    const fetchData = async () => {
      try {
        console.log("AlbumTable - Fetching with params:", params);
        const response = await fetchAlbums(params);
        console.log("AlbumTable - API response:", response);
        setData(response.data.data || []);
        setPagination(response.data.pagination || { page: 0, size: 20, total: 0 });
      } catch (err) {
        console.error("Error fetching albums:", err);
      }
    };
    fetchData();
  }, [params, refetch]);

  // Show URL sync loading
  if (isUrlSyncing) {
    return (
      <div className="p-4 space-y-4">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-xl font-semibold">Quản lý thư viện ảnh</h1>
            <p className="text-sm text-muted-foreground">
              Quản lý các thư viện ảnh và video
            </p>
          </div>
          <CreateAlbumDialog />
        </div>
        <div className="flex items-center justify-center min-h-[200px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
            <p className="text-sm text-muted-foreground">
              Đang đồng bộ trạng thái...
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (loading) return <LoadingPage />;
  if (error) return <div className="text-red-500 p-4">{error}</div>;

  const handlePageChange = (index: number) => {
    dispatch(setFilters({ page: index })); // Direct 0-based indexing
  };

  const handlePageSizeChange = (size: number) => {
    dispatch(setFilters({ size: size, page: 0 })); // Reset to page 0 when changing size
  };

  const tablePagination = {
    pageIndex: pagination?.page ?? 0, // Safe access with fallback
    pageSize: pagination?.size ?? 20,
    totalCount: data.length, // Use data length since API total is unreliable
    onPageChange: handlePageChange,
    onPageSizeChange: handlePageSizeChange,
  };

  return (
    <div className="p-4 space-y-4">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-xl font-semibold">Quản lý thư viện ảnh</h1>
          <p className="text-sm text-muted-foreground">
            Quản lý các thư viện ảnh và video
          </p>
        </div>
        <CreateAlbumDialog />
      </div>
      {data?.length === 0 ? (
        <div className="text-center text-muted-foreground py-10">
          Không có thư viện ảnh nào
        </div>
      ) : (
        <AutoTable<Album>
          columns={album_columns}
          data={data}
          pagination={tablePagination}
        />
      )}
    </div>
  );
};
