/**
 * TabInput - Tab selection component for AutoForm
 */

import React from "react";
import { FieldComponentProps } from "../../FieldFactory";

export const TabInput: React.FC<FieldComponentProps> = ({
  value,
  onChange,
  onBlur,
  disabled = false,
  options = [],
  id,
  className = "",
}) => {
  const handleTabClick = disabled ? undefined : (tab: string) => onChange(tab);

  return (
    <div className={`flex gap-2 ${className}`} id={id} onBlur={onBlur}>
      {options.map((tab, idx) => (
        <button
          key={String(tab) + idx}
          type="button"
          disabled={disabled}
          onClick={() => handleTabClick?.(String(tab))}
          className={`px-4 py-2 rounded border transition
            ${
              value === tab
                ? "bg-blue-600 text-white border-blue-600"
                : "bg-white border-gray-300 text-gray-700"
            }
            ${disabled ? "opacity-50 cursor-not-allowed" : "hover:bg-blue-50"}
          `}
        >
          {tab}
        </button>
      ))}
    </div>
  );
};
