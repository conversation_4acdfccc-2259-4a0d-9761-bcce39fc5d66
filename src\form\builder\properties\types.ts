/**
 * Properties System Types
 * Type definitions for the new properties form system
 */

import { FormNode } from "@/form/types";

// Properties form tab types
export type PropertyTab = 'attributes' | 'display';

// Display mode types
export type DisplayMode = 'basic' | 'advanced';

// Base property value types
export interface PropertyValue {
  value: string;
  label: string;
  description?: string;
}

export interface PropertyGroup {
  id: string;
  label: string;
  options: PropertyValue[];
}

// Section state interface
export interface SectionState {
  [sectionId: string]: boolean; // open/closed state
}

// Properties form props interface
export interface PropertiesFormProps {
  node: FormNode;
  onChange: (updates: Partial<FormNode>) => void;
  disabled?: boolean;
}

// Layout options for Frame nodes
export type LayoutType = 'default' | 'horizontal' | 'vertical' | 'grid';

export interface GridConfig {
  rows: number;
  cols: number;
}

// Style section types
export interface StyleUpdate {
  container?: string;
  content?: string;
  field?: string;
  label?: string;
  error?: string;
}

// Property section configuration
export interface PropertySection {
  id: string;
  title: string;
  component: React.ComponentType<PropertiesFormProps>;
  isAvailable: (nodeType: FormNode['type']) => boolean;
}