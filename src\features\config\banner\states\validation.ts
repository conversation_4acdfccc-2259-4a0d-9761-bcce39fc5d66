import { z } from "zod";

// ============================================================================
// Validation Schemas
// ============================================================================

/**
 * Validation schema for individual banner item
 * Enforces URL format for image source, alt text is optional
 */
export const bannerItemSchema = z.object({
  src: z
    .string()
    .min(1, "Vui lòng nhập URL hình ảnh")
    .url("URL hình ảnh không đúng định dạng"),
  alt: z.string().optional().default(""),
});

/**
 * Validation schema for the complete banner configuration
 * Validates array of banner items
 */
export const bannerConfigSchema = z.object({
  banners: z.array(bannerItemSchema).min(1, "Phải có ít nhất một banner"),
});

// ============================================================================
// Type Exports
// ============================================================================

/**
 * TypeScript type for banner item validation
 */
export type BannerItemValidation = z.infer<typeof bannerItemSchema>;

/**
 * TypeScript type for banner configuration validation
 */
export type BannerConfigValidation = z.infer<typeof bannerConfigSchema>;
