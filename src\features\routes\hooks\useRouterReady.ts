import { useState, useEffect } from "react";
import { useAppSelector } from "@/store/rootReducer";
import {
  selectCategoryByType,
  selectCategoryLoading,
} from "@/features/category/states/selector";

export const useRouterReady = () => {
  const [isReady, setIsReady] = useState(false);
  const [hasInitialized, setHasInitialized] = useState(false);

  const publicCategories = useAppSelector((state) =>
    selectCategoryByType(state, "public-menu")
  );
  const adminCategories = useAppSelector((state) =>
    selectCategoryByType(state, "admin-menu")
  );
  const categoryLoading = useAppSelector(selectCategoryLoading);

  useEffect(() => {
    // Check if we have essential data or if loading is complete
    const hasPublicData = publicCategories.length > 0;
    const hasAdminData = adminCategories.length > 0;

    // Consider ready if:
    // 1. We have data for both types, OR
    // 2. We're not loading and have tried to fetch (even if empty)
    const isDataReady =
      (hasPublicData && hasAdminData) || (!categoryLoading && hasInitialized);

    setIsReady(isDataReady);
  }, [
    publicCategories.length,
    adminCategories.length,
    categoryLoading,
    hasInitialized,
  ]);

  // Track that we've attempted initialization
  useEffect(() => {
    if (!hasInitialized && !categoryLoading) {
      setHasInitialized(true);
    }
  }, [categoryLoading, hasInitialized]);

  return {
    isReady,
    hasPublicData: publicCategories.length > 0,
    hasAdminData: adminCategories.length > 0,
    isLoading: categoryLoading,
  };
};
