/**
 * Form Builder Tree Operations
 * Optimized tree algorithms with immer for immutability
 */

import { produce, Draft } from 'immer';
import { FormNode } from '@/form/types';
import { TreeOperationContext, TreeOperationResult, NodePath } from './types';

/**
 * Initialize tree operation context for efficient operations
 */
export function createTreeContext(root: FormNode): TreeOperationContext {
  const nodeMap = new Map<string, FormNode>();
  const parentMap = new Map<string, string>();
  const pathMap = new Map<string, NodePath>();
  
  function traverse(node: FormNode, parentId: string | null = null, path: NodePath = []) {
    const currentPath = [...path, node.id];
    
    nodeMap.set(node.id, node);
    if (parentId) {
      parentMap.set(node.id, parentId);
    }
    pathMap.set(node.id, currentPath);
    
    if (node.children) {
      node.children.forEach(child => traverse(child, node.id, currentPath));
    }
  }
  
  traverse(root);
  
  return { root, nodeMap, parentMap, pathMap };
}

/**
 * Find node by ID - O(1) with context
 */
export function findNode(
  context: TreeOperationContext, 
  nodeId: string
): FormNode | null {
  return context.nodeMap.get(nodeId) || null;
}

/**
 * Get parent node - O(1) with context
 */
export function getParentNode(
  context: TreeOperationContext, 
  nodeId: string
): FormNode | null {
  const parentId = context.parentMap.get(nodeId);
  return parentId ? context.nodeMap.get(parentId) || null : null;
}

/**
 * Get node children - safe access
 */
export function getNodeChildren(node: FormNode): FormNode[] {
  return node.children || [];
}

/**
 * Get node siblings
 */
export function getNodeSiblings(
  context: TreeOperationContext,
  nodeId: string
): FormNode[] {
  const parent = getParentNode(context, nodeId);
  if (!parent || !parent.children) return [];
  
  return parent.children.filter(child => child.id !== nodeId);
}

/**
 * Get node depth in tree
 */
export function getNodeDepth(
  context: TreeOperationContext,
  nodeId: string
): number {
  const path = context.pathMap.get(nodeId);
  return path ? path.length - 1 : -1;
}

/**
 * Check if node is ancestor of another node
 */
export function isAncestorOf(
  context: TreeOperationContext,
  ancestorId: string,
  descendantId: string
): boolean {
  const descendantPath = context.pathMap.get(descendantId);
  return descendantPath ? descendantPath.includes(ancestorId) : false;
}

/**
 * Validate if a node can be added as child
 */
export function canAddChild(
  parentNode: FormNode, 
  childType: FormNode['type']
): boolean {
  // Only frame nodes can have children
  if (parentNode.type !== 'frame') return false;
  
  // All node types can be children of frame
  // This could be extended in the future for specific rules
  switch (childType) {
    case 'field':
    case 'frame':
    case 'title':
    case 'control':
      return true;
    default:
      return false;
  }
}

/**
 * Validate if a node can be moved to a new parent
 */
export function canMoveNode(
  context: TreeOperationContext,
  nodeId: string,
  newParentId: string
): TreeOperationResult<void> {
  try {
    const node = findNode(context, nodeId);
    const newParent = findNode(context, newParentId);
    
    if (!node || !newParent) {
      throw new Error('Node or parent not found');
    }
    
    // Cannot move root
    if (nodeId === context.root.id) {
      throw new Error('Cannot move root node');
    }
    
    // Check if new parent can accept this node type
    if (!canAddChild(newParent, node.type)) {
      throw new Error(`Cannot add ${node.type} as child of ${newParent.type}`);
    }
    
    // Check for circular reference
    if (isAncestorOf(context, nodeId, newParentId)) {
      throw new Error('Cannot move node to its own descendant');
    }
    
    return { success: true };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

/**
 * Add node to tree - Returns new tree with immer
 */
export function addNodeToTree(
  root: FormNode,
  parentId: string,
  newNode: FormNode,
  index?: number
): TreeOperationResult<FormNode> {
  try {
    const newRoot = produce(root, draft => {
      const context = createTreeContext(draft);
      const parent = context.nodeMap.get(parentId);
      
      if (!parent) {
        throw new Error(`Parent node ${parentId} not found`);
      }
      
      if (!canAddChild(parent, newNode.type)) {
        throw new Error(`Cannot add ${newNode.type} as child of ${parent.type}`);
      }
      
      if (!parent.children) {
        parent.children = [];
      }
      
      const insertIndex = index !== undefined ? 
        Math.max(0, Math.min(index, parent.children.length)) : 
        parent.children.length;
        
      parent.children.splice(insertIndex, 0, newNode as Draft<FormNode>);
    });
    
    return { success: true, data: newRoot };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

/**
 * Update node in tree
 */
export function updateNodeInTree(
  root: FormNode,
  nodeId: string,
  updates: Partial<FormNode>
): TreeOperationResult<FormNode> {
  try {
    const newRoot = produce(root, draft => {
      const context = createTreeContext(draft);
      const node = context.nodeMap.get(nodeId);
      
      if (!node) {
        throw new Error(`Node ${nodeId} not found`);
      }
      
      // Preserve immutable properties
      const { id, children, ...allowedUpdates } = updates;
      
      // Merge updates while preserving node structure
      Object.assign(node, allowedUpdates);
      
      // Handle children updates separately if provided
      if (children !== undefined) {
        node.children = children as Draft<FormNode>[];
      }
    });
    
    return { success: true, data: newRoot };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

/**
 * Delete node from tree
 */
export function deleteNodeFromTree(
  root: FormNode,
  nodeId: string
): TreeOperationResult<FormNode> {
  try {
    // Cannot delete root
    if (root.id === nodeId) {
      throw new Error('Cannot delete root node');
    }
    
    const newRoot = produce(root, draft => {
      const context = createTreeContext(draft);
      const parent = getParentNode(context, nodeId);
      
      if (!parent || !parent.children) {
        throw new Error(`Cannot find parent of node ${nodeId}`);
      }
      
      const index = parent.children.findIndex(child => child.id === nodeId);
      if (index === -1) {
        throw new Error(`Node ${nodeId} not found in parent's children`);
      }
      
      parent.children.splice(index, 1);
    });
    
    return { success: true, data: newRoot };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

/**
 * Move node within tree
 */
export function moveNodeInTree(
  root: FormNode,
  nodeId: string,
  newParentId: string,
  index: number
): TreeOperationResult<FormNode> {
  try {
    const context = createTreeContext(root);
    
    // Validate move operation first
    const validation = canMoveNode(context, nodeId, newParentId);
    if (!validation.success) {
      return { success: false, error: validation.error };
    }
    
    const newRoot = produce(root, draft => {
      const draftContext = createTreeContext(draft);
      
      // Get all involved nodes
      const node = draftContext.nodeMap.get(nodeId);
      const oldParent = getParentNode(draftContext, nodeId);
      const newParent = draftContext.nodeMap.get(newParentId);
      
      if (!node || !oldParent || !newParent) {
        throw new Error('Invalid move operation');
      }
      
      // Remove from old parent
      if (oldParent.children) {
        const oldIndex = oldParent.children.findIndex(child => child.id === nodeId);
        if (oldIndex !== -1) {
          oldParent.children.splice(oldIndex, 1);
        }
      }
      
      // Add to new parent
      if (!newParent.children) {
        newParent.children = [];
      }
      
      const insertIndex = Math.max(0, Math.min(index, newParent.children.length));
      newParent.children.splice(insertIndex, 0, node);
    });
    
    return { success: true, data: newRoot };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

/**
 * Duplicate node (deep clone)
 */
export function duplicateNode(
  root: FormNode,
  nodeId: string,
  generateId: () => string
): TreeOperationResult<FormNode> {
  try {
    const newRoot = produce(root, draft => {
      const context = createTreeContext(draft);
      const node = context.nodeMap.get(nodeId);
      const parent = getParentNode(context, nodeId);
      
      if (!node || !parent || !parent.children) {
        throw new Error('Cannot duplicate node');
      }
      
      // Deep clone with new IDs
      function cloneWithNewIds(node: FormNode): FormNode {
        const cloned = { ...node, id: generateId() };
        if (node.children) {
          cloned.children = node.children.map(child => cloneWithNewIds(child));
        }
        return cloned;
      }
      
      const cloned = cloneWithNewIds(node);
      const index = parent.children.findIndex(child => child.id === nodeId);
      parent.children.splice(index + 1, 0, cloned as Draft<FormNode>);
    });
    
    return { success: true, data: newRoot };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

/**
 * Get all descendant node IDs
 */
export function getDescendantIds(node: FormNode): string[] {
  const descendants: string[] = [];
  
  function traverse(node: FormNode) {
    if (node.children) {
      node.children.forEach(child => {
        descendants.push(child.id);
        traverse(child);
      });
    }
  }
  
  traverse(node);
  return descendants;
}

/**
 * Generate unique node ID
 */
export function generateNodeId(): string {
  return `node_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Count total nodes in tree
 */
export function countNodes(node: FormNode): number {
  let count = 1;
  if (node.children) {
    count += node.children.reduce((sum, child) => sum + countNodes(child), 0);
  }
  return count;
}

/**
 * Flatten tree to array (depth-first)
 */
export function flattenTree(root: FormNode): FormNode[] {
  const result: FormNode[] = [];
  
  function traverse(node: FormNode) {
    result.push(node);
    if (node.children) {
      node.children.forEach(traverse);
    }
  }
  
  traverse(root);
  return result;
}