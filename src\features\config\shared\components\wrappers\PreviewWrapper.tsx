import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { cn } from "@/lib/utils";

/**
 * Props for PreviewWrapper component
 */
export interface PreviewWrapperProps {
  /** Child components to wrap */
  children: React.ReactNode;
  /** Preview title */
  title?: string;
  /** Preview description/note */
  description?: string;
  /** Color theme for the preview */
  theme?: 'blue' | 'green' | 'purple' | 'indigo' | 'orange';
  /** Whether the preview is visible */
  visible?: boolean;
  /** Additional CSS classes */
  className?: string;
  /** Whether to show the preview header */
  showHeader?: boolean;
  /** Whether to show the preview note */
  showNote?: boolean;
}

/**
 * Reusable preview wrapper component
 * Adds preview-specific styling and layout to display components
 */
export const PreviewWrapper: React.FC<PreviewWrapperProps> = ({
  children,
  title = "Xem trước",
  description,
  theme = 'blue',
  visible = true,
  className,
  showHeader = true,
  showNote = true,
}) => {
  if (!visible) {
    return null;
  }

  const themeStyles = {
    blue: {
      border: 'border-blue-300',
      background: 'bg-gradient-to-r from-blue-50/30 to-indigo-50/30',
      indicator: 'bg-blue-500',
      title: 'from-blue-600 to-indigo-600',
      line: 'from-blue-200',
      note: 'bg-blue-100/50 border-blue-200 text-blue-800'
    },
    green: {
      border: 'border-green-300',
      background: 'bg-gradient-to-r from-green-50/30 to-emerald-50/30',
      indicator: 'bg-green-500',
      title: 'from-green-600 to-emerald-600',
      line: 'from-green-200',
      note: 'bg-green-100/50 border-green-200 text-green-800'
    },
    purple: {
      border: 'border-purple-300',
      background: 'bg-gradient-to-r from-purple-50/30 to-indigo-50/30',
      indicator: 'bg-purple-500',
      title: 'from-purple-600 to-indigo-600',
      line: 'from-purple-200',
      note: 'bg-purple-100/50 border-purple-200 text-purple-800'
    },
    indigo: {
      border: 'border-indigo-300',
      background: 'bg-gradient-to-r from-indigo-50/30 to-blue-50/30',
      indicator: 'bg-indigo-500',
      title: 'from-indigo-600 to-blue-600',
      line: 'from-indigo-200',
      note: 'bg-indigo-100/50 border-indigo-200 text-indigo-800'
    },
    orange: {
      border: 'border-orange-300',
      background: 'bg-gradient-to-r from-orange-50/30 to-red-50/30',
      indicator: 'bg-orange-500',
      title: 'from-orange-600 to-red-600',
      line: 'from-orange-200',
      note: 'bg-orange-100/50 border-orange-200 text-orange-800'
    }
  };

  const styles = themeStyles[theme];

  return (
    <div className="flex-1 overflow-y-auto">
      <Card className={cn(
        'border-2 border-dashed',
        styles.border,
        styles.background,
        className
      )}>
        <CardContent className="p-6">
          <div className="space-y-6">
            {/* Preview Header */}
            {showHeader && (
              <div className="flex items-center gap-2">
                <div className={cn('w-3 h-3 rounded-full animate-pulse', styles.indicator)}></div>
                <h3 className={cn(
                  'text-lg font-semibold bg-gradient-to-r bg-clip-text text-transparent',
                  styles.title
                )}>
                  {title}
                </h3>
                <div className={cn('flex-1 h-px bg-gradient-to-r to-transparent', styles.line)}></div>
              </div>
            )}

            {/* Preview Note */}
            {showNote && description && (
              <div className={cn('border rounded-lg p-3', styles.note)}>
                <p className="text-sm">
                  <span className="font-medium">💡 Xem trước:</span> {description}
                </p>
              </div>
            )}

            {/* Preview Content */}
            <div className="bg-white border rounded-lg p-6 overflow-y-auto">
              {children}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
