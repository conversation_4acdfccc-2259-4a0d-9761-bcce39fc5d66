// Pages
export { RecordRegisterAdminPage } from "./pages/loadable";

// Components
export { RecordFilters } from "./components/RecordFilters";
export { RecordTable } from "./components/RecordTable";
export { RecordPagination } from "./components/RecordPagination";

// Data & Types
export type {
  RecordRegister,
  RecordRegisterFilters,
  RecordRegisterData,
} from "./data/mockData";

export {
  mockRecordRegisters,
  generateMockRecordRegisters,
  filterRecordRegisters,
  paginateRecords,
  statusLabels,
  statusColors,
} from "./data/mockData";
