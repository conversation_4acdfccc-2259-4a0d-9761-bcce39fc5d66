import { ColumnConfig } from "@/components/table/registry";

export const user_columns: ColumnConfig[] = [
  { accessorKey: "id", header: "ID" },
  { accessorKey: "name", header: "<PERSON>ê<PERSON>" },
  {
    accessorKey: "status",
    header: "Trạng thái",
    visible: true,
    cell: {
      component: "Badge",
      props: {
        colorMap: { active: "green", inactive: "gray" },
      },
    },
  },
  {
    accessorKey: "avatar",
    header: "Trạng thái",
    visible: true,
    cell: {
      component: "Avatar",
      props: {
        url: "https://i.pravatar.cc/150?u=a042581f4e290267",
      },
    },
  },
  {
    accessorKey: "emailVerified",
    header: "Đã xác thực",
    visible: true,
    cell: {
      component: "ActionCheckbox",
      props: {
        label: "Xác thực",
      },
    },
  },
  { accessorKey: "notes", header: "<PERSON><PERSON> chú", visible: false },
  { accessorKey: "metadata", header: "<PERSON>ada<PERSON>", visible: false },
  {
    accessorKey: "actions",
    header: "Metadata",
    visible: false,
    cell: {
      component: "SimpleCheckbox",
      props: {
        label: "Xác thực",
      },
    },
  },
  {
    accessorKey: "actions",
    header: "Thao tác",
    cell: {
      component: "ActionCell",
      props: {
        actions: ["edit", "remove"],
      },
    },
  },
];

export interface User {
  id: number;
  name: string;
  status: string;
  emailVerified: boolean;
  loginCount: number;
  lastLogin: Date;
  notes: string | null;
  metadata: Record<string, string | number | boolean>;
}
export function generateMockUsers(count: number): User[] {
  const users: User[] = [];
  for (let i = 0; i < count; i++) {
    users.push({
      id: i + 1,
      name: names[i % names.length] + ` #${i + 1}`,
      status: statuses[Math.floor(Math.random() * statuses.length)],
      emailVerified: Math.random() > 0.5,
      loginCount: Math.floor(Math.random() * 100),
      lastLogin: new Date(Date.now() - Math.floor(Math.random() * 1e9)),
      notes: Math.random() > 0.3 ? `Ghi chú người dùng #${i + 1}` : null,
      metadata: {
        tier: ["free", "pro", "enterprise"][i % 3],
        isBetaTester: Math.random() > 0.8,
        rating: Number((Math.random() * 5).toFixed(2)),
      },
    });
  }
  return users;
}

const names = [
  "Nguyễn Văn A",
  "Trần Thị B",
  "Lê Văn C",
  "Phạm Văn D",
  "Hoàng Thị E",
  "Trần Văn F",
  "Lê Thị G",
  "Nguyễn Văn H",
  "Trần Thị I",
  "Lê Văn J",
  "Phạm Văn K",
  "Hoàng Thị L",
  "Nguyễn Văn M",
  "Trần Thị N",
  "Lê Văn O",
  "Phạm Văn P",
  "Hoàng Thị Q",
  "Nguyễn Văn R",
  "Trần Thị S",
  "Lê Văn T",
  "Phạm Văn U",
  "Hoàng Thị V",
  "Nguyễn Văn W",
  "Trần Thị X",
  "Lê Văn Y",
  "Phạm Văn Z",
];

const statuses = ["active", "inactive", "pending", "suspended"];
