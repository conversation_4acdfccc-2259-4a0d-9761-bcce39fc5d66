import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Save, RefreshCw, AlertCircle, Loader2, Eye } from "lucide-react";
import { ConfigStatusBadges } from "../ConfigStatusBadges";

export interface ConfigMetadata {
  title: string;
  description: string;
}

export interface ConfigPageLayoutProps {
  title?: string;
  description?: string;
  metadata?: ConfigMetadata;
  loading?: boolean;
  saving?: boolean;
  error?: string | null;
  isDirty?: boolean;
  hasValidationErrors?: boolean;
  saveDisabled?: boolean;
  onSave?: () => void;
  onRefresh?: () => void;
  onClearError?: () => void;
  onPreview?: () => void;
  showPreview?: boolean;
  wrapInCard?: boolean;
  showDirtyAlert?: boolean;
  children: React.ReactNode;
}

export const ConfigPageLayout: React.FC<ConfigPageLayoutProps> = ({
  title,
  description,
  metadata,
  loading = false,
  saving = false,
  error = null,
  isDirty = false,
  hasValidationErrors = false,
  saveDisabled = false,
  onSave,
  onRefresh,
  onClearError,
  onPreview,
  showPreview = false,
  wrapInCard = false,
  showDirtyAlert = false,
  children,
}) => {
  const pageTitle = metadata?.title || title || "";
  const pageDescription = metadata?.description || description || "";

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{pageTitle}</h1>
          {pageDescription && (
            <p className="text-muted-foreground mt-2">{pageDescription}</p>
          )}
        </div>

        <div className="flex items-center gap-3">
          <ConfigStatusBadges
            isDirty={isDirty}
            hasValidationErrors={hasValidationErrors}
          />

          <div className="flex items-center gap-2">
            {onRefresh && (
              <Button
                variant="outline"
                onClick={onRefresh}
                disabled={loading}
                size="sm"
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                Làm mới
              </Button>
            )}

            {onPreview && (
              <Button
                variant={showPreview ? "default" : "outline"}
                onClick={onPreview}
                disabled={loading}
                size="sm"
              >
                <Eye className="h-4 w-4 mr-2" />
                {showPreview ? "Hủy xem trước" : "Xem trước"}
              </Button>
            )}

            {onSave && (
              <Button
                onClick={onSave}
                disabled={saveDisabled || loading || saving || !isDirty}
                size="sm"
                className="min-w-[100px]"
              >
                {saving ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Đang lưu...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Lưu
                  </>
                )}
              </Button>
            )}
          </div>
        </div>
      </div>

      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-red-600" />
              <span className="text-red-800">{error}</span>
              {onClearError && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onClearError}
                  className="ml-auto text-red-600 hover:text-red-800"
                >
                  Đóng
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {showDirtyAlert && isDirty && !error && (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Bạn có thay đổi chưa được lưu. Nhấn "Lưu" để áp dụng các thay đổi.
          </AlertDescription>
        </Alert>
      )}

      <div className="flex-1 flex flex-col overflow-hidden h-[90vh]">
        {wrapInCard ? (
          <Card className="flex-1 flex flex-col overflow-hidden">
            {children}
          </Card>
        ) : (
          children
        )}
      </div>
    </div>
  );
};
