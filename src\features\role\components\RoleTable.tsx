import React, { useEffect } from "react";
import { useAppSelector, useAppDispatch } from "@/store/rootReducer";
import {
  selectFilteredRoles,
  selectLoading,
  selectSearchTerm,
} from "../states/selectors";
import {
  fetchRolesThunk,
  setCurrentRole,
  setSearchTerm,
} from "../states/slices";
import { useSwitchMode } from "../states/hook";
import { AutoTable } from "@/components/table/AutoTable";
import { RoleData } from "../states/type";
import { Input } from "@/components/ui/input";
import { Search } from "lucide-react";
import { ROLE_COLUMNS } from "../states/table";

export const RoleTable = () => {
  const dispatch = useAppDispatch();
  const switchMode = useSwitchMode();

  const roles = useAppSelector(selectFilteredRoles);
  const loading = useAppSelector(selectLoading);
  const searchTerm = useAppSelector(selectSearchTerm);

  useEffect(() => {
    dispatch(fetchRolesThunk());
  }, [dispatch]);

  const handleAction = (action: string, row: RoleData) => {
    if (action === "edit") {
      dispatch(setCurrentRole(row));
      switchMode("detail", row.id.toString(), "view");
    }
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setSearchTerm(event.target.value));
  };

  return (
    <div className="space-y-4">
      {/* Search */}
      <div className="flex items-center space-x-2">
        <div className="relative">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Tìm kiếm vai trò..."
            value={searchTerm}
            onChange={handleSearchChange}
            className="pl-8 max-w-sm"
          />
        </div>
      </div>

      {/* Table */}
      <AutoTable<RoleData>
        columns={ROLE_COLUMNS}
        data={roles}
        loading={loading}
        onAction={handleAction}
      />
    </div>
  );
};
