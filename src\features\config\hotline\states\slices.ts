import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import { toast } from "sonner";
import type { HotlineState, HotlineConfig } from "./type";
import { fetchHotlineConfig, updateHotlineConfig } from "./api";

// ============================================================================
// Initial State
// ============================================================================

const initialState: HotlineState = {
  data: null,
  savedData: null,
  loading: false,
  saving: false,
  error: null,
  isDirty: false,
};

// ============================================================================
// Async Thunks
// ============================================================================

/**
 * Async thunk to fetch hotline configuration
 */
export const fetchHotlineAsync = createAsyncThunk(
  "hotline/fetchConfig",
  async (_, { rejectWithValue }) => {
    try {
      const response = await fetchHotlineConfig();

      if (response.status !== 200 || !response.data) {
        throw new Error("Không thể tải cấu hình đường dây nóng");
      }

      const returnValue = response.data as unknown as { data: HotlineConfig };
      return returnValue.data;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Đã xảy ra lỗi không xác định";
      toast.error(`Không thể tải cấu hình đường dây nóng: ${errorMessage}`);
      return rejectWithValue(errorMessage);
    }
  }
);

/**
 * Async thunk to update hotline configuration
 */
export const updateHotlineAsync = createAsyncThunk(
  "hotline/updateConfig",
  async (data: HotlineConfig, { rejectWithValue }) => {
    try {
      const response = await updateHotlineConfig(data);

      if (response.status !== 200 || !response.data) {
        throw new Error("Không thể cập nhật cấu hình đường dây nóng");
      }

      toast.success("Cập nhật cấu hình đường dây nóng thành công");
      const returnValue = response.data as unknown as { data: HotlineConfig };
      return returnValue.data;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Đã xảy ra lỗi không xác định";
      toast.error(
        `Không thể cập nhật cấu hình đường dây nóng: ${errorMessage}`
      );
      return rejectWithValue(errorMessage);
    }
  }
);

// ============================================================================
// Slice Definition
// ============================================================================

const hotlineSlice = createSlice({
  name: "hotline",
  initialState,
  reducers: {
    /**
     * Mark data as dirty (unsaved changes)
     */
    setDirty: (state, action: PayloadAction<boolean>) => {
      state.isDirty = action.payload;
    },
    /**
     * Clear error state
     */
    clearError: (state) => {
      state.error = null;
    },
    /**
     * Reset state to initial values
     */
    resetState: () => initialState,
  },
  extraReducers: (builder) => {
    // ========================================================================
    // Fetch Hotline Configuration
    // ========================================================================
    builder
      .addCase(fetchHotlineAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchHotlineAsync.fulfilled, (state, action) => {
        state.loading = false;
        const serverData = action.payload;
        state.data = serverData;
        state.savedData = serverData;
        state.error = null;
        state.isDirty = false;
      })
      .addCase(fetchHotlineAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });

    // ========================================================================
    // Update Hotline Configuration
    // ========================================================================
    builder
      .addCase(updateHotlineAsync.pending, (state) => {
        state.saving = true;
        state.error = null;
      })
      .addCase(updateHotlineAsync.fulfilled, (state, action) => {
        state.saving = false;
        const serverData = action.payload;
        state.data = serverData;
        state.savedData = serverData;
        state.error = null;
        state.isDirty = false;
      })
      .addCase(updateHotlineAsync.rejected, (state, action) => {
        state.saving = false;
        state.error = action.payload as string;
      });
  },
});

// ============================================================================
// Export Actions and Reducer
// ============================================================================

export const { setDirty, clearError, resetState } = hotlineSlice.actions;
export default hotlineSlice.reducer;
