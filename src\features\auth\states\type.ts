import { UserData } from "@/features/users/states/type";

export interface LoginRequest {
  userName: string;
  password: string;
}
export interface RegisterRequest {
  userName: string;
  fullName: string;
  email: string;
  phone: string;
  password: string;
}
export interface AccountResponse extends UserData {
  roles: number[];
  groups: number[];
  accessToken: string;
  refreshToken: string;
}
export interface User {
  userName: string;
  fullName: string;
  email: string;
  phone: string;
  password?: string;
}
export interface AuthState {
  account: AccountResponse | null;
  loading: boolean;
  error: string | null;
}

export interface Account {
  account: AccountResponse | null;
  is_authenticated: boolean;
}

export const initialAccount: Account = {
  is_authenticated: false,
  account: null,
};
