import { BaseResponse, restApi } from "@/api/restApi";
import { API_ENDPOINTS } from "@/api/endpoints";
import { Group, GroupRole, CreateGroupRequest } from "./types";

const BASE_URL = API_ENDPOINTS.AUTH.ADMIN.GROUPS;
const GROUP_ROLE_BASE_URL = API_ENDPOINTS.AUTH.ADMIN.GROUP_ROLES;

// ============================================================================
// GROUP CRUD OPERATIONS
// ============================================================================

export const fetchGroups = async (): Promise<BaseResponse<Group[]>> => {
  const response = await restApi.get(BASE_URL);
  return response.data;
};

export const fetchGroupById = async (
  id: number
): Promise<BaseResponse<Group>> => {
  const response = await restApi.get(`${BASE_URL}/${id}`);
  return response.data;
};

export const createGroup = async (
  data: CreateGroupRequest
): Promise<BaseResponse<Group>> => {
  const response = await restApi.post(BASE_URL, data);
  return response.data;
};

export const updateGroup = async (
  id: number,
  data: Partial<Group>
): Promise<BaseResponse<Group>> => {
  const response = await restApi.put(`${BASE_URL}/${id}`, data);
  return response.data;
};

export const deleteGroup = async (id: number): Promise<BaseResponse> => {
  const response = await restApi.delete(`${BASE_URL}/${id}`);
  return response.data;
};

// ============================================================================
// GROUP MEMBERS OPERATIONS (API only, no Redux state)
// ============================================================================

export const fetchGroupMembers = async (
  groupId: number,
  params: { page: number; size: number; keyword?: string }
) => {
  const response = await restApi.get(`${BASE_URL}/${groupId}/users`, {
    params,
  });
  return response.data;
};

// Batch add users API
export const addUsersToGroup = async (
  groupId: number,
  userIds: number[]
): Promise<BaseResponse> => {
  const response = await restApi.post(`${BASE_URL}/${groupId}/users`, userIds);
  return response.data;
};

export const removeMemberFromGroup = async (
  groupId: number,
  userId: number
): Promise<BaseResponse> => {
  const response = await restApi.delete(
    `${BASE_URL}/${groupId}/users/${userId}`
  );
  return response.data;
};

// ============================================================================
// GROUP ROLES OPERATIONS (Legacy - for future use)
// ============================================================================

export const fetchGroupRoles = async (
  groupId: number
): Promise<BaseResponse<GroupRole[]>> => {
  const response = await restApi.get(`${BASE_URL}/${groupId}/roles`);
  return response.data;
};

export const createGroupRole = async (
  groupId: number,
  data: Omit<GroupRole, "id">
): Promise<BaseResponse<GroupRole>> => {
  const response = await restApi.post(`${BASE_URL}/${groupId}/roles`, data);
  return response.data;
};

export const updateGroupRole = async (
  groupId: number,
  roleId: number,
  data: Partial<GroupRole>
): Promise<BaseResponse<GroupRole>> => {
  const response = await restApi.put(
    `${BASE_URL}/${groupId}/roles/${roleId}`,
    data
  );
  return response.data;
};

export const deleteGroupRole = async (
  groupId: number,
  roleId: number
): Promise<BaseResponse> => {
  const response = await restApi.delete(
    `${BASE_URL}/${groupId}/roles/${roleId}`
  );
  return response.data;
};

// ============================================================================
// SYSTEM ROLES ASSIGNMENT TO GROUPS (AdminGroupHasRoleController)
// ============================================================================

export const fetchGroupRoleIds = async (
  groupId: number
): Promise<BaseResponse<number[]>> => {
  const response = await restApi.get(
    `${GROUP_ROLE_BASE_URL}/groups/${groupId}`
  );
  return response.data;
};

// Batch assign roles API - matches POST /{groupId}/roles
export const assignRolesToGroup = async (
  groupId: number,
  roleIds: number[]
): Promise<BaseResponse> => {
  const response = await restApi.post(
    `${GROUP_ROLE_BASE_URL}/${groupId}/roles`,
    roleIds
  );
  return response.data;
};

export const removeRoleFromGroup = async (
  groupId: number,
  roleId: number
): Promise<BaseResponse> => {
  const response = await restApi.delete(
    `${GROUP_ROLE_BASE_URL}/${groupId}/${roleId}`
  );
  return response.data;
};
