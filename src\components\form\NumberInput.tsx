import React from "react";
import { BasicInputProps } from "./registry";
import { Input } from "../ui/input";

export const NumberInput = React.forwardRef<
  HTMLInputElement,
  BasicInputProps<"number">
>(({ value, onChange, isViewMode, disabled, ...rest }, ref) => {
  return (
    <Input
      ref={ref}
      type="number"
      value={value}
      onChange={(e) =>
        !isViewMode && !disabled && onChange(Number(e.target.value))
      }
      readOnly={isViewMode}
      {...rest}
    />
  );
});
