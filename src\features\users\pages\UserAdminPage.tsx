import { useAppSelector } from "@/store/rootReducer";
import UserDetail from "../components/UserDetail";
import { UserTable } from "../components/UserTable";
import { useLocation } from "react-router-dom";
import { setPathParams, UserPageMode } from "../states/slices";
import { useAppDispatch } from "@/store/rootReducer";
import { selectPageMode } from "../states/selectors";
import { useEffect } from "react";

export default function UserAdminPage() {
  const dispatch = useAppDispatch();
  const location = useLocation();
  const pageMode = useAppSelector(selectPageMode);

  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const mode = searchParams.get("mode");
    const user = searchParams.get("user");
    if (!mode) {
      dispatch(setPathParams({ mode: "list" }));
    } else {
      dispatch(setPathParams({ mode: mode as UserPageMode, user: user ?? "" }));
    }
  }, [location.search, dispatch]);

  return (
    <>
      {pageMode === "list" && <UserTable />}
      {pageMode === "detail" && <UserDetail />}
    </>
  );
}
