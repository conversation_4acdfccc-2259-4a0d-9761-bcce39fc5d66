import React from "react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { BasicInputProps } from "../registry";

type PickTextInputProps = BasicInputProps<"text">;

export const PickTextInput = React.forwardRef<
  HTMLDivElement,
  PickTextInputProps
>(
  (
    { isViewMode, value = "", onChange, disabled, options = [], ...rest },
    ref
  ) => {
    const [open, setOpen] = React.useState(false);

    const handleTriggerClick = () => {
      if (!disabled) setOpen(true);
    };

    return (
      <div ref={ref} className={cn("flex items-center gap-2")} {...rest}>
        <div className="min-w-[80px] text-sm border rounded px-2 py-1 bg-muted">
          {value || "Chưa chọn"}
        </div>

        <Popover
          open={open}
          onOpenChange={(v) => !disabled && setOpen(v as boolean)}
        >
          <PopoverTrigger asChild>
            {!isViewMode && (
              <span
                role="button"
                tabIndex={disabled ? -1 : 0}
                onClick={handleTriggerClick}
                className={cn(
                  "text-xs underline",
                  disabled
                    ? "cursor-not-allowed text-muted-foreground opacity-50"
                    : "cursor-pointer text-blue-500"
                )}
              >
                Sửa
              </span>
            )}
          </PopoverTrigger>

          <PopoverContent className="grid grid-cols-2 gap-2 max-w-xs">
            {options.map((opt) => (
              <Button
                key={opt}
                variant={opt === value ? "default" : "ghost"}
                className="px-3 py-2 text-sm"
                onClick={() => {
                  onChange(opt);
                  setOpen(false);
                }}
              >
                {opt}
              </Button>
            ))}
          </PopoverContent>
        </Popover>
      </div>
    );
  }
);

PickTextInput.displayName = "PickTextInput";
