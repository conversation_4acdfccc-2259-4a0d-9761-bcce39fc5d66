# 🤖 AI Assistant Guidelines

## 🎯 Mission

Giúp phát triển KHA Portal frontend một cách hiệu quả, nhất quán và chất lượng cao.

## 🚨 Critical Rules (NEVER BREAK THESE)

```bash
# ✅ ONLY use these commands:
yarn install, yarn dev, yarn build, yarn lint

# ❌ NEVER use (will be BLOCKED):
npm install, npm run dev, pnpm install
```

## 📏 Code Quality Rules

1. **File Size Limits**: Components ≤150 lines, Hooks ≤100 lines, API files ≤200 lines
2. **Separation of Concerns**: Extract logic to custom hooks, keep components focused on UI
3. **Component Composition**: Break large components into smaller, reusable pieces
4. **No Mixed Responsibilities**: One component = one responsibility

### 🚀 Performance Guidelines for Custom Hooks

```typescript
// ❌ Avoid: Multiple Redux selectors
const albumMedia = useAppSelector(selectCurrentAlbumMedia);
const mediaLoading = useAppSelector(selectMediaLoading);

// ✅ Better: Batch selectors
const mediaState = useAppSelector((state) => ({
  albumMedia: selectCurrentAlbumMedia(state),
  mediaLoading: selectMediaLoading(state),
}));

// ❌ Avoid: Unstable dependencies
const handleAction = useCallback(() => {}, [obj.prop, loading]);

// ✅ Better: Stable references
const stableId = useMemo(() => obj?.id, [obj?.id]);
const handleAction = useCallback(() => {}, [stableId]);

// ✅ Memoize return objects in complex hooks
return useMemo(() => ({ data, handlers }), [data, handlers]);
```

## 🏗️ Project Architecture Overview

### File Organization Pattern

```
src/features/[feature-name]/
├── components/          # React components
├── pages/              # Route pages
├── states/
│   ├── api.ts          # API calls
│   ├── hooks.ts        # React Query hooks
│   └── types.ts        # TypeScript interfaces
└── utils/              # Helper functions
```

### Common Features in Project

- `album` - Photo album management
- `auth` - Authentication
- `media/images` - Image management
- `users` - User management
- `builder` - Page builder (see NODE_DEVELOPMENT_GUIDE.md)

### Key Technologies

- **React 19** + **TypeScript** + **Vite**
- **TanStack Query** (API state) + **Redux Toolkit** (app state)
- **Radix UI** components with **Tailwind CSS**
- **React Router** for navigation

## 🔄 Standard Patterns to Follow

### 1. API Layer (`src/features/[feature]/states/api.ts`)

```typescript
import { restApi } from "@/api/restApi";

// ✅ Standard pattern:
export const getAlbums = () => restApi.get<Album[]>("/admin/albums");
export const updateAlbum = (id: string, data: UpdateAlbumRequest) =>
  restApi.put<Album>(`/admin/albums/${id}`, data);
```

### 2. React Query Hooks (`src/features/[feature]/states/hooks.ts`)

```typescript
// ✅ Query pattern:
export const useAlbums = () => {
  return useQuery({
    queryKey: ["albums"],
    queryFn: getAlbums,
  });
};

// ✅ Mutation pattern:
export const useUpdateAlbum = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: ({ id, ...data }: UpdateAlbumParams) => updateAlbum(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["albums"] });
      toast.success("Updated successfully");
    },
  });
};
```

### 3. Component Structure

```typescript
// ✅ Standard component pattern:
interface ComponentProps {
  // Clear interface
}

export const ComponentName: React.FC<ComponentProps> = ({ ...props }) => {
  // 1. Hooks first
  const { data, isLoading } = useQuery();
  const mutation = useMutation();

  // 2. Event handlers
  const handleAction = useCallback(() => {}, []);

  // 3. Early returns for loading/error
  if (isLoading) return <LoadingSpinner />;
  if (error) return <ErrorMessage />;

  // 4. Main render
  return <div>{/* JSX */}</div>;
};
```

### 4. Common UI Components (Import These)

```typescript
import { Button } from "@/components/ui/button";
import { Dialog } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Card } from "@/components/ui/card";
```

## 🎯 Adding New Features - Quick Checklist

### For New Components:

1. Create in `src/features/[feature]/components/`
2. Follow existing naming: `AlbumCard.tsx`, `UserList.tsx`
3. Import UI components from `@/components/ui/`
4. Use TypeScript interfaces
5. Handle loading/error states

### For New API Endpoints:

1. Add function in `src/features/[feature]/states/api.ts`
2. Create React Query hook in `states/hooks.ts`
3. Add TypeScript types in `states/types.ts`
4. Use toast notifications for feedback

### For New Pages:

1. Create in `src/features/[feature]/pages/`
2. Export in `pages/index.ts`
3. Add route in appropriate router file

## 🔍 Before Making Changes

### Quick Investigation Steps:

1. **Check existing patterns**: Look at similar features first
2. **Find related files**: Search for similar component/API names
3. **Understand data flow**: API → Hook → Component
4. **Check types**: Look at `states/types.ts` for data structures

### Common Locations:

```bash
# Find similar components:
src/features/*/components/*[SimilarName]*

# Find API patterns:
src/features/*/states/api.ts

# Find data types:
src/features/*/states/types.ts
```

## 🚨 Common Mistakes to Avoid

1. **Wrong imports**: Use `@/` prefix, not `../../../`
2. **Missing types**: Always define TypeScript interfaces
3. **No error handling**: Handle loading/error states
4. **Wrong file location**: Follow feature-based structure
5. **Package manager**: Only use `yarn` commands

## 🛠️ Quick Commands Reference

```bash
yarn dev          # Start development
yarn build        # Check TypeScript errors
yarn lint         # Check code quality
yarn check-pm     # Verify setup
```

---

**Key Point**: Follow existing patterns, don't reinvent. When in doubt, find a similar feature and copy its structure! 🎯
Lesson learned: Khi refactor, cần update import paths và dọn dẹp code cũ để tránh chạy song song 2 hệ thống! 💡
