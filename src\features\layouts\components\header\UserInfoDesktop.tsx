import React from "react";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { useNavigate } from "react-router-dom";
import { useAppSelector } from "@/store/rootReducer";
import { selectAuthState } from "@/features/auth/states/selector";

export const UserInfoDesktop: React.FC = () => {
  const navigate = useNavigate();
  const { is_authenticated, account } = useAppSelector(selectAuthState);

  const handleLogin = () => {
    navigate("/auth/login");
  };

  const handleRegister = () => {
    navigate("/auth/register");
  };

  const handleProfile = () => {
    navigate("/me");
  };

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((word) => word[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  if (!is_authenticated) {
    return (
      <div className="flex items-center gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={handleRegister}
          className="hidden sm:inline-flex"
        >
          <PERSON><PERSON><PERSON> ký
        </Button>
        <Button size="sm" onClick={handleLogin}>
          Đăng nhập
        </Button>
      </div>
    );
  }

  return (
    <div
      onClick={handleProfile}
      className="flex items-center gap-3 cursor-pointer hover:bg-gray-50 rounded-lg p-2 transition-colors"
    >
      <Avatar className="h-8 w-8">
        <AvatarFallback className="bg-primary text-primary-foreground text-sm">
          {account?.fullName ? getInitials(account.fullName) : "U"}
        </AvatarFallback>
      </Avatar>
      <span className="font-medium text-sm hidden sm:inline">
        {account?.fullName || "Người dùng"}
      </span>
    </div>
  );
};
