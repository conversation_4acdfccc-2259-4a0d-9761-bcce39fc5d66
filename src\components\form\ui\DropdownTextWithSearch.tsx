import React from "react";
import { BasicInputProps } from "../registry";
import { DropDownTextWithSearch as BasicDropDownTextWithSearch } from "@/form/field/components/basic/DropDownTextWithSearch";

type DropdownTextWithSearchProps = BasicInputProps<"text">;

export const DropDownTextWithSearch = React.forwardRef<
  HTMLDivElement,
  DropdownTextWithSearchProps
>(
  (
    {
      isViewMode,
      value,
      onChange,
      options = [],
      labels,
      placeholder = "Chọn giá trị",
      disabled,
      id,
      ...rest
    },
    ref
  ) => {
    return (
      <div ref={ref} {...rest}>
        <BasicDropDownTextWithSearch
          value={value}
          onChange={(newValue) => onChange(newValue as string)}
          options={options}
          labels={labels || []}
          placeholder={placeholder}
          disabled={disabled || (isViewMode && !disabled)}
          id={id}
        />
      </div>
    );
  }
);

DropDownTextWithSearch.displayName = "DropDownTextWithSearch";
