/**
 * DropDownTextWithSearch - Dropdown with search functionality for AutoForm
 */

import React from "react";
import { FieldComponentProps } from "../../FieldFactory";
import { cn } from "@/lib/utils";

export const DropDownTextWithSearch: React.FC<FieldComponentProps> = ({
  value,
  onChange,
  onBlur,
  disabled = false,
  placeholder = "Chọn giá trị",
  options = [],
  labels = [],
  id,
  className = "",
}) => {
  const [open, setOpen] = React.useState(false);
  const [search, setSearch] = React.useState("");
  const containerRef = React.useRef<HTMLDivElement>(null);

  // Đóng dropdown khi click ngoài
  React.useEffect(() => {
    if (!open) return;
    const onClick = (e: MouseEvent | TouchEvent) => {
      if (
        containerRef.current &&
        !containerRef.current.contains(e.target as Node)
      ) {
        setOpen(false);
      }
    };
    document.addEventListener("mousedown", onClick);
    document.addEventListener("touchstart", onClick);
    return () => {
      document.removeEventListener("mousedown", onClick);
      document.removeEventListener("touchstart", onClick);
    };
  }, [open]);

  // Lọc options theo search
  const filteredOptions = options.filter((opt, idx) =>
    String(labels[idx] || opt)
      .toLowerCase()
      .includes(search.toLowerCase())
  );

  // Lấy nhãn hiện tại
  const currentLabel =
    labels.length === options.length && options.includes(value as string)
      ? labels[options.indexOf(value as string)]
      : value;

  const handleOptionClick = disabled
    ? undefined
    : (opt: string) => {
        setOpen(false);
        setSearch("");
        if (opt !== value) onChange(opt);
      };

  return (
    <div ref={containerRef} className={cn("relative w-full", className)}>
      <button
        type="button"
        className={cn(
          "w-full text-left px-3 py-1 border rounded-md transition focus-visible:border-ring focus-visible:ring-ring/40 focus-visible:ring-2 outline-none",
          "text-base shadow-xs min-h-9",
          disabled ? "cursor-not-allowed" : "hover:border-primary"
        )}
        onClick={() => !disabled && setOpen((v) => !v)}
        onBlur={onBlur}
        disabled={disabled}
        id={id}
      >
        {currentLabel || (
          <span className="text-muted-foreground">{placeholder}</span>
        )}
      </button>
      {open && (
        <div
          className={cn(
            "absolute z-20 left-0 right-0 mt-1 border rounded-md bg-popover shadow-lg overflow-auto",
            "max-h-60 animate-in fade-in-80"
          )}
        >
          <input
            className={cn(
              "w-full px-3 py-2 border-b text-base outline-none",
              "focus-visible:border-ring"
            )}
            placeholder="Tìm kiếm..."
            value={search}
            autoFocus
            onChange={(e) => setSearch(e.target.value)}
          />
          {filteredOptions.length === 0 && (
            <div className="px-3 py-1 text-muted-foreground text-sm">
              Không có kết quả
            </div>
          )}
          {filteredOptions.map((opt, idx) => {
            const realIndex = options.indexOf(opt);
            return (
              <div
                key={String(opt) + idx}
                className={cn(
                  "px-3 py-2 cursor-pointer transition select-none",
                  "hover:bg-accent hover:text-accent-foreground",
                  opt === value ? "bg-primary/10 font-semibold" : ""
                )}
                onClick={() => handleOptionClick?.(String(opt))}
              >
                {labels[realIndex] || opt}
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};
