import { createSelector } from "@reduxjs/toolkit";
import type { RootState } from "@/store/rootReducer";
import type { CopyrightState } from "./type";

// ============================================================================
// Base Selectors
// ============================================================================

/**
 * Selects the copyright state from the root state
 */
export const selectCopyrightState = (state: RootState): CopyrightState =>
  state.copyrightState;

// ============================================================================
// Data Selectors
// ============================================================================

/**
 * Selects copyright configuration data
 */
export const selectCopyrightData = createSelector(
  [selectCopyrightState],
  (copyrightState) => copyrightState.data || null
);

/**
 * Selects copyright saved data (for preview)
 */
export const selectCopyrightSavedData = createSelector(
  [selectCopyrightState],
  (copyrightState) => copyrightState.savedData || null
);

/**
 * Selects copyright loading state
 */
export const selectCopyrightLoading = createSelector(
  [selectCopyrightState],
  (copyrightState) => copyrightState.loading
);

/**
 * Selects copyright saving state
 */
export const selectCopyrightSaving = createSelector(
  [selectCopyrightState],
  (copyrightState) => copyrightState.saving
);

/**
 * Selects copyright error state
 */
export const selectCopyrightError = createSelector(
  [selectCopyrightState],
  (copyrightState) => copyrightState.error
);

/**
 * Selects whether copyright data has unsaved changes
 */
export const selectCopyrightIsDirty = createSelector(
  [selectCopyrightState],
  (copyrightState) => copyrightState.isDirty
);

// ============================================================================
// Utility Selectors
// ============================================================================

/**
 * Selects whether any operation is in progress
 */
export const selectCopyrightIsOperating = createSelector(
  [selectCopyrightLoading, selectCopyrightSaving],
  (loading, saving) => loading || saving
);
