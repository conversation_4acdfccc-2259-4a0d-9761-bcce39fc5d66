import React, { useState, useEffect, useCallback } from "react";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  type CarouselApi,
} from "@/components/ui/carousel";
import { Card, CardContent } from "@/components/ui/card";
import { Image } from "lucide-react";

/**
 * Props for the BannerCarousel component
 */
interface BannerCarouselProps {
  /** Array of banner items to display */
  banners: Array<{ src: string; alt: string }>;
  /** Auto-cycle interval in milliseconds (default: 3500ms) */
  autoPlayInterval?: number;
  /** Pause duration when user clicks a banner (default: 5000ms) */
  pauseDuration?: number;
  /** Callback when a banner is clicked */
  onBannerClick?: (index: number) => void;
  /** External control to jump to specific banner */
  jumpToIndex?: number;
  /** Additional CSS classes */
  className?: string;
}

/**
 * Interactive banner carousel component with auto-cycling and click-to-preview functionality
 * 
 * Features:
 * - Auto-cycles through banners every 3-4 seconds
 * - Click any banner to immediately preview it
 * - Pauses auto-cycling for 5 seconds after user interaction
 * - Smooth transitions with fade effect
 * - Handles edge cases (empty list, single banner)
 */
export const BannerCarousel: React.FC<BannerCarouselProps> = ({
  banners,
  autoPlayInterval = 3500,
  pauseDuration = 5000,
  onBannerClick,
  jumpToIndex,
  className = "",
}) => {
  const [api, setApi] = useState<CarouselApi>();
  const [current, setCurrent] = useState(0);
  const [isUserInteracting, setIsUserInteracting] = useState(false);

  // Handle carousel API changes
  useEffect(() => {
    if (!api) return;

    const onSelect = () => {
      setCurrent(api.selectedScrollSnap());
    };

    api.on("select", onSelect);
    onSelect(); // Set initial state

    return () => {
      api.off("select", onSelect);
    };
  }, [api]);

  // Auto-play functionality
  useEffect(() => {
    if (!api || banners.length <= 1 || isUserInteracting) return;

    const interval = setInterval(() => {
      // Check if we're at the last slide
      if (current === banners.length - 1) {
        // Go to first slide (restart loop)
        api.scrollTo(0);
      } else {
        // Go to next slide
        api.scrollNext();
      }
    }, autoPlayInterval);

    return () => clearInterval(interval);
  }, [api, current, banners.length, autoPlayInterval, isUserInteracting]);

  // Handle external jump to specific banner
  useEffect(() => {
    if (!api || jumpToIndex === undefined || jumpToIndex < 0 || jumpToIndex >= banners.length) return;

    // Jump to specified banner
    api.scrollTo(jumpToIndex);

    // Pause auto-cycling
    setIsUserInteracting(true);
  }, [api, jumpToIndex, banners.length]);

  // Handle user interaction pause
  useEffect(() => {
    if (!isUserInteracting) return;

    const timeout = setTimeout(() => {
      setIsUserInteracting(false);
    }, pauseDuration);

    return () => clearTimeout(timeout);
  }, [isUserInteracting, pauseDuration]);

  /**
   * Handle banner click - jump to specific banner and pause auto-cycling
   */
  const handleBannerClick = useCallback((index: number) => {
    if (!api) return;

    // Jump to clicked banner
    api.scrollTo(index);

    // Pause auto-cycling
    setIsUserInteracting(true);

    // Notify parent component
    onBannerClick?.(index);
  }, [api, onBannerClick]);

  // Handle empty banner list
  if (banners.length === 0) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="aspect-video flex items-center justify-center bg-muted rounded-lg">
            <div className="text-center text-muted-foreground">
              <Image className="w-12 h-12 mx-auto mb-2" />
              <p className="text-sm">Chưa có banner nào</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Handle single banner
  if (banners.length === 1) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="aspect-video rounded-lg overflow-hidden bg-muted">
            <img
              src={banners[0].src}
              alt={banners[0].alt}
              className="w-full h-full object-contain cursor-pointer"
              onClick={() => handleBannerClick(0)}
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.src = "/placeholder-banner.jpg";
              }}
            />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardContent className="p-6">
        <Carousel
          setApi={setApi}
          opts={{
            align: "start",
            loop: true,
          }}
          className="w-full"
        >
          <CarouselContent>
            {banners.map((banner, index) => (
              <CarouselItem key={index}>
                <div className="aspect-video rounded-lg overflow-hidden bg-muted">
                  <img
                    src={banner.src}
                    alt={banner.alt}
                    className="w-full h-full object-contain cursor-pointer transition-transform duration-300 hover:scale-105"
                    onClick={() => handleBannerClick(index)}
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = "/placeholder-banner.jpg";
                    }}
                  />
                </div>
              </CarouselItem>
            ))}
          </CarouselContent>
        </Carousel>

        {/* Current banner indicator */}
        <div className="mt-4 text-center">
          <p className="text-sm text-muted-foreground">
            Banner {current + 1} / {banners.length}
            {isUserInteracting && (
              <span className="ml-2 text-primary">• Tạm dừng</span>
            )}
          </p>
        </div>
      </CardContent>
    </Card>
  );
};
