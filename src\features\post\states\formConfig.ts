import { FormConfig } from "@/form/types";

export const CreatePostFormConfig: FormConfig = {
  code: "create-post-form",
  name: "Tạo bài viết mới",
  note: "Biểu mẫu để tạo bài viết mới",
  config: {
    id: "form-root",
    type: "frame",
    styles: {
      container: "space-y-4",
    },
    children: [
      {
        id: "title",
        type: "field",
        styles: {
          container: "space-y-2",
          label: "text-sm font-medium",
          field: "w-full",
        },
        field: {
          objectKey: "title",
          defaultValue: "",
          dataType: "string",
          component: "TextInput",
        },
        properties: {
          label: "Tiêu đề",
          placeholder: "Nhập tiêu đề bài viết",
        },
        validation: {
          required: { value: true, error: "Tiêu đề là bắt buộc" },
          maxLength: {
            value: 200,
            error: "Tiêu đề không được vượt quá 200 ký tự",
          },
        },
      },
      {
        id: "slug",
        type: "field",
        styles: {
          container: "space-y-2",
          label: "text-sm font-medium",
          field: "w-full",
        },
        field: {
          objectKey: "slug",
          defaultValue: "",
          dataType: "string",
          component: "TextInput",
        },
        properties: {
          label: "Đường dẫn",
          placeholder: "duong-dan-url-bai-viet",
        },
        validation: {
          required: { value: true, error: "Đường dẫn là bắt buộc" },
          maxLength: {
            value: 200,
            error: "Đường dẫn không được vượt quá 200 ký tự",
          },
        },
      },
      {
        id: "postType",
        type: "field",
        styles: {
          container: "space-y-2",
          label: "text-sm font-medium",
          field: "w-full",
        },
        field: {
          objectKey: "postType",
          defaultValue: "ARTICLE",
          dataType: "string",
          component: "DropdownInput",
        },
        properties: {
          label: "Loại bài viết",
          placeholder: "Chọn loại bài viết",
          options: ["ARTICLE", "PAGE"],
          labels: ["Bài viết", "Trang"],
        },
        validation: {
          required: { value: true, error: "Loại bài viết là bắt buộc" },
        },
      },
      {
        id: "authorId",
        type: "field",
        styles: {
          container: "space-y-2",
          label: "text-sm font-medium",
          field: "w-full",
        },
        field: {
          objectKey: "authorId",
          defaultValue: "admin",
          dataType: "string",
          component: "TextInput",
        },
        properties: {
          label: "ID tác giả",
          placeholder: "Nhập ID tác giả",
        },
        validation: {
          required: { value: true, error: "ID tác giả là bắt buộc" },
          maxLength: {
            value: 50,
            error: "ID tác giả không được vượt quá 50 ký tự",
          },
        },
      },
      {
        id: "excerpt-image",
        type: "field",
        styles: {
          container: "space-y-2",
          label: "text-sm font-medium",
          field: "w-full",
        },
        field: {
          objectKey: "excerpt.image",
          defaultValue: "",
          dataType: "string",
          component: "ImagePickerInput",
        },
        properties: {
          label: "Ảnh bìa",
          placeholder: "Chọn hình ảnh",
        },
        validation: {
          required: { value: true, error: "Ảnh bìa là bắt buộc" },
          maxLength: {
            value: 200,
            error: "URL ảnh không được vượt quá 200 ký tự",
          },
        },
      },
      {
        id: "excerpt-description",
        type: "field",
        styles: {
          container: "space-y-2",
          label: "text-sm font-medium",
          field: "w-full",
        },
        field: {
          objectKey: "excerpt.description",
          defaultValue: "",
          dataType: "string",
          component: "TextAreaInput",
        },
        properties: {
          label: "Tóm tắt",
          placeholder: "Nhập tóm tắt bài viết",
        },
        validation: {
          required: { value: true, error: "Tóm tắt là bắt buộc" },
          maxLength: {
            value: 500,
            error: "Tóm tắt không được vượt quá 500 ký tự",
          },
        },
      },
    ],
  },
};
