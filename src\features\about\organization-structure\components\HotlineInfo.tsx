import React from "react";
import { Phone, Mail } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Skeleton } from "@/components/ui/skeleton";
import type { HotlineInfoProps, HotlineItem } from "../states/type";

// ============================================================================
// Hotline Item Component
// ============================================================================

/**
 * Individual hotline item display component
 */
interface HotlineItemComponentProps {
  /** Hotline item data */
  hotline: HotlineItem;
  /** Optional custom className */
  className?: string;
}

const HotlineItemComponent: React.FC<HotlineItemComponentProps> = ({
  hotline,
  className = ""
}) => {
  return (
    <div className={`space-y-2 ${className}`}>
      {/* Hotline Name */}
      <h4 className="font-semibold text-base text-gray-900">
        {hotline.name}
      </h4>

      {/* Hotline Details */}
      <div className="space-y-1">
        {/* Phone - Prominent display for hotlines */}
        {hotline.phone && (
          <div className="flex items-center gap-2">
            <Phone className="h-4 w-4 flex-shrink-0 text-gray-500" />
            <a
              href={`tel:${hotline.phone}`}
              className="text-sm text-gray-600 hover:text-blue-600 transition-colors"
            >
              {hotline.phone}
            </a>
          </div>
        )}

        {/* Email */}
        {hotline.email && (
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <Mail className="h-4 w-4 flex-shrink-0 text-gray-500" />
            <a
              href={`mailto:${hotline.email}`}
              className="hover:text-blue-600 transition-colors break-all"
            >
              {hotline.email}
            </a>
          </div>
        )}
      </div>
    </div>
  );
};

// ============================================================================
// Loading Skeleton Component
// ============================================================================

/**
 * Loading skeleton for hotline information
 */
const HotlineInfoSkeleton: React.FC = () => {
  return (
    <div className="space-y-6">
      {[1, 2].map((index) => (
        <div key={index} className="space-y-2">
          <Skeleton className="h-5 w-40" />
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              <Skeleton className="h-4 w-4" />
              <Skeleton className="h-4 w-32" />
            </div>
            <div className="flex items-center gap-2">
              <Skeleton className="h-4 w-4" />
              <Skeleton className="h-4 w-40" />
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

// ============================================================================
// Main Hotline Info Component
// ============================================================================

/**
 * Hotline information display component
 * 
 * Displays emergency/support hotline information with prominent phone numbers
 * Uses red color scheme to indicate urgency and importance
 * Handles loading states and error conditions gracefully
 * 
 * @param props - Component props
 * @returns JSX element for hotline information display
 */
export const HotlineInfo: React.FC<HotlineInfoProps> = ({
  data,
  loading,
  error,
}) => {

  let contentRender = (<></>);


  if (error) {
    // ========================================================================
    // Error State
    // ========================================================================
    contentRender = (
      <Alert variant="destructive">
        <AlertDescription>
          Không thể tải thông tin đường dây nóng: {error}
        </AlertDescription>
      </Alert>
    );
  } else if (loading) {
    // ========================================================================
    // Loading State
    // ========================================================================
    contentRender = (
      <HotlineInfoSkeleton />
    );
  } else if (!data || !data.hotlines || data.hotlines.length === 0) {
    // ========================================================================
    // No Data State
    // ========================================================================
    contentRender = (
      <div className="text-center py-8 text-gray-500">
        <Phone className="h-12 w-12 mx-auto mb-4 text-gray-300" />
        <p>Chưa có thông tin đường dây nóng</p>
      </div>
    );
  } else {
    contentRender = (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {data?.hotlines.map((hotline, index) => (
            <HotlineItemComponent
              key={`hotline-${index}-${hotline.name}`}
              hotline={hotline}
            />
          ))}
        </div>
      </div>
    );
  }

  // ========================================================================
  // Data Display State
  // ========================================================================

  return (
    <div className="w-full">
      <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
        THÔNG TIN ĐƯỜNG DÂY NÓNG
      </h3>
      {contentRender}
    </div>
  );
};

export default HotlineInfo;
