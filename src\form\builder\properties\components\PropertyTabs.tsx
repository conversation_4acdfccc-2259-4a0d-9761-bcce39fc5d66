/**
 * Property Tabs Component
 * Main tabs for Attributes and Display
 */

import React from 'react';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { PropertyTab } from '../types';
import { AttributesTab } from './AttributesTab';
import { DisplayTab } from './DisplayTab';
import { FormNode } from '@/form/types';

interface PropertyTabsProps {
  node: FormNode;
  onChange: (updates: Partial<FormNode>) => void;
  disabled?: boolean;
  activeTab: PropertyTab;
  onTabChange: (tab: PropertyTab) => void;
}

export const PropertyTabs: React.FC<PropertyTabsProps> = ({
  node,
  onChange,
  disabled = false,
  activeTab,
  onTabChange
}) => {
  // Check if node has attributes
  const hasAttributes = node.type === 'field' || node.type === 'control' || node.type === 'title';
  return (
    <Tabs value={activeTab} onValueChange={(value) => onTabChange(value as PropertyTab)} className="w-full">
      <TabsList className="grid w-full grid-cols-2 mb-4 h-8">
        <TabsTrigger 
          value="attributes" 
          className="text-xs"
          disabled={!hasAttributes}
        >
          Thuộc tính
        </TabsTrigger>
        <TabsTrigger value="display" className="text-xs">
          Hiển thị
        </TabsTrigger>
      </TabsList>
      
      <TabsContent value="attributes" className="mt-0">
        <AttributesTab
          node={node}
          onChange={onChange}
          disabled={disabled}
        />
      </TabsContent>
      
      <TabsContent value="display" className="mt-0">
        <DisplayTab
          node={node}
          onChange={onChange}
          disabled={disabled}
        />
      </TabsContent>
    </Tabs>
  );
};