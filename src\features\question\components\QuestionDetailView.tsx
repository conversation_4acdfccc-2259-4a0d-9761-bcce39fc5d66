import React, { useState, useEffect } from "react";
import { Question, Answer } from "../states/types";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
  ArrowLeft,
  CheckCircle,
  XCircle,
  Clock,
  Trash2,
  FileText,
  Download,
} from "lucide-react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { useQuestion } from "../hooks/useQuestion";
import { CreateAnswerDialog } from "./CreateAnswerDialog";

interface QuestionDetailViewProps {
  question: Question | null;
  onBackToList: () => void;
}

export const QuestionDetailView: React.FC<QuestionDetailViewProps> = ({
  question,
  onBackToList,
}) => {
  const {
    updateQuestionStatus,
    questionsLoading,
    fetchQuestions,
    fetchAnswersByQuestionId,
    answers,
  } = useQuestion();
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [currentQuestion, setCurrentQuestion] = useState<Question | null>(
    question
  );
  const [questionAnswers, setQuestionAnswers] = useState<Answer[]>([]);

  // Update local state when prop changes
  useEffect(() => {
    setCurrentQuestion(question);
  }, [question]);

  // Fetch answers for the question when question changes
  useEffect(() => {
    if (currentQuestion && currentQuestion.status !== "NEW") {
      fetchAnswersByQuestionId(currentQuestion.id);
    }
  }, [currentQuestion, fetchAnswersByQuestionId]);

  // Update local answers when answers from store change
  useEffect(() => {
    if (answers && currentQuestion) {
      const filteredAnswers = answers.filter(
        (answer) => answer.questionId === currentQuestion.id
      );
      setQuestionAnswers(filteredAnswers);
    }
  }, [answers, currentQuestion]);

  const handleBackToList = () => {
    // Refresh questions data khi back về list
    fetchQuestions();
    onBackToList();
  };

  const handleCreateAnswerSuccess = () => {
    // Refresh question data and update status to DRAFT
    setCurrentQuestion({ ...currentQuestion!, status: "DRAFT" });
    fetchQuestions();
    // Fetch new answers
    if (currentQuestion) {
      fetchAnswersByQuestionId(currentQuestion.id);
    }
  };

  const handlePublish = async () => {
    if (currentQuestion && currentQuestion.status === "DRAFT") {
      console.log("Xuất bản câu hỏi:", currentQuestion.id);

      try {
        // DRAFT -> PUBLISHED
        await updateQuestionStatus(currentQuestion.id, "PUBLISHED");
        setCurrentQuestion({ ...currentQuestion, status: "PUBLISHED" });
        console.log("Chuyển trạng thái từ DRAFT -> PUBLISHED");
      } catch (error) {
        console.error("Lỗi khi xuất bản câu hỏi:", error);
      }
    }
  };

  const handleUnpublish = async () => {
    if (currentQuestion) {
      console.log("Hủy xuất bản câu hỏi:", currentQuestion.id);

      try {
        // PUBLISHED -> DRAFT
        await updateQuestionStatus(currentQuestion.id, "DRAFT");
        setCurrentQuestion({ ...currentQuestion, status: "DRAFT" });
        console.log("Chuyển trạng thái từ PUBLISHED -> DRAFT");
      } catch (error) {
        console.error("Lỗi khi hủy xuất bản câu hỏi:", error);
      }
    }
  };

  const handleDeleteConfirmed = async () => {
    if (currentQuestion) {
      console.log("Xóa câu hỏi:", currentQuestion.id);

      try {
        // Any status except TRASH -> TRASH
        await updateQuestionStatus(currentQuestion.id, "TRASH");
        setCurrentQuestion({ ...currentQuestion, status: "TRASH" });
        console.log(`Chuyển trạng thái từ ${currentQuestion.status} -> TRASH`);
        setIsDeleteDialogOpen(false);
      } catch (error) {
        console.error("Lỗi khi xóa câu hỏi:", error);
        setIsDeleteDialogOpen(false);
      }
    }
  };

  const handleRestore = async () => {
    if (currentQuestion && currentQuestion.status === "TRASH") {
      console.log("Khôi phục câu hỏi từ thùng rác:", currentQuestion.id);

      try {
        // TRASH -> DRAFT
        await updateQuestionStatus(currentQuestion.id, "DRAFT");
        setCurrentQuestion({ ...currentQuestion, status: "DRAFT" });
        console.log("Chuyển trạng thái từ TRASH -> DRAFT");
      } catch (error) {
        console.error("Lỗi khi khôi phục câu hỏi:", error);
      }
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "PUBLISHED":
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case "TRASH":
        return <XCircle className="h-5 w-5 text-red-500" />;
      case "NEW":
        return <Clock className="h-5 w-5 text-blue-500" />;
      case "DRAFT":
        return <Clock className="h-5 w-5 text-yellow-500" />;
      default:
        return null;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "PUBLISHED":
        return "Đã xuất bản";
      case "TRASH":
        return "Đã xóa";
      case "NEW":
        return "Chưa trả lời";
      case "DRAFT":
        return "Đã trả lời";
      default:
        return status;
    }
  };

  const getStatusVariant = (
    status: string
  ): "default" | "secondary" | "destructive" | "outline" => {
    switch (status) {
      case "PUBLISHED":
        return "default";
      case "TRASH":
        return "destructive";
      case "NEW":
        return "secondary";
      case "DRAFT":
        return "outline";
      default:
        return "outline";
    }
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString("vi-VN", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  if (!currentQuestion) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-4">
            <Button onClick={handleBackToList} variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Quay lại
            </Button>
            <h1 className="text-2xl font-bold">Chi tiết câu hỏi</h1>
          </div>
        </div>
        <div className="flex flex-col items-center justify-center h-64 space-y-4">
          <p className="text-gray-500">Không tìm thấy câu hỏi</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      {/* Header với Back button theo format chuẩn */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <Button onClick={handleBackToList} variant="outline" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Quay lại
          </Button>
          <h1 className="text-2xl font-bold">Chi tiết câu hỏi</h1>
        </div>

        {/* Action buttons */}
        <div className="flex gap-2">
          {/* Create Answer button - for NEW status */}
          {currentQuestion.status === "NEW" && (
            <CreateAnswerDialog
              questionId={currentQuestion.id}
              onSuccess={handleCreateAnswerSuccess}
            />
          )}

          {/* Publish button - chỉ cho DRAFT status */}
          {currentQuestion.status === "DRAFT" && (
            <Button
              onClick={handlePublish}
              variant="default"
              disabled={questionsLoading}
            >
              <CheckCircle className="h-4 w-4 mr-2" />
              Xuất bản
            </Button>
          )}

          {/* Unpublish button for PUBLISHED */}
          {currentQuestion.status === "PUBLISHED" && (
            <Button
              onClick={handleUnpublish}
              variant="secondary"
              disabled={questionsLoading}
            >
              <Clock className="h-4 w-4 mr-2" />
              Hủy xuất bản
            </Button>
          )}

          {/* Restore button for TRASH */}
          {currentQuestion.status === "TRASH" && (
            <Button
              onClick={handleRestore}
              variant="outline"
              disabled={questionsLoading}
            >
              <CheckCircle className="h-4 w-4 mr-2" />
              Khôi phục
            </Button>
          )}

          {/* Delete button với AlertDialog - available for all status except TRASH */}
          {currentQuestion.status !== "TRASH" && (
            <AlertDialog
              open={isDeleteDialogOpen}
              onOpenChange={setIsDeleteDialogOpen}
            >
              <AlertDialogTrigger asChild>
                <Button variant="destructive" disabled={questionsLoading}>
                  <Trash2 className="h-4 w-4 mr-2" />
                  Xóa
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Xác nhận xóa câu hỏi</AlertDialogTitle>
                  <AlertDialogDescription>
                    Bạn có chắc chắn muốn xóa câu hỏi "
                    {currentQuestion.content.title}" không? Câu hỏi sẽ được
                    chuyển vào thùng rác và có thể khôi phục sau này.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Hủy</AlertDialogCancel>
                  <AlertDialogAction
                    className="bg-red-600 hover:bg-red-700"
                    onClick={handleDeleteConfirmed}
                    disabled={questionsLoading}
                  >
                    Xóa
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          )}
        </div>
      </div>

      {/* Question Details */}
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <div className="flex justify-between items-start">
              <div className="flex-1">
                <CardTitle className="text-xl mb-4">
                  {currentQuestion.content.title}
                </CardTitle>
                <div className="flex gap-2 mb-4">
                  <Badge
                    variant={getStatusVariant(currentQuestion.status)}
                    className="flex items-center gap-1"
                  >
                    {getStatusIcon(currentQuestion.status)}
                    {getStatusText(currentQuestion.status)}
                  </Badge>
                  <Badge variant="outline">{currentQuestion.topic}</Badge>
                </div>
              </div>
              <div className="text-sm text-gray-500">
                ID: {currentQuestion.id}
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Question Content */}
            <div>
              <h3 className="font-semibold mb-2">Nội dung câu hỏi:</h3>
              <div className="bg-gray-50 p-4 rounded-md">
                <p className="whitespace-pre-wrap">
                  {currentQuestion.content.question}
                </p>
              </div>
            </div>

            {/* Question Info */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold mb-2">Thông tin người hỏi:</h3>
                <div className="space-y-2">
                  <div>
                    <span className="text-sm text-gray-500">Họ tên:</span>
                    <p className="font-medium">
                      {currentQuestion.asker.fullName}
                    </p>
                  </div>
                  <div>
                    <span className="text-sm text-gray-500">Email:</span>
                    <p className="font-medium">{currentQuestion.asker.email}</p>
                  </div>
                  <div>
                    <span className="text-sm text-gray-500">
                      Số điện thoại:
                    </span>
                    <p className="font-medium">
                      {currentQuestion.asker.phoneNumber}
                    </p>
                  </div>
                  <div>
                    <span className="text-sm text-gray-500">Địa chỉ:</span>
                    <p className="font-medium">
                      {currentQuestion.asker.address}
                    </p>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="font-semibold mb-2">Thông tin câu hỏi:</h3>
                <div className="space-y-2">
                  <div>
                    <span className="text-sm text-gray-500">Chủ đề:</span>
                    <p className="font-medium">{currentQuestion.topic}</p>
                  </div>
                  <div>
                    <span className="text-sm text-gray-500">Ngày tạo:</span>
                    <p className="font-medium">
                      {formatDate(currentQuestion.createdAt)}
                    </p>
                  </div>
                  <div>
                    <span className="text-sm text-gray-500">
                      Ngày cập nhật:
                    </span>
                    <p className="font-medium">
                      {formatDate(currentQuestion.updatedAt)}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Answer Management Section */}
        <Card>
          <CardHeader>
            <CardTitle>Quản lý câu trả lời</CardTitle>
          </CardHeader>
          <CardContent>
            {currentQuestion.status === "NEW" ? (
              <div className="text-center py-8">
                <p className="text-gray-500 mb-4">
                  Câu hỏi chưa có câu trả lời. Khi tạo câu trả lời, trạng thái
                  sẽ chuyển thành "Đã trả lời".
                </p>
                <CreateAnswerDialog
                  questionId={currentQuestion.id}
                  onSuccess={handleCreateAnswerSuccess}
                />
              </div>
            ) : questionAnswers.length > 0 ? (
              <div className="space-y-4">
                {questionAnswers.map((answer, index) => (
                  <div
                    key={answer.id}
                    className="border rounded-lg p-4 bg-blue-50"
                  >
                    <div className="flex justify-between items-start mb-3">
                      <h4 className="font-semibold text-blue-900">
                        Câu trả lời #{index + 1}
                      </h4>
                      <div className="text-sm text-gray-500">
                        ID: {answer.id}
                      </div>
                    </div>

                    {/* Answer Content */}
                    <div className="mb-4">
                      <h5 className="font-medium text-gray-700 mb-2">
                        Nội dung:
                      </h5>
                      <div className="bg-white p-3 rounded border">
                        <p className="whitespace-pre-wrap">
                          {answer.content.answer}
                        </p>
                      </div>
                    </div>

                    {/* File Attachments */}
                    {answer.content.fileList &&
                      answer.content.fileList.length > 0 && (
                        <div>
                          <h5 className="font-medium text-gray-700 mb-2">
                            Tệp đính kèm ({answer.content.fileList.length}):
                          </h5>
                          <div className="space-y-2">
                            {answer.content.fileList.map(
                              (fileUrl, fileIndex) => (
                                <div
                                  key={fileIndex}
                                  className="flex items-center justify-between p-2 bg-white rounded border"
                                >
                                  <div className="flex items-center gap-2">
                                    <FileText className="h-4 w-4 text-gray-400" />
                                    <span className="text-sm text-gray-700">
                                      {fileUrl.split("/").pop()}
                                    </span>
                                  </div>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() =>
                                      window.open(fileUrl, "_blank")
                                    }
                                  >
                                    <Download className="h-4 w-4" />
                                  </Button>
                                </div>
                              )
                            )}
                          </div>
                        </div>
                      )}
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-gray-500">
                  Câu hỏi này chưa có câu trả lời nào.
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
