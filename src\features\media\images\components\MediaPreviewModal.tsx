import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Edit, Trash2, X, Save, Undo2, Image, Loader2 } from "lucide-react";
import { useState, useEffect } from "react";
import { Media } from "../states/types";
import { DeleteConfirmDialog } from "./DeleteConfirmDialog";
import { toast } from "sonner";

interface MediaPreviewModalProps {
  media: Media | null;
  isOpen: boolean;
  onClose: () => void;
  onEdit?: (
    media: Media,
    updates: { name: string; description?: { text: string } }
  ) => Promise<void>;
  onDelete?: (mediaId: number) => void;
  onSetCoverImage?: (imageUrl: string) => void;
  albumId?: number;
  showActions?: boolean;
}

export function MediaPreviewModal({
  media,
  isOpen,
  onClose,
  onEdit,
  onDelete,
  onSetCoverImage,
  albumId,
  showActions = true,
}: MediaPreviewModalProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [isSettingCover, setIsSettingCover] = useState(false);
  const [currentMedia, setCurrentMedia] = useState<Media | null>(null);
  const [editData, setEditData] = useState({
    name: "",
    description: "",
  });

  useEffect(() => {
    if (media) {
      setCurrentMedia(media);
      setEditData({
        name: media.name,
        description: media.description?.text || "",
      });
      setIsEditing(false);
    }
  }, [media]);

  const displayMedia = currentMedia;

  if (!displayMedia) return null;

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleSave = async () => {
    if (onEdit && displayMedia) {
      const updates = {
        name: editData.name,
        ...(editData.description && {
          description: { text: editData.description },
        }),
      };

      try {
        await onEdit(displayMedia, updates);

        const updatedMedia = {
          ...displayMedia,
          name: editData.name,
          description: editData.description
            ? { text: editData.description }
            : displayMedia.description,
        };
        setCurrentMedia(updatedMedia);

        setIsEditing(false);
        toast.success("Cập nhật thông tin ảnh thành công!");
      } catch (error) {
        toast.error("Không thể cập nhật thông tin ảnh: " + (error as Error).message);
      }
    }
  };

  const handleCancel = () => {
    setEditData({
      name: displayMedia.name,
      description: displayMedia.description?.text || "",
    });
    setIsEditing(false);
  };

  const handleDelete = () => {
    if (onDelete) {
      onDelete(displayMedia.id);
      setShowDeleteConfirm(false);
      onClose();
    }
  };

  const handleSetCoverImage = async () => {
    if (onSetCoverImage && displayMedia && albumId) {
      try {
        setIsSettingCover(true);
        await onSetCoverImage(displayMedia.src);
        toast.success("Đã đặt làm ảnh bìa thành công!");
      } catch (error) {
        toast.error("Có lỗi xảy ra khi đặt ảnh bìa: " + (error as Error).message);
      } finally {
        setIsSettingCover(false);
      }
    }
  };

  const handleClose = () => {
    if (isEditing) {
      handleCancel();
    }
    onClose();
  };

  return (
    <>
      {/* Custom Modal Overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 z-50 bg-black/80 backdrop-blur-sm flex items-center justify-center p-4"
          onClick={handleClose}
        >
          {/* Modal Content */}
          <div
            className="bg-white rounded-2xl shadow-2xl overflow-hidden w-full max-w-5xl max-h-[90vh] flex flex-col"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header - Fixed */}
            <div className="px-6 py-4 border-b bg-gray-50/50 flex items-center justify-between flex-shrink-0">
              <div className="flex items-center gap-3">
                <h2 className="text-xl font-semibold text-gray-900">
                  Xem trước ảnh
                </h2>
                {isEditing && (
                  <div className="px-2 py-1 bg-blue-100 text-blue-700 text-xs font-medium rounded-full">
                    Đang chỉnh sửa
                  </div>
                )}
              </div>
              <Button
                variant="ghost"
                size="icon"
                onClick={handleClose}
                className="h-8 w-8 rounded-full"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>

            {/* Content - Scrollable */}
            <div className="flex-1 flex min-h-0">
              {/* Left: Image */}
              <div className="flex-1 p-6 flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 min-w-0">
                <div className="w-full h-full flex items-center justify-center">
                  <img
                    src={displayMedia.src}
                    alt={displayMedia.name}
                    className="max-w-full max-h-full object-contain rounded-xl shadow-xl border border-white/20"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.style.display = "none";
                      target.nextElementSibling?.setAttribute(
                        "style",
                        "display: flex"
                      );
                    }}
                  />
                  {/* Fallback */}
                  <div
                    className="w-96 h-96 bg-white/60 backdrop-blur-sm rounded-xl border-2 border-dashed border-gray-300 flex items-center justify-center"
                    style={{ display: "none" }}
                  >
                    <div className="text-center text-gray-500">
                      <div className="font-medium mb-2">
                        {displayMedia.name}
                      </div>
                      <div className="text-sm">Không thể tải ảnh</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Right: Properties Panel - Scrollable */}
              <div className="w-96 border-l bg-white shadow-inner flex flex-col">
                <div className="flex-1 overflow-y-auto">
                  <div className="p-6">
                    <div className="space-y-6">
                      {/* Name Field */}
                      <div className="space-y-2">
                        <Label
                          htmlFor="media-name"
                          className="text-sm font-semibold text-gray-700"
                        >
                          Tên ảnh *
                        </Label>
                        {isEditing ? (
                          <Input
                            id="media-name"
                            value={editData.name}
                            onChange={(e) =>
                              setEditData((prev) => ({
                                ...prev,
                                name: e.target.value,
                              }))
                            }
                            placeholder="Nhập tên ảnh"
                            className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                          />
                        ) : (
                          <div className="p-3 bg-gray-50 rounded-lg text-sm font-medium">
                            {displayMedia.name}
                          </div>
                        )}
                      </div>

                      {/* Description Field */}
                      <div className="space-y-2">
                        <Label
                          htmlFor="media-description"
                          className="text-sm font-semibold text-gray-700"
                        >
                          Mô tả
                        </Label>
                        {isEditing ? (
                          <Textarea
                            id="media-description"
                            value={editData.description}
                            onChange={(e) =>
                              setEditData((prev) => ({
                                ...prev,
                                description: e.target.value,
                              }))
                            }
                            placeholder="Nhập mô tả cho ảnh"
                            rows={4}
                            className="border-gray-300 focus:border-blue-500 focus:ring-blue-500 resize-none"
                          />
                        ) : (
                          <div className="p-3 bg-gray-50 rounded-lg text-sm min-h-[100px]">
                            {displayMedia.description?.text || (
                              <span className="text-gray-400 italic">
                                Chưa có mô tả
                              </span>
                            )}
                          </div>
                        )}
                      </div>

                      {/* System Info - Simplified */}
                      <div className="space-y-3 pt-4 border-t border-gray-200">
                        <div className="flex justify-between text-sm">
                          <span className="font-medium text-gray-600">ID:</span>
                          <span className="text-gray-900 font-mono">
                            #{displayMedia.id}
                          </span>
                        </div>

                        <div>
                          <Label className="text-sm font-medium text-gray-600">
                            URL:
                          </Label>
                          <div className="mt-1 p-2 bg-gray-50 rounded text-xs text-gray-700 break-all font-mono">
                            {displayMedia.src}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Action Buttons - Fixed at bottom */}
                {showActions && (
                  <div className="p-6 bg-white border-t border-gray-200 flex-shrink-0">
                    <div className="space-y-3">
                      {isEditing ? (
                        // Edit mode buttons
                        <div className="space-y-2">
                          <Button
                            onClick={handleSave}
                            disabled={!editData.name.trim()}
                            className="w-full bg-blue-600 hover:bg-blue-700 focus:ring-blue-500 text-white font-medium"
                          >
                            <Save className="w-4 h-4 mr-2" />
                            Lưu thay đổi
                          </Button>
                          <Button
                            variant="outline"
                            onClick={handleCancel}
                            className="w-full border-gray-300 text-gray-700 hover:bg-gray-50"
                          >
                            <Undo2 className="w-4 h-4 mr-2" />
                            Hủy chỉnh sửa
                          </Button>
                        </div>
                      ) : (
                        // View mode button
                        <Button
                          onClick={handleEdit}
                          variant="outline"
                          className="w-full border-blue-300 text-blue-700 hover:bg-blue-50 font-medium"
                          disabled={!onEdit}
                        >
                          <Edit className="w-4 h-4 mr-2" />
                          Chỉnh sửa thông tin
                        </Button>
                      )}

                      {/* Delete button */}
                      {onDelete && (
                        <Button
                          variant="outline"
                          onClick={() => setShowDeleteConfirm(true)}
                          className="w-full border-red-300 text-red-700 hover:bg-red-50 font-medium"
                        >
                          <Trash2 className="w-4 h-4 mr-2" />
                          Xóa ảnh
                        </Button>
                      )}

                      {/* Set Cover Image button */}
                      {onSetCoverImage && (
                        <Button
                          variant="outline"
                          onClick={handleSetCoverImage}
                          disabled={isSettingCover}
                          className="w-full border-green-300 text-green-700 hover:bg-green-50 font-medium"
                        >
                          {isSettingCover ? (
                            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                          ) : (
                            <Image className="w-4 h-4 mr-2" />
                          )}
                          {isSettingCover
                            ? "Đang đặt ảnh bìa..."
                            : "Đặt làm ảnh bìa"}
                        </Button>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmDialog
        isOpen={showDeleteConfirm}
        onClose={() => setShowDeleteConfirm(false)}
        onConfirm={handleDelete}
        media={displayMedia}
      />
    </>
  );
}
