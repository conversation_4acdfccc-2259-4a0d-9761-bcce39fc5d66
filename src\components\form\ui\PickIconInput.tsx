import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { IconOptions } from "@/components/other/DynamicIcon";
import { BasicInputProps } from "../registry";

type PickIconInputProps = BasicInputProps<"text">;

export const PickIconInput = React.forwardRef<
  HTMLDivElement,
  PickIconInputProps
>(({ isViewMode, value = "Dot", onChange, disabled, ...rest }, ref) => {
  const [open, setOpen] = React.useState(false);
  const selected = IconOptions.find((opt) => opt.value === value);
  return (
    <div ref={ref} className={cn("flex items-center gap-2")} {...rest}>
      <div className="h-8 w-8 flex items-center justify-center border rounded-md">
        {selected?.label ?? "?"}
      </div>
      <Popover
        open={open}
        onOpenChange={(v) => !disabled && setOpen(v as boolean)}
      >
        <PopoverTrigger asChild>
          {!isViewMode && (
            <span
              className={cn(
                "text-xs underline",
                disabled
                  ? "cursor-not-allowed text-muted-foreground opacity-50"
                  : "cursor-pointer text-blue-500"
              )}
            >
              Sửa
            </span>
          )}
        </PopoverTrigger>
        <PopoverContent className="grid grid-cols-6 gap-2 max-w-xs">
          {IconOptions.map((icon) => (
            <Button
              key={icon.value}
              variant={icon.value === value ? "default" : "ghost"}
              className="h-8 w-8 p-2"
              onClick={() => {
                onChange(icon.value);
                setOpen(false);
              }}
            >
              {icon.label}
            </Button>
          ))}
        </PopoverContent>
      </Popover>
    </div>
  );
});

PickIconInput.displayName = "PickIconInput";
