import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { useAppSelector } from "@/store/rootReducer";
import { selectActionType } from "../states/selectors";
import { ActionCreateForm } from "./ActionCreateForm";
import { ActionDetailView } from "./ActionDetailView";

export const ActionDetailBasic: React.FC = () => {
  const actionType = useAppSelector(selectActionType);

  return (
    <Card className="relative">
      <CardContent className="pt-10">
        {actionType === "view" ? (
          <ActionDetailView />
        ) : (
          <ActionCreateForm mode={actionType} />
        )}
      </CardContent>
    </Card>
  );
};
