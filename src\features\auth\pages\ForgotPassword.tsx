import { useTranslation } from "react-i18next";

export const ForgotPassword: React.FC = () => {
  const { t } = useTranslation("login");

  return (
    <div
      onClick={() => console.log("Quên Mật khẩu")}
      className="cursor-pointer mt-1"
    >
      <b className="text-sm text-text-500 underline hover:text-text-400 transition-colors">
        {t("login.forgotPassword.label")}
      </b>
    </div>
  );
};
