import { RootState } from "@/store/rootReducer";
import { createSelector } from "@reduxjs/toolkit";

// Basic selectors
export const selectActionState = (state: RootState) => state.actionState;

export const selectActions = (state: RootState) => state.actionState.data;

export const selectCurrentAction = (state: RootState) =>
  state.actionState.currentAction;

export const selectActionLoading = (state: RootState) =>
  state.actionState.loading;

export const selectActionError = (state: RootState) => state.actionState.error;

export const selectPageMode = (state: RootState) => state.actionState.pageMode;

export const selectActionType = (state: RootState) =>
  state.actionState.actionType;

export const selectSearchTerm = (state: RootState) =>
  state.actionState.searchTerm;

// Filtered actions with search
export const selectFilteredActions = createSelector(
  [selectActions, selectSearchTerm],
  (actions, searchTerm) => {
    if (!searchTerm.trim()) {
      return actions;
    }

    const term = searchTerm.toLowerCase();
    return actions.filter(
      (action) =>
        action.name.toLowerCase().includes(term) ||
        action.code.toLowerCase().includes(term)
    );
  }
);

// Find action by ID
export const selectActionById = (id: number) =>
  createSelector([selectActions], (actions) =>
    actions.find((action) => action.id === id)
  );
