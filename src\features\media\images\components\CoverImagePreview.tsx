import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ImageLoader } from "@/components/image/ImageLoader";
import { X } from "lucide-react";

interface CoverImagePreviewProps {
  albumName?: string;
  coverImage?: string;
  onDeleteCoverImage?: () => void;
}

export const CoverImagePreview: React.FC<CoverImagePreviewProps> = ({
  albumName = "Album Demo",
  coverImage = "https://picsum.photos/200/200?random=1",
  onDeleteCoverImage,
}) => {
  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle className="text-lg font-semibold">
          Vị trí nút xóa ảnh bìa mới
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Demo Album Info với Cover Image */}
        <div className="border rounded-lg p-4">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <h3 className="text-lg font-semibold mb-2">{albumName}</h3>
              <div className="text-sm text-gray-600">
                <p>
                  <strong>Mô tả:</strong> Album demo để test nút xóa ảnh bìa
                </p>
                <p>
                  <strong>Loại:</strong> image
                </p>
                <p>
                  <strong>Trạng thái:</strong> draft
                </p>
              </div>
            </div>

            {/* Cover Image với nút xóa ở góc trên phải */}
            {coverImage && (
              <div className="ml-4 relative group">
                <ImageLoader
                  src={coverImage}
                  alt={albumName}
                  className="w-24 h-24 object-contain rounded-lg border bg-gray-50"
                />
                {/* Nút xóa ảnh bìa */}
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={onDeleteCoverImage}
                  className="absolute -top-2 -right-2 h-6 w-6 bg-red-500 hover:bg-red-600 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity shadow-lg"
                  title="Xóa ảnh bìa"
                >
                  <X className="w-3 h-3" />
                </Button>
              </div>
            )}
          </div>
        </div>

        {/* Hướng dẫn */}
        <div className="text-xs text-gray-500 space-y-1 bg-blue-50 p-3 rounded-lg">
          <p className="font-medium text-blue-800">✨ Cách sử dụng:</p>
          <p>• Hover vào ảnh bìa để thấy nút xóa (X đỏ) ở góc trên phải</p>
          <p>• Nút sẽ có animation fade in/out khi hover</p>
          <p>• Click nút X để xóa ảnh bìa</p>
          <p>• UX trực quan và dễ truy cập hơn so với nằm trong modal</p>
        </div>

        {/* Demo Multiple Cover Images */}
        <div className="space-y-2">
          <p className="text-sm font-medium">Demo với nhiều kích thước:</p>
          <div className="flex gap-4 items-center">
            {/* Size 64x64 */}
            <div className="relative group">
              <ImageLoader
                src="https://picsum.photos/200/200?random=2"
                alt="Small cover"
                className="w-16 h-16 object-contain rounded-lg border bg-gray-50"
              />
              <Button
                variant="ghost"
                size="icon"
                className="absolute -top-1 -right-1 h-5 w-5 bg-red-500 hover:bg-red-600 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity shadow-lg"
                title="Xóa ảnh bìa"
              >
                <X className="w-2.5 h-2.5" />
              </Button>
            </div>

            {/* Size 96x96 (default) */}
            <div className="relative group">
              <ImageLoader
                src="https://picsum.photos/200/200?random=3"
                alt="Medium cover"
                className="w-24 h-24 object-contain rounded-lg border bg-gray-50"
              />
              <Button
                variant="ghost"
                size="icon"
                className="absolute -top-2 -right-2 h-6 w-6 bg-red-500 hover:bg-red-600 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity shadow-lg"
                title="Xóa ảnh bìa"
              >
                <X className="w-3 h-3" />
              </Button>
            </div>

            {/* Size 128x128 */}
            <div className="relative group">
              <ImageLoader
                src="https://picsum.photos/200/200?random=4"
                alt="Large cover"
                className="w-32 h-32 object-contain rounded-lg border bg-gray-50"
              />
              <Button
                variant="ghost"
                size="icon"
                className="absolute -top-2 -right-2 h-7 w-7 bg-red-500 hover:bg-red-600 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity shadow-lg"
                title="Xóa ảnh bìa"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
