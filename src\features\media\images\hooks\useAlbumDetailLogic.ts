import { useState, useEffect, useCallback } from "react";
import { useAppSelector } from "@/store/rootReducer";
import { useSwitchMode, useAlbumMedia } from "../states/hooks";
import {
  selectCurrentAlbumMedia,
  selectMediaLoading,
  selectMediaError,
  selectMediaHasMore,
  selectMediaCurrentPage,
} from "../states/selector";
import { Album, Media, CreateMediaRequest } from "../states/types";

interface UseAlbumDetailLogicProps {
  album: Album;
  onAlbumUpdated?: () => void | Promise<void>;
}

/**
 * Custom hook for managing album detail page logic
 * Provides handlers for media operations, modal state, and album management
 * @param props - Configuration object containing album and callback
 * @returns Object containing state, handlers, and utility functions
 */
export function useAlbumDetailLogic({
  album,
  onAlbumUpdated,
}: UseAlbumDetailLogicProps) {
  const { switchMode } = useSwitchMode();
  const {
    loadAlbumMedia,
    loadMoreAlbumMedia,
    addMediaToAlbum,
    addExistingMediaToAlbumHook,
    updateMediaDetails,
    removeMedia,
    updatePriority,
    setCoverImageHook,
    deleteCoverImageHook,
  } = useAlbumMedia(onAlbumUpdated);

  // Selectors
  const albumMedia = useAppSelector(selectCurrentAlbumMedia);
  const mediaLoading = useAppSelector(selectMediaLoading);
  const mediaError = useAppSelector(selectMediaError);
  const mediaHasMore = useAppSelector(selectMediaHasMore);
  const mediaCurrentPage = useAppSelector(selectMediaCurrentPage);

  // Local state
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [previewMedia, setPreviewMedia] = useState<Media | null>(null);
  const [deleteTarget, setDeleteTarget] = useState<Media | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  // Effects
  useEffect(() => {
    if (album?.id) {
      loadAlbumMedia(album.id);
    }
  }, [album?.id, loadAlbumMedia]);

  // Handlers
  const handleBackToList = useCallback(() => {
    switchMode("list");
  }, [switchMode]);

  const handleAddMedia = useCallback(
    async (mediaData: Omit<CreateMediaRequest, "albumId">) => {
      if (album?.id) {
        await addMediaToAlbum(album.id, mediaData);
      }
    },
    [album?.id, addMediaToAlbum]
  );

  const handleAddExistingMedia = useCallback(
    async (mediaId: number) => {
      if (album?.id) {
        await addExistingMediaToAlbumHook(album.id, mediaId);
      }
    },
    [album?.id, addExistingMediaToAlbumHook]
  );

  const handleDeleteMedia = useCallback(
    async (mediaId: number) => {
      if (album?.id) {
        await removeMedia(album.id, mediaId);
        setShowDeleteConfirm(false);
        setDeleteTarget(null);
      }
    },
    [album?.id, removeMedia]
  );

  const handleDeleteClick = useCallback((media: Media) => {
    setDeleteTarget(media);
    setShowDeleteConfirm(true);
  }, []);

  const handleSetCoverImage = useCallback(
    async (imageUrl: string) => {
      if (album?.id) {
        await setCoverImageHook(album.id, imageUrl);
      }
    },
    [album?.id, setCoverImageHook]
  );

  const handleDeleteCoverImage = useCallback(async () => {
    if (album?.id) {
      await deleteCoverImageHook(album.id);
    }
  }, [album?.id, deleteCoverImageHook]);

  const handleLoadMore = useCallback(async () => {
    if (album?.id && mediaHasMore && !mediaLoading) {
      await loadMoreAlbumMedia(album.id, mediaCurrentPage);
    }
  }, [
    album?.id,
    mediaHasMore,
    mediaLoading,
    loadMoreAlbumMedia,
    mediaCurrentPage,
  ]);

  return {
    // Data
    albumMedia,
    mediaLoading,
    mediaError,
    mediaHasMore,

    // Modal state
    isAddModalOpen,
    setIsAddModalOpen,
    previewMedia,
    setPreviewMedia,
    deleteTarget,
    showDeleteConfirm,

    // Handlers
    handleBackToList,
    handleAddMedia,
    handleAddExistingMedia,
    handleDeleteMedia,
    handleDeleteClick,
    handleSetCoverImage,
    handleDeleteCoverImage,
    handleLoadMore,

    // Additional actions
    updateMediaDetails,
    updatePriority,
  };
}
