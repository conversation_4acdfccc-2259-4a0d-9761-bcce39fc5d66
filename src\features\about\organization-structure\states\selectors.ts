import { createSelector } from "@reduxjs/toolkit";
import type { RootState } from "@/store/rootReducer";
import type { ContactState, HotlineState } from "./type";

// ============================================================================
// Base Selectors
// ============================================================================

/**
 * Selects the contact state
 */
export const selectContactState = (state: RootState): ContactState =>
  state.organizationStructure.contacts;

/**
 * Selects the hotline state
 */
export const selectHotlineState = (state: RootState): HotlineState =>
  state.organizationStructure.hotlines;

// ============================================================================
// Contact Data Selectors
// ============================================================================

/**
 * Selects contact configuration data
 */
export const selectContactData = createSelector(
  [selectContactState],
  (contactState) => contactState.data
);

/**
 * Selects contact loading state
 */
export const selectContactLoading = createSelector(
  [selectContactState],
  (contactState) => contactState.loading
);

/**
 * Selects contact error state
 */
export const selectContactError = createSelector(
  [selectContactState],
  (contactState) => contactState.error
);

/**
 * Selects contact last updated timestamp
 */
export const selectContactLastUpdated = createSelector(
  [selectContactState],
  (contactState) => contactState.lastUpdated
);

// ============================================================================
// Hotline Data Selectors
// ============================================================================

/**
 * Selects hotline configuration data
 */
export const selectHotlineData = createSelector(
  [selectHotlineState],
  (hotlineState) => hotlineState.data
);

/**
 * Selects hotline loading state
 */
export const selectHotlineLoading = createSelector(
  [selectHotlineState],
  (hotlineState) => hotlineState.loading
);

/**
 * Selects hotline error state
 */
export const selectHotlineError = createSelector(
  [selectHotlineState],
  (hotlineState) => hotlineState.error
);

/**
 * Selects hotline last updated timestamp
 */
export const selectHotlineLastUpdated = createSelector(
  [selectHotlineState],
  (hotlineState) => hotlineState.lastUpdated
);

// ============================================================================
// Utility Selectors
// ============================================================================

/**
 * Selects whether data needs to be refreshed (older than 5 minutes)
 */
export const selectNeedsRefresh = createSelector(
  [selectContactLastUpdated, selectHotlineLastUpdated],
  (contactLastUpdated, hotlineLastUpdated) => {
    const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;

    const contactNeedsRefresh =
      !contactLastUpdated || contactLastUpdated < fiveMinutesAgo;
    const hotlineNeedsRefresh =
      !hotlineLastUpdated || hotlineLastUpdated < fiveMinutesAgo;

    return contactNeedsRefresh || hotlineNeedsRefresh;
  }
);
