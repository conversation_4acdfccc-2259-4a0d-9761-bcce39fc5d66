/**
 * Size Section Component
 * For Frame nodes - width and height controls
 */

import React from 'react';
import { FormNode } from '@/form/types';
import { PropertiesFormProps } from '../types';
import { Label } from '@/components/ui/label';
import { SizePopover } from '../input/SizePopover';

export const SizeSection: React.FC<PropertiesFormProps> = ({
  node,
  onChange,
  disabled = false
}) => {
  const currentClasses = node.styles?.container || '';

  // Extract current width
  const getCurrentWidth = (): string => {
    const widthMatch = currentClasses.match(/w-(auto|full|screen|min|max|fit|\d+\/\d+|\d+)/);
    return widthMatch ? widthMatch[1] : 'auto';
  };

  // Extract current height
  const getCurrentHeight = (): string => {
    const heightMatch = currentClasses.match(/h-(auto|full|screen|min|max|fit|\d+)/);
    return heightMatch ? heightMatch[1] : 'auto';
  };

  // Clean up classes utility
  const cleanupClasses = (classes: string): string => {
    return classes
      .split(' ')
      .filter(cls => cls.trim() !== '')
      .filter((cls, index, arr) => arr.indexOf(cls) === index) // Remove duplicates
      .join(' ')
      .trim();
  };

  // Update width
  const updateWidth = (width: string) => {
    let classArray = currentClasses.split(' ').filter(cls => cls.trim() !== '');

    // Remove existing width classes
    classArray = classArray.filter(cls => 
      !cls.match(/^w-(auto|full|screen|min|max|fit|\d+\/\d+|\d+)$/)
    );

    // Add new width class
    if (width && width !== 'auto') {
      classArray.push(`w-${width}`);
    }

    const newClasses = cleanupClasses(classArray.join(' '));

    onChange({
      styles: {
        ...node.styles,
        container: newClasses
      }
    });
  };

  // Update height
  const updateHeight = (height: string) => {
    let classArray = currentClasses.split(' ').filter(cls => cls.trim() !== '');

    // Remove existing height classes
    classArray = classArray.filter(cls => 
      !cls.match(/^h-(auto|full|screen|min|max|fit|\d+)$/)
    );

    // Add new height class
    if (height && height !== 'auto') {
      classArray.push(`h-${height}`);
    }

    const newClasses = cleanupClasses(classArray.join(' '));

    onChange({
      styles: {
        ...node.styles,
        container: newClasses
      }
    });
  };

  return (
    <div className="space-y-2">
      {/* Width & Height */}
      <div className="grid grid-cols-2 gap-3">
        <div className="space-y-1">
          <Label className="text-xs">Chiều rộng</Label>
          <SizePopover
            value={getCurrentWidth()}
            onValueChange={updateWidth}
            disabled={disabled}
            type="width"
            placeholder="Chọn chiều rộng"
          />
        </div>
        
        <div className="space-y-1">
          <Label className="text-xs">Chiều cao</Label>
          <SizePopover
            value={getCurrentHeight()}
            onValueChange={updateHeight}
            disabled={disabled}
            type="height"
            placeholder="Chọn chiều cao"
          />
        </div>
      </div>
    </div>
  );
};