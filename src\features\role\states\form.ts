import { FormConfig } from "@/components/form/registry";

export const RoleFormConfig: FormConfig = {
  code: "role-form",
  name: "Thông tin vai trò",
  note: "Dùng để nhập thông tin vai trò",
  config: {
    id: "root",
    type: "group",
    label: "",
    style: {
      frame: "p-4",
      label: "",
      content: "",
      error: "",
    },
    children: [
      {
        id: "code-field",
        type: "field",
        fieldConfig: {
          id: "code",
          label: "Mã vai trò",
          data_type: "text",
          input_type: "TextInput",
          placeholder: "Nhập mã vai trò (vd: admin, user)",
          default_value: "",
          validation: {
            required: true,
            maxLength: 50,
            pattern: "^[a-zA-Z0-9_-]+$",
          },
        },
        children: [],
      },
      {
        id: "name-field",
        type: "field",
        fieldConfig: {
          id: "name",
          label: "Tên vai trò",
          data_type: "text",
          input_type: "TextInput",
          placeholder: "Nhập tên vai trò",
          default_value: "",
          validation: {
            required: true,
            maxLength: 200,
          },
        },
        children: [],
      },
    ],
  },
};
