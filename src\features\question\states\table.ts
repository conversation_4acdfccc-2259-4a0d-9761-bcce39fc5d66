import { ColumnConfig } from "@/components/table/registry";

export const QUESTION_COLUMNS: ColumnConfig[] = [
  {
    accessorKey: "id",
    header: "ID",
  },
  {
    accessorKey: "content.title",
    header: "Ti<PERSON>u đề",
    cell: {
      component: "TextCell",
      props: {},
    },
  },
  {
    accessorKey: "asker.fullName",
    header: "Người hỏi",
    cell: {
      component: "TextCell",
      props: {},
    },
  },

  {
    accessorKey: "topic",
    header: "Chủ đề",
    cell: {
      component: "TextCell",
      props: {},
    },
  },
  {
    accessorKey: "status",
    header: "Trạng thái",
    cell: {
      component: "Badge",
      props: {
        colorMap: {
          NEW: "blue",
          DRAFT: "yellow",
          PUBLISHED: "green",
          TRASH: "red",
        },
      },
    },
  },
  {
    accessorKey: "createdAt",
    header: "Ngày tạo",
    cell: {
      component: "DateTimeCell",
      props: {
        format: "dd/MM/yyyy HH:mm",
      },
    },
  },
  {
    accessorKey: "actions",
    header: "Thao tác",
    cell: {
      component: "ActionCell",
      props: {
        actions: ["view"],
      },
    },
  },
];
