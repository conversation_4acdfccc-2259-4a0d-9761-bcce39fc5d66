import React, { forwardRef, useImperativeHandle } from "react";
import { AutoForm, AutoFormRef } from "@/form/context";
import { CreateUserFormConfig } from "../states/formConfig";
import { CreateUser } from "../states/type";

export interface UserAutoFormProps {
  initialData?: CreateUser;
  onSubmit: (data: CreateUser) => void;
  viewOnly?: boolean;
}

export interface UserAutoFormRef {
  submitForm: () => void;
}

export const UserAutoForm = forwardRef<UserAutoFormRef, UserAutoFormProps>(
  ({ initialData = {} as CreateUser, onSubmit, viewOnly = false }, ref) => {
    const defaultData: CreateUser = {
      ...initialData,
      userName: initialData.userName || "",
      password: initialData.password || "",
      fullName: initialData.fullName || "",
      email: initialData.email || "",
      phone: initialData.phone || "",
    };

    const handleSubmit = (data: CreateUser) => {
      onSubmit(data);
    };

    const formRef = React.useRef<AutoFormRef<CreateUser>>(null);

    useImperativeHandle(ref, () => ({
      submitForm: () => {
        formRef.current?.submitForm();
      },
    }));

    const node = CreateUserFormConfig.config;

    return (
      <AutoForm<CreateUser>
        ref={formRef}
        node={node}
        viewOnly={viewOnly}
        initialData={defaultData}
        onSubmit={handleSubmit}
        validationMode="onSubmit"
      />
    );
  }
);
