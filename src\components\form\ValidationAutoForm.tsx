import React, { forwardRef, useImperative<PERSON>andle } from "react";
import { useForm } from "@tanstack/react-form";
import { FieldRender } from "./FieldRender";
import { extractFieldConfigs } from "./helper";
import { FieldConfig, FieldValue, FormConfig, FormNode } from "./registry";
import { FieldError } from "./FieldError";
import { Separator } from "../ui/separator";

export interface ValidationAutoFormProps {
  config: FormConfig;
  onSubmit: (value: Record<string, FieldValue>) => void;
  initialData?: Record<string, FieldValue>;
  isViewMode?: boolean;
}

export const ValidationAutoForm = forwardRef(function ValidationAutoForm(
  {
    config,
    onSubmit,
    initialData = {},
    isViewMode = false,
  }: ValidationAutoFormProps,
  ref
) {
  const fields: FieldConfig[] = extractFieldConfigs(config);

  const initialValues: Record<string, FieldValue> = Object.fromEntries(
    fields.map((f) => {
      const initialValue = initialData[f.id];
      if (initialValue !== undefined) {
        return [f.id, initialValue];
      }

      switch (f.data_type) {
        case "boolean":
          return [f.id, f.default_value ?? false];
        case "number":
          return [f.id, f.default_value ?? 0];
        case "text":
          return [f.id, f.default_value ?? ""];
        case "boolean_array":
        case "number_array":
        case "text_array":
          return [f.id, f.default_value ?? []];
        default:
          return [f.id, ""];
      }
    })
  );

  const form = useForm({
    defaultValues: initialValues,
    onSubmit: async ({ value }) => {
      onSubmit(value);
    },
  });

  useImperativeHandle(ref, () => ({
    submit: () => form.handleSubmit(),
  }));

  const renderNode = (node: FormNode): React.ReactNode => {
    const getClass = (cls?: string, fallback?: string) =>
      cls?.trim() ? cls : fallback || "";

    if (node.type === "group") {
      return (
        <div key={node.id} className={getClass(node.style?.frame, "space-y-2")}>
          {node.label && (
            <h3 className={getClass(node.style?.label, "font-bold")}>
              {node.label}
            </h3>
          )}
          <div className={getClass(node.style?.content, "space-y-2")}>
            {node.children?.map((child: FormNode) => renderNode(child))}
          </div>
        </div>
      );
    }

    if (node.type === "field" && node.fieldConfig) {
      const field = node.fieldConfig;
      return (
        <form.Field key={field.id} name={field.id}>
          {(fieldApi) => (
            <div className="space-y-2">
              <div className={getClass(field.style?.frame)}>
                <FieldRender
                  field={field}
                  value={fieldApi.state.value}
                  onChange={(v) => fieldApi.handleChange(v)}
                  onBlur={fieldApi.handleBlur}
                  isViewMode={isViewMode}
                />
              </div>
              <div>
                <FieldError fieldApi={fieldApi} />
              </div>
            </div>
          )}
        </form.Field>
      );
    }

    return null;
  };

  return (
    <form
      onSubmit={(e) => {
        e.preventDefault();
        form.handleSubmit();
      }}
      className="space-y-8"
    >
      {config.name && (
        <>
          <h2 className="font-bold">{config.name}</h2>
          <Separator />
        </>
      )}
      <div>{renderNode(config.config)}</div>
    </form>
  );
});
