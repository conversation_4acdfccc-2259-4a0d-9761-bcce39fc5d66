/**
 * Form Builder - Main UI Component
 */

import React from "react";
import { FormBuilderToolbar } from "./FormBuilderToolbar";
import { FormTreePanel } from "./FormTreePanel";
import { FormCanvas } from "./FormCanvas";
import { PropertiesPanel } from "./PropertiesPanel";
import { ComponentSelectorModal } from "./ComponentSelectorModal";

/**
 * Main Form Builder UI
 */
export const FormBuilder: React.FC = () => {
  return (
    <div className="h-screen flex flex-col bg-gray-50">
      {/* Toolbar */}
      <FormBuilderToolbar />
      
      {/* Main Content */}
      <div className="flex-1 flex overflow-hidden">
        {/* Left Panel - Tree View */}
        <FormTreePanel />
        
        {/* Center Panel - Canvas */}
        <FormCanvas />
        
        {/* Right Panel - Properties */}
        <PropertiesPanel />
      </div>

      {/* Component Selection Modal */}
      <ComponentSelectorModal />
    </div>
  );
};