import React, { useState, useMemo, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Save, Edit, X, AlertCircle, CheckSquare, Square } from "lucide-react";
import { useAppSelector, useAppDispatch } from "@/store/rootReducer";
import { PermissionRow } from "./PermissionRow";
import { setMode, updateRolePermissionsThunk } from "../states/slices";
import {
  PermissionMatrix as PermissionMatrixType,
  ResourcePermissions,
} from "../states/types";
import { selectPermissionMatrixData } from "../states/selectors";

interface PermissionMatrixProps {
  roleId: number;
  roleName?: string;
}

export const PermissionMatrix: React.FC<PermissionMatrixProps> = ({
  roleId,
  roleName,
}) => {
  const dispatch = useAppDispatch();
  const [localMatrix, setLocalMatrix] = useState<PermissionMatrixType>({});
  const [hasChanges, setHasChanges] = useState(false);

  const { permissionData, resources, actions, mode, loading } = useAppSelector(
    selectPermissionMatrixData
  );

  // Initialize local matrix when permission data changes
  React.useEffect(() => {
    if (permissionData?.matrix) {
      setLocalMatrix(permissionData.matrix);
      setHasChanges(false);
    } else if (
      permissionData &&
      !permissionData.loading &&
      !permissionData.error
    ) {
      // Handle case where permissions array is empty (no permissions granted yet)
      setLocalMatrix({});
      setHasChanges(false);
    }
  }, [permissionData?.matrix, permissionData?.loading, permissionData?.error]);

  // Get current matrix (local in edit mode, server data in view mode)
  const currentMatrix =
    mode === "edit" ? localMatrix : permissionData?.matrix || {};

  // Filter resources that have at least one action defined
  const resourcesWithActions = useMemo(() => {
    return resources.filter(
      (resource) => resource.actions && resource.actions.length > 0
    );
  }, [resources]);

  // Calculate if all permissions are selected
  const totalAvailablePermissions = useMemo(() => {
    return resourcesWithActions.reduce((total, resource) => {
      const availableActions = actions.filter((action) =>
        resource.actions.includes(action.id)
      );
      return total + availableActions.length;
    }, 0);
  }, [resourcesWithActions, actions]);

  const totalGrantedPermissions = useMemo(() => {
    return Object.values(currentMatrix).reduce(
      (total, actionIds) => total + actionIds.length,
      0
    );
  }, [currentMatrix]);

  const allPermissionsSelected =
    totalGrantedPermissions === totalAvailablePermissions &&
    totalAvailablePermissions > 0;

  const handleActionToggle = useCallback(
    (resourceId: number, actionId: number, checked: boolean) => {
      if (mode !== "edit") return;

      setLocalMatrix((prev) => {
        const newMatrix = { ...prev };
        if (!newMatrix[resourceId]) {
          newMatrix[resourceId] = [];
        }

        if (checked) {
          // Add action if not already present
          if (!newMatrix[resourceId].includes(actionId)) {
            newMatrix[resourceId] = [...newMatrix[resourceId], actionId];
          }
        } else {
          // Remove action
          newMatrix[resourceId] = newMatrix[resourceId].filter(
            (id) => id !== actionId
          );
        }

        return newMatrix;
      });

      setHasChanges(true);
    },
    [mode]
  );

  const handleSelectAllPermissions = useCallback(() => {
    if (mode !== "edit") return;

    const newMatrix: PermissionMatrixType = {};

    if (allPermissionsSelected) {
      // Deselect all
      resourcesWithActions.forEach((resource) => {
        newMatrix[resource.id] = [];
      });
    } else {
      // Select all
      resourcesWithActions.forEach((resource) => {
        const availableActions = actions.filter((action) =>
          resource.actions.includes(action.id)
        );
        newMatrix[resource.id] = availableActions.map((action) => action.id);
      });
    }

    setLocalMatrix(newMatrix);
    setHasChanges(true);
  }, [mode, allPermissionsSelected, resourcesWithActions, actions]);

  const handleEdit = () => {
    dispatch(setMode("edit"));
  };

  const handleCancel = () => {
    setLocalMatrix(permissionData?.matrix || {});
    setHasChanges(false);
    dispatch(setMode("view"));
  };

  const handleSave = async () => {
    try {
      const resourcePermissions: ResourcePermissions[] = [];

      // Include all resources with actions, whether they have permissions or not
      resourcesWithActions.forEach((resource) => {
        const actionIds = localMatrix[resource.id] || [];
        resourcePermissions.push({
          resourceId: resource.id,
          actionIds,
        });
      });

      await dispatch(
        updateRolePermissionsThunk({
          roleId,
          resourcePermissions,
        })
      ).unwrap();

      setHasChanges(false);
    } catch (error) {
      // Error is handled in the thunk
      console.error("Failed to save permissions:", error);
    }
  };

  if (permissionData?.loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-gray-500">
            Đang tải dữ liệu phân quyền...
          </div>
        </CardContent>
      </Card>
    );
  }

  if (permissionData?.error) {
    return (
      <Card>
        <CardContent className="p-6">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Lỗi tải dữ liệu phân quyền: {permissionData.error}
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  // Show info message if role has no permissions yet
  const hasNoPermissions =
    permissionData && Object.keys(currentMatrix).length === 0;

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-start">
          <div className="flex-1">
            <CardTitle className="text-lg">
              Phân quyền cho vai trò: {roleName || `ID ${roleId}`}
            </CardTitle>

            {/* Summary - Moved to top */}
            <div className="mt-4 p-4 bg-gray-50 rounded-lg">
              <h4 className="font-medium text-gray-900 mb-3">
                📊 Tổng quan phân quyền
              </h4>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div className="bg-white p-3 rounded border">
                  <div className="text-gray-600 text-xs mb-1">
                    Tổng resources
                  </div>
                  <div className="text-2xl font-bold text-gray-900">
                    {resourcesWithActions.length}
                  </div>
                </div>
                <div className="bg-white p-3 rounded border">
                  <div className="text-gray-600 text-xs mb-1">
                    Resources có quyền
                  </div>
                  <div className="text-2xl font-bold text-blue-600">
                    {
                      Object.keys(currentMatrix).filter(
                        (resourceId) =>
                          currentMatrix[parseInt(resourceId)]?.length > 0
                      ).length
                    }
                  </div>
                </div>
                <div className="bg-white p-3 rounded border">
                  <div className="text-gray-600 text-xs mb-1">
                    Quyền được cấp
                  </div>
                  <div className="text-2xl font-bold text-green-600">
                    {totalGrantedPermissions}
                  </div>
                </div>
                <div className="bg-white p-3 rounded border">
                  <div className="text-gray-600 text-xs mb-1">
                    Tổng quyền có thể
                  </div>
                  <div className="text-2xl font-bold text-gray-500">
                    {totalAvailablePermissions}
                  </div>
                </div>
              </div>
              {totalAvailablePermissions > 0 && (
                <div className="mt-4">
                  <div className="flex justify-between text-xs text-gray-600 mb-2">
                    <span className="font-medium">Tiến độ phân quyền</span>
                    <span className="font-bold text-blue-600">
                      {Math.round(
                        (totalGrantedPermissions / totalAvailablePermissions) *
                          100
                      )}
                      %
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-3 overflow-hidden">
                    <div
                      className="bg-gradient-to-r from-blue-500 to-green-500 h-3 rounded-full transition-all duration-500 ease-out"
                      style={{
                        width: `${
                          (totalGrantedPermissions /
                            totalAvailablePermissions) *
                          100
                        }%`,
                      }}
                    ></div>
                  </div>
                </div>
              )}
            </div>

            {mode === "edit" && totalAvailablePermissions > 0 && (
              <div className="mt-3">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleSelectAllPermissions}
                  className={`${
                    allPermissionsSelected
                      ? "border-red-300 text-red-700 hover:bg-red-50"
                      : "border-blue-300 text-blue-700 hover:bg-blue-50"
                  }`}
                >
                  {allPermissionsSelected ? (
                    <>
                      <Square className="w-4 h-4 mr-2" />
                      Bỏ chọn tất cả quyền
                    </>
                  ) : (
                    <>
                      <CheckSquare className="w-4 h-4 mr-2" />
                      Chọn tất cả quyền
                    </>
                  )}
                </Button>
              </div>
            )}
          </div>
          <div className="flex items-center space-x-2">
            {mode === "view" ? (
              <Button onClick={handleEdit} variant="outline">
                <Edit className="w-4 h-4 mr-2" />
                Chỉnh sửa
              </Button>
            ) : (
              <>
                <Button
                  onClick={handleCancel}
                  variant="outline"
                  disabled={loading}
                >
                  <X className="w-4 h-4 mr-2" />
                  Hủy
                </Button>
                <Button
                  onClick={handleSave}
                  disabled={!hasChanges || loading}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  <Save className="w-4 h-4 mr-2" />
                  {loading ? "Đang lưu..." : "Lưu thay đổi"}
                </Button>
              </>
            )}
          </div>
        </div>

        {mode === "edit" && hasChanges && (
          <Alert className="mt-4">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Bạn có thay đổi chưa được lưu. Nhấn "Lưu thay đổi" để cập nhật
              hoặc "Hủy" để bỏ qua.
            </AlertDescription>
          </Alert>
        )}

        {hasNoPermissions && mode === "view" && (
          <Alert className="mt-4">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Vai trò này chưa được cấp quyền nào. Nhấn "Chỉnh sửa" để cấp
              quyền.
            </AlertDescription>
          </Alert>
        )}
      </CardHeader>

      <CardContent className="space-y-4">
        {resourcesWithActions.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            Không có resource nào có quyền được định nghĩa
          </div>
        ) : (
          resourcesWithActions.map((resource) => (
            <PermissionRow
              key={resource.id}
              resource={resource}
              actions={actions}
              grantedActionIds={currentMatrix[resource.id] || []}
              isEditMode={mode === "edit"}
              onActionToggle={handleActionToggle}
            />
          ))
        )}
      </CardContent>
    </Card>
  );
};
