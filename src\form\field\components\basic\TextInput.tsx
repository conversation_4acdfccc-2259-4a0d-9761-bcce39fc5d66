/**
 * TextInput - Text input component for AutoForm
 */

import React from "react";
import { FieldComponentProps } from "../../FieldFactory";
import { Input } from "@/components/ui/input";

export const TextInput: React.FC<FieldComponentProps> = ({
  value,
  onChange,
  onBlur,
  disabled = false,
  placeholder = "",
  id,
  className = "",
}) => {
  const handleChange = disabled
    ? undefined
    : (e: React.ChangeEvent<HTMLInputElement>) => onChange(e.target.value);

  return (
    <Input
      id={id}
      type="text"
      value={value as string}
      onChange={handleChange}
      onBlur={onBlur}
      disabled={disabled}
      placeholder={placeholder}
      className={className}
    />
  );
};
