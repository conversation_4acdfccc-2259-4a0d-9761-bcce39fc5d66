import React from "react";
import { CopyrightDisplay } from "../../shared/components/displays";
import { PreviewWrapper } from "../../shared/components/wrappers/PreviewWrapper";
import type { CopyrightConfig } from "../states/type";

/**
 * Props for the CopyrightPreview component
 */
interface CopyrightPreviewProps {
  /** Copyright configuration data to preview */
  data: CopyrightConfig | null;
  /** Whether to show the preview */
  visible: boolean;
}

/**
 * CopyrightPreview component displays clean copyright text
 * without highlighting effects, suitable for reuse in public pages
 */
export const CopyrightPreview: React.FC<CopyrightPreviewProps> = ({
  data,
  visible,
}) => {
  if (!visible || !data) {
    return null;
  }

  return (
    <PreviewWrapper
      title="Xem trước: Bản quyền"
      description="Hiển thị thông tin bản quyền như sẽ xuất hiện trên trang công khai."
      theme="blue"
      visible={visible}
    >
      <CopyrightDisplay
        copyright={data}
        className="bg-[#002B45] text-white"
      />
    </PreviewWrapper>
  );
};
