import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Plus, RefreshCw, Check, X } from "lucide-react";

interface CreateNewPageCardProps {
  newPageTitle: string;
  newPageSlug: string;
  slugChecking: boolean;
  slugValid: boolean | null;
  loading: boolean;
  onTitleChange: (value: string) => void;
  onSlugChange: (value: string) => void;
  onGenerateSlug: () => void;
  onCreatePage: () => void;
}

export const CreateNewPageCard: React.FC<CreateNewPageCardProps> = ({
  newPageTitle,
  newPageSlug,
  slugChecking,
  slugValid,
  loading,
  onTitleChange,
  onSlugChange,
  onGenerateSlug,
  onCreatePage,
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Tạo trang mới</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <label className="block text-sm font-medium mb-1">Tiêu đề</label>
          <Input
            value={newPageTitle}
            onChange={(e) => onTitleChange(e.target.value)}
            placeholder="Nhập tiêu đề trang"
          />
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">Đường dẫn</label>
          <div className="flex space-x-2">
            <div className="flex-1 relative">
              <Input
                value={newPageSlug}
                onChange={(e) => onSlugChange(e.target.value)}
                placeholder="Nhập slug hoặc tạo tự động"
              />
              {/* Slug validity indicator */}
              {newPageSlug && (
                <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                  {slugChecking ? (
                    <RefreshCw
                      size={16}
                      className="animate-spin text-gray-400"
                    />
                  ) : slugValid === true ? (
                    <Check size={16} className="text-green-500" />
                  ) : slugValid === false ? (
                    <X size={16} className="text-red-500" />
                  ) : null}
                </div>
              )}
            </div>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={onGenerateSlug}
              disabled={!newPageTitle.trim()}
            >
              <RefreshCw size={16} className="mr-1" />
              Tạo
            </Button>
          </div>
          {slugValid === false && (
            <p className="text-sm text-red-500 mt-1">
              Đường dẫn đã tồn tại, vui lòng chọn đường dẫn khác
            </p>
          )}
        </div>
        <Button
          onClick={onCreatePage}
          disabled={
            !newPageTitle.trim() ||
            !newPageSlug.trim() ||
            slugValid === false ||
            loading
          }
          className="w-full"
        >
          <Plus size={16} className="mr-1" />
          {loading ? "Đang tạo..." : "Tạo trang mới"}
        </Button>
      </CardContent>
    </Card>
  );
};
