/**
 * FieldFactory - Maps component string to actual React component
 */

import React from "react";
import { TextInput } from "./components/basic/TextInput";
import { NumberInput } from "./components/basic/NumberInput";
import { PasswordInput } from "./components/basic/PasswordInput";
import { CheckboxInput } from "./components/basic/CheckboxInput";
import { TextAreaInput } from "./components/basic/TextAreaInput";
import { ToggleInput } from "./components/basic/ToggleInput";
import { DropdownInput } from "./components/basic/DropdownInput";
import { TabInput } from "./components/basic/TabInput";
import { DropDownTextWithSearch } from "./components/basic/DropDownTextWithSearch";
import { DropDownNumberWithSearch } from "./components/basic/DropDownNumberWithSearch";
import { ImagePickerInput } from "./components/basic/ImagePickerInput";
import { PickCategoryInput } from "./components/category/PickCategoryInput";
import { PickIconInput } from "./components/template/PickIconInput";
import { PickComponentInput } from "./components/template/PickComponentInput";

/**
 * Common props for all field components
 */
export interface FieldComponentProps {
  value: string | number | boolean | (string | number | boolean)[];
  onChange: (
    value: string | number | boolean | (string | number | boolean)[]
  ) => void;
  onBlur?: () => void;
  disabled?: boolean;
  placeholder?: string;
  options?: (string | number | boolean)[];
  labels?: string[];
  id?: string;
  className?: string;
}

/**
 * Registry mapping component name to component
 */
export const FIELD_REGISTRY = {
  TextInput,
  NumberInput,
  PasswordInput,
  CheckboxInput,
  TextAreaInput,
  ToggleInput,
  DropdownInput,
  TabInput,
  DropDownTextWithSearch,
  DropDownNumberWithSearch,
  ImagePickerInput,
  PickCategoryInput,
  PickIconInput,
  PickComponentInput,
} as const;

export type FieldType = keyof typeof FIELD_REGISTRY;

interface FieldFactoryProps extends FieldComponentProps {
  type: FieldType;
}

export const FieldFactory: React.FC<FieldFactoryProps> = ({
  type,
  ...props
}) => {
  const Component = FIELD_REGISTRY[type];

  if (!Component) {
    console.warn(`Unknown field type: ${type}`);
    return <div>Unknown field type: {type}</div>;
  }

  // Cast options to string[] for components that need it
  const componentProps = {
    ...props,
    options: props.options?.map(String),
  };

  return <Component {...componentProps} />;
};
