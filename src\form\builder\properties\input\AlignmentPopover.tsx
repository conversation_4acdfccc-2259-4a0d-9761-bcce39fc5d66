/**
 * Alignment Popover Component
 * Popover for justify and align selection with visual icons
 */

import React, { useState } from 'react';
import { cn } from '@/lib/utils';
import { ChevronDown, Check } from 'lucide-react';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Button } from '@/components/ui/button';

interface AlignmentOption {
  value: string;
  label: string;
  description: string;
  icon: React.ReactNode;
}

interface AlignmentPopoverProps {
  value?: string;
  onValueChange?: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  type: 'justify' | 'align';
}

// Justify content options
const JUSTIFY_OPTIONS: AlignmentOption[] = [
  {
    value: 'start',
    label: 'Start',
    description: 'Đầu',
    icon: (
      <div className="flex justify-start w-6 h-4 gap-0.5">
        <div className="w-1 h-3 bg-blue-400 rounded"></div>
        <div className="w-1 h-3 bg-blue-400 rounded"></div>
        <div className="w-1 h-3 bg-blue-400 rounded"></div>
      </div>
    )
  },
  {
    value: 'center',
    label: 'Center',
    description: 'Giữa',
    icon: (
      <div className="flex justify-center w-6 h-4 gap-0.5">
        <div className="w-1 h-3 bg-blue-400 rounded"></div>
        <div className="w-1 h-3 bg-blue-400 rounded"></div>
        <div className="w-1 h-3 bg-blue-400 rounded"></div>
      </div>
    )
  },
  {
    value: 'end',
    label: 'End',
    description: 'Cuối',
    icon: (
      <div className="flex justify-end w-6 h-4 gap-0.5">
        <div className="w-1 h-3 bg-blue-400 rounded"></div>
        <div className="w-1 h-3 bg-blue-400 rounded"></div>
        <div className="w-1 h-3 bg-blue-400 rounded"></div>
      </div>
    )
  },
  {
    value: 'between',
    label: 'Between',
    description: 'Khoảng cách đều',
    icon: (
      <div className="flex justify-between w-6 h-4">
        <div className="w-1 h-3 bg-blue-400 rounded"></div>
        <div className="w-1 h-3 bg-blue-400 rounded"></div>
        <div className="w-1 h-3 bg-blue-400 rounded"></div>
      </div>
    )
  },
  {
    value: 'around',
    label: 'Around',
    description: 'Khoảng cách xung quanh',
    icon: (
      <div className="flex justify-around w-6 h-4">
        <div className="w-1 h-3 bg-blue-400 rounded"></div>
        <div className="w-1 h-3 bg-blue-400 rounded"></div>
        <div className="w-1 h-3 bg-blue-400 rounded"></div>
      </div>
    )
  },
  {
    value: 'evenly',
    label: 'Evenly',
    description: 'Khoảng cách đều nhau',
    icon: (
      <div className="flex justify-evenly w-6 h-4">
        <div className="w-1 h-3 bg-blue-400 rounded"></div>
        <div className="w-1 h-3 bg-blue-400 rounded"></div>
        <div className="w-1 h-3 bg-blue-400 rounded"></div>
      </div>
    )
  }
];

// Align items options
const ALIGN_OPTIONS: AlignmentOption[] = [
  {
    value: 'start',
    label: 'Start',
    description: 'Đầu',
    icon: (
      <div className="flex items-start w-6 h-4 gap-0.5">
        <div className="w-1 h-2 bg-green-400 rounded"></div>
        <div className="w-1 h-3 bg-green-400 rounded"></div>
        <div className="w-1 h-2 bg-green-400 rounded"></div>
      </div>
    )
  },
  {
    value: 'center',
    label: 'Center',
    description: 'Giữa',
    icon: (
      <div className="flex items-center w-6 h-4 gap-0.5">
        <div className="w-1 h-2 bg-green-400 rounded"></div>
        <div className="w-1 h-3 bg-green-400 rounded"></div>
        <div className="w-1 h-2 bg-green-400 rounded"></div>
      </div>
    )
  },
  {
    value: 'end',
    label: 'End',
    description: 'Cuối',
    icon: (
      <div className="flex items-end w-6 h-4 gap-0.5">
        <div className="w-1 h-2 bg-green-400 rounded"></div>
        <div className="w-1 h-3 bg-green-400 rounded"></div>
        <div className="w-1 h-2 bg-green-400 rounded"></div>
      </div>
    )
  },
  {
    value: 'stretch',
    label: 'Stretch',
    description: 'Kéo dãn',
    icon: (
      <div className="flex items-stretch w-6 h-4 gap-0.5">
        <div className="w-1 bg-green-400 rounded"></div>
        <div className="w-1 bg-green-400 rounded"></div>
        <div className="w-1 bg-green-400 rounded"></div>
      </div>
    )
  },
  {
    value: 'baseline',
    label: 'Baseline',
    description: 'Đường cơ sở',
    icon: (
      <div className="flex items-baseline w-6 h-4 gap-0.5">
        <div className="w-1 h-2 bg-green-400 rounded"></div>
        <div className="w-1 h-3 bg-green-400 rounded"></div>
        <div className="w-1 h-2 bg-green-400 rounded"></div>
      </div>
    )
  }
];

export const AlignmentPopover: React.FC<AlignmentPopoverProps> = ({
  value = 'start',
  onValueChange,
  placeholder = 'Chọn alignment',
  disabled = false,
  type
}) => {
  const [open, setOpen] = useState(false);
  
  const options = type === 'justify' ? JUSTIFY_OPTIONS : ALIGN_OPTIONS;
  const currentOption = options.find(opt => opt.value === value) || options[0];

  const handleValueSelect = (newValue: string) => {
    onValueChange?.(newValue);
    setOpen(false);
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            "h-7 justify-between text-xs",
            !currentOption && "text-muted-foreground",
            disabled && "opacity-50 cursor-not-allowed"
          )}
          disabled={disabled}
        >
          <div className="flex items-center gap-2">
            <div className="flex items-center justify-center w-4 h-3 flex-shrink-0">
              {currentOption.icon}
            </div>
            <span className="truncate">{currentOption?.label || placeholder}</span>
          </div>
          <ChevronDown className="ml-2 h-3 w-3 opacity-50 flex-shrink-0" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-64 p-2" align="start">
        <div className="grid grid-cols-3 gap-2">
          {options.map((option) => (
            <button
              key={option.value}
              onClick={() => handleValueSelect(option.value)}
              className={cn(
                "flex flex-col items-center p-2 text-xs rounded-lg hover:bg-gray-100 transition-colors relative border border-gray-200",
                value === option.value && "bg-blue-100 text-blue-900 ring-1 ring-blue-500 border-blue-200"
              )}
              title={`${option.label} - ${option.description}`}
            >
              <div className="flex items-center justify-center w-6 h-4 mb-1">
                {option.icon}
              </div>
              <span className="font-medium text-center mb-1">
                {option.label}
              </span>
              <span className="text-gray-500 text-xs text-center leading-tight">
                {option.description}
              </span>
              
              {value === option.value && (
                <Check className="h-3 w-3 absolute top-1 right-1 text-blue-600" />
              )}
            </button>
          ))}
        </div>
      </PopoverContent>
    </Popover>
  );
};