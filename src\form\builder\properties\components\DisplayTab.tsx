/**
 * Display Tab Component - Simplified
 * Clean UI with collapsible sections
 */

import React, { useState } from 'react';
import { FormNode } from '@/form/types';
import { PropertiesFormProps, SectionState, DisplayMode } from '../types';
import { CollapsibleSection } from './CollapsibleSection';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';

// Import display sections
import { LayoutSection } from '../sections/LayoutSection';
import { SizeSection } from '../sections/SizeSection';
import { SpacingSection } from '../sections/SpacingSection';
import { BorderSection } from '../sections/BorderSection';
import { AppearanceSection } from '../sections/AppearanceSection';
import { EffectsSection } from '../sections/EffectsSection';

export const DisplayTab: React.FC<PropertiesFormProps> = ({
  node,
  onChange,
  disabled = false
}) => {
  const [displayMode, setDisplayMode] = useState<DisplayMode>('basic');
  const [openSection, setOpenSection] = useState<string | null>(
    node.type === 'frame' ? 'layout' : 'spacing'
  );

  const isAdvancedMode = displayMode === 'advanced';

  // Toggle section - only one open at a time
  const toggleSection = (sectionId: string) => {
    setOpenSection(openSection === sectionId ? null : sectionId);
  };

  return (
    <div className="space-y-4">
      {/* Simplified Mode Toggle */}
      <div className="flex items-center justify-between py-2">
        <span className="text-xs font-medium">
          {isAdvancedMode ? 'Nâng cao' : 'Cơ bản'}
        </span>
        <Switch
          checked={isAdvancedMode}
          onCheckedChange={(checked) => setDisplayMode(checked ? 'advanced' : 'basic')}
          disabled={disabled}
        />
      </div>

      {isAdvancedMode ? (
        /* Advanced Mode - Direct className editing */
        <div className="space-y-4">
          <div className="space-y-2">
            <Label className="text-xs">Container</Label>
            <Textarea
              value={node.styles?.container || ''}
              onChange={(e) => onChange({
                styles: {
                  ...node.styles,
                  container: e.target.value
                }
              })}
              disabled={disabled}
              placeholder="space-y-4 p-6 bg-white..."
              className="text-xs font-mono min-h-[3rem] resize-none"
              rows={Math.max(3, Math.ceil((node.styles?.container || '').length / 50))}
            />
          </div>

          <div className="space-y-2">
            <Label className="text-xs">Content</Label>
            <Textarea
              value={node.styles?.content || ''}
              onChange={(e) => onChange({
                styles: {
                  ...node.styles,
                  content: e.target.value
                }
              })}
              disabled={disabled}
              placeholder="text-lg font-semibold..."
              className="text-xs font-mono min-h-[3rem] resize-none"
              rows={Math.max(3, Math.ceil((node.styles?.content || '').length / 50))}
            />
          </div>

          {node.type === 'field' && (
            <>
              <div className="space-y-2">
                <Label className="text-xs">Field</Label>
                <Textarea
                  value={node.styles?.field || ''}
                  onChange={(e) => onChange({
                    styles: {
                      ...node.styles,
                      field: e.target.value
                    }
                  })}
                  disabled={disabled}
                  placeholder="w-full border border-gray-300..."
                  className="text-xs font-mono min-h-[2.5rem] resize-none"
                  rows={Math.max(2, Math.ceil((node.styles?.field || '').length / 50))}
                />
              </div>

              <div className="space-y-2">
                <Label className="text-xs">Label</Label>
                <Textarea
                  value={node.styles?.label || ''}
                  onChange={(e) => onChange({
                    styles: {
                      ...node.styles,
                      label: e.target.value
                    }
                  })}
                  disabled={disabled}
                  placeholder="text-sm font-medium..."
                  className="text-xs font-mono min-h-[2.5rem] resize-none"
                  rows={Math.max(2, Math.ceil((node.styles?.label || '').length / 50))}
                />
              </div>
            </>
          )}
        </div>
      ) : (
        /* Basic Mode - Visual interface with single open section */
        <div className="space-y-4">
          {/* Layout Section (Frame only) */}
          {node.type === 'frame' && (
            <CollapsibleSection
              title="Bố cục"
              isOpen={openSection === 'layout'}
              onToggle={() => toggleSection('layout')}
              disabled={disabled}
            >
              <LayoutSection
                node={node}
                onChange={onChange}
                disabled={disabled}
              />
            </CollapsibleSection>
          )}

          {/* Size Section (Frame only) */}
          {node.type === 'frame' && (
            <CollapsibleSection
              title="Kích thước"
              isOpen={openSection === 'size'}
              onToggle={() => toggleSection('size')}
              disabled={disabled}
            >
              <SizeSection
                node={node}
                onChange={onChange}
                disabled={disabled}
              />
            </CollapsibleSection>
          )}

          {/* Spacing Section */}
          <CollapsibleSection
            title="Lề"
            isOpen={openSection === 'spacing'}
            onToggle={() => toggleSection('spacing')}
            disabled={disabled}
          >
            <SpacingSection
              node={node}
              onChange={onChange}
              disabled={disabled}
            />
          </CollapsibleSection>

          {/* Border Section */}
          <CollapsibleSection
            title="Viền"
            isOpen={openSection === 'border'}
            onToggle={() => toggleSection('border')}
            disabled={disabled}
          >
            <BorderSection
              node={node}
              onChange={onChange}
              disabled={disabled}
            />
          </CollapsibleSection>

          {/* Appearance Section */}
          <CollapsibleSection
            title="Giao diện"
            isOpen={openSection === 'appearance'}
            onToggle={() => toggleSection('appearance')}
            disabled={disabled}
          >
            <AppearanceSection
              node={node}
              onChange={onChange}
              disabled={disabled}
            />
          </CollapsibleSection>

          {/* Effects Section */}
          <CollapsibleSection
            title="Đổ bóng"
            isOpen={openSection === 'effects'}
            onToggle={() => toggleSection('effects')}
            disabled={disabled}
          >
            <EffectsSection
              node={node}
              onChange={onChange}
              disabled={disabled}
            />
          </CollapsibleSection>
        </div>
      )}
    </div>
  );
};