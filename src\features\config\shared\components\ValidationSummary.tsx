import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { AlertTriangle, X } from "lucide-react";
import { Button } from "@/components/ui/button";

// ============================================================================
// Types
// ============================================================================

interface ValidationSummaryProps {
  /** Array of validation error messages */
  errors: string[];
  /** Whether to show the summary */
  show: boolean;
  /** Callback to dismiss the summary */
  onDismiss: () => void;
  /** Title for the error summary */
  title?: string;
}

// ============================================================================
// Component
// ============================================================================

/**
 * ValidationSummary component for displaying a summary of validation errors
 * Shows when user attempts to save with validation errors
 */
export const ValidationSummary: React.FC<ValidationSummaryProps> = ({
  errors,
  show,
  onDismiss,
  title = "Có lỗi trong dữ liệu"
}) => {
  if (!show || errors.length === 0) return null;

  return (
    <Card className="border-red-200 bg-red-50 mb-4">
      <CardContent className="pt-4">
        <div className="flex items-start gap-3">
          <AlertTriangle className="h-5 w-5 text-red-600 flex-shrink-0 mt-0.5" />
          <div className="flex-1">
            <h4 className="font-medium text-red-800 mb-2">{title}</h4>
            <ul className="space-y-1">
              {errors.map((error, index) => (
                <li key={index} className="text-sm text-red-700">
                  • {error}
                </li>
              ))}
            </ul>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onDismiss}
            className="text-red-600 hover:text-red-800 hover:bg-red-100"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default ValidationSummary;
