import { FormConfig } from "@/form/types";

export const CreateUserFormConfig: FormConfig = {
  code: "create-user-form",
  name: "Tạo người dùng mới",
  note: "Biểu mẫu để tạo tài khoản người dùng mới",
  config: {
    id: "form-root",
    type: "frame",
    styles: {
      container: "space-y-4",
    },
    children: [
      {
        id: "userName",
        type: "field",
        styles: {
          container: "space-y-2",
          label: "text-sm font-medium",
          field: "w-full",
        },
        field: {
          objectKey: "userName",
          defaultValue: "",
          dataType: "string",
          component: "TextInput",
        },
        properties: {
          label: "Tên đăng nhập",
          placeholder: "Nhập tên đăng nhập",
        },
        validation: {
          required: { value: true, error: "Tên đăng nhập là bắt buộc" },
          minLength: {
            value: 3,
            error: "Tên đăng nhập phải có ít nhất 3 ký tự",
          },
          maxLength: {
            value: 50,
            error: "Tên đăng nhập không được quá 50 ký tự",
          },
          pattern: {
            value: "^[a-zA-Z0-9_]+$",
            error: "Tên đăng nhập chỉ được chứa chữ cái, số và dấu gạch dưới",
          },
        },
      },
      {
        id: "password",
        type: "field",
        styles: {
          container: "space-y-2",
          label: "text-sm font-medium",
          field: "w-full",
        },
        field: {
          objectKey: "password",
          defaultValue: "",
          dataType: "string",
          component: "PasswordInput",
        },
        properties: {
          label: "Mật khẩu",
          placeholder: "Nhập mật khẩu",
        },
        validation: {
          required: { value: true, error: "Mật khẩu là bắt buộc" },
          minLength: {
            value: 6,
            error: "Mật khẩu phải có ít nhất 6 ký tự",
          },
          maxLength: {
            value: 100,
            error: "Mật khẩu không được quá 100 ký tự",
          },
        },
      },
      {
        id: "fullName",
        type: "field",
        styles: {
          container: "space-y-2",
          label: "text-sm font-medium",
          field: "w-full",
        },
        field: {
          objectKey: "fullName",
          defaultValue: "",
          dataType: "string",
          component: "TextInput",
        },
        properties: {
          label: "Họ và tên",
          placeholder: "Nhập họ và tên đầy đủ",
        },
        validation: {
          required: { value: true, error: "Họ và tên là bắt buộc" },
          minLength: {
            value: 2,
            error: "Họ và tên phải có ít nhất 2 ký tự",
          },
          maxLength: {
            value: 100,
            error: "Họ và tên không được quá 100 ký tự",
          },
        },
      },
      {
        id: "email",
        type: "field",
        styles: {
          container: "space-y-2",
          label: "text-sm font-medium",
          field: "w-full",
        },
        field: {
          objectKey: "email",
          defaultValue: "",
          dataType: "string",
          component: "TextInput",
        },
        properties: {
          label: "Email",
          placeholder: "Nhập địa chỉ email",
          type: "email",
        },
        validation: {
          required: { value: true, error: "Email là bắt buộc" },
          pattern: {
            value: "^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$",
            error: "Email không hợp lệ",
          },
          maxLength: {
            value: 255,
            error: "Email không được quá 255 ký tự",
          },
        },
      },
      {
        id: "phone",
        type: "field",
        styles: {
          container: "space-y-2",
          label: "text-sm font-medium",
          field: "w-full",
        },
        field: {
          objectKey: "phone",
          defaultValue: "",
          dataType: "string",
          component: "TextInput",
        },
        properties: {
          label: "Số điện thoại",
          placeholder: "Nhập số điện thoại",
          type: "tel",
        },
        validation: {
          required: { value: true, error: "Số điện thoại là bắt buộc" },
          pattern: {
            value: "^[0-9+\\-\\s()]{10,15}$",
            error: "Số điện thoại không hợp lệ",
          },
        },
      },
    ],
  },
};
