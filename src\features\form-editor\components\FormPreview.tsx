/**
 * FormPreview - Form Preview & Submission Component
 */

import React from "react";
import { AutoForm } from "../../../form";
import { FormNode, FormData } from "../../../form/types";
import { FormUser } from "../types/FormUser";

interface FormPreviewProps {
  rootNode: FormNode | null;
  submittedUser: FormUser | null;
  submitError: string;
  onFormSubmit: (data: FormData) => void;
}

export const FormPreview: React.FC<FormPreviewProps> = ({
  rootNode,
  submittedUser,
  submitError,
  onFormSubmit,
}) => {
  return (
    <div>
      <h3 className="text-lg font-semibold mb-4 text-gray-800">
        📋 Form Preview
      </h3>

      {/* Form Preview */}
      <div>
        {rootNode ? (
          <AutoForm<FormData>
            node={rootNode}
            viewOnly={false}
            onSubmit={onFormSubmit}
            className="h-full"
          />
        ) : (
          <div className="text-center py-8 text-gray-500">
            No valid form node to display
          </div>
        )}
      </div>

      {/* Display submitted FormUser */}
      {submittedUser && (
        <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
          <h4 className="font-medium text-green-800 mb-2">
            ✅ FormUser Submitted:
          </h4>
          <pre className="text-sm text-green-700 bg-green-100 p-2 rounded overflow-auto">
            {JSON.stringify(submittedUser, null, 2)}
          </pre>
        </div>
      )}

      {/* Display submit error */}
      {submitError && (
        <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
          <h4 className="font-medium text-red-800 mb-2">❌ Submit Error:</h4>
          <div className="text-sm text-red-700">{submitError}</div>
        </div>
      )}
    </div>
  );
};
