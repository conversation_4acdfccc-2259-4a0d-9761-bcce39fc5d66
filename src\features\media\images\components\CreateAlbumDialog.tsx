import React, { useState, useRef } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Title,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { FolderPlus } from "lucide-react";
import { AutoForm, AutoFormRef } from "@/form/context";
import { createAlbumFormNode } from "../states/form";
import { CreateAlbum } from "../states/types";
import { createAlbum } from "../states/api";
import { useSwitchMode } from "../states/hooks";
import { toast } from "sonner";

interface CreateAlbumDialogProps {
  onAlbumCreated?: (albumId: number) => void;
}

export const CreateAlbumDialog: React.FC<CreateAlbumDialogProps> = ({
  onAlbumCreated,
}) => {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const { switchMode } = useSwitchMode();
  // Define the album form data type
  interface AlbumFormData {
    name: string;
    description: {
      text: string;
    };
  }

  const formRef = useRef<AutoFormRef<AlbumFormData>>(null);

  const handleSubmit = async (data: AlbumFormData) => {
    try {
      setLoading(true);

      // Transform form data to match API requirements
      const albumData: CreateAlbum = {
        name: data.name,
        description: data.description,
        coverImage: "", // Will be set later when uploading images
        type: "image", // Always image type
      };

      const response = await createAlbum(albumData);

      // Handle BaseResponse structure
      if (response.data.data) {
        toast.success(response.data.message || "Tạo album thành công");
        setOpen(false);

        // Navigate to album detail view using URL params (single source of truth)
        switchMode("detail", response.data.data.id.toString());

        // Call callback if provided
        if (onAlbumCreated) {
          onAlbumCreated(response.data.data.id);
        }
      } else if (response.data.errors) {
        // Handle validation errors from BaseResponse
        const errorMessages = Object.values(response.data.errors).join(", ");
        toast.error(errorMessages);
      } else {
        toast.error(response.data.message || "Có lỗi xảy ra khi tạo album");
      }
    } catch (error) {
      console.error("Error creating album:", error);
      toast.error("Có lỗi xảy ra khi tạo thư viện ảnh");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm">
          <FolderPlus className="w-4 h-4 mr-2" />
          Tạo album
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-lg">
        <DialogHeader>
          <DialogTitle>Tạo album mới</DialogTitle>
        </DialogHeader>
        <div className="py-4">
          <AutoForm<AlbumFormData>
            ref={formRef}
            node={createAlbumFormNode}
            onSubmit={handleSubmit}
            validationMode="onChange"
            viewOnly={loading}
          />
          <div className="flex gap-3 justify-end mt-6">
            <Button
              type="button"
              variant="outline"
              onClick={() => setOpen(false)}
              disabled={loading}
            >
              Hủy
            </Button>
            <Button
              type="button"
              onClick={() => formRef.current?.submitForm()}
              disabled={loading}
            >
              {loading ? "Đang tạo..." : "Tạo album"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
