import type { FieldValue } from "@/components/form/registry";

export interface Action {
  id: number;
  createdAt: number;
  updatedAt: number;
  code: string;
  name: string;
  metadata: Record<string, FieldValue>;
}

export interface CreateActionRequest {
  code: string;
  name: string;
  metadata?: Record<string, FieldValue> | null;
}

export interface UpdateActionRequest {
  id: number;
  payload: Partial<Action>;
}

export type ActionPageMode = "list" | "detail";
export type ActionAction = "view" | "edit" | "create";

export interface ActionState {
  data: Action[];
  currentAction: Action | null;
  // URL sync states
  pageMode: ActionPageMode;
  actionType: ActionAction;
  // Search/filter states
  searchTerm: string;
  loading: boolean;
  error: string | null;
}

export interface ActionQueryParams {
  mode?: ActionPageMode;
  action?: string; // action id
  actionType?: ActionAction;
  search?: string;
}
