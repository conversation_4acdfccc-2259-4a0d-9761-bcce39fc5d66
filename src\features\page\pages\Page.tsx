import { useMemo } from "react";
import { useLocation } from "react-router-dom";
import { usePageBySlug } from "../states/hooks";

const Page = () => {
  const location = useLocation();

  // Extract last segment as category slug (the correct approach)
  const slug = useMemo(() => {
    const pathSegments = location.pathname.split("/").filter(Boolean);
    return pathSegments[pathSegments.length - 1] || "";
  }, [location.pathname]);

  // Use the optimized hook to fetch page content
  const { category, page, loading, error, found, refetch } =
    usePageBySlug(slug);

  console.log("Page component - Simple Debug:", {
    pathname: location.pathname,
    extractedSlug: slug,
    categoryFound: !!category,
    categoryName: category?.name,
    categoryPostId: category?.postId,
    pageInCache: !!page,
    pageId: page?.id,
    loading,
    error,
    found,
  });

  // Loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Đang tải nội dung...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center max-w-md">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h2 className="text-2xl font-bold text-gray-800 mb-2">
            Có lỗi xảy ra
          </h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={refetch}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
          >
            Thử lại
          </button>
        </div>
      </div>
    );
  }

  // Category not found
  if (!found || !category) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center max-w-md">
          <div className="text-gray-400 text-6xl mb-4">📄</div>
          <h2 className="text-2xl font-bold text-gray-800 mb-2">
            Không tìm thấy trang
          </h2>
          <p className="text-gray-600">
            Trang với đường dẫn "<strong>{slug}</strong>" không tồn tại hoặc
            chưa được liên kết với nội dung.
          </p>
        </div>
      </div>
    );
  }

  // Category found but no linked page
  if (!page) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center max-w-md">
          <div className="text-yellow-500 text-6xl mb-4">⚠️</div>
          <h2 className="text-2xl font-bold text-gray-800 mb-2">
            Trang chưa có nội dung
          </h2>
          <p className="text-gray-600">
            Chuyên mục "<strong>{category.name}</strong>" đã được tìm thấy nhưng
            chưa có nội dung trang được liên kết.
          </p>
        </div>
      </div>
    );
  }

  // Success: render page content
  return (
    <div className="page-container">
      {/* SEO metadata */}
      <title>{page.title}</title>
      <meta name="description" content={page.excerpt.description} />

      {/* Page content */}
      <div className="min-h-screen">
        {/* ViewerRenderer now handles null content gracefully, but we can add explicit check here if needed */}
      </div>
    </div>
  );
};

export default Page;
