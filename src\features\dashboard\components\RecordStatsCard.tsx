import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { LucideIcon } from "lucide-react";

interface RecordStatsCardProps {
  title: string;
  mainValue: number | string;
  subValue?: number | string;
  icon: LucideIcon;
  description?: string;
  trend?: {
    value: number;
    isPositive: boolean;
    label?: string;
  };
  badge?: {
    text: string;
    variant: "success" | "warning" | "error" | "info";
  };
}

export const RecordStatsCard = ({
  title,
  mainValue,
  subValue,
  icon: Icon,
  description,
  trend,
  badge,
}: RecordStatsCardProps) => {
  const getBadgeColor = (variant: string) => {
    switch (variant) {
      case "success":
        return "bg-green-100 text-green-800";
      case "warning":
        return "bg-yellow-100 text-yellow-800";
      case "error":
        return "bg-red-100 text-red-800";
      case "info":
        return "bg-blue-100 text-blue-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <Icon className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <div className="text-2xl font-bold">{mainValue}</div>

          {subValue && (
            <div className="text-sm text-muted-foreground">{subValue}</div>
          )}

          {badge && (
            <div
              className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getBadgeColor(
                badge.variant
              )}`}
            >
              {badge.text}
            </div>
          )}

          {trend && (
            <p
              className={`text-xs flex items-center space-x-1 ${
                trend.isPositive ? "text-green-600" : "text-red-600"
              }`}
            >
              <span>{trend.isPositive ? "↗" : "↘"}</span>
              <span>
                {trend.isPositive ? "+" : ""}
                {trend.value}%
              </span>
              {trend.label && (
                <span className="text-muted-foreground">({trend.label})</span>
              )}
            </p>
          )}

          {description && (
            <p className="text-xs text-muted-foreground">{description}</p>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
