import { createAsyncThunk, createSlice, PayloadAction } from "@reduxjs/toolkit";
import {
  lockUserApi,
  unverifyEmail<PERSON>pi,
  unverifyP<PERSON><PERSON><PERSON>,
  UserQueryParams,
  verifyEmail<PERSON>pi,
  verifyPhone<PERSON><PERSON>,
} from "./api";
import { UserData } from "./type";
export type UserPageMode = "list" | "detail";

export interface PathParams {
  mode: UserPageMode;
  user: string;
}

export interface lockPayload {
  locked: boolean;
  lockedUntil?: number;
}

export const verifyUserPhoneAsync = createAsyncThunk<unknown, UserData>(
  "userFilter/verifyUserPhoneAsync",
  async (user, { rejectWithValue }) => {
    const response = await verify<PERSON><PERSON><PERSON><PERSON>(user);

    if (!response) {
      return rejectWithValue("Có lỗi xảy ra khi xác thực");
    }
    return response as unknown as UserData;
  }
);

export const unverifyUserPhoneAsync = createAsyncThunk<unknown, UserData>(
  "userFilter/unverifyUserPhoneAsync",
  async (user, { rejectWithValue }) => {
    const response = await unverify<PERSON><PERSON><PERSON><PERSON>(user);

    if (!response) {
      return rejectWithValue("Có lỗi xảy ra khi hủy xác thực");
    }
    return response as unknown as UserData;
  }
);

export const verifyUserEmailAsync = createAsyncThunk<unknown, UserData>(
  "userFilter/verifyUserEmailAsync",
  async (user, { rejectWithValue }) => {
    const response = await verifyEmailApi(user);

    if (!response) {
      return rejectWithValue("Có lỗi xảy ra khi xác thực");
    }
    return response as unknown as UserData;
  }
);

export const unverifyUserEmailAsync = createAsyncThunk<unknown, UserData>(
  "userFilter/unverifyUserEmailAsync",
  async (user, { rejectWithValue }) => {
    const response = await unverifyEmailApi(user);

    if (!response) {
      return rejectWithValue("Có lỗi xảy ra khi hủy xác thực");
    }
    return response as unknown as UserData;
  }
);

export const lockUserAsync = createAsyncThunk<
  unknown,
  { user: UserData; dateNumber: number; locked: boolean }
>(
  "userFilter/lockUserAsync",
  async ({ dateNumber, user, locked }, { rejectWithValue }) => {
    const payload: lockPayload = {
      locked: locked,
      lockedUntil: dateNumber,
    };

    const response = await lockUserApi(user, payload);

    if (!response) {
      return rejectWithValue("Có lỗi xảy ra khi khóa tài khoản");
    }
    return response as unknown as UserData;
  }
);

export interface UserFilterState {
  queryParams: UserQueryParams;
  currentUser: UserData | null;
  loading: boolean;
  error: string | null;
  refetch: boolean;
  pathParams: PathParams;
}

export const initialState: UserFilterState = {
  queryParams: {
    page: 0,
    size: 10,
    keyword: "",
  },
  currentUser: null,
  loading: false,
  error: null,
  refetch: false,
  pathParams: {
    mode: "list",
    user: "",
  },
};

const userFilterSlice = createSlice({
  name: "userFilter",
  initialState,
  reducers: {
    setFilters(state, action: PayloadAction<Partial<UserQueryParams>>) {
      state.queryParams = { ...state.queryParams, ...action.payload };
    },
    setLoading(state, action: PayloadAction<boolean>) {
      state.loading = action.payload;
    },
    setError(state, action: PayloadAction<string | null>) {
      state.error = action.payload;
    },
    triggerRefetch(state) {
      state.refetch = !state.refetch;
    },
    setPathParams(state, action: PayloadAction<Partial<PathParams>>) {
      state.pathParams = { ...state.pathParams, ...action.payload };
    },
    setCurrentUser(state, action: PayloadAction<UserData | null>) {
      state.currentUser = action.payload;
    },
    resetFilters(state) {
      state.queryParams = initialState.queryParams;
      state.currentUser = initialState.currentUser;
      state.loading = initialState.loading;
      state.error = initialState.error;
      state.refetch = initialState.refetch;
      state.pathParams = initialState.pathParams;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(verifyUserPhoneAsync.rejected, (state, action) => {
        state.error =
          action.error.message || "Có lỗi xảy ra khi xác thực số điện thoại";
      })
      .addCase(verifyUserEmailAsync.rejected, (state, action) => {
        state.error =
          action.error.message || "Có lỗi xảy ra khi xác thực email";
      })
      .addCase(unverifyUserPhoneAsync.rejected, (state, action) => {
        state.error =
          action.error.message ||
          "Có lỗi xảy ra khi hủy xác thực số điện thoại";
      })
      .addCase(unverifyUserEmailAsync.rejected, (state, action) => {
        state.error =
          action.error.message || "Có lỗi xảy ra khi hủy xác thực email";
      });
  },
});

export const {
  setFilters,
  setCurrentUser,
  setLoading,
  setError,
  triggerRefetch,
  setPathParams,
  resetFilters,
} = userFilterSlice.actions;

export default userFilterSlice.reducer;
