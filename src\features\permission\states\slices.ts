import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import { toast } from "sonner";
import {
  PermissionState,
  RolePermission,
  UpdateRolePermissionsRequest,
  PermissionMatrix,
  PermissionPageMode,
} from "./types";
import { fetchRolePermissions, updateRolePermissions } from "./api";

const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

const initialState: PermissionState = {
  rolePermissions: {},
  selectedRoleId: null,
  mode: "view",
  isRoleSelectionOpen: false,
  loading: false,
  error: null,
};

// Helper function to convert permissions array to matrix
const createPermissionMatrix = (
  permissions: RolePermission[]
): PermissionMatrix => {
  const matrix: PermissionMatrix = {};

  // Handle empty array case
  if (!permissions || permissions.length === 0) {
    return matrix;
  }

  permissions.forEach((permission) => {
    // Ensure we have valid permission data
    if (permission.resourceId && permission.actionId) {
      if (!matrix[permission.resourceId]) {
        matrix[permission.resourceId] = [];
      }
      if (!matrix[permission.resourceId].includes(permission.actionId)) {
        matrix[permission.resourceId].push(permission.actionId);
      }
    }
  });

  return matrix;
};

// Async thunks
export const fetchRolePermissionsThunk = createAsyncThunk(
  "permission/fetchRolePermissions",
  async (roleId: number, { getState, rejectWithValue }) => {
    try {
      const state = getState() as { permissionState: PermissionState };
      const cached = state.permissionState.rolePermissions[roleId];

      // Check if we have fresh cached data
      if (
        cached &&
        Date.now() - cached.lastFetched < CACHE_DURATION &&
        !cached.error
      ) {
        return { roleId, fromCache: true, data: cached.data };
      }

      const response = await fetchRolePermissions(roleId);

      // Handle BaseResponse<RolePermission[]> structure
      // response.data should contain the RolePermission[]
      const data = response.data || [];

      return { roleId, fromCache: false, data };
    } catch (error) {
      return rejectWithValue({
        roleId,
        error:
          error instanceof Error
            ? error.message
            : "Failed to fetch permissions",
      });
    }
  }
);

export const updateRolePermissionsThunk = createAsyncThunk(
  "permission/updateRolePermissions",
  async (data: UpdateRolePermissionsRequest, { rejectWithValue }) => {
    try {
      await updateRolePermissions(data);
      toast.success("Cập nhật phân quyền thành công");
      return data;
    } catch (error) {
      const message =
        error instanceof Error ? error.message : "Failed to update permissions";
      toast.error(`Lỗi cập nhật phân quyền: ${message}`);
      return rejectWithValue({ roleId: data.roleId, error: message });
    }
  }
);

const permissionSlice = createSlice({
  name: "permission",
  initialState,
  reducers: {
    setSelectedRole: (state, action: PayloadAction<number | null>) => {
      state.selectedRoleId = action.payload;
    },

    setMode: (state, action: PayloadAction<PermissionPageMode>) => {
      state.mode = action.payload;
    },

    setRoleSelectionOpen: (state, action: PayloadAction<boolean>) => {
      state.isRoleSelectionOpen = action.payload;
    },

    setPathParams: (
      state,
      action: PayloadAction<{
        roleId?: number | null;
        mode?: PermissionPageMode;
      }>
    ) => {
      const { roleId, mode } = action.payload;
      if (roleId !== undefined) state.selectedRoleId = roleId;
      if (mode) state.mode = mode;
    },

    // Optimistic update for better UX
    updatePermissionMatrix: (
      state,
      action: PayloadAction<{
        roleId: number;
        resourceId: number;
        actionIds: number[];
      }>
    ) => {
      const { roleId, resourceId, actionIds } = action.payload;
      if (state.rolePermissions[roleId]) {
        state.rolePermissions[roleId].matrix[resourceId] = actionIds;
      }
    },

    clearError: (state) => {
      state.error = null;
    },

    clearRolePermissionsCache: (state, action: PayloadAction<number>) => {
      delete state.rolePermissions[action.payload];
    },
  },

  extraReducers: (builder) => {
    builder
      // Fetch role permissions
      .addCase(fetchRolePermissionsThunk.pending, (state, action) => {
        const roleId = action.meta.arg;
        if (!state.rolePermissions[roleId]) {
          state.rolePermissions[roleId] = {
            data: [],
            matrix: {},
            loading: true,
            error: null,
            lastFetched: 0,
          };
        } else {
          state.rolePermissions[roleId].loading = true;
          state.rolePermissions[roleId].error = null;
        }
      })

      .addCase(fetchRolePermissionsThunk.fulfilled, (state, action) => {
        const { roleId, data, fromCache } = action.payload;
        const matrix = createPermissionMatrix(data);

        state.rolePermissions[roleId] = {
          data,
          matrix,
          loading: false,
          error: null,
          lastFetched: fromCache
            ? state.rolePermissions[roleId]?.lastFetched || Date.now()
            : Date.now(),
        };
      })

      .addCase(fetchRolePermissionsThunk.rejected, (state, action) => {
        const { roleId, error } = action.payload as {
          roleId: number;
          error: string;
        };
        if (state.rolePermissions[roleId]) {
          state.rolePermissions[roleId].loading = false;
          state.rolePermissions[roleId].error = error;
        }
        state.error = error;
      })

      // Update role permissions
      .addCase(updateRolePermissionsThunk.pending, (state) => {
        state.loading = true;
        state.error = null;
      })

      .addCase(updateRolePermissionsThunk.fulfilled, (state, action) => {
        state.loading = false;
        state.mode = "view";

        const { roleId, resourcePermissions } = action.payload;

        // Update cache with new data
        if (state.rolePermissions[roleId]) {
          const newMatrix: PermissionMatrix = {};
          resourcePermissions.forEach(({ resourceId, actionIds }) => {
            if (actionIds.length > 0) {
              newMatrix[resourceId] = actionIds;
            }
          });

          state.rolePermissions[roleId].matrix = newMatrix;
          // Mark as needs refresh on next fetch to get fresh server data
          state.rolePermissions[roleId].lastFetched = 0;
        }
      })

      .addCase(updateRolePermissionsThunk.rejected, (state, action) => {
        state.loading = false;
        const { error } = action.payload as { roleId: number; error: string };
        state.error = error;
      });
  },
});

export const {
  setSelectedRole,
  setMode,
  setRoleSelectionOpen,
  setPathParams,
  updatePermissionMatrix,
  clearError,
  clearRolePermissionsCache,
} = permissionSlice.actions;

export default permissionSlice.reducer;
