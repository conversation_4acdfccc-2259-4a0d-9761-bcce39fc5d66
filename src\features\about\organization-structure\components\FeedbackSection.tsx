import React from "react";
import { MessageSquare, Send, Mail, Phone } from "lucide-react";

// ============================================================================
// Types
// ============================================================================

interface FeedbackSectionProps {
  /** Optional custom className */
  className?: string;
}

// ============================================================================
// Main Feedback Section Component
// ============================================================================

/**
 * Feedback Section Component
 * 
 * Displays feedback and contact information with an image
 * Includes contact methods and feedback submission guidance
 * 
 * @param props - Component props
 * @returns JSX element for feedback section
 */
export const FeedbackSection: React.FC<FeedbackSectionProps> = ({
  className = ""
}) => {
  return (
    <div className={`w-full ${className}`}>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
        {/* Image Section */}
        <div className="order-2 lg:order-1">
          <img
            src="https://api.hocnhe.com/storage/v1/public/view/2025-06-04/original/445fdb9bfbfec1d5934cdcd61c2d8cfa7dbdd9e1-1749010360098.jpg"
            alt="Khánh Hòa - Thành phố biển"
            className="w-full h-auto rounded-lg shadow-lg object-cover"
            loading="lazy"
          />
        </div>

        {/* Content Section */}
        <div className="order-1 lg:order-2 space-y-6">
          {/* Title */}
          <div>
            <h3 className="text-xl font-bold text-gray-900 mb-4">
              Gửi phản ánh, kiến nghị và địa chỉ
            </h3>
            <p className="text-gray-600 leading-relaxed">
              Công chúng có thể gửi các cảnh báo chính qua kênh Hòa, địa chỉ chính quy 
              của Trung tâm Lưu trữ lịch sử tỉnh Khánh Hòa.
            </p>
          </div>

          {/* Contact Methods */}
          <div className="space-y-4">
            {/* Website */}
            <div className="flex items-start gap-3">
              <MessageSquare className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
              <div>
                <p className="text-sm text-gray-600">
                  Trang thông tin điện tử của Sở Nội vụ, địa chỉ: 
                  <a 
                    href="https://sonoivu.khanhhoa.gov.vn" 
                    className="text-blue-600 hover:text-blue-800 ml-1"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    https://sonoivu.khanhhoa.gov.vn
                  </a>
                </p>
              </div>
            </div>

            {/* Government Portal */}
            <div className="flex items-start gap-3">
              <Send className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
              <div>
                <p className="text-sm text-gray-600">
                  Tại phòng Chính quyền địa phương, địa chỉ: 
                  <a 
                    href="https://chinhquyen.khanhhoa.gov.vn" 
                    className="text-blue-600 hover:text-blue-800 ml-1"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    https://chinhquyen.khanhhoa.gov.vn
                  </a>
                </p>
              </div>
            </div>

            {/* Email Contact */}
            <div className="flex items-start gap-3">
              <Mail className="h-5 w-5 text-red-600 mt-0.5 flex-shrink-0" />
              <div>
                <p className="text-sm text-gray-600">
                  Email liên hệ: 
                  <a 
                    href="mailto:<EMAIL>" 
                    className="text-blue-600 hover:text-blue-800 ml-1"
                  >
                    <EMAIL>
                  </a>
                </p>
              </div>
            </div>

            {/* Phone Contact */}
            <div className="flex items-start gap-3">
              <Phone className="h-5 w-5 text-purple-600 mt-0.5 flex-shrink-0" />
              <div>
                <p className="text-sm text-gray-600">
                  Điện thoại liên hệ: 
                  <a 
                    href="tel:02583811901" 
                    className="text-blue-600 hover:text-blue-800 ml-1"
                  >
                    0258.3811901
                  </a>
                </p>
              </div>
            </div>
          </div>

          {/* Additional Information */}
          <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
            <div className="flex items-start gap-2">
              <MessageSquare className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-blue-800">
                <p className="font-medium mb-1">Hướng dẫn gửi phản ánh:</p>
                <p>
                  Quý khách có thể gửi phản ánh, kiến nghị qua các kênh trên. 
                  Chúng tôi cam kết phản hồi trong vòng 24-48 giờ làm việc.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FeedbackSection;
