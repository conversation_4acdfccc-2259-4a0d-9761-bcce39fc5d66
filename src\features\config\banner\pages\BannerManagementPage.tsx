import React, { useEffect, useState } from "react";
import { useAppDispatch, useAppSelector } from "@/store/rootReducer";
import { Button } from "@/components/ui/button";
import { Plus, Save, Loader2 } from "lucide-react";

import { BannerPageLayout } from "../components/BannerPageLayout";
import { BannerGrid } from "../components/BannerGrid";
import { BannerMediaModal } from "../components/BannerMediaModal";
import type { BannerMetadata, BannerConfig, BannerItem } from "../states/type";

import {
  fetchBannerAsync,
  updateBannerAsync,
  setBannerData,
  clearError,
} from "../states/slices";
import {
  selectBannerData,
  selectBannerLoading,
  selectBannerSaving,
  selectBannerError,
  selectBannerIsDirty,
} from "../states/selector";
import { toast } from "sonner";

// ============================================================================
// Configuration
// ============================================================================

const BANNER_METADATA: BannerMetadata = {
  title: 'Quản lý banner',
  description: 'Quản lý banner hiển thị trên trang chủ',
  type: 'array',
  arrayKey: 'banners',
  fields: [
    {
      key: 'src',
      label: 'URL Hình ảnh',
      type: 'url',
      required: true,
      placeholder: 'https://example.com/banner.jpg'
    },
  ],
};

// ============================================================================
// Component
// ============================================================================

export const BannerManagementPage: React.FC = () => {
  const dispatch = useAppDispatch();
  const data = useAppSelector(selectBannerData);
  const loading = useAppSelector(selectBannerLoading);
  const saving = useAppSelector(selectBannerSaving);
  const error = useAppSelector(selectBannerError);
  const isDirty = useAppSelector(selectBannerIsDirty);

  const [isValid, setIsValid] = useState(true);
  const [hasValidationErrors, setHasValidationErrors] = useState(false);
  const [isMediaModalOpen, setIsMediaModalOpen] = useState(false);

  // ============================================================================
  // Effects
  // ============================================================================

  /**
   * Fetch initial data when component mounts
   * Only fetches if data is not already loaded and not currently loading
   */
  useEffect(() => {
    if (!data && !loading) {
      dispatch(fetchBannerAsync());
    }
  }, [dispatch, data, loading]);

  // ============================================================================
  // Event Handlers
  // ============================================================================

  /**
   * Handles saving banner configuration changes
   * Validates data before saving and shows appropriate notifications
   */
  const handleSave = async () => {
    if (!isValid) {
      toast.error("Vui lòng sửa các lỗi trước khi lưu");
      return;
    }

    if (data && isDirty) {
      try {
        await dispatch(updateBannerAsync(data)).unwrap();
        toast.success("Lưu cấu hình banner thành công");
      } catch (error) {
        toast.error(`Lỗi khi lưu cấu hình banner: ${String(error)}`);
        // Error is handled in the slice
      }
    }
  };

  /**
   * Handles refreshing banner data from server
   */
  const handleRefresh = () => {
    dispatch(fetchBannerAsync());
  };

  /**
   * Handles clearing error state
   */
  const handleClearError = () => {
    dispatch(clearError());
  };

  /**
   * Handles banner data changes from table component
   */
  const handleDataChange = (newData: BannerConfig) => {
    dispatch(setBannerData(newData));
  };

  /**
   * Handles validation state changes from grid component
   */
  const handleValidationChange = (valid: boolean) => {
    setIsValid(valid);
    setHasValidationErrors(!valid);
  };

  /**
   * Handles opening the media modal for adding banners
   */
  const handleAddBanner = () => {
    setIsMediaModalOpen(true);
  };

  /**
   * Handles banner selection from media modal
   */
  const handleBannerSelected = (banner: BannerItem) => {
    if (data) {
      const newBanners = [...data.banners, banner];
      const newData = { banners: newBanners };
      dispatch(setBannerData(newData));
      toast.success("Đã thêm banner mới");
    }
  };

  /**
   * Renders action buttons for the header
   */
  const renderActionButtons = () => (
    <div className="flex items-center gap-2">
      <Button
        variant="outline"
        onClick={handleAddBanner}
        disabled={loading}
        size="sm"
      >
        <Plus className="h-4 w-4 mr-2" />
        Thêm banner
      </Button>

      <Button
        onClick={handleSave}
        disabled={!isDirty || saving || !isValid}
        size="sm"
        className="min-w-[100px]"
      >
        {saving ? (
          <>
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            Đang lưu...
          </>
        ) : (
          <>
            <Save className="h-4 w-4 mr-2" />
            Lưu
          </>
        )}
      </Button>
    </div>
  );

  return (
    <>
      <BannerPageLayout
        metadata={BANNER_METADATA}
        loading={loading}
        saving={saving}
        error={error}
        isDirty={isDirty && isValid}
        hasValidationErrors={hasValidationErrors}
        onSave={handleSave}
        onRefresh={handleRefresh}
        onClearError={handleClearError}
        actionButtons={renderActionButtons()}
      >
        <BannerGrid
          data={data}
          loading={loading}
          onDataChange={handleDataChange}
          onValidationChange={handleValidationChange}
        />
      </BannerPageLayout>

      <BannerMediaModal
        isOpen={isMediaModalOpen}
        onClose={() => setIsMediaModalOpen(false)}
        onSelectBanner={handleBannerSelected}
        existingBanners={data?.banners || []}
      />
    </>
  );
};

export default BannerManagementPage;
