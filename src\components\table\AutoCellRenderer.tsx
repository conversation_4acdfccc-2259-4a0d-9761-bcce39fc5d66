import { JSX } from "react";
import {
  CellComponentPropsMap,
  CellComponentRegistry,
  CellConfig,
} from "./registry";
import { Post } from "@/features/post/states/types";
import { Album } from "@/features/media/images/states/types";

interface AutoCellRendererProps<T> {
  config: CellConfig;
  value: T[keyof T];
  normalizedValue: string;
  row: T;
  accessorKey: keyof T;
  onAction?: (action: string, row: T) => void;
  onChange?: (row: T, accessorKey: keyof T, value: T[keyof T]) => void;
  onUpdate?: (oldRow: T, newRow: T) => void;
}

export const AutoCellRenderer = <T,>({
  config,
  value,
  normalizedValue,
  row,
  accessorKey,
  onAction,
  onChange,
}: AutoCellRendererProps<T>): JSX.Element => {
  switch (config.component) {
    case "Badge":
      return (
        <CellComponentRegistry.Badge
          {...config.props}
          value={value as CellComponentPropsMap["Badge"]["value"]}
        />
      );

    case "Avatar":
      return (
        <CellComponentRegistry.Avatar
          {...config.props}
          value={value as CellComponentPropsMap["Avatar"]["value"]}
        />
      );

    case "SimpleCheckbox":
      return (
        <CellComponentRegistry.SimpleCheckbox
          {...config.props}
          value={value as CellComponentPropsMap["SimpleCheckbox"]["value"]}
        />
      );

    case "ActionCell":
      return (
        <CellComponentRegistry.ActionCell
          {...config.props}
          row={row}
          onAction={onAction}
        />
      );
    case "ActionCheckbox":
      return (
        <CellComponentRegistry.ActionCheckbox
          {...config.props}
          value={value as CellComponentPropsMap["ActionCheckbox"]["value"]}
          row={row}
          accessorKey={accessorKey}
          onChange={onChange}
        />
      );
    case "IconCell":
      return (
        <CellComponentRegistry.IconCell
          {...config.props}
          value={value as CellComponentPropsMap["IconCell"]["value"]}
        />
      );
    case "TextCell":
      return (
        <CellComponentRegistry.TextCell
          {...config.props}
          value={value as CellComponentPropsMap["TextCell"]["value"]}
        />
      );

    case "DateTimeCell":
      return (
        <CellComponentRegistry.DateTimeCell
          {...config.props}
          value={value as CellComponentPropsMap["DateTimeCell"]["value"]}
        />
      );
    case "ImageCell":
      return (
        <CellComponentRegistry.ImageCell
          {...config.props}
          value={value as CellComponentPropsMap["ImageCell"]["value"]}
        />
      );
    case "PostBadge":
      return (
        <CellComponentRegistry.PostBadge
          {...config.props}
          value={value as CellComponentPropsMap["PostBadge"]["value"]}
        />
      );
    case "PostAction":
      return (
        <CellComponentRegistry.PostAction {...config.props} row={row as Post} />
      );
    case "AlbumBadge":
      return (
        <CellComponentRegistry.AlbumBadge
          {...config.props}
          value={value as CellComponentPropsMap["AlbumBadge"]["value"]}
        />
      );
    case "AlbumAction":
      return (
        <CellComponentRegistry.AlbumAction
          {...config.props}
          row={row as Album}
        />
      );
    default:
      return <div className="text-center">{normalizedValue}</div>;
  }
};
