import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Album } from "../../states/types";
import { AlbumBadge } from "../AlbumBadge";
import { EditAlbumDialog } from "../EditAlbumDialog";

interface AlbumInfoProps {
  album: Album;
}

export function AlbumInfo({ album }: AlbumInfoProps) {
  return (
    <Card>
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center justify-between mb-3">
              <CardTitle className="text-lg">{album.name}</CardTitle>
              <EditAlbumDialog album={album} />
            </div>
            <div className="flex items-center gap-4 mb-4">
              <AlbumBadge value={album.status} />
              <Badge variant="outline">{album.type}</Badge>
            </div>
            {album.description?.text && (
              <p className="text-muted-foreground text-sm mb-4">
                {album.description.text}
              </p>
            )}
          </div>
        </div>
      </CardHeader>
    </Card>
  );
}
