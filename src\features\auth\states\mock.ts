import type { FormConfig } from "@/form/types";

export const LoginFormConfig: FormConfig = {
  code: "login-form",
  name: "<PERSON><PERSON>ng nhập",
  note: "<PERSON>iểu mẫu để người dùng đăng nhập",
  config: {
    id: "form-root",
    type: "frame",
    styles: {
      container: "space-y-4 p-8 bg-white rounded-lg shadow-md",
    },
    children: [
      {
        id: "frame-header",
        type: "frame",
        styles: {
          container: "flex flex-col items-center justify-center",
        },
        children: [
          {
            id: "login-title",
            type: "title",
            styles: {
              content: "text-4xl font-bold text-primary",
            },
            properties: {
              text: "Đăng nhập",
            },
          },
        ],
      },
      {
        id: "frame-content",
        type: "frame",
        styles: {
          container: "space-y-4 p-8",
        },
        children: [
          {
            id: "userName",
            type: "field",
            styles: {
              container: "grid-cols-[120px_1fr]",
              label: "text-base",
              field: "w-64 h-10",
            },
            field: {
              objectKey: "userName",
              defaultValue: "",
              dataType: "string",
              component: "TextInput",
            },
            properties: {
              label: "Tên đăng nhập",
              placeholder: "Nhập tên đăng nhập",
            },
            validation: {
              required: { value: true, error: "Tên đăng nhập là bắt buộc" },
              minLength: {
                value: 1,
                error: "Tên đăng nhập phải có ít nhất 1 ký tự",
              },
              maxLength: {
                value: 100,
                error: "Tên đăng nhập tối đa 100 ký tự",
              },
            },
          },
          {
            id: "password",
            type: "field",
            styles: {
              container: "grid-cols-[120px_1fr]",
              label: "text-base",
              field: "w-64 h-10",
            },
            field: {
              objectKey: "password",
              defaultValue: "",
              dataType: "string",
              component: "PasswordInput",
            },
            properties: {
              label: "Mật khẩu",
              placeholder: "Nhập mật khẩu",
            },
            validation: {
              required: { value: true, error: "Mật khẩu là bắt buộc" },
              minLength: {
                value: 1,
                error: "Mật khẩu phải có ít nhất 1 ký tự",
              },
              maxLength: { value: 100, error: "Mật khẩu tối đa 100 ký tự" },
            },
          },
        ],
      },
      {
        id: "frame-footer",
        type: "frame",
        styles: {
          container: "flex flex-col items-center gap-y-4",
        },
        children: [
          {
            id: "login-submit",
            type: "control",
            styles: {
              container: "w-64 h-10",
              content: "h-10 text-base font-bold",
            },
            properties: {
              controlType: "submit",
              text: "Đăng nhập",
              icon: "ArrowUpFromLine",
              size: "lg",
            },
          },
          {
            id: "login-reset",
            type: "redirect",
            styles: {
              container: "text-base",
            },
            properties: {
              url: "/auth/register",
              prefix: "Bạn chưa có tài khoản?",
              suffix: "Đăng ký",
            },
          },
        ],
      },
    ],
  },
};
export const RegisterFormConfig: FormConfig = {
  code: "register-form",
  name: "Đăng ký",
  note: "Biểu mẫu để người dùng đăng ký",
  config: {
    id: "form-root",
    type: "frame",
    styles: {
      container: "space-y-4 p-8 bg-white rounded-lg shadow-md",
    },
    children: [
      {
        id: "frame-header",
        type: "frame",
        styles: {
          container: "flex flex-col items-center justify-center",
        },
        children: [
          {
            id: "register-title",
            type: "title",
            styles: {
              content: "text-4xl font-bold text-primary",
            },
            properties: {
              text: "Đăng ký",
            },
          },
        ],
      },
      {
        id: "frame-content",
        type: "frame",
        styles: {
          container: "space-y-4 p-8",
        },
        children: [
          {
            id: "userName",
            type: "field",
            styles: {
              container: "grid-cols-[120px_1fr]",
              label: "text-base",
              field: "w-64 h-10",
            },
            field: {
              objectKey: "userName",
              defaultValue: "",
              dataType: "string",
              component: "TextInput",
            },
            properties: {
              label: "Tên đăng nhập",
              placeholder: "Nhập tên đăng nhập",
            },
            validation: {
              required: { value: true, error: "Tên đăng nhập là bắt buộc" },
              minLength: {
                value: 1,
                error: "Tên đăng nhập phải có ít nhất 1 ký tự",
              },
              maxLength: {
                value: 100,
                error: "Tên đăng nhập tối đa 100 ký tự",
              },
              pattern: {
                value: "[a-zA-Z]",
                error: "Tên đăng nhập chỉ gồm ký tự a-z, A-Z",
              },
            },
          },
          {
            id: "fullName",
            type: "field",
            styles: {
              container: "grid-cols-[120px_1fr]",
              label: "text-base",
              field: "w-64 h-10",
            },
            field: {
              objectKey: "fullName",
              defaultValue: "",
              dataType: "string",
              component: "TextInput",
            },
            properties: {
              label: "Họ và tên",
              placeholder: "Nhập họ và tên",
            },
            validation: {
              required: { value: true, error: "Họ và tên là bắt buộc" },
              minLength: {
                value: 1,
                error: "Họ và tên phải có ít nhất 1 ký tự",
              },
              maxLength: { value: 100, error: "Họ và tên tối đa 100 ký tự" },
            },
          },
          {
            id: "email",
            type: "field",
            styles: {
              container: "grid-cols-[120px_1fr]",
              label: "text-base",
              field: "w-64 h-10",
            },
            field: {
              objectKey: "email",
              defaultValue: "",
              dataType: "string",
              component: "TextInput",
            },
            properties: {
              label: "Email",
              placeholder: "Nhập email",
            },
            validation: {
              required: { value: true, error: "Email là bắt buộc" },
              minLength: { value: 1, error: "Email phải có ít nhất 1 ký tự" },
              maxLength: { value: 100, error: "Email tối đa 100 ký tự" },
              pattern: {
                value: "^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$",
                error: "Email không đúng định dạng",
              },
            },
          },
          {
            id: "phone",
            type: "field",
            styles: {
              container: "grid-cols-[120px_1fr]",
              label: "text-base",
              field: "w-64 h-10",
            },
            field: {
              objectKey: "phone",
              defaultValue: "",
              dataType: "string",
              component: "TextInput",
            },
            properties: {
              label: "Số điện thoại",
              placeholder: "Nhập số điện thoại",
            },
            validation: {
              required: { value: false, error: "Số điện thoại là bắt buộc" },
              minLength: {
                value: 1,
                error: "Số điện thoại phải có ít nhất 1 ký tự",
              },
              maxLength: {
                value: 100,
                error: "Số điện thoại tối đa 100 ký tự",
              },
              // Có thể thêm pattern phone nếu muốn
            },
          },
          {
            id: "password",
            type: "field",
            styles: {
              container: "grid-cols-[120px_1fr]",
              label: "text-base",
              field: "w-64 h-10",
            },
            field: {
              objectKey: "password",
              defaultValue: "",
              dataType: "string",
              component: "PasswordInput",
            },
            properties: {
              label: "Mật khẩu",
              placeholder: "Nhập mật khẩu",
            },
            validation: {
              required: { value: true, error: "Mật khẩu là bắt buộc" },
              minLength: {
                value: 8,
                error: "Mật khẩu phải có ít nhất 8 ký tự",
              },
              maxLength: { value: 64, error: "Mật khẩu tối đa 64 ký tự" },
              pattern: {
                value: "^(?=.*[a-z])(?=.*[A-Z])(?=.*[\\W_]).{8,}$",
                error: "Mật khẩu phải có ít nhất 8 ký tự, bao gồm chữ hoa, chữ thường và ký tự đặc biệt",
              },
            },
          },
          {
            id: "repassword",
            type: "field",
            styles: {
              container: "grid-cols-[120px_1fr]",
              label: "text-base",
              field: "w-64 h-10",
            },
            field: {
              objectKey: "repassword",
              defaultValue: "",
              dataType: "string",
              component: "PasswordInput",
            },
            properties: {
              label: "Nhập lại mật khẩu",
              placeholder: "Nhập lại mật khẩu",
            },
            validation: {
              required: { value: true, error: "Vui lòng nhập lại mật khẩu" },
              minLength: {
                value: 1,
                error: "Nhập lại mật khẩu phải có ít nhất 1 ký tự",
              },
              maxLength: {
                value: 100,
                error: "Nhập lại mật khẩu tối đa 100 ký tự",
              },
              matchField: {
                value: "password",
                error: "Mật khẩu không trùng khớp",
              },
            },
          },
        ],
      },
      {
        id: "frame-footer",
        type: "frame",
        styles: {
          container: "flex flex-col items-center gap-y-4",
        },
        children: [
          {
            id: "register-submit",
            type: "control",
            styles: {
              container: "w-64 h-10",
              content: "h-10 text-base font-bold",
            },
            properties: {
              controlType: "submit",
              text: "Đăng ký",
              icon: "UserPlus2",
              size: "lg",
            },
          },
          {
            id: "register-reset",
            type: "redirect",
            styles: {
              container: "text-base",
            },
            properties: {
              url: "/auth/login",
              prefix: "Đã có tài khoản?",
              suffix: "Đăng nhập",
            },
          },
        ],
      },
    ],
  },
};
