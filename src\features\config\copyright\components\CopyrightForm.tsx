import React, { useState, useEffect, useCallback } from "react";
import { Textarea } from "@/components/ui/textarea";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import { z } from "zod";

import type { CopyrightConfig, CopyrightFormProps } from "../states/type";
import { copyrightConfigSchema } from "../states/validation";

/**
 * CopyrightForm component for managing copyright configuration data
 * Provides a minimal textarea interface for copyright content
 */
export const CopyrightForm: React.FC<CopyrightFormProps> = ({
  data,
  loading,
  onDataChange,
  onValidationChange,
}) => {
  // ============================================================================
  // State Management
  // ============================================================================
  const [content, setContent] = useState("");
  const [validationError, setValidationError] = useState<string | null>(null);

  // ============================================================================
  // Data Processing
  // ============================================================================

  /**
   * Initialize content when data changes
   */
  useEffect(() => {
    if (data) {
      setContent(data.content || "");
    } else {
      setContent("");
    }
  }, [data]);

  // ============================================================================
  // Validation Functions
  // ============================================================================

  /**
   * Validates the copyright content
   */
  const validateContent = useCallback((contentValue: string) => {
    try {
      copyrightConfigSchema.parse({ content: contentValue });
      setValidationError(null);
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const firstError = error.errors[0];
        setValidationError(firstError?.message || "Lỗi xác thực");
        return false;
      }
      setValidationError("Lỗi xác thực không xác định");
      return false;
    }
  }, []);

  /**
   * Validate content whenever it changes
   */
  useEffect(() => {
    const isValid = validateContent(content);

    // Notify parent component about validation state
    if (onValidationChange) {
      onValidationChange(isValid);
    }
  }, [content, validateContent, onValidationChange]);

  // ============================================================================
  // Event Handlers
  // ============================================================================

  /**
   * Handles content change
   */
  const handleContentChange = useCallback((value: string) => {
    setContent(value);

    // Update parent component with new data
    const newData: CopyrightConfig = { content: value };
    onDataChange(newData);
  }, [onDataChange]);

  // ============================================================================
  // Loading State
  // ============================================================================

  if (loading && !data) {
    return (
      <div className="text-center text-muted-foreground py-4">
        Đang tải dữ liệu...
      </div>
    );
  }

  // ============================================================================
  // Main Render
  // ============================================================================

  return (
    <div className="space-y-4">
      <Textarea
        placeholder="Nhập nội dung bản quyền..."
        value={content}
        onChange={(e) => handleContentChange(e.target.value)}
        disabled={loading}
        className={`min-h-[120px] resize-y ${validationError ? 'border-red-500 focus:border-red-500' : ''
          }`}
      />

      {/* Validation Error */}
      {validationError && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{validationError}</AlertDescription>
        </Alert>
      )}
    </div>
  );
};
