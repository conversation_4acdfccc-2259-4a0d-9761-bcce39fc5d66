import React, { useRef } from "react";
import { ActionAutoForm, ActionAutoFormRef } from "./ActionAutoForm";
import { CreateActionRequest, Action } from "../states/types";
import { createActionApi, updateActionApi, deleteActionApi } from "../states/api";
import { useAppSelector, useAppDispatch } from "@/store/rootReducer";
import { selectCurrentAction } from "../states/selectors";
import { setCurrentAction } from "../states/slices";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Save, Trash2, X, Edit } from "lucide-react";
import {
  Dialog,
  DialogTrigger,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";

interface ActionCreateFormProps {
  mode: "create" | "edit" | "view";
  onActionSaved?: (action: Action) => void;
  onActionChange?: (actionType: "create" | "edit" | "view") => void;
  onDeleted?: () => void;
}

export const ActionCreateForm: React.FC<ActionCreateFormProps> = ({
  mode,
  onActionSaved,
  onActionChange,
  onDeleted,
}) => {
  const dispatch = useAppDispatch();
  const currentAction = useAppSelector(selectCurrentAction);
  const formRef = useRef<ActionAutoFormRef>(null);
  const [confirmDelete, setConfirmDelete] = React.useState(false);

  const handleSubmit = async (data: CreateActionRequest) => {
    try {
      let response;

      if (mode === "create") {
        response = await createActionApi(data);
        toast.success("Tạo quyền thành công!");
      } else if (mode === "edit" && currentAction) {
        // Convert to Partial<Action> for update API
        const updateData: Partial<Action> = {
          code: data.code,
          name: data.name,
          metadata: data.metadata || {},
        };
        response = await updateActionApi(currentAction.id, updateData);
        toast.success("Cập nhật quyền thành công!");
      } else {
        throw new Error("Invalid mode or missing current action");
      }

      if (response.status === 200 && response.data.data) {
        const action = response.data.data as Action;
        dispatch(setCurrentAction(action));
        onActionSaved?.(action);
      }
    } catch (error) {
      console.error("Error saving action:", error);
      toast.error(
        mode === "create"
          ? "Có lỗi xảy ra khi tạo quyền"
          : "Có lỗi xảy ra khi cập nhật quyền"
      );
    }
  };

  const initialData: CreateActionRequest =
    mode === "edit" && currentAction
      ? {
          code: currentAction.code,
          name: currentAction.name,
          metadata: currentAction.metadata,
        }
      : {
          code: "",
          name: "",
          metadata: null,
        };

  const handleFormSubmit = () => {
    if (formRef.current) {
      formRef.current.submitForm();
    }
  };

  const handleDelete = async () => {
    if (!currentAction || mode !== "edit") return;

    try {
      const response = await deleteActionApi(currentAction.id);
      
      if (response.status === 200) {
        toast.success("Xóa quyền thành công!");
        dispatch(setCurrentAction(null));
        onDeleted?.(); // Navigate back to list
      }
    } catch (error) {
      console.error("Error deleting action:", error);
      toast.error("Có lỗi xảy ra khi xóa quyền");
    } finally {
      setConfirmDelete(false);
    }
  };

  const handleCancel = () => {
    if (mode === "create") {
      onDeleted?.(); // Go back to list
    } else {
      onActionChange?.("view"); // Go back to view mode
    }
  };

  const isEditing = mode === "edit" || mode === "create";
  const isCreating = mode === "create";

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle>
            {isCreating ? "Tạo quyền mới" : "Thông tin quyền"}
          </CardTitle>
          {!isEditing && currentAction && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => onActionChange?.("edit")}
            >
              <Edit className="h-4 w-4 mr-2" />
              Chỉnh sửa
            </Button>
          )}
        </div>
      </CardHeader>

      <CardContent>
        {!currentAction && mode !== "create" ? (
          <div className="text-center py-8 text-gray-500">
            Chọn tạo mới hoặc chọn một quyền để xem thông tin
          </div>
        ) : (
          <div className="space-y-4">
            <ActionAutoForm
              ref={formRef}
              initialData={initialData}
              onSubmit={handleSubmit}
              viewOnly={!isEditing}
            />
            
            {/* Action Buttons */}
            {isEditing && (
              <div className="flex justify-end gap-2 pt-4 border-t">
                <Button variant="outline" onClick={handleCancel}>
                  <X className="w-4 h-4 mr-2" />
                  Hủy
                </Button>
                
                <Button onClick={handleFormSubmit}>
                  <Save className="w-4 h-4 mr-2" />
                  {isCreating ? "Tạo mới" : "Lưu"}
                </Button>
                
                {mode === "edit" && currentAction && (
                  <Dialog open={confirmDelete} onOpenChange={setConfirmDelete}>
                    <DialogTrigger asChild>
                      <Button variant="destructive">
                        <Trash2 className="w-4 h-4 mr-2" />
                        Xóa
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Xác nhận xóa</DialogTitle>
                      </DialogHeader>
                      <p className="text-sm text-muted-foreground">
                        Bạn có chắc chắn muốn xóa quyền "{currentAction?.name}"?
                        Hành động này không thể hoàn tác.
                      </p>
                      <DialogFooter>
                        <Button
                          variant="outline"
                          onClick={() => setConfirmDelete(false)}
                        >
                          Hủy
                        </Button>
                        <Button
                          variant="destructive"
                          onClick={handleDelete}
                        >
                          Xác nhận xóa
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                )}
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};
