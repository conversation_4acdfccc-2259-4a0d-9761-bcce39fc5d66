import React from "react";
import { Badge } from "@/components/ui/badge";
import { FolderOpen, Plus } from "lucide-react";
import { Post } from "@/features/post/states/types";
import { CategoryDTO } from "@/features/category/states/types";
import { CurrentLinkedPageCard } from "./CurrentLinkedPageCard";
import { SearchPageCard } from "./SearchPageCard";

interface PageManagementPanelProps {
  selectedCategoryId: number | null;
  categories: CategoryDTO[];
  hasLinkedPage: boolean;
  linkedPage: Post | null;
  loading: boolean;
  onEdit: () => void;
  onUnlink: () => void;
  onDelete: (pageId: number, pageTitle: string) => void;
  onCreatePage: () => void;
  onLinkPage: (pageId: number) => void;
}

export const PageManagementPanel: React.FC<PageManagementPanelProps> = ({
  selectedCategoryId,
  categories,
  hasLinkedPage,
  linkedPage,
  loading,
  onEdit,
  onUnlink,
  onDelete,
  onCreatePage,
  onLinkPage,
}) => {
  if (!selectedCategoryId) {
    return (
      <div className="text-center text-gray-500 mt-20">
        <FolderOpen size={48} className="mx-auto mb-4 opacity-50" />
        <p>Chọn một chuyên mục từ cây thư mục bên trái</p>
      </div>
    );
  }

  const selectedCategory = categories.find((c) => c.id === selectedCategoryId);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold">
          Quản lý trang cho: {selectedCategory?.name}
        </h2>
        <Badge variant={hasLinkedPage ? "default" : "secondary"}>
          {hasLinkedPage ? "Đã có trang liên kết" : "Chưa có trang liên kết"}
        </Badge>
      </div>

      {/* Current Linked Page or Create New Page */}
      {hasLinkedPage ? (
        linkedPage ? (
          <CurrentLinkedPageCard
            linkedPage={linkedPage}
            onEdit={onEdit}
            onUnlink={onUnlink}
            onDelete={onDelete}
          />
        ) : (
          <div className="p-6 border rounded-lg bg-gray-50">
            <div className="flex items-center justify-center">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
              <span className="ml-2 text-gray-600">
                Đang tải thông tin trang...
              </span>
            </div>
          </div>
        )
      ) : (
        <div className="p-6 border-2 border-dashed border-gray-300 rounded-lg text-center">
          <div className="space-y-4">
            <div className="w-16 h-16 mx-auto bg-blue-100 rounded-full flex items-center justify-center">
              <Plus className="w-8 h-8 text-blue-600" />
            </div>
            <h3 className="text-lg font-medium text-gray-900">
              Chưa có trang liên kết
            </h3>
            <p className="text-gray-500 max-w-sm mx-auto">
              Chuyên mục này chưa có trang liên kết. Bạn có thể tạo trang mới
              hoặc liên kết với trang có sẵn.
            </p>
            <button
              onClick={onCreatePage}
              disabled={loading}
              className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Tạo trang mới
            </button>
          </div>
        </div>
      )}

      {/* Search and Link Existing Page */}
      <SearchPageCard hasLinkedPage={hasLinkedPage} onLinkPage={onLinkPage} />
    </div>
  );
};
