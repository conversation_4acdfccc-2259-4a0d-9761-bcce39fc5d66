import { createSelector } from "@reduxjs/toolkit";
import type { RootState } from "@/store/rootReducer";
import type { PortalLinkState } from "./type";

// ============================================================================
// Base Selectors
// ============================================================================

/**
 * Selects the portal link state from the root state
 */
export const selectPortalLinkState = (state: RootState): PortalLinkState =>
  state.portalLink;

// ============================================================================
// Data Selectors
// ============================================================================

/**
 * Selects portal link configuration data
 */
export const selectPortalLinkData = createSelector(
  [selectPortalLinkState],
  (portalLinkState) => portalLinkState.data
);

/**
 * Selects portal link saved data (for preview)
 */
export const selectPortalLinkSavedData = createSelector(
  [selectPortalLinkState],
  (portalLinkState) => portalLinkState.savedData
);

/**
 * Selects portal link loading state
 */
export const selectPortalLinkLoading = createSelector(
  [selectPortalLinkState],
  (portalLinkState) => portalLinkState.loading
);

/**
 * Selects portal link saving state
 */
export const selectPortalLinkSaving = createSelector(
  [selectPortalLinkState],
  (portalLinkState) => portalLinkState.saving
);

/**
 * Selects portal link error state
 */
export const selectPortalLinkError = createSelector(
  [selectPortalLinkState],
  (portalLinkState) => portalLinkState.error
);

/**
 * Selects whether portal link data has unsaved changes
 */
export const selectPortalLinkIsDirty = createSelector(
  [selectPortalLinkState],
  (portalLinkState) => portalLinkState.isDirty
);

// ============================================================================
// Utility Selectors
// ============================================================================

/**
 * Selects whether any operation is in progress
 */
export const selectPortalLinkIsOperating = createSelector(
  [selectPortalLinkLoading, selectPortalLinkSaving],
  (loading, saving) => loading || saving
);
