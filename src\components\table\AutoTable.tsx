import React, { useMemo, memo } from "react";
import {
  useReactTable,
  getCoreRowModel,
  getPaginationRowModel,
  flexRender,
  ColumnDef,
  PaginationState,
} from "@tanstack/react-table";
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from "@/components/ui/table";
import { ColumnConfig, CellConfig } from "./registry";
import { AutoCellRenderer } from "./AutoCellRenderer";
import { PaginationBar } from "./pagination/PaginationBar";
import { getNestedValue, normalizeValue } from "./helper";

export interface PaginationProps {
  pageIndex: number;
  pageSize: number;
  totalCount: number;
  onPageChange: (newIndex: number) => void;
  onPageSizeChange: (newSize: number) => void;
}

interface AutoTableProps<T extends object> {
  columns: ColumnConfig[];
  data: T[];
  pagination?: PaginationProps;
  onAction?: (action: string, row: T) => void;
  onChange?: (row: T, accessorKey: keyof T, value: T[keyof T]) => void;
  loading?: boolean;
  loadingComponent?: React.ReactNode;
  error?: Error | null;
  onError?: (error: Error) => void;
  emptyStateMessage?: string;
  emptyStateComponent?: React.ReactNode;
  "aria-label"?: string;
}

// Empty state component
const EmptyState = memo(
  ({
    colSpan,
    message = "Không có dữ liệu để hiển thị",
    customComponent,
  }: {
    colSpan: number;
    message?: string;
    customComponent?: React.ReactNode;
  }) => (
    <TableRow>
      <TableCell colSpan={colSpan} className="text-center py-8 text-gray-500">
        {customComponent || message}
      </TableCell>
    </TableRow>
  )
);

EmptyState.displayName = "EmptyState";

// Default loading component
const DefaultLoadingComponent = () => (
  <div className="flex items-center justify-center py-8">
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
  </div>
);

// Generic component function
function AutoTableComponent<T extends object>({
  columns,
  data,
  pagination,
  onAction,
  onChange,
  loading = false,
  loadingComponent,
  error,
  onError,
  emptyStateMessage,
  emptyStateComponent,
  "aria-label": ariaLabel = "Data table",
}: AutoTableProps<T>) {
  // Memoize visible columns
  const visibleColumns = useMemo(
    () => columns.filter((col) => col.visible !== false),
    [columns]
  );

  // Memoize table columns configuration
  const tableColumns = useMemo<ColumnDef<T>[]>(
    () =>
      visibleColumns.map((col) => {
        const base: ColumnDef<T> = {
          accessorKey: col.accessorKey,
          header: col.header,
        };

        if (col.cell) {
          base.cell = ({ row }) => {
            const value = getNestedValue(row.original, col.accessorKey);
            const normalizedValue = normalizeValue(value);

            return (
              <AutoCellRenderer<T>
                config={col.cell as CellConfig}
                value={value}
                row={row.original}
                normalizedValue={normalizedValue}
                onAction={onAction}
                accessorKey={col.accessorKey as keyof T}
                onChange={onChange}
              />
            );
          };
        }

        return base;
      }),
    [visibleColumns, onAction, onChange]
  );

  // Memoize pagination state
  const paginationState = useMemo<PaginationState>(
    () => ({
      pageIndex: pagination?.pageIndex ?? 0,
      pageSize: pagination?.pageSize ?? 10,
    }),
    [pagination?.pageIndex, pagination?.pageSize]
  );

  // Calculate page count
  const pageCount = useMemo(() => {
    if (pagination && pagination.totalCount > 0) {
      return Math.ceil(pagination.totalCount / pagination.pageSize);
    }
    return Math.max(1, Math.ceil(data.length / paginationState.pageSize));
  }, [pagination, data.length, paginationState.pageSize]);

  // Initialize table instance
  const table = useReactTable({
    data,
    columns: tableColumns,
    state: {
      pagination: paginationState,
    },
    manualPagination: !!pagination,
    pageCount,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
  });

  // Handle error state
  if (error) {
    if (onError) {
      onError(error);
    }
    return (
      <div className="w-full rounded-md border p-4 text-red-500">
        Error: {error.message}
      </div>
    );
  }

  // Handle loading state
  if (loading) {
    return (
      <div className="w-full rounded-md border">
        {loadingComponent || <DefaultLoadingComponent />}
      </div>
    );
  }

  const rows = table.getRowModel().rows;
  const hasData = rows.length > 0;

  return (
    <div className="w-full rounded-md border">
      <Table role="table" aria-label={ariaLabel}>
        <TableHeader role="rowgroup">
          {table.getHeaderGroups().map((hg) => (
            <TableRow key={hg.id} role="row">
              {hg.headers.map((header) => (
                <TableHead key={header.id} role="columnheader">
                  {flexRender(
                    header.column.columnDef.header,
                    header.getContext()
                  )}
                </TableHead>
              ))}
            </TableRow>
          ))}
        </TableHeader>
        <TableBody role="rowgroup">
          {!hasData ? (
            <EmptyState
              colSpan={visibleColumns.length}
              message={emptyStateMessage}
              customComponent={emptyStateComponent}
            />
          ) : (
            rows.map((row) => (
              <TableRow key={row.id} role="row">
                {row.getVisibleCells().map((cell) => (
                  <TableCell key={cell.id} role="cell">
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </TableCell>
                ))}
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>

      {pagination && hasData && (
        <PaginationBar
          pageIndex={pagination.pageIndex}
          pageCount={pageCount}
          pageSize={pagination.pageSize}
          pageSizeOptions={[10, 20, 50, 100]}
          dataLength={data.length}
          onPageChange={pagination.onPageChange}
          onPageSizeChange={pagination.onPageSizeChange}
        />
      )}
    </div>
  );
}

// Export với memo và giữ generic type
export const AutoTable = memo(AutoTableComponent) as typeof AutoTableComponent;
