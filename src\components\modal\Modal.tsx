import { X } from "lucide-react";
import React from "react";

interface ModalProps {
  isOpen: boolean;
  children: React.ReactNode;
  onClose?: () => void; // Add an onClose prop for handling the close action
}

export const Modal: React.FC<ModalProps> = ({ isOpen, children, onClose }) => {
  if (!isOpen) {
    return null;
  }

  const handleClose = () => {
    onClose?.(); // Call the onClose function if provided
  };

  return (
    <div className="fixed inset-0 z-50 p-4 flex w-full items-center justify-center bg-black/60 overflow-y-auto scrolling-touch">
      <div className="relative bg-white rounded-lg">
        {/* Close Button */}
        {onClose && (
          <X
            onClick={handleClose}
            size={20}
            className="z-50 absolute bg-accent top-2 right-2 text-base rounded-full cursor-pointer hover:bg-foreground/10 transition-all duration-200 ease-in-out "
          ></X>
        )}
        {/* Modal Content */}
        <div>{children}</div>
      </div>
    </div>
  );
};
