import { useEffect, useCallback, useMemo } from "react";
import { useAppDispatch, useAppSelector } from "@/store/rootReducer";
import { CategoryType, CategoryDTO } from "../states/types";
import {
  selectCategoryByType,
  selectCategoryLoadingByType,
  selectCategoryErrorByType,
  selectCategoryNeedsRefreshByType,
  selectCategoryHasDataByType,
  selectCategoryIsFreshByType,
} from "../states/selector";
import { fetchCategoryByType } from "../states/slices";

export interface UseCategoryDataOptions {
  /** Loại category cần fetch */
  type: CategoryType;
  /** Có tự động fetch khi component mount không */
  autoFetch?: boolean;
  /** Có force refresh không, bỏ qua cache */
  forceRefresh?: boolean;
  /** Dependency array để trigger re-fetch */
  deps?: React.DependencyList;
}

export interface UseCategoryDataReturn {
  /** Dữ liệu categories */
  data: CategoryDTO[];
  /** Trạng thái loading */
  loading: boolean;
  /** Lỗi nếu có */
  error: string | null;
  /** Có dữ liệu không */
  hasData: boolean;
  /** Dữ liệu có fresh không (trong vòng 5 phút) */
  isFresh: boolean;
  /** Cần refresh không */
  needsRefresh: boolean;
  /** Function để fetch dữ liệu */
  fetchData: (force?: boolean) => void;
  /** Function để refresh dữ liệu */
  refreshData: () => void;
}

/**
 * Hook để quản lý việc fetch category data một cách thông minh
 * - Tự động cache và kiểm tra thời gian
 * - Tránh fetch trùng lặp
 * - Hỗ trợ force refresh
 */
export const useCategoryData = ({
  type,
  autoFetch = true,
  forceRefresh = false,
  deps = [],
}: UseCategoryDataOptions): UseCategoryDataReturn => {
  const dispatch = useAppDispatch();

  // Selectors
  const data = useAppSelector((state) => selectCategoryByType(state, type));
  const loading = useAppSelector((state) =>
    selectCategoryLoadingByType(state, type)
  );
  const error = useAppSelector((state) =>
    selectCategoryErrorByType(state, type)
  );
  const hasData = useAppSelector((state) =>
    selectCategoryHasDataByType(state, type)
  );
  const isFresh = useAppSelector((state) =>
    selectCategoryIsFreshByType(state, type)
  );
  const needsRefresh = useAppSelector((state) =>
    selectCategoryNeedsRefreshByType(state, type)
  );

  // Fetch function
  const fetchData = useCallback(
    (force = false) => {
      dispatch(
        fetchCategoryByType({
          type,
          forceRefresh: force || forceRefresh,
        })
      );
    },
    [dispatch, type, forceRefresh]
  );

  // Refresh function (always force)
  const refreshData = useCallback(() => {
    fetchData(true);
  }, [fetchData]);

  // Memoize deps to prevent constant changes
  const memoizedDeps = useMemo(() => deps, [JSON.stringify(deps)]);

  // Auto fetch effect
  useEffect(() => {
    if (autoFetch && needsRefresh) {
      fetchData();
    }
  }, [autoFetch, needsRefresh, fetchData, memoizedDeps]);

  return {
    data,
    loading,
    error,
    hasData,
    isFresh,
    needsRefresh,
    fetchData,
    refreshData,
  };
};

/**
 * Hook đơn giản để lấy category data, tự động fetch nếu cần
 */
export const useCategory = (type: CategoryType) => {
  return useCategoryData({ type });
};

/**
 * Hook để lấy nhiều loại category cùng lúc
 */
export const useMultipleCategories = (types: CategoryType[]) => {
  // Use memoized types array to prevent dependency changes
  const memoizedTypes = useMemo(() => types, [types.join(",")]);

  // Use individual hooks for each type with conditional fetching
  const publicData = useCategoryData({
    type: "public-menu",
    autoFetch: memoizedTypes.includes("public-menu"),
  });
  const adminData = useCategoryData({
    type: "admin-menu",
    autoFetch: memoizedTypes.includes("admin-menu"),
  });
  const userData = useCategoryData({
    type: "user-menu",
    autoFetch: memoizedTypes.includes("user-menu"),
  });

  const getDataForType = useCallback(
    (type: CategoryType) => {
      switch (type) {
        case "public-menu":
          return publicData;
        case "admin-menu":
          return adminData;
        case "user-menu":
          return userData;
        default:
          return publicData;
      }
    },
    [publicData, adminData, userData]
  );

  // Memoize the return object to prevent recreations
  return useMemo(
    () => ({
      data: memoizedTypes.reduce((acc, type) => {
        acc[type] = getDataForType(type).data;
        return acc;
      }, {} as Record<CategoryType, CategoryDTO[]>),

      loading: memoizedTypes.some((type) => getDataForType(type).loading),

      errors: memoizedTypes.reduce((acc, type) => {
        const error = getDataForType(type).error;
        if (error) {
          acc[type] = error;
        }
        return acc;
      }, {} as Record<CategoryType, string>),

      allLoaded: memoizedTypes.every((type) => getDataForType(type).hasData),

      refreshAll: () => {
        memoizedTypes.forEach((type) => {
          getDataForType(type).refreshData();
        });
      },
    }),
    [memoizedTypes, getDataForType]
  );
};

/**
 * Simplified hook for loading multiple categories with simple loading state
 * Used for routes guard to ensure categories are loaded before building routes
 */
export const useMultipleCategoriesSimple = (types: CategoryType[]) => {
  const dispatch = useAppDispatch();

  // Memoize types to prevent infinite re-renders
  const memoizedTypes = useMemo(() => types, [types.join(",")]);

  useEffect(() => {
    memoizedTypes.forEach((type) => {
      dispatch(fetchCategoryByType({ type, forceRefresh: false }));
    });
  }, [dispatch, memoizedTypes]);

  // Check loading and data states for each type
  const publicLoading = useAppSelector((state) =>
    selectCategoryLoadingByType(state, "public-menu")
  );
  const adminLoading = useAppSelector((state) =>
    selectCategoryLoadingByType(state, "admin-menu")
  );
  const publicData = useAppSelector((state) =>
    selectCategoryByType(state, "public-menu")
  );
  const adminData = useAppSelector((state) =>
    selectCategoryByType(state, "admin-menu")
  );

  // Calculate combined states
  const loading =
    (memoizedTypes.includes("public-menu") && publicLoading) ||
    (memoizedTypes.includes("admin-menu") && adminLoading);

  const allLoaded =
    (memoizedTypes.includes("public-menu")
      ? publicData && publicData.length > 0
      : true) &&
    (memoizedTypes.includes("admin-menu")
      ? adminData && adminData.length > 0
      : true);

  return { loading, allLoaded };
};
