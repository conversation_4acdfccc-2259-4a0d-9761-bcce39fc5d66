// categoryApi.ts

import { API_URL, BaseResponse, restApi } from "@/api/restApi";
import { API_ENDPOINTS } from "@/api/endpoints";
import type { CategoryDTO, CategoryType } from "./types";
import { AxiosResponse } from "axios";

const PUBLIC_API = API_URL + API_ENDPOINTS.PORTAL.PUBLIC.CATEGORIES;
const ADMIN_API = API_URL + API_ENDPOINTS.PORTAL.ADMIN.CATEGORIES;

// GET /portal/v1/categories?type=menu
export async function fetchCategories(
  type?: CategoryType
): Promise<AxiosResponse<BaseResponse<CategoryDTO[]>>> {
  return await restApi.get<BaseResponse<CategoryDTO[]>>(`${PUBLIC_API}`, {
    params: type ? { type } : undefined,
  });
}

// GET /portal/v1/categories/:id
export async function fetchCategory(
  id: number
): Promise<AxiosResponse<BaseResponse<CategoryDTO>>> {
  return await restApi.get<BaseResponse<CategoryDTO>>(
    `${API_URL}${API_ENDPOINTS.PORTAL.PUBLIC.CATEGORIES}/${id}`
  );
}

// POST /portal/v1/categories
export async function createCategory(
  payload: Omit<CategoryDTO, "id" | "createdAt" | "updatedAt">
): Promise<AxiosResponse<BaseResponse<CategoryDTO>>> {
  return await restApi.post<BaseResponse<CategoryDTO>>(`${ADMIN_API}`, payload);
}

// PUT /portal/v1/categories/:id
export async function updateCategory(
  id: number,
  payload: Partial<CategoryDTO>
): Promise<AxiosResponse<BaseResponse<CategoryDTO>>> {
  return await restApi.put<BaseResponse<CategoryDTO>>(
    `${API_URL}${API_ENDPOINTS.PORTAL.ADMIN.CATEGORIES}/${id}`,
    payload
  );
}

// DELETE /portal/v1/categories/:id
export async function deleteCategory(
  id: number
): Promise<AxiosResponse<BaseResponse>> {
  return await restApi.delete<BaseResponse>(
    `${API_URL}${API_ENDPOINTS.PORTAL.ADMIN.CATEGORIES}/${id}`
  );
}

// GET /portal/v1/categories/by-slug/:slug
export async function fetchCategoryBySlug(
  slug: string
): Promise<AxiosResponse<BaseResponse<CategoryDTO>>> {
  return await restApi.get<BaseResponse<CategoryDTO>>(
    `${API_URL}${API_ENDPOINTS.PORTAL.PUBLIC.CATEGORIES}/slug/${slug}`
  );
}

// Link a post to a category
export async function linkPostToCategory(
  categoryId: number,
  postId: number
): Promise<AxiosResponse<BaseResponse>> {
  const requestBody = {
    postId: postId,
  };

  return await restApi.put<BaseResponse>(
    `${API_URL}${API_ENDPOINTS.PORTAL.ADMIN.CATEGORIES}/${categoryId}/post`,
    requestBody
  );
}

// Unlink post from category (send postId: null)
export async function unlinkPostFromCategory(
  categoryId: number
): Promise<AxiosResponse<BaseResponse>> {
  const requestBody = {
    postId: null,
  };

  return await restApi.put<BaseResponse>(
    `${API_URL}${API_ENDPOINTS.PORTAL.ADMIN.CATEGORIES}/${categoryId}/post`,
    requestBody
  );
}
