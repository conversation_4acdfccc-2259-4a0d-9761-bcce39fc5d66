import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Search } from "lucide-react";

const SearchBar = () => {
  return (
    <div className="flex items-center gap-4 w-full max-w-4xl border border-primary/20 rounded-md p-2 bg-white">
      {/* Ô input có icon bên trong */}
      <div className="relative flex-1">
        <Search className="absolute left-4 top-1/2 -translate-y-1/2 text-primary w-5 h-5" />
        <Input
          type="text"
          placeholder="Tìm kiếm thông tin"
          className="w-full pl-10 pr-4 py-2 rounded-full
           border border-primary
           text-accent-foreground
            placeholder-gray-400
           focus-visible:ring-0 
           focus-visible:ring-primary 
           focus-visible:ring-offset-0"
        />
      </div>

      {/* Nút tìm kiếm */}
      <Button type="submit" variant="default">
        T<PERSON><PERSON> kiếm
      </Button>
    </div>
  );
};

export default SearchBar;
