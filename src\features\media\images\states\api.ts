import { restApi, BaseResponse } from "@/api/restApi";
import { API_ENDPOINTS } from "@/api/endpoints";
import type {
  AlbumType,
  AlbumStatus,
  AlbumQueryParams,
  CreateAlbum,
  Album,
  Media,
  MediaQueryParams,
  CreateMediaRequest,
  UpdateMediaRequest,
} from "./types";
import { AxiosResponse } from "axios";

const ADMIN_ALBUMS_URL = API_ENDPOINTS.PORTAL.ADMIN.ALBUMS;
const ADMIN_MEDIA_URL = API_ENDPOINTS.PORTAL.ADMIN.MEDIA;
const ADMIN_MEDIA_IN_ALBUM_URL = API_ENDPOINTS.PORTAL.ADMIN.MEDIA_IN_ALBUM;

/**
 * Fetches albums list with filtering parameters (admin)
 * @param params - Query parameters for filtering albums
 * @returns Promise resolving to album response with pagination info
 */
export async function fetchAlbums(
  params: AlbumQueryParams
): Promise<AxiosResponse<BaseResponse<Album[]>>> {
  return await restApi.get<BaseResponse<Album[]>>(`${ADMIN_ALBUMS_URL}`, {
    params: params,
  });
}

// Lấy 1 album chi tiết (admin)
export async function fetchAlbum(
  id: number
): Promise<AxiosResponse<BaseResponse<Album>>> {
  return await restApi.get<BaseResponse<Album>>(`${ADMIN_ALBUMS_URL}/${id}`);
}

/**
 * Creates a new album (admin)
 * @param payload - Album creation data
 * @returns Promise resolving to created album details
 */
export async function createAlbum(
  payload: CreateAlbum
): Promise<AxiosResponse<BaseResponse<Album>>> {
  return await restApi.post<BaseResponse<Album>>(
    `${ADMIN_ALBUMS_URL}`,
    payload
  );
}

/**
 * Updates an existing album (admin)
 * @param id - Album ID to update
 * @param payload - Album update data
 * @returns Promise resolving to updated album details
 */
export async function updateAlbum(
  id: number,
  payload: {
    name: string;
    type: AlbumType;
    description: { text: string };
    coverImage?: string;
  }
): Promise<AxiosResponse<BaseResponse<Album>>> {
  console.log(
    `Updating album ${id} with payload:`,
    JSON.stringify(payload, null, 2)
  );

  return await restApi.put<BaseResponse<Album>>(
    `${ADMIN_ALBUMS_URL}/${id}`,
    payload
  );
}

/**
 * Updates album status (admin)
 * @param id - Album ID to update
 * @param status - New status to set
 * @returns Promise that resolves when status is updated
 */
export async function updateAlbumStatus(
  id: number,
  status: AlbumStatus
): Promise<void> {
  await restApi.put(`${ADMIN_ALBUMS_URL}/${id}/status/${status}`);
}

/**
 * Approves an album (admin) - uses specific approve endpoint
 * @param id - Album ID to approve
 * @returns Promise that resolves when album is approved
 */
export async function approveAlbum(id: number): Promise<void> {
  await restApi.put(`${ADMIN_ALBUMS_URL}/${id}/approve`);
}

/**
 * Rejects an album (admin) - uses specific reject endpoint
 * @param id - Album ID to reject
 * @returns Promise that resolves when album is rejected
 */
export async function rejectAlbum(id: number): Promise<void> {
  await restApi.put(`${ADMIN_ALBUMS_URL}/${id}/reject`);
}

/**
 * Deletes an album (admin)
 * @param id - Album ID to delete
 * @returns Promise that resolves when album is deleted
 */
export async function deleteAlbum(id: number): Promise<void> {
  await restApi.delete(`${ADMIN_ALBUMS_URL}/${id}`);
}

/**
 * Fetches albums filtered by type (image/video) - admin
 * @param type - Album type to filter by
 * @param params - Optional query parameters
 * @returns Promise resolving to filtered album response
 */
export async function fetchAlbumsByType(
  type: AlbumType,
  params?: Partial<AlbumQueryParams>
): Promise<AxiosResponse<BaseResponse<Album[]>>> {
  const apiParams = {
    type,
    ...params,
  };

  return await restApi.get<BaseResponse<Album[]>>(`${ADMIN_ALBUMS_URL}`, {
    params: apiParams,
  });
}

/**
 * Fetches all media belonging to an album
 * @param albumId - Album ID to fetch media from
 * @param params - Optional query parameters for pagination and search
 * @returns Promise resolving to media response with pagination info
 */
export async function fetchAlbumMedia(
  albumId: number,
  params?: MediaQueryParams
): Promise<AxiosResponse<BaseResponse<Media[]>>> {
  const apiParams = {
    albumId,
    page: params?.page || 0,
    size: params?.size || 20,
    ...(params?.search && { keyword: params.search }),
  };

  return await restApi.get<BaseResponse<Media[]>>(
    `${ADMIN_MEDIA_IN_ALBUM_URL}`,
    {
      params: apiParams,
    }
  );
}

/**
 * Creates new media and adds it to an album
 * @param albumId - Album ID to add media to
 * @param payload - Media creation data (without albumId)
 * @returns Promise resolving to created media response
 */
export async function createMediaInAlbum(
  albumId: number,
  payload: Omit<CreateMediaRequest, "albumId">
): Promise<AxiosResponse<BaseResponse<Media>>> {
  const requestData = {
    ...payload,
    albumId,
  };

  return await restApi.post<BaseResponse<Media>>(
    `${ADMIN_MEDIA_URL}`,
    requestData
  );
}

/**
 * Adds existing media from another album to current album
 * @param albumId - Target album ID
 * @param mediaId - Media ID to add
 * @returns Promise that resolves when media is added
 */
export async function addExistingMediaToAlbum(
  albumId: number,
  mediaId: number
): Promise<void> {
  const payload = {
    albumId,
    mediaId,
  };

  await restApi.post(`${ADMIN_MEDIA_IN_ALBUM_URL}`, payload);
}

/**
 * Updates media information
 * @param mediaId - Media ID to update
 * @param payload - Media update data
 * @returns Promise resolving to updated media response
 */
export async function updateMedia(
  mediaId: number,
  payload: UpdateMediaRequest
): Promise<AxiosResponse<BaseResponse<Media>>> {
  return await restApi.put<BaseResponse<Media>>(
    `${ADMIN_MEDIA_URL}/${mediaId}`,
    payload
  );
}

/**
 * Removes media (will delete from all albums)
 * @param _albumId - Album ID (unused but kept for interface compatibility)
 * @param mediaId - Media ID to remove
 * @returns Promise that resolves when media is removed
 */
export async function removeMediaFromAlbum(
  _albumId: number,
  mediaId: number
): Promise<void> {
  await restApi.delete(`${ADMIN_MEDIA_URL}/${mediaId}`);
}

/**
 * Updates media priority order within an album
 * @param albumId - Album ID containing the media
 * @param mediaId - Media ID to update priority for
 * @param priority - New priority value
 * @returns Promise that resolves when priority is updated
 */
export async function updateMediaPriority(
  albumId: number,
  mediaId: number,
  priority: number
): Promise<void> {
  await restApi.put(`${ADMIN_MEDIA_IN_ALBUM_URL}`, {
    mediaId,
    albumId,
    priority,
  });
}

/**
 * Sets cover image for an album
 * @param albumId - Album ID to set cover image for
 * @param imageUrl - URL of the image to set as cover
 * @returns Promise resolving to updated album details
 */
export async function setCoverImage(
  albumId: number,
  imageUrl: string
): Promise<AxiosResponse<BaseResponse<Album>>> {
  return await restApi.put<BaseResponse<Album>>(
    `${ADMIN_ALBUMS_URL}/${albumId}/cover-image`,
    {
      coverImage: imageUrl,
    }
  );
}

/**
 * Removes cover image from an album
 * @param albumId - Album ID to remove cover image from
 * @returns Promise resolving to updated album details
 */
export async function deleteCoverImage(
  albumId: number
): Promise<AxiosResponse<BaseResponse<Album>>> {
  return await restApi.put<BaseResponse<Album>>(
    `${ADMIN_ALBUMS_URL}/${albumId}/cover-image`,
    {
      coverImage: null,
    }
  );
}
