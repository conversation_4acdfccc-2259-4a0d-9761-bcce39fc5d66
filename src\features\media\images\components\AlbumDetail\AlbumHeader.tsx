import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";

interface AlbumHeaderProps {
  albumName: string;
  onBack: () => void;
}

export function AlbumHeader({ albumName, onBack }: AlbumHeaderProps) {
  return (
    <div className="flex items-center justify-between mb-6">
      <h1 className="text-2xl font-semibold">{albumName}</h1>
      <Button onClick={onBack} className="w-30">
        <ArrowLeft className="w-4 h-4 mr-2" />
        Quay lại
      </Button>
    </div>
  );
}
