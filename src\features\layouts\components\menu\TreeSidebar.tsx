import { useState, useEffect, useCallback } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { MenuTree } from "./MenuTree";
import { CategoryTree } from "@/features/category/states/types";

interface TreeSidebarProps {
  menuList: CategoryTree[];
  onMenuClick: () => void;
  pathPrefix: string; // e.g., "/admin", "/me"
}

export const TreeSidebar: React.FC<TreeSidebarProps> = ({
  menuList,
  onMenuClick,
  pathPrefix,
}) => {
  const [menuMap, setMenuMap] = useState<Record<number, CategoryTree>>({});
  const [selectedId, setSelectedId] = useState<number | null>(null);
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    const map: Record<number, CategoryTree> = {};
    const traverse = (node: CategoryTree) => {
      map[node.id] = node;
      node.children.forEach(traverse);
    };
    menuList.forEach(traverse);
    setMenuMap(map);
  }, [menuList, pathPrefix]);

  const onClick = useCallback(
    (id: number) => {
      const node = menuMap[id];
      if (!node) {
        return;
      }

      setSelectedId(id);

      if (node.description.component) {
        const path = node.slug ? `${pathPrefix}/${node.slug}` : pathPrefix;
        navigate(path);
        onMenuClick();
      }
    },
    [menuMap, navigate, onMenuClick, pathPrefix]
  );

  useEffect(() => {
    const candidates = Object.values(menuMap).filter((node) =>
      location.pathname.startsWith(
        node.slug ? `${pathPrefix}/${node.slug}` : pathPrefix
      )
    );
    if (candidates.length > 0) {
      const current = candidates.reduce((a, b) =>
        (a.slug?.length ?? 0) > (b.slug?.length ?? 0) ? a : b
      );
      setSelectedId(current.id);
    }
  }, [location.pathname, menuMap, pathPrefix]);

  return (
    <div className="w-full md:w-64">
      <MenuTree items={menuList} selectedId={selectedId} onClick={onClick} />
    </div>
  );
};
