import React, { useEffect, useRef } from "react";
import { Checkbox } from "@/components/ui/checkbox";
import { Resource } from "@/features/resource/states/types";
import { Action } from "@/features/action/states/types";

interface PermissionRowProps {
  resource: Resource;
  actions: Action[];
  grantedActionIds: number[];
  isEditMode: boolean;
  onActionToggle: (
    resourceId: number,
    actionId: number,
    checked: boolean
  ) => void;
}

export const PermissionRow: React.FC<PermissionRowProps> = ({
  resource,
  actions,
  grantedActionIds,
  isEditMode,
  onActionToggle,
}) => {
  const selectAllCheckboxRef = useRef<HTMLButtonElement>(null);

  // Filter actions to only show those supported by this resource
  const availableActions = actions.filter((action) =>
    resource.actions.includes(action.id)
  );

  // Calculate select all state
  const grantedActionsInResource = grantedActionIds.filter((id) =>
    resource.actions.includes(id)
  );
  const allSelected =
    grantedActionsInResource.length === availableActions.length &&
    availableActions.length > 0;
  const someSelected =
    grantedActionsInResource.length > 0 &&
    grantedActionsInResource.length < availableActions.length;

  // Set indeterminate state
  useEffect(() => {
    if (selectAllCheckboxRef.current) {
      (
        selectAllCheckboxRef.current as HTMLButtonElement & {
          indeterminate: boolean;
        }
      ).indeterminate = someSelected;
    }
  }, [someSelected]);

  const handleActionChange = (actionId: number, checked: boolean) => {
    if (isEditMode) {
      onActionToggle(resource.id, actionId, checked);
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (!isEditMode) return;

    // Toggle all actions for this resource
    availableActions.forEach((action) => {
      const isCurrentlyGranted = grantedActionIds.includes(action.id);
      if (checked !== isCurrentlyGranted) {
        onActionToggle(resource.id, action.id, checked);
      }
    });
  };

  return (
    <div className="border rounded-lg p-4 space-y-3">
      {/* Resource Info Row */}
      <div className="border-b pb-3">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <h3 className="font-medium text-gray-900">
              {resource.name}
              <span className="ml-2 text-sm text-gray-500 font-mono">
                ({resource.code})
              </span>
            </h3>
          </div>

          {/* Select All Checkbox */}
          {availableActions.length > 0 && (
            <div className="flex items-center space-x-2">
              <Checkbox
                ref={selectAllCheckboxRef}
                id={`select-all-${resource.id}`}
                checked={allSelected}
                onCheckedChange={handleSelectAll}
                disabled={!isEditMode}
                className={
                  someSelected
                    ? "data-[state=checked]:bg-orange-500 data-[state=checked]:border-orange-500"
                    : ""
                }
              />
              <label
                htmlFor={`select-all-${resource.id}`}
                className={`text-sm font-medium ${
                  isEditMode ? "cursor-pointer" : ""
                } ${
                  allSelected
                    ? "text-blue-700"
                    : someSelected
                    ? "text-orange-700"
                    : "text-gray-600"
                }`}
              >
                {allSelected ? "Bỏ chọn tất cả" : "Chọn tất cả"}
              </label>
            </div>
          )}
        </div>
      </div>

      {/* Actions Row */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
        {availableActions.length === 0 ? (
          <div className="col-span-full text-sm text-gray-500 italic">
            Không có quyền nào được định nghĩa cho resource này
          </div>
        ) : (
          availableActions.map((action) => {
            const isGranted = grantedActionIds.includes(action.id);

            return (
              <div
                key={action.id}
                className={`flex items-center space-x-2 p-2 rounded border transition-all ${
                  isEditMode ? "hover:bg-gray-50 cursor-pointer" : ""
                } ${isGranted ? "bg-blue-50 border-blue-200" : "bg-gray-50"}`}
                onClick={() =>
                  isEditMode && handleActionChange(action.id, !isGranted)
                }
              >
                <Checkbox
                  id={`${resource.id}-${action.id}`}
                  checked={isGranted}
                  onCheckedChange={(checked) =>
                    handleActionChange(action.id, checked as boolean)
                  }
                  disabled={!isEditMode}
                />
                <label
                  htmlFor={`${resource.id}-${action.id}`}
                  className={`text-sm flex-1 ${
                    isEditMode ? "cursor-pointer" : ""
                  }`}
                >
                  <span className="font-medium">{action.name}</span>
                  <span className="ml-1 text-xs text-gray-500 font-mono">
                    ({action.code})
                  </span>
                  {isGranted && (
                    <span className="ml-2 text-xs bg-green-100 text-green-800 px-1 rounded">
                      ✓
                    </span>
                  )}
                </label>
              </div>
            );
          })
        )}
      </div>

      {/* Summary */}
      <div className="text-xs text-gray-500 pt-2 border-t flex justify-between items-center">
        <span>
          Đã cấp:{" "}
          {
            grantedActionIds.filter((id) => resource.actions.includes(id))
              .length
          }
          /{availableActions.length} quyền
        </span>
        {someSelected && (
          <span className="text-orange-600 font-medium">
            Một phần được chọn
          </span>
        )}
        {allSelected && availableActions.length > 0 && (
          <span className="text-blue-600 font-medium">Tất cả được chọn</span>
        )}
      </div>
    </div>
  );
};
