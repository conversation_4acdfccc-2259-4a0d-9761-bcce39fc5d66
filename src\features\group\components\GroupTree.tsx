import React, { useState, useEffect } from "react";
import { useAppDispatch, useAppSelector } from "@/store/rootReducer";
import { selectGroupTree, selectGroupSelectedId } from "../states/selectors";
import { setSelectedId } from "../states/slices";
import { GroupTree as GroupTreeType } from "../states/types";
import { ChevronDown, ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { useNavigate, useLocation } from "react-router-dom";

interface GroupTreeItemProps {
  group: GroupTreeType;
  level: number;
  selectedId: number;
  onSelected: (id: number) => void;
  expandedIds: Set<number>;
  onToggleExpanded: (id: number) => void;
}

const GroupTreeItem: React.FC<GroupTreeItemProps> = ({
  group,
  level,
  selectedId,
  onSelected,
  expandedIds,
  onToggleExpanded,
}) => {
  const isSelected = selectedId === group.id;
  const isExpanded = expandedIds.has(group.id);
  const hasChildren = group.children.length > 0;

  return (
    <div>
      <div
        className={cn(
          "flex items-center p-2 cursor-pointer rounded hover:bg-gray-100",
          isSelected && "bg-blue-50 border border-blue-200"
        )}
        style={{ paddingLeft: level * 16 + 8 }}
        onClick={() => onSelected(group.id)}
      >
        {hasChildren ? (
          <Button
            variant="ghost"
            size="sm"
            className="w-6 h-6 p-0 mr-2"
            onClick={(e) => {
              e.stopPropagation();
              onToggleExpanded(group.id);
            }}
          >
            {isExpanded ? (
              <ChevronDown className="w-4 h-4" />
            ) : (
              <ChevronRight className="w-4 h-4" />
            )}
          </Button>
        ) : (
          <div className="w-6 h-6 mr-2" />
        )}

        <span className="font-medium truncate">{group.name}</span>
      </div>

      {hasChildren && isExpanded && (
        <div>
          {group.children.map((child) => (
            <GroupTreeItem
              key={child.id}
              group={child}
              level={level + 1}
              selectedId={selectedId}
              onSelected={onSelected}
              expandedIds={expandedIds}
              onToggleExpanded={onToggleExpanded}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export const GroupTree: React.FC = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const groupTree = useAppSelector(selectGroupTree);
  const selectedId = useAppSelector(selectGroupSelectedId);

  const [expandedIds, setExpandedIds] = useState<Set<number>>(new Set());

  // Auto expand path to selected item
  useEffect(() => {
    if (selectedId && selectedId !== 0) {
      // Find path from root to selected item
      const findPath = (
        nodes: GroupTreeType[],
        targetId: number,
        path: number[] = []
      ): number[] | null => {
        for (const node of nodes) {
          const currentPath = [...path, node.id];
          if (node.id === targetId) {
            return currentPath;
          }
          if (node.children.length > 0) {
            const childPath = findPath(node.children, targetId, currentPath);
            if (childPath) {
              return childPath;
            }
          }
        }
        return null;
      };

      const pathToSelected = findPath(groupTree, selectedId);
      if (pathToSelected) {
        setExpandedIds(new Set(pathToSelected));
      }
    }
  }, [selectedId, groupTree]);

  const handleToggleExpanded = (id: number) => {
    const newExpanded = new Set(expandedIds);
    if (newExpanded.has(id)) {
      newExpanded.delete(id);
    } else {
      newExpanded.add(id);
    }
    setExpandedIds(newExpanded);
  };

  const handleSelected = (id: number) => {
    dispatch(setSelectedId(id));

    // Update URL with new group selection
    const searchParams = new URLSearchParams(location.search);
    searchParams.set("group", id.toString());
    searchParams.set("tab", "basic");
    searchParams.set("action", "view");
    navigate(`${location.pathname}?${searchParams.toString()}`);
  };

  return (
    <div className="space-y-1">
      {groupTree.length === 0 ? (
        <div className="text-center py-8 text-gray-500">Chưa có nhóm nào</div>
      ) : (
        groupTree.map((group) => (
          <GroupTreeItem
            key={group.id}
            group={group}
            level={0}
            selectedId={selectedId}
            onSelected={handleSelected}
            expandedIds={expandedIds}
            onToggleExpanded={handleToggleExpanded}
          />
        ))
      )}
    </div>
  );
};
