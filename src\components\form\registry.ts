// import { FormNode } from "@/features/builder/autoform/states/types";
import { CheckBoxInput } from "./CheckBoxInput";
import { NumberInput } from "./NumberInput";
import { TabInput } from "./TabsInput";
import { TextInput } from "./TextInput";
import { ToggleInput } from "./ui/ToggleInput";
import { DropdownInput } from "./ui/DropdownInput";
import { DropDownTextWithSearch } from "./ui/DropdownTextWithSearch";
import { PickCategoryInput } from "@/features/category/components/PickCategoryInput";
import { DropDownNumberWithSearch } from "./ui/DropDownNumberWithSearch";
import { PickIconInput } from "./ui/PickIconInput";
import { ComponentPicker } from "@/features/routes/components/ComponentPicker";
import { TextAreaInput } from "./TextAreaInput";
import { ImagePickerInput } from "./ui/ImagePickerInput";
import { PasswordInput } from "@/form/field/components/basic/PasswordInput";
// Remove imports for non-existent components
// import { BooleanArrayInput } from "./ui/BooleanArrayInput";
// import { NumberArrayInput } from "./ui/NumberArrayInput";
// import { TextArrayInput } from "./ui/TextArrayInput";
// import { RichTextInput } from "./ui/RichTextInput";
// import { SelectInput } from "./ui/SelectInput";
// import { ImageInput } from "./ui/ImageInput";
// import { FilesInput } from "./ui/FilesInput";
// import { VideoInput } from "./ui/VideoInput";

export type DataType =
  | "text"
  | "number"
  | "boolean"
  | "boolean_array"
  | "text_array"
  | "number_array"
  | "nullable";

export type DataTypeValue = {
  text: string;
  number: number;
  boolean: boolean;
  boolean_array: boolean[];
  text_array: string[];
  number_array: number[];
  nullable: null;
};

export type FieldValue = DataTypeValue[keyof DataTypeValue];

export interface FormStyle {
  frame?: string;
  label?: string;
  content?: string;
  error?: string;
}

export interface FormValidation {
  min?: number;
  max?: number;
  minLength?: number;
  maxLength?: number;
  pattern?: string;
  required?: boolean;
}

export type FieldConfig<K extends DataType = DataType> = {
  id: string;
  data_type: K;
  input_type: InputType;
  label: string;
  options?: DataTypeValue[K][];
  labels?: string[];
  placeholder?: string;
  disabled?: boolean;
  default_value?: DataTypeValue[K];
  style?: FormStyle;
  validation?: FormValidation;
};

// New form node types
export type FieldGroup = "group" | "basic" | "advance";
export type FormNodeType = "group" | "field";

/**
 * Định nghĩa root của form (form node)
 */
type NodeType = "group" | "field";

export interface FormNode {
  id: string;
  type: NodeType;
  children: FormNode[];
  label?: string;
  style?: FormStyle;
  fieldConfig?: FieldConfig;
}

export const inputRegistry = {
  TextInput: TextInput,
  TextAreaInput: TextAreaInput,
  PasswordInput: PasswordInput,
  NumberInput: NumberInput,
  CheckBoxInput: CheckBoxInput,
  TabInput: TabInput,
  ToggleInput: ToggleInput,
  DropdownInput: DropdownInput,
  DropDownTextWithSearch: DropDownTextWithSearch,
  DropDownNumberWithSearch: DropDownNumberWithSearch,
  PickCategoryInput: PickCategoryInput,
  PickIconInput: PickIconInput,
  PickComponentInput: ComponentPicker,
  ImagePickerInput: ImagePickerInput,
  // Remove non-existent components
  // boolean_array: BooleanArrayInput,
  // number_array: NumberArrayInput,
  // text_array: TextArrayInput,
  // richtext: RichTextInput,
  // select: SelectInput,
  // image: ImageInput,
  // files: FilesInput,
  // video: VideoInput,
} as const;

export type InputType = keyof typeof inputRegistry;

export type BasicInputProps<T extends DataType> = {
  isViewMode?: boolean;
  value: DataTypeValue[T];
  onChange: (value: DataTypeValue[T]) => void;
  onBlur?: () => void;
  placeholder?: string;
  disabled?: boolean;
  name?: string;
  id?: string;
  options?: DataTypeValue[T][];
  labels?: string[];
  default_value?: DataTypeValue[T];
};

export type FormConfig = {
  code: string;
  name: string;
  note: string;
  config: FormNode; // Now using proper type
};

//Mẫu các field sẽ gen
export const formFieldsRegistry: FieldConfig[] = [
  {
    id: "text-input",
    data_type: "text",
    input_type: "TextInput",
    label: "Văn bản ",
    placeholder: "Nhập văn bản...",
    default_value: "",
    validation: {
      required: true,
      minLength: 1,
      maxLength: 100,
      pattern: "[a-zA-Z]",
    },
  },
  {
    id: "number-input",
    data_type: "number",
    input_type: "NumberInput",
    label: "Số ",
    placeholder: "Nhập số...",
    validation: {
      required: true,
      min: 1,
      max: 100,
    },
  },
  {
    id: "checkbox-input",
    data_type: "boolean",
    input_type: "CheckBoxInput",
    label: "Checkbox",
    default_value: "",
    validation: {
      required: true,
    },
  },
  {
    id: "textarea-input",
    data_type: "text",
    input_type: "TextInput",
    label: "Text Area",
    placeholder: "Nhập văn bản dài...",
    default_value: "",
    validation: {
      required: true,
      minLength: 1,
      maxLength: 100,
    },
  },
  {
    id: "tab-input",
    data_type: "text",
    input_type: "TabInput",
    label: "Chọn tab",
    options: ["Tab 1", "Tab 2", "Tab 3"],
    default_value: "",
    validation: {
      required: true,
    },
  },
  {
    id: "toggle-input",
    data_type: "boolean",
    input_type: "ToggleInput",
    label: "Bật / Tắt",
    default_value: false,
    validation: {
      required: true,
    },
  },
  {
    id: "dropdown-input",
    data_type: "text",
    input_type: "DropdownInput",
    label: "Dropdown chọn 1",
    placeholder: "Chọn một mục...",
    options: ["Option 1", "Option 2", "Option 3"],
    default_value: "Option 1",
    validation: { required: true },
  },
  {
    id: "dropdown-text-search",
    data_type: "text",
    input_type: "DropDownTextWithSearch",
    label: "Dropdown có tìm kiếm (text)",
    placeholder: "Tìm và chọn...",
    options: ["Alpha", "Beta", "Gamma"],
    default_value: "Alpha",
    validation: { required: true },
  },
  {
    id: "dropdown-number-search",
    data_type: "number",
    input_type: "DropDownNumberWithSearch",
    label: "Dropdown có tìm kiếm (số)",
    placeholder: "Tìm và chọn số...",
    options: [1, 2, 3, 4, 5],
    default_value: 1,
    validation: { required: true },
  },
  {
    id: "pick-category",
    data_type: "text",
    input_type: "PickCategoryInput",
    label: "Chọn danh mục",
    placeholder: "Chọn danh mục...",
    default_value: "",
    validation: { required: true },
  },
  {
    id: "pick-icon",
    data_type: "text",
    input_type: "PickIconInput",
    label: "Chọn icon",
    placeholder: "Chọn biểu tượng...",
    default_value: "",
    validation: { required: true },
  },
  {
    id: "pick-component",
    data_type: "text",
    input_type: "PickComponentInput",
    label: "Chọn component",
    placeholder: "Chọn component...",
    default_value: "",
    validation: { required: true },
  },
];
