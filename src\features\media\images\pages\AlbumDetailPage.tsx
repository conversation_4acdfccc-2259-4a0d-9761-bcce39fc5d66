import { useEffect, useState } from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, Edit, Trash2 } from "lucide-react";
import { LoadingPage } from "@/components/loading/LoadingPage";
import { fetchAlbum } from "../states/api";
import { Album } from "../states/types";
import { AlbumBadge } from "../components/AlbumBadge";
import { toast } from "sonner";

const AlbumDetailPage = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [album, setAlbum] = useState<Album | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!id) return;

    const loadAlbum = async () => {
      try {
        setLoading(true);
        const response = await fetchAlbum(parseInt(id));
        setAlbum(response.data);
      } catch (error) {
        toast.error("Không thể tải thông tin thư viện: " + (error as Error).message);
      } finally {
        setLoading(false);
      }
    };

    loadAlbum();
  }, [id]);

  if (loading) return <LoadingPage />;

  if (!album) {
    return (
      <div className="p-6 text-center">
        <h1 className="text-xl font-semibold mb-4">Album không tồn tại</h1>
        <Button onClick={() => navigate(-1)}>
          <ArrowLeft className="w-4 h-4 mr-2" />
          Quay lại
        </Button>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <Button variant="ghost" onClick={() => navigate(-1)}>
          <ArrowLeft className="w-4 h-4 mr-2" />
          Quay lại
        </Button>
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <Edit className="w-4 h-4 mr-2" />
            Chỉnh sửa
          </Button>
          <Button variant="destructive" size="sm">
            <Trash2 className="w-4 h-4 mr-2" />
            Xóa
          </Button>
        </div>
      </div>

      {/* Album Info */}
      <Card>
        <CardHeader>
          <div className="flex items-start justify-between">
            <div>
              <CardTitle className="text-2xl mb-2">{album.name}</CardTitle>
              <div className="flex items-center gap-4">
                <AlbumBadge value={album.status} />
                <Badge variant="outline">{album.type}</Badge>
              </div>
            </div>
            {album.coverImage && (
              <div className="w-32 h-32 flex items-center justify-center bg-gray-50 rounded-lg overflow-hidden">
                <img
                  src={album.coverImage}
                  alt={album.name}
                  className="w-full h-full object-contain rounded-lg"
                />
              </div>
            )}
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h3 className="font-semibold mb-2">Mô tả</h3>
            <p className="text-muted-foreground">
              {album.description?.text || "Chưa có mô tả"}
            </p>
          </div>

          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-semibold">ID:</span> {album.id}
            </div>
            <div>
              <span className="font-semibold">Loại:</span> {album.type}
            </div>
            <div>
              <span className="font-semibold">Tạo lúc:</span>{" "}
              {album.createdAt
                ? new Date(album.createdAt).toLocaleString("vi-VN")
                : "N/A"}
            </div>
            <div>
              <span className="font-semibold">Cập nhật:</span>{" "}
              {album.updatedAt
                ? new Date(album.updatedAt).toLocaleString("vi-VN")
                : "N/A"}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Images Section - TODO */}
      <Card>
        <CardHeader>
          <CardTitle>Hình ảnh trong album</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center text-muted-foreground py-8">
            Chức năng hiển thị hình ảnh sẽ được phát triển trong tương lai
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AlbumDetailPage;
