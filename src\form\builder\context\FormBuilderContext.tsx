/**
 * Form Builder Context
 * Manages state using optimized core algorithms and command pattern
 */

import React, { createContext, useContext, useReducer, useCallback, useMemo } from "react";
import { FormNode } from "@/form/types";
import {
  FormBuilderState,
  FormBuilderAction,
  FormConfig,
  EditorUIState,
  CommandFactory,
  HistoryManager,
  generateNodeId,
  createTreeContext,
  findNode,
  validateFormConfig,
} from "../core";
import { useAutoSave, loadAutoSave, clearAutoSave } from "../hooks/useAutoSave";
import { toast } from "sonner";

// ============================================================================
// CONTEXT DEFINITION
// ============================================================================

interface FormBuilderContextType {
  // State
  state: FormBuilderState;
  
  // Form operations
  addNode: (parentId: string, node: FormNode, index?: number) => void;
  updateNode: (nodeId: string, updates: Partial<FormNode>) => void;
  deleteNode: (nodeId: string) => void;
  moveNode: (nodeId: string, newParentId: string, index: number) => void;
  duplicateNode: (nodeId: string) => void;
  
  // Editor operations
  selectNode: (nodeId: string | null) => void;
  toggleNodeExpand: (nodeId: string) => void;
  setEditorMode: (mode: EditorUIState['mode']) => void;
  showComponentSelector: (parentId: string) => void;
  hideComponentSelector: () => void;
  setPropertiesTab: (tab: 'attributes' | 'display') => void;
  
  // History operations
  undo: () => void;
  redo: () => void;
  canUndo: boolean;
  canRedo: boolean;
  
  // Form management
  saveForm: () => void;
  loadForm: (config: FormConfig) => void;
  newForm: () => void;
  exportForm: () => string;
  importForm: (configJson: string) => void;
  updateFormConfig: (updates: Partial<Pick<FormConfig, 'name' | 'description' | 'code'>>) => void;
  
  // Utilities
  generateId: () => string;
  findNode: (nodeId: string) => FormNode | null;
  validateForm: () => boolean;
}

const FormBuilderContext = createContext<FormBuilderContextType | null>(null);

// ============================================================================
// INITIAL STATE
// ============================================================================

const createInitialFormConfig = (): FormConfig => ({
  code: "new-form",
  name: "New Form",
  description: "A new form created with Form Builder",
  dataSchema: {},
  uiConfig: {
    id: "form-root",
    type: "frame",
    properties: {
      title: "Form Root"
    },
    styles: {
      container: "space-y-6 p-6"
    },
    children: [
      {
        id: "title-1",
        type: "title",
        properties: {
          text: "Sample Form"
        },
        styles: {
          content: "text-2xl font-bold text-gray-900 mb-4"
        }
      },
      {
        id: "field-1",
        type: "field",
        field: {
          objectKey: "name",
          component: "TextInput",
          dataType: "string",
          defaultValue: ""
        },
        properties: {
          label: "Full Name",
          placeholder: "Enter your full name..."
        }
      },
      {
        id: "field-2", 
        type: "field",
        field: {
          objectKey: "email",
          component: "TextInput",
          dataType: "string",
          defaultValue: ""
        },
        properties: {
          label: "Email Address",
          placeholder: "Enter your email..."
        }
      },
      {
        id: "control-1",
        type: "control",
        properties: {
          text: "Submit",
          controlType: "submit",
          variant: "default"
        }
      }
    ]
  }
});

const createInitialEditorState = (): EditorUIState => ({
  selectedNodeId: 'form-root',
  expandedNodes: new Set(['form-root']),
  showComponentSelector: false,
  componentSelectorParentId: null,
  propertiesPanel: {
    activeTab: 'display',  // Frame nodes only have display tab
    scrollPosition: 0
  },
  mode: 'design'
});

const createInitialState = (): FormBuilderState => ({
  formConfig: createInitialFormConfig(),
  editor: createInitialEditorState(),
  history: {
    past: [],
    future: []
  },
  isDirty: false,
  isLoading: false,
  lastAction: null
});

// ============================================================================
// REDUCER
// ============================================================================

function formBuilderReducer(state: FormBuilderState, action: FormBuilderAction): FormBuilderState {
  switch (action.type) {
    case 'FORM/SET_CONFIG':
      return {
        ...state,
        formConfig: action.payload,
        isDirty: false,
        lastAction: 'Load form'
      };
      
    case 'FORM/UPDATE_METADATA':
      return {
        ...state,
        formConfig: {
          ...state.formConfig,
          ...action.payload
        },
        isDirty: true,
        lastAction: 'Update form metadata'
      };
      
    case 'EDITOR/SELECT_NODE':
      // Auto-set correct tab based on node type
      const context = createTreeContext(state.formConfig.uiConfig);
      // Always fallback to root if nodeId is null
      const finalNodeId = action.payload.nodeId || 'form-root';
      const selectedNode = findNode(context, finalNodeId) || state.formConfig.uiConfig;
      const correctTab = selectedNode?.type === 'frame' ? 'display' : 'attributes';
      
      return {
        ...state,
        editor: {
          ...state.editor,
          selectedNodeId: finalNodeId,
          propertiesPanel: {
            ...state.editor.propertiesPanel,
            activeTab: correctTab
          }
        }
      };
      
    case 'EDITOR/TOGGLE_NODE_EXPAND':
      const newExpanded = new Set(state.editor.expandedNodes);
      if (newExpanded.has(action.payload.nodeId)) {
        newExpanded.delete(action.payload.nodeId);
      } else {
        newExpanded.add(action.payload.nodeId);
      }
      return {
        ...state,
        editor: {
          ...state.editor,
          expandedNodes: newExpanded
        }
      };
      
    case 'EDITOR/SET_MODE':
      return {
        ...state,
        editor: {
          ...state.editor,
          mode: action.payload
        }
      };
      
    case 'EDITOR/SHOW_COMPONENT_SELECTOR':
      return {
        ...state,
        editor: {
          ...state.editor,
          showComponentSelector: true,
          componentSelectorParentId: action.payload.parentId
        }
      };
      
    case 'EDITOR/HIDE_COMPONENT_SELECTOR':
      return {
        ...state,
        editor: {
          ...state.editor,
          showComponentSelector: false,
          componentSelectorParentId: null
        }
      };
      
    case 'EDITOR/SET_PROPERTIES_TAB':
      return {
        ...state,
        editor: {
          ...state.editor,
          propertiesPanel: {
            ...state.editor.propertiesPanel,
            activeTab: action.payload
          }
        }
      };
      
    default:
      return state;
  }
}

// ============================================================================
// PROVIDER COMPONENT
// ============================================================================

interface FormBuilderProviderProps {
  children: React.ReactNode;
  initialConfig?: FormConfig;
}

export const FormBuilderProvider: React.FC<FormBuilderProviderProps> = ({
  children,
  initialConfig
}) => {
  const [state, dispatch] = useReducer(formBuilderReducer, createInitialState());
  
  // Initialize with config if provided, otherwise load from autosave
  React.useEffect(() => {
    if (initialConfig) {
      dispatch({ type: 'FORM/SET_CONFIG', payload: initialConfig });
    } else {
      // Try to load from autosave
      const savedConfig = loadAutoSave();
      if (savedConfig) {
        dispatch({ type: 'FORM/SET_CONFIG', payload: savedConfig });
        console.log('Loaded form from autosave');
      }
    }
  }, [initialConfig]);
  
  // ============================================================================
  // FORM OPERATIONS
  // ============================================================================
  
  const addNode = useCallback((parentId: string, node: FormNode, index?: number) => {
    const command = CommandFactory.addNode(parentId, { ...node, id: generateNodeId() }, index);
    const newState = HistoryManager.addCommand(state, command);
    
    dispatch({ type: 'FORM/SET_CONFIG', payload: newState.formConfig });
    dispatch({ type: 'FORM/UPDATE_METADATA', payload: {} }); // Trigger dirty flag
  }, [state]);

  // Auto-save functionality
  useAutoSave(state.formConfig, state.isDirty);
  
  const updateNode = useCallback((nodeId: string, updates: Partial<FormNode>) => {
    const command = CommandFactory.updateNode(nodeId, updates, state);
    const newState = HistoryManager.addCommand(state, command);
    
    // Mark as dirty for autosave
    const updatedState = { ...newState, isDirty: true };
    dispatch({ type: 'FORM/SET_CONFIG', payload: updatedState.formConfig });
    dispatch({ type: 'FORM/UPDATE_METADATA', payload: {} }); // Trigger dirty flag
  }, [state]);
  
  const deleteNode = useCallback((nodeId: string) => {
    if (nodeId === state.formConfig.uiConfig.id) {
      console.warn("Cannot delete root node");
      return;
    }
    
    const command = CommandFactory.deleteNode(nodeId, state);
    const newState = HistoryManager.addCommand(state, command);
    
    dispatch({ type: 'FORM/SET_CONFIG', payload: newState.formConfig });
    dispatch({ type: 'FORM/UPDATE_METADATA', payload: {} }); // Trigger dirty flag
    
    // Clear selection if deleted node was selected, fallback to root
    if (state.editor.selectedNodeId === nodeId) {
      dispatch({ type: 'EDITOR/SELECT_NODE', payload: { nodeId: 'form-root' } });
    }
  }, [state]);
  
  const moveNode = useCallback((nodeId: string, newParentId: string, index: number) => {
    const command = CommandFactory.moveNode(nodeId, newParentId, index, state);
    const newState = HistoryManager.addCommand(state, command);
    
    dispatch({ type: 'FORM/SET_CONFIG', payload: newState.formConfig });
    dispatch({ type: 'FORM/UPDATE_METADATA', payload: {} }); // Trigger dirty flag
  }, [state]);
  
  const duplicateNode = useCallback((nodeId: string) => {
    const context = createTreeContext(state.formConfig.uiConfig);
    const node = findNode(context, nodeId);
    if (!node) return;
    
    // Clone the node with new ID
    const cloneWithNewIds = (node: FormNode): FormNode => {
      const cloned = { ...node, id: generateNodeId() };
      if (node.children) {
        cloned.children = node.children.map((child: FormNode) => cloneWithNewIds(child));
      }
      return cloned;
    };
    
    const clonedNode = cloneWithNewIds(node);
    
    // Find parent to add after original
    const parent = Object.values(context.nodeMap).find(n => 
      n.children?.some((child: FormNode) => child.id === nodeId)
    );
    
    if (parent) {
      const index = parent.children!.findIndex((child: FormNode) => child.id === nodeId);
      addNode(parent.id, clonedNode, index + 1);
    }
  }, [state, addNode]);
  
  // ============================================================================
  // EDITOR OPERATIONS
  // ============================================================================
  
  const selectNode = useCallback((nodeId: string | null) => {
    // Always fallback to root if nodeId is null
    const finalNodeId = nodeId || 'form-root';
    dispatch({ type: 'EDITOR/SELECT_NODE', payload: { nodeId: finalNodeId } });
  }, []);
  
  const toggleNodeExpand = useCallback((nodeId: string) => {
    dispatch({ type: 'EDITOR/TOGGLE_NODE_EXPAND', payload: { nodeId } });
  }, []);
  
  const setEditorMode = useCallback((mode: EditorUIState['mode']) => {
    dispatch({ type: 'EDITOR/SET_MODE', payload: mode });
  }, []);
  
  const showComponentSelector = useCallback((parentId: string) => {
    dispatch({ type: 'EDITOR/SHOW_COMPONENT_SELECTOR', payload: { parentId } });
  }, []);
  
  const hideComponentSelector = useCallback(() => {
    dispatch({ type: 'EDITOR/HIDE_COMPONENT_SELECTOR' });
  }, []);
  
  const setPropertiesTab = useCallback((tab: 'attributes' | 'display') => {
    dispatch({ type: 'EDITOR/SET_PROPERTIES_TAB', payload: tab });
  }, []);
  
  // ============================================================================
  // HISTORY OPERATIONS
  // ============================================================================
  
  const undo = useCallback(() => {
    const newState = HistoryManager.undo(state);
    dispatch({ type: 'FORM/SET_CONFIG', payload: newState.formConfig });
    dispatch({ type: 'FORM/UPDATE_METADATA', payload: {} }); // Trigger dirty flag
  }, [state]);
  
  const redo = useCallback(() => {
    const newState = HistoryManager.redo(state);
    dispatch({ type: 'FORM/SET_CONFIG', payload: newState.formConfig });
    dispatch({ type: 'FORM/UPDATE_METADATA', payload: {} }); // Trigger dirty flag
  }, [state]);
  
  const canUndo = useMemo(() => HistoryManager.canUndo(state), [state]);
  const canRedo = useMemo(() => HistoryManager.canRedo(state), [state]);
  
  // ============================================================================
  // FORM MANAGEMENT
  // ============================================================================
  
  const saveForm = useCallback(() => {
    const validation = validateFormConfig(state.formConfig);
    if (!validation.success) {
      console.error("Form validation failed:", validation.error);
      toast.error('Cannot save form', {
        description: 'Form validation failed'
      });
      return;
    }
    
    // Here you would save to backend
    console.log("Saving form:", state.formConfig);
    dispatch({ type: 'FORM/UPDATE_METADATA', payload: {} }); // Reset dirty flag
    
    // Clear autosave after successful save
    clearAutoSave();
    
    toast.success('Form saved successfully!');
  }, [state.formConfig]);
  
  const loadForm = useCallback((config: FormConfig) => {
    dispatch({ type: 'FORM/SET_CONFIG', payload: config });
  }, []);
  
  const newForm = useCallback(() => {
    const newConfig = createInitialFormConfig();
    dispatch({ type: 'FORM/SET_CONFIG', payload: newConfig });
    
    // Clear autosave when creating new form
    clearAutoSave();
    
    toast.success('New form created!');
  }, []);
  
  const exportForm = useCallback((): string => {
    return JSON.stringify(state.formConfig, null, 2);
  }, [state.formConfig]);
  
  const importForm = useCallback((configJson: string) => {
    try {
      const config = JSON.parse(configJson) as FormConfig;
      const validation = validateFormConfig(config);
      
      if (validation.success) {
        dispatch({ type: 'FORM/SET_CONFIG', payload: config });
        toast.success('Form imported successfully!');
      } else {
        console.error("Invalid form config:", validation.error);
        toast.error('Failed to import form', {
          description: 'Invalid form configuration format'
        });
      }
    } catch (error) {
      console.error("Failed to parse form config:", error);
      toast.error('Failed to import form', {
        description: 'Could not parse JSON file'
      });
    }
  }, []);
  
  const updateFormConfig = useCallback((updates: Partial<Pick<FormConfig, 'name' | 'description' | 'code'>>) => {
    dispatch({ type: 'FORM/UPDATE_METADATA', payload: updates });
  }, []);
  
  // ============================================================================
  // UTILITIES
  // ============================================================================
  
  const generateId = useCallback(() => generateNodeId(), []);
  
  const findNodeById = useCallback((nodeId: string): FormNode | null => {
    const context = createTreeContext(state.formConfig.uiConfig);
    return findNode(context, nodeId);
  }, [state.formConfig.uiConfig]);
  
  const validateForm = useCallback((): boolean => {
    const validation = validateFormConfig(state.formConfig);
    return validation.success;
  }, [state.formConfig]);
  
  // ============================================================================
  // CONTEXT VALUE
  // ============================================================================
  
  const contextValue: FormBuilderContextType = useMemo(() => ({
    state,
    
    // Form operations
    addNode,
    updateNode,
    deleteNode,
    moveNode,
    duplicateNode,
    
    // Editor operations
    selectNode,
    toggleNodeExpand,
    setEditorMode,
    showComponentSelector,
    hideComponentSelector,
    setPropertiesTab,
    
    // History operations
    undo,
    redo,
    canUndo,
    canRedo,
    
    // Form management
    saveForm,
    loadForm,
    newForm,
    exportForm,
    importForm,
    updateFormConfig,
    
    // Utilities
    generateId,
    findNode: findNodeById,
    validateForm,
  }), [
    state,
    addNode,
    updateNode,
    deleteNode,
    moveNode,
    duplicateNode,
    selectNode,
    toggleNodeExpand,
    setEditorMode,
    showComponentSelector,
    hideComponentSelector,
    setPropertiesTab,
    undo,
    redo,
    canUndo,
    canRedo,
    saveForm,
    loadForm,
    newForm,
    exportForm,
    importForm,
    updateFormConfig,
    generateId,
    findNodeById,
    validateForm,
  ]);
  
  return (
    <FormBuilderContext.Provider value={contextValue}>
      {children}
    </FormBuilderContext.Provider>
  );
};

// ============================================================================
// HOOK
// ============================================================================

export const useFormBuilder = (): FormBuilderContextType => {
  const context = useContext(FormBuilderContext);
  if (!context) {
    throw new Error("useFormBuilder must be used within a FormBuilderProvider");
  }
  return context;
};