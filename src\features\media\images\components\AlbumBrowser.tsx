import { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Search, ArrowLeft, CheckCircle, Loader2 } from "lucide-react";
import { ImageLoader } from "@/components/image/ImageLoader";
import { fetchAlbums, fetchAlbumMedia } from "../states/api";
import { Album, Media, AlbumQueryParams } from "../states/types";
import { AlbumBadge } from "./AlbumBadge";
import { toast } from "sonner";

interface AlbumBrowserProps {
  onSelectMedia: (media: Media) => void;
  selectedMediaId?: number;
  currentAlbumId?: number; // Exclude current album from selection
  albumType?: "image" | "video";
}

export function AlbumBrowser({
  onSelectMedia,
  selectedMediaId,
  currentAlbumId,
  albumType = "image",
}: AlbumBrowserProps) {
  const [albums, setAlbums] = useState<Album[]>([]);
  const [albumsLoading, setAlbumsLoading] = useState(false);
  const [selectedAlbum, setSelectedAlbum] = useState<Album | null>(null);
  const [albumMedia, setAlbumMedia] = useState<Media[]>([]);
  const [mediaLoading, setMediaLoading] = useState(false);
  const [mediaHasMore, setMediaHasMore] = useState(false);
  const [mediaCurrentPage, setMediaCurrentPage] = useState(1);
  const [searchQuery, setSearchQuery] = useState("");

  // Load albums when component mounts
  useEffect(() => {
    loadAlbums();
  }, [albumType]);

  const loadAlbums = async () => {
    try {
      setAlbumsLoading(true);
      const params: AlbumQueryParams = {
        type: albumType,
        page: 1,
        size: 50,
        search: searchQuery,
      };

      const response = await fetchAlbums(params);
      // Filter out current album
      const filteredAlbums = response.data.filter(
        (album) => album.id !== currentAlbumId
      );
      setAlbums(filteredAlbums);
    } catch (error) {
      toast.error("Lỗi tải thư viện: " + error);
      // Error loading albums - silently fail
    } finally {
      setAlbumsLoading(false);
    }
  };

  const loadAlbumMedia = async (album: Album, append = false) => {
    try {
      setMediaLoading(true);
      if (!append) {
        setSelectedAlbum(album);
        setMediaCurrentPage(1);
      }

      const page = append ? mediaCurrentPage + 1 : 1;
      const response = await fetchAlbumMedia(album.id, { page, size: 12 });

      if (append) {
        setAlbumMedia((prev) => [...prev, ...response.data]);
        setMediaCurrentPage(page);
      } else {
        setAlbumMedia(response.data);
        setMediaCurrentPage(1);
      }

      setMediaHasMore(response.hasMore || false);
    } catch {
      setAlbumMedia([]);
      setMediaHasMore(false);
    } finally {
      setMediaLoading(false);
    }
  };

  const handleBackToAlbums = () => {
    setSelectedAlbum(null);
    setAlbumMedia([]);
    setMediaHasMore(false);
    setMediaCurrentPage(1);
  };

  const handleSearch = () => {
    loadAlbums();
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSearch();
    }
  };

  const handleLoadMoreMedia = () => {
    if (selectedAlbum && mediaHasMore && !mediaLoading) {
      loadAlbumMedia(selectedAlbum, true);
    }
  };

  if (selectedAlbum) {
    return (
      <div className="space-y-4">
        {/* Album detail header */}
        <div className="flex items-center gap-3">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleBackToAlbums}
            className="w-30"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Quay lại
          </Button>
          <div>
            <h3 className="font-medium">{selectedAlbum.name}</h3>
            <p className="text-sm text-muted-foreground">
              {albumMedia.length} media
            </p>
          </div>
        </div>

        {/* Media grid */}
        <div className="max-h-96 overflow-y-auto">
          {mediaLoading ? (
            <div className="grid grid-cols-3 gap-3">
              {[1, 2, 3, 4, 5, 6].map((i) => (
                <div
                  key={i}
                  className="aspect-square bg-muted rounded animate-pulse"
                />
              ))}
            </div>
          ) : albumMedia.length > 0 ? (
            <div className="grid grid-cols-3 gap-3">
              {albumMedia.map((media) => (
                <div
                  key={media.id}
                  className={`relative cursor-pointer rounded-lg overflow-hidden border-2 transition-all bg-white ${selectedMediaId === media.id
                    ? "border-primary ring-2 ring-primary/20"
                    : "border-transparent hover:border-gray-300"
                    }`}
                  onClick={() => onSelectMedia(media)}
                >
                  {/* Image container with proper aspect ratio */}
                  <div className="aspect-square w-full relative overflow-hidden">
                    <ImageLoader
                      src={media.src}
                      alt={media.name}
                      className="w-full h-full object-contain bg-gray-50"
                      containerClassName="aspect-square"
                      fallbackText={media.name}
                    />
                  </div>

                  {/* Selected indicator */}
                  {selectedMediaId === media.id && (
                    <div className="absolute top-2 right-2 bg-primary rounded-full p-1">
                      <CheckCircle className="w-4 h-4 text-white" />
                    </div>
                  )}

                  {/* Media name overlay */}
                  <div className="p-2 bg-white border-t">
                    <p className="text-xs font-medium truncate">{media.name}</p>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              <p>Album này chưa có media nào</p>
            </div>
          )}

          {/* Load More Button for Media */}
          {mediaHasMore && albumMedia.length > 0 && (
            <div className="flex justify-center mt-4">
              <Button
                variant="outline"
                onClick={handleLoadMoreMedia}
                disabled={mediaLoading}
                size="sm"
              >
                {mediaLoading && (
                  <Loader2 className="mr-2 h-3 w-3 animate-spin" />
                )}
                Xem thêm
              </Button>
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Search */}
      <div className="flex gap-2">
        <Input
          placeholder="Tìm kiếm album..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          onKeyPress={handleKeyPress}
        />
        <Button onClick={handleSearch} disabled={albumsLoading}>
          <Search className="w-4 h-4" />
        </Button>
      </div>

      {/* Albums grid */}
      <div className="max-h-96 overflow-y-auto">
        {albumsLoading ? (
          <div className="grid grid-cols-2 gap-3">
            {[1, 2, 3, 4].map((i) => (
              <Card key={i} className="animate-pulse">
                <CardContent className="p-3">
                  <div className="aspect-video bg-muted rounded mb-2" />
                  <div className="h-4 bg-muted rounded mb-1" />
                  <div className="h-3 bg-muted rounded w-2/3" />
                </CardContent>
              </Card>
            ))}
          </div>
        ) : albums.length > 0 ? (
          <div className="grid grid-cols-2 gap-3">
            {albums.map((album) => (
              <Card
                key={album.id}
                className="cursor-pointer hover:shadow-md transition-shadow"
                onClick={() => loadAlbumMedia(album)}
              >
                <CardContent className="p-3">
                  <div className="aspect-video rounded mb-2 overflow-hidden flex items-center justify-center bg-gray-50">
                    <ImageLoader
                      src={album.coverImage || ""}
                      alt={album.name}
                      className="w-full h-full object-contain"
                      containerClassName="aspect-video"
                      fallbackText="No cover"
                    />
                  </div>

                  <h4 className="font-medium text-sm truncate mb-1">
                    {album.name}
                  </h4>
                  <div className="flex items-center gap-2">
                    <AlbumBadge value={album.status} />
                    <Badge variant="outline" className="text-xs">
                      {album.type}
                    </Badge>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-muted-foreground">
            <p>Không tìm thấy album nào</p>
            {searchQuery && (
              <Button
                variant="ghost"
                onClick={() => {
                  setSearchQuery("");
                  loadAlbums();
                }}
                className="mt-2"
              >
                Xóa bộ lọc
              </Button>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
