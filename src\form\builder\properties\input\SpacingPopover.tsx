/**
 * Spacing Popover Component
 * Popover with grid layout for margin/padding selection
 */

import React, { useState } from 'react';
import { cn } from '@/lib/utils';
import { ChevronDown, Check } from 'lucide-react';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Button } from '@/components/ui/button';

interface SpacingOption {
  value: string;
  label: string;
  description: string;
}

interface SpacingPopoverProps {
  value?: string;
  onValueChange?: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  type: 'margin' | 'padding';
}

// Tailwind spacing scale
const SPACING_OPTIONS: SpacingOption[] = [
  { value: 'none', label: 'None', description: 'Không có' },
  { value: '0', label: '0', description: '0px' },
  { value: '1', label: '1', description: '4px' },
  { value: '2', label: '2', description: '8px' },
  { value: '3', label: '3', description: '12px' },
  { value: '4', label: '4', description: '16px' },
  { value: '5', label: '5', description: '20px' },
  { value: '6', label: '6', description: '24px' },
  { value: '8', label: '8', description: '32px' },
  { value: '10', label: '10', description: '40px' },
  { value: '12', label: '12', description: '48px' },
  { value: '16', label: '16', description: '64px' },
  { value: '20', label: '20', description: '80px' },
  { value: '24', label: '24', description: '96px' },
  { value: '32', label: '32', description: '128px' },
  { value: '40', label: '40', description: '160px' },
  { value: '48', label: '48', description: '192px' },
  { value: '64', label: '64', description: '256px' }
];

export const SpacingPopover: React.FC<SpacingPopoverProps> = ({
  value = 'none',
  onValueChange,
  placeholder = 'Chọn spacing',
  disabled = false,
  type
}) => {
  const [open, setOpen] = useState(false);

  // Find current option to display
  const currentOption = SPACING_OPTIONS.find(opt => opt.value === value) || 
    { value, label: value, description: value };

  const handleValueSelect = (newValue: string) => {
    onValueChange?.(newValue);
    setOpen(false);
  };

  const typeLabel = type === 'margin' ? 'Lề ngoài' : 'Lề trong';

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            "h-7 w-full justify-between text-xs",
            !currentOption && "text-muted-foreground",
            disabled && "opacity-50 cursor-not-allowed"
          )}
          disabled={disabled}
        >
          <span className="truncate">
            {currentOption.label !== 'none' ? currentOption.label : placeholder}
          </span>
          <ChevronDown className="ml-2 h-3 w-3 shrink-0 opacity-50 flex-shrink-0" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-0" align="start">
        <div className="p-3">
          <h4 className="text-sm font-medium mb-3">{typeLabel}</h4>
          
          {/* Grid layout for better UX */}
          <div className="grid grid-cols-6 gap-2">
            {SPACING_OPTIONS.map((option) => (
              <button
                key={option.value}
                onClick={() => handleValueSelect(option.value)}
                className={cn(
                  "flex flex-col items-center p-1.5 text-xs rounded hover:bg-gray-100 transition-colors relative min-h-[50px] justify-center",
                  value === option.value && "bg-blue-100 text-blue-900 ring-1 ring-blue-500"
                )}
                title={`${option.label} - ${option.description}`}
              >
                <span className="font-medium text-center leading-tight text-xs">
                  {option.label}
                </span>
                <span className="text-gray-500 text-xs mt-0.5 text-center leading-tight">
                  {option.description}
                </span>
                {value === option.value && (
                  <Check className="h-3 w-3 absolute top-1 right-1" />
                )}
              </button>
            ))}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
};