import { LoadingPage } from "@/components/loading/LoadingPage";
import { lazyLoad } from "@/logic/utils/lazyload";

export const QuestionAdminPage = lazyLoad(
  () => import("./QuestionAdminPage"),
  (module) => module.default,
  { fallback: <LoadingPage /> }
);

export const QuestionConfigAdminPage = lazyLoad(
  () => import("./QuestionConfigAdminPage"),
  (module) => module.default,
  { fallback: <LoadingPage /> }
);

export const QuestionListAdminPage = lazyLoad(
  () => import("./QuestionListAdminPage"),
  (module) => module.default,
  { fallback: <LoadingPage /> }
);
