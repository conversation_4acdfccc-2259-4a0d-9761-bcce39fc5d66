import React, { useEffect } from "react";
import { useSearchParams } from "react-router-dom";
import { useAppSelector, useAppDispatch } from "@/store/rootReducer";
import {
  selectCategoryLoading,
  selectCategoryGlobalError,
  selectCategorySelectedId,
  selectCategorySelectedType,
  selectCategoryMode,
  // selectCategoryCreateParentId,
} from "../states/selector";
import {
  setSelectedId,
  setSelectedType,
  setCategoryMode,
  setCreateParentId,
} from "../states/slices";
import { CategoryTree } from "../components/CategoryTree";
import { CategoryTabs } from "../components/CategoryTabs";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AutoFormCategory } from "../components/AutoFormCategory";
import { LoadingPage } from "@/components/loading/LoadingPage";
import { AlertCircle } from "lucide-react";
import type { CategoryType } from "../states/types";

export const CategoryAdminPage: React.FC = () => {
  const dispatch = useAppDispatch();
  const [searchParams, setSearchParams] = useSearchParams();

  // Read state from URL params
  const urlType = searchParams.get("type") as CategoryType;
  const urlMode = searchParams.get("mode") as "create" | "view" | "edit" | null;
  const urlId = searchParams.get("id");

  // Get Redux state
  const selectedType = useAppSelector(selectCategorySelectedType);
  const selectedId = useAppSelector(selectCategorySelectedId);
  const mode = useAppSelector(selectCategoryMode);
  // const createParentId = useAppSelector(selectCategoryCreateParentId);
  const loading = useAppSelector(selectCategoryLoading);
  const error = useAppSelector(selectCategoryGlobalError);

  // Parse URL params
  const parsedType = urlType || "public-menu";
  const parsedMode = urlMode || "view";
  const parsedId = urlId ? parseInt(urlId, 10) : 0;

  // Auto-switch to create mode when on root (id=0)
  const actualMode = parsedId === 0 ? "create" : parsedMode;

  // Sync URL params with Redux state
  useEffect(() => {
    // let needsUpdate = false;

    if (selectedType !== parsedType) {
      dispatch(setSelectedType(parsedType));
      // needsUpdate = true;
    }

    if (selectedId !== parsedId) {
      dispatch(setSelectedId(parsedId));
      // needsUpdate = true;
    }

    if (mode !== actualMode) {
      dispatch(setCategoryMode(actualMode));
      // needsUpdate = true;
    }

    // Initialize URL params if not set
    const params = new URLSearchParams(searchParams);
    let urlNeedsUpdate = false;

    if (!urlType) {
      params.set("type", parsedType);
      urlNeedsUpdate = true;
    }
    if (!urlMode) {
      params.set("mode", actualMode);
      urlNeedsUpdate = true;
    }
    if (!urlId) {
      params.set("id", String(parsedId));
      urlNeedsUpdate = true;
    }

    if (urlNeedsUpdate) {
      setSearchParams(params, { replace: true });
    }
  }, [
    dispatch,
    searchParams,
    setSearchParams,
    selectedType,
    selectedId,
    mode,
    parsedType,
    parsedId,
    actualMode,
    urlType,
    urlMode,
    urlId,
  ]);

  // Sync Redux state back to URL when Redux state changes (from actions)
  useEffect(() => {
    // Only sync if Redux state differs from URL params to prevent loops
    const currentType = searchParams.get("type") || "public-menu";
    const currentId = searchParams.get("id") || "0";
    const currentMode = searchParams.get("mode") || "view";

    const needsSync =
      currentType !== selectedType ||
      currentId !== String(selectedId) ||
      currentMode !== mode;

    if (!needsSync) return;

    const params = new URLSearchParams(searchParams);

    params.set("type", selectedType);
    params.set("id", String(selectedId));
    params.set("mode", mode);

    // Sync Redux state to URL

    setSearchParams(params, { replace: true });
  }, [selectedType, selectedId, mode]); // Remove setSearchParams and searchParams from deps

  const handleTypeChange = (type: CategoryType) => {
    // Update both Redux and URL
    dispatch(setSelectedType(type));
    dispatch(setSelectedId(0));
    dispatch(setCategoryMode("view"));

    const params = new URLSearchParams(searchParams);
    params.set("type", type);
    params.set("id", "0");
    params.set("mode", "view");
    setSearchParams(params);
  };

  const handleIdChange = (id: number) => {
    // Update both Redux and URL
    dispatch(setSelectedId(id));
    const newMode = id > 0 ? "view" : "create";
    dispatch(setCategoryMode(newMode));

    const params = new URLSearchParams(searchParams);
    params.set("id", String(id));
    params.set("mode", newMode);
    setSearchParams(params);
  };

  const handleModeChange = (newMode: "create" | "view" | "edit") => {
    // Update both Redux and URL
    dispatch(setCategoryMode(newMode));

    const params = new URLSearchParams(searchParams);
    params.set("mode", newMode);
    setSearchParams(params);
  };

  const handleCreateNew = (parentId: number) => {
    // Update Redux state - no more URL parentId
    dispatch(setSelectedId(0));
    dispatch(setCategoryMode("create"));
    dispatch(setCreateParentId(parentId));
  };

  // Show loading if state is not in sync yet
  const isReady =
    selectedType === parsedType &&
    selectedId === parsedId &&
    mode === actualMode;

  if (loading || !isReady) return <LoadingPage />;

  return (
    <div className="w-full h-full flex flex-col gap-4">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Quản lý danh mục</h1>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Lỗi</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Category Type Tabs */}
      <div className="mb-2">
        <CategoryTabs value={selectedType} onChange={handleTypeChange} />
      </div>

      <div className="flex-1 flex gap-4">
        <div className="w-1/3">
          <CategoryTree
            selectedType={selectedType}
            selectedId={selectedId}
            onIdChange={handleIdChange}
            onCreateNew={handleCreateNew}
          />
        </div>
        <div className="flex-1">
          <AutoFormCategory
            selectedType={selectedType}
            selectedId={selectedId}
            mode={actualMode}
            onModeChange={handleModeChange}
          />
        </div>
      </div>
    </div>
  );
};

export default CategoryAdminPage;
