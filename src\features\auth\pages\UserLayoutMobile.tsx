import { useMemo } from "react";
import { Outlet } from "react-router";
import { ChevronDown, ChevronUp } from "lucide-react";
import { SharedHeader } from "@/features/layouts/components/header/SharedHeader";
import { TreeSidebar } from "@/features/layouts/components/menu/TreeSidebar";
import {
  buildCategoryTree,
  CategoryTree,
} from "@/features/category/states/types";
import { LoadingPage } from "@/components/loading/LoadingPage";
import { useCategory } from "@/features/category/hooks/useCategoryData";
import { ProtectedRoute } from "@/components/auth/ProtectedRoute";
import { useAppDispatch } from "@/store/rootReducer";
import { useLayoutManager } from "@/features/layouts/hooks/useLayoutManager";
import { setUserMenuOpen } from "@/features/layouts/states/slices";

const UserLayoutMobile: React.FC = () => {
  const dispatch = useAppDispatch();
  const { data: flatMenuList, loading } = useCategory("user-menu");

  // Use layout manager for user menu (separate from public menu)
  const { userMenuOpen, headerHeight } = useLayoutManager();

  const treeMenuList: CategoryTree[] = useMemo(
    () => buildCategoryTree(flatMenuList),
    [flatMenuList]
  );

  const handleUserMenuToggle = () => {
    dispatch(setUserMenuOpen(!userMenuOpen));
  };

  const onMenuClick = () => {
    dispatch(setUserMenuOpen(false)); // Auto-close on mobile
  };

  if (loading || !treeMenuList.length) return <LoadingPage />;

  return (
    <ProtectedRoute>
      <div className="w-full h-full min-h-screen flex flex-col bg-primary-foreground">
        {/* Shared Header - Public menu + User info */}
        <SharedHeader />

        {/* User Menu Toggle Button */}
        <div
          className="bg-white border-b"
          style={{ marginTop: `${headerHeight}px` }}
        >
          <button
            onClick={handleUserMenuToggle}
            className="w-full p-3 flex items-center justify-between bg-gray-50 hover:bg-gray-100 transition-colors border-b"
            aria-label="Toggle user menu"
          >
            <span className="font-medium text-gray-700">Chức năng</span>
            {userMenuOpen ? (
              <ChevronUp className="w-5 h-5 text-gray-600" />
            ) : (
              <ChevronDown className="w-5 h-5 text-gray-600" />
            )}
          </button>

          {/* Collapsible User Menu */}
          {userMenuOpen && (
            <div className="bg-white">
              <TreeSidebar
                menuList={treeMenuList}
                onMenuClick={onMenuClick}
                pathPrefix="/me"
              />
            </div>
          )}
        </div>

        {/* Main Content */}
        <div className="flex-1 bg-white p-4">
          <Outlet />
        </div>
      </div>
    </ProtectedRoute>
  );
};

export default UserLayoutMobile;
