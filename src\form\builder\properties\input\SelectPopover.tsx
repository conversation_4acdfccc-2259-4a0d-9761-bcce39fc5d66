/**
 * Select Popover Component
 * Generic popover for select-like options
 */

import React, { useState } from 'react';
import { cn } from '@/lib/utils';
import { ChevronDown, Check } from 'lucide-react';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Button } from '@/components/ui/button';

interface SelectOption {
  value: string;
  label: string;
  description?: string;
}

interface SelectPopoverProps {
  value?: string;
  onValueChange?: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  options: SelectOption[];
  className?: string;
}

export const SelectPopover: React.FC<SelectPopoverProps> = ({
  value,
  onValueChange,
  placeholder = 'Select...',
  disabled = false,
  options,
  className
}) => {
  const [open, setOpen] = useState(false);
  
  const currentOption = options.find(opt => opt.value === value);

  const handleValueSelect = (newValue: string) => {
    onValueChange?.(newValue);
    setOpen(false);
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            "h-7 justify-between text-xs",
            !currentOption && "text-muted-foreground",
            disabled && "opacity-50 cursor-not-allowed",
            className
          )}
          disabled={disabled}
        >
          <span className="truncate">
            {currentOption?.label || placeholder}
          </span>
          <ChevronDown className="ml-2 h-3 w-3 opacity-50 flex-shrink-0" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-56 p-1" align="start">
        <div className="space-y-0.5">
          {options.map((option) => (
            <button
              key={option.value}
              onClick={() => handleValueSelect(option.value)}
              className={cn(
                "flex items-center justify-between w-full p-2 text-xs rounded hover:bg-gray-100 transition-colors text-left",
                value === option.value && "bg-blue-100 text-blue-900"
              )}
            >
              <div className="flex-1">
                <div className="font-medium">{option.label}</div>
                {option.description && (
                  <div className="text-gray-500 text-xs">{option.description}</div>
                )}
              </div>
              {value === option.value && (
                <Check className="h-3 w-3 text-blue-600 flex-shrink-0" />
              )}
            </button>
          ))}
        </div>
      </PopoverContent>
    </Popover>
  );
};