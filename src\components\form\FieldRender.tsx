import { DynamicField } from "./DynamicField";
import { DataType, DataTypeValue, FieldConfig } from "./registry";

type FieldRenderProps = {
  isViewMode: boolean;
  field: FieldConfig;
  value: DataTypeValue[DataType];
  onChange: (value: DataTypeValue[DataType]) => void;
  onBlur?: () => void;
  error?: string;
};

export function FieldRender({
  isViewMode,
  field,
  value,
  onChange,
  onBlur,
  error,
}: FieldRenderProps) {
  switch (field.data_type) {
    case "text":
      return (
        <DynamicField<"text">
          isViewMode={isViewMode}
          key={field.label}
          field={field as FieldConfig<"text">}
          value={value as string}
          onChange={onChange as (v: string) => void}
          onBlur={onBlur}
          error={error}
        />
      );
    case "number":
      return (
        <DynamicField<"number">
          isViewMode={isViewMode}
          key={field.label}
          field={field as FieldConfig<"number">}
          value={value as number}
          onChange={onChange as (v: number) => void}
          onBlur={onBlur}
          error={error}
        />
      );
    case "boolean":
      return (
        <DynamicField<"boolean">
          isViewMode={isViewMode}
          key={field.label}
          field={field as FieldConfig<"boolean">}
          value={value as boolean}
          onChange={onChange as (v: boolean) => void}
          onBlur={onBlur}
          error={error}
        />
      );
    case "boolean_array":
      return (
        <DynamicField<"boolean_array">
          isViewMode={isViewMode}
          key={field.label}
          field={field as FieldConfig<"boolean_array">}
          value={value as boolean[]}
          onChange={onChange as (v: boolean[]) => void}
          onBlur={onBlur}
          error={error}
        />
      );
    case "number_array":
      return (
        <DynamicField<"number_array">
          isViewMode={isViewMode}
          key={field.label}
          field={field as FieldConfig<"number_array">}
          value={value as number[]}
          onChange={onChange as (v: number[]) => void}
          onBlur={onBlur}
          error={error}
        />
      );
    case "text_array":
      return (
        <DynamicField<"text_array">
          isViewMode={isViewMode}
          key={field.label}
          field={field as FieldConfig<"text_array">}
          value={value as string[]}
          onChange={onChange as (v: string[]) => void}
          onBlur={onBlur}
          error={error}
        />
      );
    default:
      return null;
  }
}
