/**
 * Mock data for Form Editor testing
 */

export const registerFormMock = {
  id: "register-form",
  node: {
    id: "root-frame",
    type: "frame",
    styles: {
      container:
        "w-full max-w-3xl mx-auto p-8 bg-white rounded-xl shadow-lg grid grid-cols-1 md:grid-cols-2 gap-8",
    },
    children: [
      {
        id: "form-title",
        type: "title",
        styles: {
          container: "col-span-2 text-center mb-8",
          content: "text-4xl font-bold text-gray-800",
        },
        properties: {
          text: "Đăng Ký Tà<PERSON>hoản",
        },
      },
      {
        id: "main-content",
        type: "frame",
        styles: {
          container: "col-span-2 grid grid-cols-1 md:grid-cols-2 gap-8",
        },
        children: [
          {
            id: "left-frame",
            type: "frame",
            styles: { container: "space-y-4" },
            children: [
              {
                id: "email-field",
                type: "field",
                styles: { container: "" },
                properties: { label: "Email", placeholder: "Nhập email" },
                validation: {
                  required: { value: true, error: "Bạn phải nhập email" },
                  pattern: {
                    value: "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+.[a-zA-Z]{2,}$",
                    error: "Email không hợp lệ",
                  },
                },
                field: {
                  objectKey: "email",
                  defaultValue: "",
                  dataType: "string",
                  component: "TextInput",
                },
              },
              {
                id: "password-field",
                type: "field",
                styles: { container: "mb-4" },
                properties: { label: "Mật khẩu", placeholder: "Nhập mật khẩu" },
                validation: {
                  required: { value: true, error: "Bạn phải nhập mật khẩu" },
                  minLength: { value: 6, error: "Mật khẩu tối thiểu 6 ký tự" },
                },
                field: {
                  objectKey: "password",
                  defaultValue: "",
                  dataType: "string",
                  component: "PasswordInput",
                },
              },
              {
                id: "confirm-password-field",
                type: "field",
                styles: { container: "mb-4" },
                properties: {
                  label: "Nhập lại mật khẩu",
                  placeholder: "Nhập lại mật khẩu",
                },
                validation: {
                  required: {
                    value: true,
                    error: "Bạn phải nhập lại mật khẩu",
                  },
                  matchField: {
                    value: "password",
                    error: "Mật khẩu nhập lại không khớp",
                  },
                },
                field: {
                  objectKey: "confirmPassword",
                  defaultValue: "",
                  dataType: "string",
                  component: "PasswordInput",
                },
              },
              {
                id: "age-field",
                type: "field",
                styles: { container: "mb-4" },
                properties: { label: "Tuổi", placeholder: "Nhập tuổi" },
                validation: {
                  required: { value: true, error: "Bạn phải nhập tuổi" },
                  min: { value: 18, error: "Tuổi tối thiểu là 18" },
                  max: { value: 100, error: "Tuổi tối đa là 100" },
                },
                field: {
                  objectKey: "age",
                  defaultValue: "",
                  dataType: "number",
                  component: "NumberInput",
                },
              },
            ],
          },
          {
            id: "right-frame",
            type: "frame",
            styles: { container: "space-y-4" },
            children: [
              {
                id: "upload-field",
                type: "field",
                styles: { container: "mb-4" },
                properties: { label: "Tải lên File" },
                field: {
                  objectKey: "image",
                  defaultValue: "",
                  dataType: "string",
                  component: "SimpleUploadInput",
                },
                validation: {
                  required: { value: true, error: "Bạn phải tải lên File" },
                },
              },
              {
                id: "multi-upload-field",
                type: "field",
                styles: { container: "mb-4" },
                properties: { label: "Tải lên nhiều File" },
                field: {
                  objectKey: "images",
                  defaultValue: [],
                  dataType: "string[]",
                  component: "MultipleUploadInput",
                },
                validation: {
                  required: {
                    value: true,
                    error: "Bạn phải tải lên File",
                  },
                },
              },
              {
                id: "checkbox-agree",
                type: "field",
                styles: { container: "mb-4" },
                properties: {
                  placeholder:
                    "Click vào đăng ký nghĩa là bạn đã đồng ý với điều khoản sử dụng.",
                },
                validation: {
                  required: {
                    value: true,
                    error: "Bạn phải đồng ý với điều khoản sử dụng",
                  },
                },
                field: {
                  objectKey: "agreeTerms",
                  defaultValue: true,
                  dataType: "boolean",
                  component: "CheckboxInput",
                },
              },
            ],
          },
        ],
      },
      {
        id: "button-group",
        type: "frame",
        styles: { container: "col-span-2 flex space-x-4 w-full mt-4" },
        children: [
          {
            id: "cancel-button",
            type: "control",
            styles: {
              container: "flex-1",
              content:
                "w-full bg-gray-600 text-white py-2 px-4 rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition duration-200",
            },
            properties: { controlType: "reset", text: "Hủy" },
          },
          {
            id: "submit-button",
            type: "control",
            styles: {
              container: "flex-1",
              content:
                "w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition duration-200",
            },
            properties: { controlType: "submit", text: "Đăng Ký" },
          },
        ],
      },
      {
        id: "login-redirect",
        type: "redirect",
        styles: {
          container: "col-span-2 text-center mt-4",
          content: "text-blue-600 hover:text-blue-800 underline",
        },
        properties: { text: "Đã có tài khoản? Đăng nhập", url: "/login" },
      },
    ],
  },
};
