# KHA Portal - Frontend

## 📖 Overview

Frontend application for KHA Portal built with React, TypeScript, and Vite.

## 🚀 Quick Start

### Prerequisites

- Node.js >= 18.0.0
- Yarn >= 1.22.0 (Required - this project uses Yarn exclusively)

### Installation

```bash
yarn install
```

### Development

```bash
yarn dev
```

### Build

```bash
yarn build
```

### Other Commands

```bash
yarn lint      # Run ESLint
yarn preview   # Preview production build
yarn check-pm  # Check package manager setup
```

## 📁 Project Structure

```
src/
├── api/           # API configuration
├── components/    # Shared components
├── features/      # Feature-based modules
│   ├── album/     # Album management
│   ├── auth/      # Authentication
│   ├── media/     # Image management
│   ├── users/     # User management
│   └── builder/   # Page builder (nodes)
├── hooks/         # Custom React hooks
├── store/         # Redux store
├── styles/        # Global styles
└── utils/         # Utility functions
```

## 🛠️ Tech Stack

- **Framework**: React 19
- **Build Tool**: Vite 6
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **State Management**: Redux Toolkit
- **Routing**: React Router
- **UI Components**: Radix UI
- **Forms**: TanStack Form
- **Data Fetching**: TanStack Query

## 📚 Guidelines for AI Assistants & Developers

**Essential reading before working on this project:**

- **[AI Assistant Guidelines](./guidelines/AI_ASSISTANT_GUIDELINES.md)** - Architecture, patterns, workflows, code quality & performance (READ THIS FIRST)
- **[Node Development Guide](./guidelines/NODE_DEVELOPMENT_GUIDE.md)** - For builder components development
- **[Package Manager Policy](./guidelines/PACKAGE_MANAGER.md)** - Yarn enforcement details

## 🚨 Critical Rules for AI Assistants

1. **Package Manager**: Use `yarn` only (npm/pnpm are blocked at runtime)
2. **File Organization**: Follow feature-based structure (`src/features/[feature]/`)
3. **Patterns**: Copy existing patterns from similar features
4. **TypeScript**: Always define interfaces, no `any` types

## 🎯 Quick Development Pattern

```bash
# 1. Find similar feature for reference
src/features/album/  # Example: album management
src/features/auth/   # Example: authentication

# 2. Follow the structure
src/features/[your-feature]/
├── components/     # React components
├── states/         # API, hooks, types
└── pages/          # Route pages

# 3. Use existing UI components
import { Button } from '@/components/ui/button';
```

## 🤝 Contributing

Please read the guidelines in the `./guidelines/` directory before contributing.

---

**For AI Assistants**: Start by reading [AI Assistant Guidelines](./guidelines/AI_ASSISTANT_GUIDELINES.md) to understand the project architecture and development patterns.
