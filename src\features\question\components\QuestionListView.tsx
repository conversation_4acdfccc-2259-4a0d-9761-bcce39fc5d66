import React from "react";
import { Question, Pagination } from "../states/types";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Edit,
  Trash2,
  CheckCircle,
  XCircle,
  Clock,
  Eye,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
} from "lucide-react";
import { useQuestion } from "../hooks/useQuestion";

interface QuestionListViewProps {
  questions: Question[];
  pagination: Pagination;
  onPageChange: (page: number) => void;
  onSizeChange: (size: number) => void;
  onViewDetail: (id: number) => void;
  currentPage: number;
  currentSize: number;
}

export const QuestionListView: React.FC<QuestionListViewProps> = ({
  questions,
  pagination,
  onPageChange,
  onSizeChange,
  onViewDetail,
  currentPage,
  currentSize,
}) => {
  const {
    selectQuestion,
    deleteQuestion,
    updateQuestionStatus,
    publishQuestion,
  } = useQuestion();

  const handleEdit = (question: Question) => {
    selectQuestion(question);
  };

  const handleDelete = (questionId: number) => {
    if (confirm("Bạn có chắc chắn muốn xóa câu hỏi này?")) {
      deleteQuestion(questionId);
    }
  };

  const handlePublish = (questionId: number) => {
    publishQuestion(questionId);
  };

  const handleTrash = (questionId: number) => {
    updateQuestionStatus(questionId, "TRASH");
  };

  const handleDraft = (questionId: number) => {
    updateQuestionStatus(questionId, "DRAFT");
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "PUBLISHED":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "TRASH":
        return <XCircle className="h-4 w-4 text-red-500" />;
      case "NEW":
        return <Clock className="h-4 w-4 text-blue-500" />;
      case "DRAFT":
        return <Clock className="h-4 w-4 text-yellow-500" />;
      default:
        return null;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "PUBLISHED":
        return "Đã xuất bản";
      case "TRASH":
        return "Thùng rác";
      case "NEW":
        return "Chưa trả lời";
      case "DRAFT":
        return "Đã trả lời";
      default:
        return status;
    }
  };

  const getStatusVariant = (
    status: string
  ): "default" | "secondary" | "destructive" | "outline" => {
    switch (status) {
      case "PUBLISHED":
        return "default";
      case "TRASH":
        return "destructive";
      case "NEW":
        return "secondary";
      case "DRAFT":
        return "outline";
      default:
        return "outline";
    }
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString("vi-VN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const totalPages = Math.ceil((pagination?.totalElements || 0) / currentSize);
  const startItem = currentPage * currentSize + 1;
  const endItem = Math.min(
    (currentPage + 1) * currentSize,
    pagination?.totalElements || 0
  );

  if (!questions || questions.length === 0) {
    return (
      <Card>
        <CardContent className="p-6">
          <p className="text-center text-gray-500">
            Không tìm thấy câu hỏi nào
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Results header */}
      <div className="flex justify-between items-center">
        <h2 className="text-lg font-semibold">
          Danh sách câu hỏi ({pagination?.totalElements || 0} kết quả)
        </h2>
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-500">Hiển thị:</span>
          <Select
            value={String(currentSize)}
            onValueChange={(value) => onSizeChange(Number(value))}
          >
            <SelectTrigger className="w-20">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="10">10</SelectItem>
              <SelectItem value="20">20</SelectItem>
              <SelectItem value="50">50</SelectItem>
              <SelectItem value="100">100</SelectItem>
            </SelectContent>
          </Select>
          <span className="text-sm text-gray-500">mục/trang</span>
        </div>
      </div>

      {/* Question list */}
      <div className="space-y-3">
        {questions.map((question) => (
          <Card key={question.id} className="hover:shadow-md transition-shadow">
            <CardHeader className="pb-2">
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <CardTitle className="text-sm mb-2">
                    {question.content.title}
                  </CardTitle>
                  <div className="text-xs text-gray-500 space-y-1">
                    <div>Người hỏi: {question.asker.fullName}</div>
                    <div>Email: {question.asker.email}</div>
                    <div>Ngày tạo: {formatDate(question.createdAt)}</div>
                  </div>
                </div>
                <div className="flex gap-1 ml-4">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => onViewDetail(question.id)}
                    title="Xem chi tiết"
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleEdit(question)}
                    title="Chỉnh sửa"
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  {question.status === "NEW" && (
                    <>
                      <Button
                        size="sm"
                        variant="default"
                        onClick={() => handleDraft(question.id)}
                        title="Chuyển về nháp"
                      >
                        <CheckCircle className="h-4 w-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="destructive"
                        onClick={() => handleTrash(question.id)}
                        title="Chuyển vào thùng rác"
                      >
                        <XCircle className="h-4 w-4" />
                      </Button>
                    </>
                  )}
                  {question.status === "DRAFT" && (
                    <>
                      <Button
                        size="sm"
                        variant="default"
                        onClick={() => handlePublish(question.id)}
                        title="Xuất bản"
                      >
                        <CheckCircle className="h-4 w-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="destructive"
                        onClick={() => handleTrash(question.id)}
                        title="Chuyển vào thùng rác"
                      >
                        <XCircle className="h-4 w-4" />
                      </Button>
                    </>
                  )}
                  {question.status === "PUBLISHED" && (
                    <Button
                      size="sm"
                      variant="secondary"
                      onClick={() => handleDraft(question.id)}
                      title="Chuyển về nháp"
                    >
                      <Clock className="h-4 w-4" />
                    </Button>
                  )}
                  {question.status === "TRASH" && (
                    <Button
                      size="sm"
                      variant="default"
                      onClick={() => handleDraft(question.id)}
                      title="Khôi phục về nháp"
                    >
                      <Clock className="h-4 w-4" />
                    </Button>
                  )}
                  <Button
                    size="sm"
                    variant="destructive"
                    onClick={() => handleDelete(question.id)}
                    title="Xóa"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                {question.content.question}
              </p>
              <div className="flex justify-between items-center">
                <div className="flex gap-2">
                  <Badge
                    variant={getStatusVariant(question.status)}
                    className="flex items-center gap-1"
                  >
                    {getStatusIcon(question.status)}
                    {getStatusText(question.status)}
                  </Badge>
                  <Badge variant="outline">{question.topic}</Badge>
                </div>
                <div className="text-xs text-gray-400">ID: {question.id}</div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-between items-center pt-4">
          <div className="text-sm text-gray-500">
            Hiển thị {startItem}-{endItem} của {pagination?.totalElements || 0}{" "}
            kết quả
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(0)}
              disabled={currentPage === 0}
            >
              <ChevronsLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(currentPage - 1)}
              disabled={currentPage === 0}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <span className="text-sm">
              Trang {currentPage + 1} / {totalPages}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(currentPage + 1)}
              disabled={currentPage >= totalPages - 1}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(totalPages - 1)}
              disabled={currentPage >= totalPages - 1}
            >
              <ChevronsRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};
