import { z, ZodTypeAny, ZodObject } from "zod";
import { FieldConfig } from "./registry";

const REQUIRED_MESSAGE = "Nội dung này là bắt buộc";

export function fieldConfigToZod(field: FieldConfig): ZodTypeAny {
  const v = field.validation ?? {};
  const required = field.validation?.required === true;

  switch (field.data_type) {
    case "text": {
      let s = z.string();
      if (v.minLength !== undefined)
        s = s.min(v.minLength, {
          message: `Đ<PERSON> dài tối thiểu là ${v.minLength} ký tự`,
        });
      if (v.maxLength !== undefined)
        s = s.max(v.maxLength, {
          message: `Độ dài tối đa là ${v.maxLength} ký tự`,
        });
      if (v.pattern)
        s = s.regex(new RegExp(v.pattern), {
          message: "Nội dung không đúng định dạng",
        });
      return required ? s.nonempty(REQUIRED_MESSAGE) : s.optional();
    }
    case "number": {
      let s = z.number({ required_error: REQUIRED_MESSAGE });
      if (v.min !== undefined)
        s = s.min(v.min, { message: `Giá trị tối thiểu là ${v.min}` });
      if (v.max !== undefined)
        s = s.max(v.max, { message: `Giá trị tối đa là ${v.max}` });
      return required ? s : s.optional();
    }
    case "boolean": {
      const s = z.boolean({ required_error: REQUIRED_MESSAGE });
      return required ? s : s.optional();
    }
    case "text_array": {
      const s = z.array(z.string());
      return required ? s.nonempty(REQUIRED_MESSAGE) : s.optional();
    }
    case "number_array": {
      const s = z.array(z.number());
      return required ? s.nonempty(REQUIRED_MESSAGE) : s.optional();
    }
    case "boolean_array": {
      const s = z.array(z.boolean());
      return required ? s.nonempty(REQUIRED_MESSAGE) : s.optional();
    }
    default:
      return z.any();
  }
}

export function buildZodSchema(
  fields: FieldConfig[]
): ZodObject<Record<string, ZodTypeAny>> {
  const shape: Record<string, ZodTypeAny> = {};
  for (const field of fields) {
    shape[field.id] = fieldConfigToZod(field);
  }
  return z.object(shape);
}
