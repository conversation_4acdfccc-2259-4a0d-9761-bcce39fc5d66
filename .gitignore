# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Package manager lockfiles (keep yarn.lock, ignore others)
package-lock.json
pnpm-lock.yaml
.pnpm-debug.log

# Note: .md files are now tracked in version control
# Guidelines and documentation are important for the project
