import { ColumnConfig } from "@/components/table/registry";

export const ACTION_COLUMNS: ColumnConfig[] = [
  {
    accessorKey: "id",
    header: "ID",
  },
  {
    accessorKey: "code",
    header: "<PERSON><PERSON> quyền",
    cell: {
      component: "TextCell",
      props: {},
    },
  },
  {
    accessorKey: "name",
    header: "Tên quyền",
    cell: {
      component: "TextCell",
      props: {},
    },
  },
  {
    accessorKey: "createdAt",
    header: "Ngày tạo",
    cell: {
      component: "DateTimeCell",
      props: {
        format: "dd/MM/yyyy HH:mm",
      },
    },
  },
  {
    accessorKey: "updatedAt",
    header: "<PERSON><PERSON><PERSON> cập nhật",
    cell: {
      component: "DateTimeCell",
      props: {
        format: "dd/MM/yyyy HH:mm",
      },
    },
  },
  {
    accessorKey: "actions",
    header: "<PERSON><PERSON> tác",
    cell: {
      component: "ActionCell",
      props: {
        actions: ["edit"],
      },
    },
  },
];
