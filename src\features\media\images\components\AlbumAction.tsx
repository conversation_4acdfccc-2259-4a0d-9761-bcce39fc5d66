import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  PenLine,
  Trash2,
  UploadCloud,
  Undo2,
  XCircle,
  CheckCircle,
  RefreshCcw,
} from "lucide-react";
import {
  Tooltip,
  TooltipTrigger,
  TooltipContent,
} from "@/components/ui/tooltip";
import type { Album, AlbumStatus } from "../states/types";
import { updateAlbumStatus, approveAlbum, rejectAlbum } from "../states/api";
import { useAppDispatch } from "@/store/rootReducer";
import { triggerRefetch, setCurrentAlbum } from "../states/slices";
import { toast } from "sonner";
import { ActionConfirmDialog } from "./ActionConfirmDialog";
import { useSwitchMode } from "../states/hooks";

export type AlbumAction =
  | "edit"
  | "delete"
  | "request-publish"
  | "publish"
  | "reject"
  | "unpublish"
  | "restore";

const actionMap: Record<
  AlbumAction,
  { icon: React.ReactNode; label: string; confirm: string }
> = {
  delete: {
    icon: <Trash2 className="w-4 h-4" />,
    label: "Xoá",
    confirm: "Bạn chắc chắn muốn chuyển thư viện ảnh vào thùng rác?",
  },
  "request-publish": {
    icon: <UploadCloud className="w-4 h-4" />,
    label: "Gửi xuất bản",
    confirm: "Gửi yêu cầu xuất bản thư viện ảnh này?",
  },
  publish: {
    icon: <CheckCircle className="w-4 h-4" />,
    label: "Xuất bản",
    confirm: "Xuất bản thư viện ảnh này?",
  },
  reject: {
    icon: <XCircle className="w-4 h-4" />,
    label: "Từ chối",
    confirm: "Từ chối thư viện ảnh này?",
  },
  unpublish: {
    icon: <RefreshCcw className="w-4 h-4" />,
    label: "Huỷ xuất bản",
    confirm: "Huỷ xuất bản thư viện ảnh này?",
  },
  restore: {
    icon: <Undo2 className="w-4 h-4" />,
    label: "Khôi phục",
    confirm: "Khôi phục thư viện ảnh về bản nháp?",
  },
  edit: {
    icon: <PenLine className="w-4 h-4" />,
    label: "Xem chi tiết",
    confirm: "Xem chi tiết thư viện ảnh này?",
  },
};

const editAction = {
  icon: <PenLine className="w-4 h-4" />,
  label: "Xem chi tiết",
};

function getActionsByStatus(status: AlbumStatus): AlbumAction[] {
  switch (status) {
    case "DRAFT":
      return ["edit", "delete", "request-publish"];
    case "REVIEW":
      return ["publish", "reject"];
    case "PUBLISHED":
      return ["unpublish"];
    case "UNPUBLISHED":
    case "REJECTED":
      return ["restore"];
    case "TRASH":
      return ["restore"];
    default:
      return [];
  }
}

export function AlbumAction({ row: album }: { row: Album }) {
  const dispatch = useAppDispatch();
  const { switchMode } = useSwitchMode();
  const actions = getActionsByStatus(album.status);
  const [loading, setLoading] = useState<AlbumAction | null>(null);
  const [dialog, setDialog] = useState<AlbumAction | null>(null);

  const doAction = async (action: AlbumAction) => {
    setLoading(action);
    try {
      switch (action) {
        case "delete":
          await updateAlbumStatus(album.id, "TRASH");
          toast.success("Đã xoá vào thùng rác");
          break;
        case "request-publish":
          await updateAlbumStatus(album.id, "REVIEW");
          toast.success("Đã gửi yêu cầu xuất bản");
          break;
        case "publish":
          await approveAlbum(album.id);
          toast.success("Xuất bản thành công");
          break;
        case "reject":
          await rejectAlbum(album.id);
          toast.success("Đã từ chối thư viện ảnh");
          break;
        case "unpublish":
          await updateAlbumStatus(album.id, "UNPUBLISHED");
          toast.success("Đã huỷ xuất bản");
          break;
        case "restore":
          await updateAlbumStatus(album.id, "DRAFT");
          toast.success("Đã khôi phục về bản nháp");
          break;
        case "edit":
          onEdit();
          break;
      }
      dispatch(triggerRefetch());
      setDialog(null);
    } catch {
      toast.error("Thao tác thất bại!");
    }
    setLoading(null);
  };

  const onEdit = () => {
    switchMode("detail", String(album.id));
    dispatch(setCurrentAlbum(album));
  };

  const handleClick = (action: AlbumAction) => {
    if (action === "edit" && album.status === "DRAFT") {
      onEdit();
    } else {
      setDialog(action);
    }
  };

  return (
    <>
      <div className="flex gap-1">
        {actions.map((action) =>
          action === "edit" ? (
            <Tooltip key="edit">
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => handleClick("edit")}
                  disabled={loading !== null}
                >
                  {editAction.icon}
                </Button>
              </TooltipTrigger>
              <TooltipContent side="top">{editAction.label}</TooltipContent>
            </Tooltip>
          ) : (
            <Tooltip key={action}>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => handleClick(action)}
                  disabled={loading !== null}
                >
                  {actionMap[action].icon}
                </Button>
              </TooltipTrigger>
              <TooltipContent side="top">
                {actionMap[action].label}
              </TooltipContent>
            </Tooltip>
          )
        )}
      </div>

      {dialog && (
        <ActionConfirmDialog
          open={!!dialog}
          onOpenChange={(open: boolean) => {
            if (!open) setDialog(null);
          }}
          onConfirm={() => doAction(dialog)}
          icon={actionMap[dialog].icon}
          confirmText={actionMap[dialog].confirm}
          loading={loading === dialog}
        />
      )}
    </>
  );
}
