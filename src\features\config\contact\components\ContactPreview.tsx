import React from "react";
import { ContactInfoDisplay } from "../../shared/components/displays";
import { PreviewWrapper } from "../../shared/components/wrappers/PreviewWrapper";
import type { ContactConfig } from "../states/type";

/**
 * Props for the ContactPreview component
 */
interface ContactPreviewProps {
  /** Contact configuration data to preview */
  data: ContactConfig | null;
  /** Whether to show the preview */
  visible: boolean;
}

/**
 * ContactPreview component displays clean contact information
 * without highlighting effects, suitable for reuse in public pages
 */
export const ContactPreview: React.FC<ContactPreviewProps> = ({
  data,
  visible,
}) => {
  if (!visible || !data?.contacts) {
    return null;
  }

  return (
    <PreviewWrapper
      title="Xem trước: Thông tin liên hệ"
      description="Hiển thị thông tin liên hệ như sẽ xuất hiện trên trang công khai."
      theme="blue"
      visible={visible}
    >
      <ContactInfoDisplay
        contacts={data.contacts}
      />
    </PreviewWrapper>
  );
};
