import { AlbumHeader, AlbumInfo } from "./index";
import { useAlbumDetailLogic } from "../../hooks";
import { Album } from "../../states/types";
// import { AddMediaModal } from "../AddMediaModal";
// import { MediaPreviewModal } from "../MediaPreviewModal";
// import { DeleteConfirmDialog } from "../DeleteConfirmDialog";

interface AlbumDetailRefactoredProps {
  album: Album;
  onAlbumUpdated?: () => void | Promise<void>;
}

/**
 * Refactored AlbumDetail Component - DEMO/PROOF OF CONCEPT
 *
 * ✅ Separation of Concerns:
 * - UI Components: AlbumHeader, AlbumInfo
 * - Logic: useAlbumDetailLogic hook
 * - Each component has single responsibility
 *
 * ✅ File Size: ~50 lines (vs original 465 lines)
 * ✅ Maintainability: Easy to test and modify individual parts
 * ✅ Reusability: Components can be reused elsewhere
 *
 * Note: This is a proof-of-concept showing refactoring patterns.
 * Some prop interfaces may need adjustment to match existing components.
 */
export function AlbumDetailRefactored({
  album,
  onAlbumUpdated,
}: AlbumDetailRefactoredProps) {
  const logic = useAlbumDetailLogic({ album, onAlbumUpdated });

  return (
    <div className="p-4 space-y-6">
      {/* Header Section */}
      <AlbumHeader albumName={album.name} onBack={logic.handleBackToList} />

      {/* Album Information */}
      <AlbumInfo album={album} />

      {/* TODO: Add remaining sections */}
      {/* - AlbumCoverSection */}
      {/* - AlbumMediaGrid */}
      {/* - AlbumActions */}

      {/* Modals - TODO: Fix prop interfaces to match existing components */}
      {/*
      <AddMediaModal
        isOpen={logic.isAddModalOpen}
        onClose={() => logic.setIsAddModalOpen(false)}
        onAddNew={logic.handleAddMedia}
        onAddExisting={logic.handleAddExistingMedia}
      />

      {logic.previewMedia && (
        <MediaPreviewModal
          media={logic.previewMedia}
          onClose={() => logic.setPreviewMedia(null)}
          onEdit={(media, updates) =>
            logic.updateMediaDetails(album.id, media.id, updates)
          }
          onDelete={() => logic.handleDeleteMedia(logic.previewMedia!.id)}
          onSetCover={logic.handleSetCoverImage}
        />
      )}

      <DeleteConfirmDialog
        isOpen={logic.showDeleteConfirm}
        onClose={() => logic.setIsAddModalOpen(false)}
        onConfirm={() =>
          logic.deleteTarget && logic.handleDeleteMedia(logic.deleteTarget.id)
        }
        itemName={logic.deleteTarget?.name || ""}
      />
      */}
    </div>
  );
}
