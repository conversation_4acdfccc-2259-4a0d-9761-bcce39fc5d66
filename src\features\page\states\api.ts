import { BaseResponse } from "@/api/restApi";
import { AxiosResponse } from "axios";
import { API_ENDPOINTS } from "@/api/endpoints";
import { Post } from "@/features/post/states/types";
import {
  fetchPost,
  createPost,
  updatePost,
  checkSlugExists,
  generateSlugFromTitle,
  generateUniqueSlug,
  fetchPostsByType,
} from "@/features/post/states/api";
import {
  linkPostToCategory,
  unlinkPostFromCategory,
} from "@/features/category/states/api";
import { CreatePageRequest, LinkPageToCategoryRequest } from "./types";

// Types for lightweight page data (optimized API response)
export interface PageSummary {
  id: number;
  title: string;
  slug: string;
  status: "DRAFT" | "PUBLISHED" | "UNPUBLISHED" | "TRASH";
}

// Search pages with lightweight data (requires keyword, returns paginated results)
// Follow project pattern: AxiosResponse<BaseResponse<T>>
export async function searchPages(
  keyword: string,
  page: number = 0,
  size: number = 20,
  status?: string // Add status filter parameter
): Promise<AxiosResponse<BaseResponse<PageSummary[]>>> {
  const { restApi } = await import("@/api/restApi");

  const params: {
    type: string;
    keyword: string;
    page: number;
    size: number;
    status?: string;
  } = {
    type: "PAGE",
    keyword: keyword.trim(),
    page,
    size,
  };

  // Add status filter if provided
  if (status) {
    params.status = status;
  }

  const res = await restApi.get<BaseResponse<PageSummary[]>>(
    `${API_ENDPOINTS.PORTAL.ADMIN.POSTS}/filter`,
    { params }
  );
  return res;
}

// Fetch pages summary - now returns empty since API requires keyword
export async function fetchPagesSummary(): Promise<
  BaseResponse<PageSummary[]>
> {
  // Since API requires keyword, return empty array for initial load
  return {
    code: 200,
    message: "Use search with keyword to get pages",
    data: [],
  };
}

// Fetch all pages (posts with type PAGE) - with full content (heavy)
// NOTE: This loads full content and should be used sparingly
// DEPRECATED: Use fetchPagesSummary() for lists, fetchPageById() for individual pages
export async function fetchPages(): Promise<BaseResponse<Post[]>> {
  console.warn(
    "fetchPages() loads heavy content. Consider using fetchPagesSummary() for lists."
  );
  return fetchPostsByType("PAGE");
}

// Fetch a specific page by ID - reuse post API
export async function fetchPageById(id: number): Promise<BaseResponse<Post>> {
  return fetchPost(id);
}

// Create a new page - reuse post API
export async function createPage(
  payload: CreatePageRequest
): Promise<BaseResponse<Post>> {
  const postData = {
    title: payload.title,
    slug: payload.slug,
    excerpt: payload.excerpt,
    content: {
      type: "root",
      children: [],
    }, // Default page content structure
    postType: "PAGE" as const,
    status: "DRAFT" as const, // Default status for new pages
    authorId: "", // Empty string or should come from auth state
    parentId: null, // Pages typically don't have parents
  };

  console.log("Creating page with payload:", JSON.stringify(postData, null, 2));

  try {
    const result = await createPost(postData);
    console.log("Page created successfully:", result);
    return result;
  } catch (error) {
    console.error("Failed to create page:", error);
    throw error;
  }
}

// Update a page - reuse post API
export async function updatePage(
  id: number,
  payload: Omit<
    Post,
    | "id"
    | "createdAt"
    | "updatedAt"
    | "publishedAt"
    | "postType"
    | "status"
    | "authorId"
  >
): Promise<BaseResponse<Post>> {
  return updatePost(id, payload);
}

// Update page status - for restoring from TRASH or changing status
export async function updatePageStatus(
  id: number,
  status: "DRAFT" | "PUBLISHED" | "UNPUBLISHED" | "TRASH"
): Promise<BaseResponse<void>> {
  const { API_URL, restApi } = await import("@/api/restApi");

  const res = await restApi.put<BaseResponse<void>>(
    `${API_URL}${API_ENDPOINTS.PORTAL.ADMIN.POSTS}/${id}/status/${status}`
  );
  return res.data;
}

// Delete a page - UPDATE: Change status to TRASH instead of DELETE
export async function deletePage(id: number): Promise<BaseResponse<void>> {
  // Use updatePageStatus to set status to TRASH instead of actual deletion
  console.log(`🗑️ Moving page ${id} to trash instead of permanent deletion`);
  return updatePageStatus(id, "TRASH");
}

// Link a page to a category - reuse category API
export async function linkPageToCategory(
  payload: LinkPageToCategoryRequest
): Promise<BaseResponse> {
  const result = await linkPostToCategory(payload.categoryId, payload.pageId);
  return result.data as BaseResponse;
}

// Unlink page from category - reuse category API
export async function unlinkPageFromCategory(
  categoryId: number
): Promise<BaseResponse> {
  const result = await unlinkPostFromCategory(categoryId);
  return result.data as BaseResponse;
}

// Re-export utility functions from post module
export { checkSlugExists, generateSlugFromTitle, generateUniqueSlug };
