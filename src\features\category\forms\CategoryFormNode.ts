import { FormNode } from "@/form/types";

export const CategoryFormNode: FormNode = {
  id: "category-form",
  type: "frame",
  properties: {
    label: "Thông tin chuyên mục",
  },
  children: [
    {
      id: "category-id",
      type: "field",
      properties: {
        label: "ID",
        disabled: true,
        placeholder: "Tự động",
        fieldType: "NumberInput",
      },
      field: {
        objectKey: "id",
        defaultValue: 0,
        dataType: "number",
        component: "NumberInput",
      },
    },
    {
      id: "category-type",
      type: "field",
      properties: {
        label: "Loại",
        disabled: true,
        options: ["public-menu", "admin-menu", "user-menu"],
        labels: ["<PERSON><PERSON>ng khai", "Quản trị viên", "Người dùng"],
        fieldType: "DropdownInput",
      },
      field: {
        objectKey: "type",
        defaultValue: "public-menu",
        dataType: "string",
        component: "DropdownInput",
      },
    },
    {
      id: "category-parent",
      type: "field",
      properties: {
        label: "Chuyên mục cha",
        placeholder: "Chọn chuyên mục cha",
        fieldType: "PickCategoryInput",
      },
      field: {
        objectKey: "parentId",
        defaultValue: null,
        dataType: "number",
        component: "PickCategoryInput",
      },
    },
    {
      id: "category-name",
      type: "field",
      properties: {
        label: "Tên chuyên mục",
        placeholder: "Nhập tên chuyên mục",
        fieldType: "TextInput",
      },
      field: {
        objectKey: "name",
        defaultValue: "",
        dataType: "string",
        component: "TextInput",
      },
      validation: {
        required: {
          value: true,
          error: "Tên chuyên mục là bắt buộc",
        },
        maxLength: {
          value: 200,
          error: "Tên chuyên mục không được quá 200 ký tự",
        },
      },
    },
    {
      id: "category-slug",
      type: "field",
      properties: {
        label: "Slug",
        placeholder: "Nhập slug",
        fieldType: "TextInput",
      },
      field: {
        objectKey: "slug",
        defaultValue: "",
        dataType: "string",
        component: "TextInput",
      },
      validation: {
        required: {
          value: true,
          error: "Slug là bắt buộc",
        },
        maxLength: {
          value: 200,
          error: "Slug không được quá 200 ký tự",
        },
      },
    },
    {
      id: "category-icon",
      type: "field",
      properties: {
        label: "Icon",
        placeholder: "Chọn icon",
        fieldType: "PickIconInput",
      },
      field: {
        objectKey: "description.icon",
        defaultValue: "Dot",
        dataType: "string",
        component: "PickIconInput",
      },
    },
    {
      id: "category-component",
      type: "field",
      properties: {
        label: "Component",
        placeholder: "Chọn component",
        fieldType: "PickComponentInput",
      },
      field: {
        objectKey: "description.component",
        defaultValue: "",
        dataType: "string",
        component: "PickComponentInput",
      },
    },
    {
      id: "category-status",
      type: "field",
      properties: {
        label: "Trạng thái",
        options: ["ACTIVE", "DISABLED"],
        labels: ["Hoạt động", "Vô hiệu hóa"],
        fieldType: "ToggleInput",
      },
      field: {
        objectKey: "status",
        defaultValue: "ACTIVE",
        dataType: "string",
        component: "ToggleInput",
      },
    },
    {
      id: "category-priority",
      type: "field",
      properties: {
        label: "Độ ưu tiên",
        placeholder: "Nhập độ ưu tiên (0-1000)",
        fieldType: "NumberInput",
      },
      field: {
        objectKey: "priority",
        defaultValue: 0,
        dataType: "number",
        component: "NumberInput",
      },
      validation: {
        min: {
          value: 0,
          error: "Độ ưu tiên không được nhỏ hơn 0",
        },
        max: {
          value: 1000,
          error: "Độ ưu tiên không được lớn hơn 1000",
        },
      },
    },
  ],
};
