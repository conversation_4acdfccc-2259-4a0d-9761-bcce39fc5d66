/**
 * Border Popover Component
 * Popover with grid layout for border width selection
 */

import React, { useState } from 'react';
import { cn } from '@/lib/utils';
import { ChevronDown, Check } from 'lucide-react';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Button } from '@/components/ui/button';

interface BorderOption {
  value: string;
  label: string;
  description: string;
  preview: string; // CSS for preview
}

interface BorderPopoverProps {
  value?: string;
  onValueChange?: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  type: 'width' | 'style' | 'radius';
}

// Border width options
const BORDER_WIDTH_OPTIONS: BorderOption[] = [
  { value: 'none', label: 'None', description: 'Không có', preview: 'border-0' },
  { value: '0', label: '0', description: '0px', preview: 'border-0' },
  { value: '1', label: '1', description: '1px', preview: 'border' },
  { value: '2', label: '2', description: '2px', preview: 'border-2' },
  { value: '4', label: '4', description: '4px', preview: 'border-4' },
  { value: '8', label: '8', description: '8px', preview: 'border-8' }
];

// Border style options
const BORDER_STYLE_OPTIONS: BorderOption[] = [
  { value: 'solid', label: 'Solid', description: 'Liền', preview: 'border-solid' },
  { value: 'dashed', label: 'Dashed', description: 'Nét đứt', preview: 'border-dashed' },
  { value: 'dotted', label: 'Dotted', description: 'Chấm', preview: 'border-dotted' },
  { value: 'double', label: 'Double', description: 'Đôi', preview: 'border-double' },
  { value: 'none', label: 'None', description: 'Không có', preview: 'border-none' }
];

// Border radius options
const BORDER_RADIUS_OPTIONS: BorderOption[] = [
  { value: 'none', label: 'None', description: 'Không bo', preview: 'rounded-none' },
  { value: 'sm', label: 'SM', description: '2px', preview: 'rounded-sm' },
  { value: 'base', label: 'Base', description: '4px', preview: 'rounded' },
  { value: 'md', label: 'MD', description: '6px', preview: 'rounded-md' },
  { value: 'lg', label: 'LG', description: '8px', preview: 'rounded-lg' },
  { value: 'xl', label: 'XL', description: '12px', preview: 'rounded-xl' },
  { value: '2xl', label: '2XL', description: '16px', preview: 'rounded-2xl' },
  { value: '3xl', label: '3XL', description: '24px', preview: 'rounded-3xl' },
  { value: 'full', label: 'Full', description: 'Tròn', preview: 'rounded-full' }
];

export const BorderPopover: React.FC<BorderPopoverProps> = ({
  value = 'none',
  onValueChange,
  placeholder = 'Chọn border',
  disabled = false,
  type
}) => {
  const [open, setOpen] = useState(false);

  // Get options based on type
  const getOptions = () => {
    switch (type) {
      case 'width':
        return BORDER_WIDTH_OPTIONS;
      case 'style':
        return BORDER_STYLE_OPTIONS;
      case 'radius':
        return BORDER_RADIUS_OPTIONS;
      default:
        return [];
    }
  };

  const options = getOptions();
  
  // Find current option to display
  const currentOption = options.find(opt => opt.value === value) || 
    { value, label: value, description: value, preview: '' };

  const handleValueSelect = (newValue: string) => {
    onValueChange?.(newValue);
    setOpen(false);
  };

  const getTypeLabel = () => {
    switch (type) {
      case 'width':
        return 'Độ rộng viền';
      case 'style':
        return 'Kiểu viền';
      case 'radius':
        return 'Bo góc';
      default:
        return 'Border';
    }
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            "h-7 w-full justify-between text-xs",
            !currentOption && "text-muted-foreground",
            disabled && "opacity-50 cursor-not-allowed"
          )}
          disabled={disabled}
        >
          <span className="truncate">
            {currentOption.label !== 'none' ? currentOption.label : placeholder}
          </span>
          <ChevronDown className="ml-2 h-3 w-3 shrink-0 opacity-50 flex-shrink-0" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-72 p-0" align="start">
        <div className="p-3">
          <h4 className="text-sm font-medium mb-3">{getTypeLabel()}</h4>
          
          {/* Grid layout for better UX */}
          <div className={cn(
            "grid gap-2",
            type === 'radius' ? "grid-cols-3" : "grid-cols-2"
          )}>
            {options.map((option) => (
              <button
                key={option.value}
                onClick={() => handleValueSelect(option.value)}
                className={cn(
                  "flex flex-col items-center p-3 text-xs rounded hover:bg-gray-100 transition-colors relative min-h-[70px] justify-center border border-gray-200",
                  value === option.value && "bg-blue-100 text-blue-900 ring-1 ring-blue-500"
                )}
                title={`${option.label} - ${option.description}`}
              >
                {/* Visual Preview */}
                <div className={cn(
                  "w-8 h-6 bg-gray-300 mb-2",
                  type === 'width' && option.preview,
                  type === 'style' && `border border-gray-600 ${option.preview}`,
                  type === 'radius' && `bg-blue-300 ${option.preview}`,
                  type === 'radius' && option.value === 'full' && "w-6 h-6"
                )} />
                
                <span className="font-medium text-center leading-tight">
                  {option.label}
                </span>
                <span className="text-gray-500 text-xs mt-0.5 text-center leading-tight">
                  {option.description}
                </span>
                {value === option.value && (
                  <Check className="h-3 w-3 absolute top-1 right-1" />
                )}
              </button>
            ))}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
};