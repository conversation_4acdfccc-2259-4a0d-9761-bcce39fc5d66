import { RootState } from "@/store/rootReducer";
import {
  AlbumQueryParams,
  AlbumStatus,
  Album,
  Media,
  MediaQueryParams,
} from "./types";
import { AlbumPageMode } from "./slices";

export const selectAlbumStatus = (state: RootState): AlbumStatus =>
  state.albumsFilterState.params.status || "DRAFT";

export const selectAlbumLoading = (state: RootState): boolean =>
  state.albumsFilterState.loading;

export const selectAlbumError = (state: RootState): string | null =>
  state.albumsFilterState.error;

export const selectAlbumQueryParams = (state: RootState): AlbumQueryParams =>
  state.albumsFilterState.params;

export const selectAlbumRefetch = (state: RootState): boolean =>
  state.albumsFilterState.refetch;

export const selectCurrentAlbum = (state: RootState): Album | null =>
  state.albumsFilterState.currentAlbum;

export const selectPageMode = (state: RootState): AlbumPageMode =>
  state.albumsFilterState.pageMode;

// Media selectors
export const selectCurrentAlbumMedia = (state: RootState): Media[] =>
  state.albumsFilterState.currentAlbumMedia;

export const selectMediaLoading = (state: RootState): boolean =>
  state.albumsFilterState.mediaLoading;

export const selectMediaError = (state: RootState): string | null =>
  state.albumsFilterState.mediaError;

export const selectMediaQueryParams = (state: RootState): MediaQueryParams =>
  state.albumsFilterState.mediaParams;

export const selectMediaHasMore = (state: RootState): boolean =>
  state.albumsFilterState.mediaHasMore;

export const selectMediaCurrentPage = (state: RootState): number =>
  state.albumsFilterState.mediaCurrentPage;
