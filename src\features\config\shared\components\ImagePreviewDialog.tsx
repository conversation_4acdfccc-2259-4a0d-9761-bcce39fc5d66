import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Trigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Eye, ExternalLink } from "lucide-react";

/**
 * Props for the ImagePreviewDialog component
 */
interface ImagePreviewDialogProps {
  /** Image URL to preview */
  imageUrl: string;
  /** Optional title for the dialog */
  title?: string;
  /** Optional trigger button text */
  triggerText?: string;
  /** Whether to show as icon button or text button */
  variant?: "icon" | "text";
  /** Button size */
  size?: "sm" | "default" | "lg";
}

/**
 * ImagePreviewDialog component for previewing images in a modal dialog
 * Used by portal links and introduction features for image preview functionality
 */
export const ImagePreviewDialog: React.FC<ImagePreviewDialogProps> = ({
  imageUrl,
  title = "Xem trước hình ảnh",
  triggerText = "Xem trước",
  variant = "icon",
  size = "sm",
}) => {
  // Don't render if no image URL provided
  if (!imageUrl || imageUrl.trim() === "") {
    return null;
  }

  const triggerButton = variant === "icon" ? (
    <Button variant="ghost" size={size} className="h-8 w-8 p-0">
      <Eye className="h-4 w-4" />
    </Button>
  ) : (
    <Button variant="outline" size={size}>
      <Eye className="h-4 w-4 mr-2" />
      {triggerText}
    </Button>
  );

  return (
    <Dialog>
      <DialogTrigger asChild>
        {triggerButton}
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-auto bg-white">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {title}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => window.open(imageUrl, '_blank')}
              className="h-8 w-8 p-0"
            >
              <ExternalLink className="h-4 w-4" />
            </Button>
          </DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div className="text-sm text-muted-foreground break-all">
            <strong>URL:</strong> {imageUrl}
          </div>
          <div className="flex justify-center">
            <img
              src={imageUrl}
              alt="Preview"
              className="max-w-full max-h-[60vh] object-contain rounded-lg shadow-lg"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.style.display = 'none';
                const errorDiv = target.nextElementSibling as HTMLElement;
                if (errorDiv) {
                  errorDiv.style.display = 'block';
                }
              }}
            />
            <div
              className="hidden text-center py-8 text-muted-foreground"
              style={{ display: 'none' }}
            >
              <div className="text-red-500 mb-2">Không thể tải hình ảnh</div>
              <div className="text-sm">Vui lòng kiểm tra URL hình ảnh</div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
