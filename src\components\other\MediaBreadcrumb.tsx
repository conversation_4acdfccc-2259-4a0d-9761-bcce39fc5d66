import React from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { Button } from "../ui/button";

/**
 * Props for the MediaBreadcrumb component
 */
interface MediaBreadcrumbProps {
  /** Optional callback function for back navigation */
  onBack?: () => void;
  /** Text to display for current location (e.g., album name) */
  currentPath: string;
  /** Optional custom text for back link */
  backText?: string;
  /** Optional additional CSS classes */
  className?: string;
}

/**
 * Reusable breadcrumb component for media navigation
 * Provides consistent navigation UI across media-related components
 */
export const MediaBreadcrumb: React.FC<MediaBreadcrumbProps> = ({
  onBack,
  currentPath,
  backText = "Quay lại danh sách thư viện",
  className = "",
}) => {
  return (
    <nav className={`text-sm text-muted-foreground ${className}`}>
      <ol className="flex items-center gap-2">
        {onBack && (
          <>
            <div className="flex flex-row items-center gap-3">
              <Button
                variant="default"
                size="sm"
                onClick={onBack}
                className="flex items-center gap-2"
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
            </div>
            <li className="inline-flex items-center gap-1">
              <button
                type="button"
                onClick={onBack}
                className="hover:text-primary text-foreground cursor-pointer transition-colors"
              >
                {backText}
              </button>
            </li>
            <li className="inline-flex items-center gap-1">
              <ChevronRight className="w-4 h-4" />
            </li>
          </>
        )}
        <li className="inline-flex items-center gap-1">
          <span className="text-foreground font-medium">{currentPath}</span>
        </li>
      </ol>
    </nav>
  );
};
