import React from "react";
import { UserAutoForm } from "../components/UserAutoForm";
import { CreateUser } from "../states/type";
import { toast } from "sonner";

export const UserCreatePage: React.FC = () => {
  const handleUserSubmit = (data: CreateUser) => {
    console.log("User form submitted:", data);

    // Hiển thị data để demo
    toast.success(`Dữ liệu form: ${JSON.stringify(data, null, 2)}`);

    // Có thể call API hoặc redirect sau khi tạo thành công
    // navigate('/users');
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center py-8">
      <div className="max-w-md w-full">
        <UserAutoForm onSubmit={handleUserSubmit} />
      </div>
    </div>
  );
};
