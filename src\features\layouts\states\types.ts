export interface LayoutState {
  // Sidebar state
  sidebarVisible: boolean;
  sidebarLocked: boolean; // For very wide screens
  hasSidebar: boolean; // Whether current layout has sidebar (admin/user)

  // Mobile menu state
  mobileMenuOpen: boolean; // For public menu in SharedHeader
  userMenuOpen: boolean; // For user menu in UserLayoutMobile

  // Header state
  showPublicMenu: boolean; // Whether public menu is visible (affects header height)

  // Screen size detection
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  isVeryWideScreen: boolean;
}

export const initialLayoutState: LayoutState = {
  sidebarVisible: true,
  sidebarLocked: false,
  hasSidebar: false, // Default to false for MainLayout
  mobileMenuOpen: false,
  userMenuOpen: false,
  showPublicMenu: true, // Default to true for desktop
  isMobile: false,
  isTablet: false,
  isDesktop: true,
  isVeryWideScreen: false,
};
