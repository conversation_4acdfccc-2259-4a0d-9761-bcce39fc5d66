/**
 * Form Config Dialog
 * Dialog for editing form configuration (name, description)
 */

import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { FormConfig } from '../core/types';

interface FormConfigDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  formConfig: Pick<FormConfig, 'code' | 'name' | 'description'>;
  onSave: (updates: { name: string; description: string }) => void;
}

export const FormConfigDialog: React.FC<FormConfigDialogProps> = ({
  open,
  onOpenChange,
  formConfig,
  onSave
}) => {
  const [name, setName] = useState(formConfig.name);
  const [description, setDescription] = useState(formConfig.description);

  // Reset form when dialog opens
  React.useEffect(() => {
    if (open) {
      setName(formConfig.name);
      setDescription(formConfig.description);
    }
  }, [open, formConfig]);

  const handleSave = () => {
    onSave({ name, description });
    onOpenChange(false);
  };

  const handleCancel = () => {
    setName(formConfig.name);
    setDescription(formConfig.description);
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Form Configuration</DialogTitle>
          <DialogDescription>
            Update form name and description. Code cannot be changed.
          </DialogDescription>
        </DialogHeader>
        
        <div className="grid gap-4 py-4">
          {/* Code (Read-only) */}
          <div className="grid gap-2">
            <Label htmlFor="code">Code</Label>
            <Input
              id="code"
              value={formConfig.code}
              disabled
              className="bg-gray-50 cursor-not-allowed"
            />
          </div>

          {/* Name */}
          <div className="grid gap-2">
            <Label htmlFor="name">Name</Label>
            <Input
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="Enter form name..."
            />
          </div>

          {/* Description */}
          <div className="grid gap-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              rows={3}
              placeholder="Enter form description..."
              className="resize-none"
            />
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleCancel}>
            Cancel
          </Button>
          <Button onClick={handleSave}>
            Save Changes
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};