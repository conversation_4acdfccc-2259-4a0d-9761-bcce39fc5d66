/**
 * Spacing Section Component
 * For margin and padding controls with popover UX
 */

import React from 'react';
import { FormNode } from '@/form/types';
import { PropertiesFormProps } from '../types';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { SpacingPopover } from '../input/SpacingPopover';

export const SpacingSection: React.FC<PropertiesFormProps> = ({
  node,
  onChange,
  disabled = false
}) => {
  const currentClasses = node.styles?.container || '';

  // Extract current spacing values
  const getCurrentSpacing = (type: 'margin' | 'padding', direction?: 'x' | 'y' | 't' | 'r' | 'b' | 'l') => {
    const prefix = type === 'margin' ? 'm' : 'p';
    const directionSuffix = direction ? (direction === 'x' || direction === 'y' ? direction : direction) : '';
    const fullPrefix = directionSuffix ? `${prefix}${directionSuffix}` : prefix;
    
    const regex = new RegExp(`\\b${fullPrefix}-(\\w+)\\b`);
    const match = currentClasses.match(regex);
    return match ? match[1] : 'none';
  };

  // Update spacing
  const updateSpacing = (type: 'margin' | 'padding', direction: string | undefined, value: string) => {
    const prefix = type === 'margin' ? 'm' : 'p';
    const fullPrefix = direction ? `${prefix}${direction}` : prefix;
    
    let newClasses = currentClasses;

    // Remove existing classes for this spacing type and direction
    const regex = new RegExp(`\\b${fullPrefix}-\\w+\\b`, 'g');
    newClasses = newClasses.replace(regex, '').replace(/\s+/g, ' ').trim();

    // Add new class if value is not 'none'
    if (value && value !== 'none') {
      newClasses = `${newClasses} ${fullPrefix}-${value}`.trim();
    }

    onChange({
      styles: {
        ...node.styles,
        container: newClasses
      }
    });
  };

  return (
    <div className="space-y-2">
      <Tabs defaultValue="margin" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="margin" className="text-xs">Lề ngoài</TabsTrigger>
          <TabsTrigger value="padding" className="text-xs">Lề trong</TabsTrigger>
        </TabsList>
        
        {/* Margin Tab */}
        <TabsContent value="margin" className="space-y-2 mt-3">
          <div className="space-y-2">
            {/* All Margin */}
            <div className="space-y-1">
              <Label className="text-xs">Tất cả</Label>
              <SpacingPopover
                value={getCurrentSpacing('margin')}
                onValueChange={(value) => updateSpacing('margin', undefined, value)}
                disabled={disabled}
                type="margin"
                placeholder="Chọn lề ngoài"
              />
            </div>

            {/* Directional Margins */}
            <div className="grid grid-cols-2 gap-3">
              {/* Margin X */}
              <div className="space-y-1">
                <Label className="text-xs">Ngang</Label>
                <SpacingPopover
                  value={getCurrentSpacing('margin', 'x')}
                  onValueChange={(value) => updateSpacing('margin', 'x', value)}
                  disabled={disabled}
                  type="margin"
                  placeholder="Margin X"
                />
              </div>

              {/* Margin Y */}
              <div className="space-y-1">
                <Label className="text-xs">Dọc</Label>
                <SpacingPopover
                  value={getCurrentSpacing('margin', 'y')}
                  onValueChange={(value) => updateSpacing('margin', 'y', value)}
                  disabled={disabled}
                  type="margin"
                  placeholder="Margin Y"
                />
              </div>
            </div>

            {/* Individual Margins */}
            <div className="grid grid-cols-2 gap-3">
              {[
                { key: 't', label: 'Trên (mt)' },
                { key: 'r', label: 'Phải (mr)' },
                { key: 'b', label: 'Dưới (mb)' },
                { key: 'l', label: 'Trái (ml)' }
              ].map(({ key, label }) => (
                <div key={key} className="space-y-1">
                  <Label className="text-xs">{label.split(' ')[0]}</Label>
                  <SpacingPopover
                    value={getCurrentSpacing('margin', key as any)}
                    onValueChange={(value) => updateSpacing('margin', key, value)}
                    disabled={disabled}
                    type="margin"
                    placeholder={`Margin ${key.toUpperCase()}`}
                  />
                </div>
              ))}
            </div>
          </div>
        </TabsContent>
        
        {/* Padding Tab */}
        <TabsContent value="padding" className="space-y-2 mt-3">
          <div className="space-y-2">
            {/* All Padding */}
            <div className="space-y-1">
              <Label className="text-xs">Tất cả</Label>
              <SpacingPopover
                value={getCurrentSpacing('padding')}
                onValueChange={(value) => updateSpacing('padding', undefined, value)}
                disabled={disabled}
                type="padding"
                placeholder="Chọn lề trong"
              />
            </div>

            {/* Directional Padding */}
            <div className="grid grid-cols-2 gap-3">
              {/* Padding X */}
              <div className="space-y-1">
                <Label className="text-xs">Ngang</Label>
                <SpacingPopover
                  value={getCurrentSpacing('padding', 'x')}
                  onValueChange={(value) => updateSpacing('padding', 'x', value)}
                  disabled={disabled}
                  type="padding"
                  placeholder="Padding X"
                />
              </div>

              {/* Padding Y */}
              <div className="space-y-1">
                <Label className="text-xs">Dọc</Label>
                <SpacingPopover
                  value={getCurrentSpacing('padding', 'y')}
                  onValueChange={(value) => updateSpacing('padding', 'y', value)}
                  disabled={disabled}
                  type="padding"
                  placeholder="Padding Y"
                />
              </div>
            </div>

            {/* Individual Padding */}
            <div className="grid grid-cols-2 gap-3">
              {[
                { key: 't', label: 'Trên (pt)' },
                { key: 'r', label: 'Phải (pr)' },
                { key: 'b', label: 'Dưới (pb)' },
                { key: 'l', label: 'Trái (pl)' }
              ].map(({ key, label }) => (
                <div key={key} className="space-y-1">
                  <Label className="text-xs">{label.split(' ')[0]}</Label>
                  <SpacingPopover
                    value={getCurrentSpacing('padding', key as any)}
                    onValueChange={(value) => updateSpacing('padding', key, value)}
                    disabled={disabled}
                    type="padding"
                    placeholder={`Padding ${key.toUpperCase()}`}
                  />
                </div>
              ))}
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};