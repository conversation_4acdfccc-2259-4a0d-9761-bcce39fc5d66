/**
 * Edit Layout - Fullscreen layout for editing interfaces
 * No header/footer, pure editing experience
 */

import React from "react";
import { Outlet } from "react-router-dom";

interface EditLayoutProps {
  children?: React.ReactNode;
}

export const EditLayout: React.FC<EditLayoutProps> = ({ children }) => {
  return (
    <div className="h-screen w-screen bg-gray-50 overflow-hidden">
      <main className="h-full w-full">
        {children || <Outlet />}
      </main>
    </div>
  );
};

export default EditLayout;