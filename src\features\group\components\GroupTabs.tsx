import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { GroupBasicInfo } from "./GroupBasicInfo";
import { GroupMetadata } from "./GroupMetadata";
import { GroupSystemRoles } from "./GroupSystemRoles";
import { GroupUsers } from "./GroupUsers";
import { Info, Users, Shield, Database } from "lucide-react";
import { GroupTab, GroupAction } from "../states/types";

interface GroupTabsProps {
  currentTab: GroupTab;
  currentAction: GroupAction;
  onTabChange: (tab: GroupTab, action: GroupAction) => void;
}

export const GroupTabs: React.FC<GroupTabsProps> = ({
  currentTab,
  currentAction,
  onTabChange,
}) => {
  const handleTabChange = (value: string) => {
    onTabChange(value as GroupTab, "view");
  };

  return (
    <Tabs value={currentTab} onValueChange={handleTabChange} className="w-full">
      <TabsList className="grid w-full grid-cols-4">
        <TabsTrigger value="basic" className="flex items-center gap-2">
          <Info className="h-4 w-4" />
          Cơ bản
        </TabsTrigger>
        <TabsTrigger value="metadata" className="flex items-center gap-2">
          <Database className="h-4 w-4" />
          Metadata
        </TabsTrigger>
        <TabsTrigger value="roles" className="flex items-center gap-2">
          <Shield className="h-4 w-4" />
          Vai trò
        </TabsTrigger>
        <TabsTrigger value="users" className="flex items-center gap-2">
          <Users className="h-4 w-4" />
          Người dùng
        </TabsTrigger>
      </TabsList>

      <TabsContent value="basic" className="mt-0">
        <GroupBasicInfo
          currentAction={currentAction}
          onActionChange={(action: GroupAction) => onTabChange("basic", action)}
        />
      </TabsContent>

      <TabsContent value="metadata" className="mt-0">
        <GroupMetadata
          currentAction={currentAction}
          onActionChange={(action: GroupAction) =>
            onTabChange("metadata", action)
          }
        />
      </TabsContent>

      <TabsContent value="roles" className="mt-0">
        <GroupSystemRoles />
      </TabsContent>

      <TabsContent value="users" className="mt-0">
        <GroupUsers
          currentAction={currentAction}
          onActionChange={(action: GroupAction) => onTabChange("users", action)}
        />
      </TabsContent>
    </Tabs>
  );
};
