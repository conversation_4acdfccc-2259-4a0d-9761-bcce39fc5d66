import { createRoot } from "react-dom/client";
import "./index.css";
import "@/services/i18n/index.ts";

import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import store from "./store/store.ts";
import { Provider } from "react-redux";
import App from "./app/App.tsx";

// Import validation function for browser console
import { validate } from "./form/test/browserTest.ts";

// Make validation function available globally
if (typeof window !== "undefined") {
  (window as unknown as Record<string, unknown>).validate = validate;
  console.log("👉 validate({...data}) function ready");
}

console.log("🚀 Application starting...");

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      gcTime: 1000 * 60 * 60 * 24,
      staleTime: 1000 * 60 * 60 * 24,
    },
  },
});

console.log("📦 Store and query client created, rendering...");

createRoot(document.getElementById("root")!).render(
  <>
    <Provider store={store}>
      <QueryClientProvider client={queryClient}>
        <App />
      </QueryClientProvider>
    </Provider>
  </>
);
