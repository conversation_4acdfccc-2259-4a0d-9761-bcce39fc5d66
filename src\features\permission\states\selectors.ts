import { createSelector } from "@reduxjs/toolkit";
import { RootState } from "@/store/rootReducer";

// Base selectors
const selectPermissionState = (state: RootState) => state.permissionState;
const selectRoleState = (state: RootState) => state.roleState;
const selectResourceState = (state: RootState) => state.resourceState;
const selectActionState = (state: RootState) => state.actionState;

// Memoized selectors
export const selectSelectedRoleId = createSelector(
  [selectPermissionState],
  (permissionState) => permissionState.selectedRoleId
);

export const selectPermissionMode = createSelector(
  [selectPermissionState],
  (permissionState) => permissionState.mode
);

export const selectIsRoleSelectionOpen = createSelector(
  [selectPermissionState],
  (permissionState) => permissionState.isRoleSelectionOpen
);

export const selectRoles = createSelector(
  [selectRoleState],
  (roleState) => roleState.data
);

export const selectRolesLoading = createSelector(
  [selectRoleState],
  (roleState) => roleState.loading
);

export const selectResources = createSelector(
  [selectResourceState],
  (resourceState) => resourceState.data
);

export const selectResourcesLoading = createSelector(
  [selectResourceState],
  (resourceState) => resourceState.loading
);

export const selectActions = createSelector(
  [selectActionState],
  (actionState) => actionState.data
);

export const selectActionsLoading = createSelector(
  [selectActionState],
  (actionState) => actionState.loading
);

// Combined selectors
export const selectPermissionQueryParams = createSelector(
  [selectSelectedRoleId, selectPermissionMode],
  (selectedRoleId, mode) => ({
    selectedRoleId,
    mode,
  })
);

export const selectLoadingStates = createSelector(
  [selectRolesLoading, selectResourcesLoading, selectActionsLoading],
  (rolesLoading, resourcesLoading, actionsLoading) => ({
    rolesLoading,
    resourcesLoading,
    actionsLoading,
    isLoading: rolesLoading || resourcesLoading || actionsLoading,
  })
);

export const selectRoleSelectionData = createSelector(
  [selectIsRoleSelectionOpen, selectRoles, selectRolesLoading],
  (isOpen, roles, loading) => ({
    isOpen,
    roles,
    loading,
  })
);

export const selectSelectedRole = createSelector(
  [selectSelectedRoleId, selectRoles],
  (selectedRoleId, roles) => {
    if (!selectedRoleId) return null;
    return roles.find((role) => role.id === selectedRoleId) || null;
  }
);

export const selectRolePermissionData = createSelector(
  [selectPermissionState, selectSelectedRoleId],
  (permissionState, selectedRoleId) => {
    if (!selectedRoleId) return null;
    return permissionState.rolePermissions[selectedRoleId] || null;
  }
);

export const selectPermissionMatrixData = createSelector(
  [
    selectRolePermissionData,
    selectResources,
    selectActions,
    selectPermissionMode,
    selectPermissionState,
  ],
  (permissionData, resources, actions, mode, permissionState) => ({
    permissionData,
    resources,
    actions,
    mode,
    loading: permissionState.loading,
  })
);
