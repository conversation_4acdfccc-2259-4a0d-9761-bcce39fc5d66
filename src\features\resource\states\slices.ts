import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import {
  ResourceState,
  Resource,
  ResourceRequest,
  ResourcePageMode,
  ResourceAction,
} from "./types";
import {
  fetchResources,
  fetchResource,
  createResourceApi,
  updateResource<PERSON>pi,
  deleteResource<PERSON>pi,
} from "./api";
import { toast } from "sonner";

// Simple state without over-engineered cache
const initialState: ResourceState = {
  data: [],
  currentResource: null,
  pageMode: "list",
  actionType: "view",
  searchTerm: "",
  loading: false,
  error: null,
  deletedIds: [], // Only track deleted IDs
};

// Simple fetch resources - no cache logic
export const fetchResourcesThunk = createAsyncThunk(
  "resource/fetchResources",
  async () => {
    const response = await fetchResources();
    if (!response.data.data) {
      throw new Error("No data");
    }
    return response.data.data;
  }
);

// Fetch by ID with simple deleted check
export const fetchResourceByIdThunk = createAsyncThunk(
  "resource/fetchById",
  async (id: number, { getState }) => {
    const state = getState() as { resourceState: ResourceState };

    // Only check if resource was deleted
    if (state.resourceState.deletedIds.includes(id)) {
      throw new Error(`Resource ${id} has been deleted`);
    }

    const response = await fetchResource(id);
    if (!response.data.data) {
      throw new Error("No data");
    }
    return response.data.data;
  }
);

export const createResourceThunk = createAsyncThunk(
  "resources/create",
  async (data: ResourceRequest) => {
    const response = await createResourceApi(data);
    if (!response.data.data) {
      throw new Error("Failed to create resource");
    }
    toast.success("Tạo nhóm quyền thành công");
    return response.data.data;
  }
);

export const updateResourceThunk = createAsyncThunk(
  "resources/update",
  async ({ id, data }: { id: number; data: ResourceRequest }) => {
    const response = await updateResourceApi(id, data);
    if (!response.data.data) {
      throw new Error("Failed to update resource");
    }
    toast.success("Cập nhật nhóm quyền thành công");
    return response.data.data;
  }
);

export const deleteResourceThunk = createAsyncThunk(
  "resources/delete",
  async (id: number) => {
    await deleteResourceApi(id);
    toast.success("Xóa nhóm quyền thành công");
    return id;
  }
);

const resourceSlice = createSlice({
  name: "resource",
  initialState,
  reducers: {
    setCurrentResource: (state, action: PayloadAction<Resource | null>) => {
      state.currentResource = action.payload;
    },
    setPathParams: (
      state,
      action: PayloadAction<{
        mode?: ResourcePageMode;
        actionType?: ResourceAction;
      }>
    ) => {
      const { mode, actionType } = action.payload;
      if (mode) state.pageMode = mode;
      if (actionType) state.actionType = actionType;
    },
    setSearchTerm: (state, action: PayloadAction<string>) => {
      state.searchTerm = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch resources
      .addCase(fetchResourcesThunk.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchResourcesThunk.fulfilled, (state, action) => {
        state.loading = false;
        state.data = action.payload;
      })
      .addCase(fetchResourcesThunk.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || "Failed to fetch resources";
      })

      // Fetch resource by id
      .addCase(fetchResourceByIdThunk.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchResourceByIdThunk.fulfilled, (state, action) => {
        state.loading = false;
        state.currentResource = action.payload;

        // Update in list if present
        const index = state.data.findIndex(
          (item) => item.id === action.payload.id
        );
        if (index !== -1) {
          state.data[index] = action.payload;
        }
      })
      .addCase(fetchResourceByIdThunk.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || "Failed to fetch resource";

        // Clear current resource if it was a deleted resource error
        if (action.error.message?.includes("has been deleted")) {
          state.currentResource = null;
        }
      })

      // Create resource
      .addCase(createResourceThunk.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createResourceThunk.fulfilled, (state, action) => {
        state.loading = false;
        state.data.push(action.payload);
        state.currentResource = action.payload;
      })
      .addCase(createResourceThunk.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || "Failed to create resource";
      })

      // Update resource
      .addCase(updateResourceThunk.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateResourceThunk.fulfilled, (state, action) => {
        state.loading = false;
        const updatedResource = action.payload;

        const index = state.data.findIndex(
          (item) => item.id === updatedResource.id
        );
        if (index !== -1) {
          state.data[index] = updatedResource;
        }

        if (state.currentResource?.id === updatedResource.id) {
          state.currentResource = updatedResource;
        }
      })
      .addCase(updateResourceThunk.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || "Failed to update resource";
      })

      // Delete resource
      .addCase(deleteResourceThunk.pending, (state, action) => {
        state.loading = true;
        state.error = null;

        // Mark as deleted immediately to prevent further API calls
        const resourceId = action.meta.arg;
        if (!state.deletedIds.includes(resourceId)) {
          state.deletedIds.push(resourceId);
        }
      })
      .addCase(deleteResourceThunk.fulfilled, (state, action) => {
        state.loading = false;
        const deletedId = action.payload;

        // Remove from list
        state.data = state.data.filter((item) => item.id !== deletedId);

        // Clear current resource if it was deleted
        if (state.currentResource?.id === deletedId) {
          state.currentResource = null;
        }

        // Ensure deleted ID is tracked
        if (!state.deletedIds.includes(deletedId)) {
          state.deletedIds.push(deletedId);
        }
      })
      .addCase(deleteResourceThunk.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || "Failed to delete resource";

        // Remove from deletedIds if delete failed
        const resourceId = action.meta.arg;
        state.deletedIds = state.deletedIds.filter((id) => id !== resourceId);
      });
  },
});

export const { setCurrentResource, setPathParams, setSearchTerm, clearError } =
  resourceSlice.actions;

export default resourceSlice.reducer;
