import { BaseResponse, restApi } from "@/api/restApi";
import type { ContactConfig, HotlineConfig } from "./type";

// ============================================================================
// API Configuration
// ============================================================================

/** Base URL for public configuration endpoints */
const PUBLIC_CONFIG_URL = "/portal/v1/public/config";

// ============================================================================
// Contact API Functions
// ============================================================================

/**
 * Fetches contact configuration from the API
 *
 * @returns Promise resolving to contact configuration response
 * @throws Error if the API request fails
 *
 * @example
 */
export async function fetchContactConfig(): Promise<
  BaseResponse<ContactConfig>
> {
  const res = await restApi.get<BaseResponse<ContactConfig>>(
    `${PUBLIC_CONFIG_URL}/contacts`
  );

  return res.data;
}

// ============================================================================
// Hotline API Functions
// ============================================================================

/**
 * Fetches hotline configuration from the API
 *
 * @returns Promise resolving to hotline configuration response
 * @throws Error if the API request fails
 *
 */
export async function fetchHotlineConfig(): Promise<
  BaseResponse<HotlineConfig>
> {
  const res = await restApi.get<BaseResponse<HotlineConfig>>(
    `${PUBLIC_CONFIG_URL}/hot_lines`
  );

  return res.data;
}

// ============================================================================
// Combined API Functions
// ============================================================================

/**
 * Fetches both contact and hotline configurations in parallel
 *
 * @returns Promise resolving to an object containing both configurations
 * @throws Error if either API request fails
 */
export async function fetchOrganizationData(): Promise<{
  contacts: ContactConfig | null;
  hotlines: HotlineConfig | null;
}> {
  try {
    // Fetch both configurations in parallel for better performance
    const [contactResponse, hotlineResponse] = await Promise.allSettled([
      fetchContactConfig(),
      fetchHotlineConfig(),
    ]);

    // Extract contact data or null if failed
    const contacts =
      contactResponse.status === "fulfilled"
        ? contactResponse.value.data || null
        : null;

    // Extract hotline data or null if failed
    const hotlines =
      hotlineResponse.status === "fulfilled"
        ? hotlineResponse.value.data || null
        : null;

    // Log any failures for debugging
    if (contactResponse.status === "rejected") {
      console.error("Failed to fetch contacts:", contactResponse.reason);
    }

    if (hotlineResponse.status === "rejected") {
      console.error("Failed to fetch hotlines:", hotlineResponse.reason);
    }

    return { contacts, hotlines };
  } catch (error) {
    console.error("Failed to fetch organization data:", error);
    throw error;
  }
}

// ============================================================================
// Error Handling Utilities
// ============================================================================

/**
 * Extracts error message from API response or error object
 *
 * @param error - The error object from API call
 * @returns Human-readable error message
 */
export function extractErrorMessage(error: unknown): string {
  if (error instanceof Error) {
    return error.message;
  }

  if (typeof error === "object" && error !== null && "message" in error) {
    return String((error as { message: unknown }).message);
  }

  return "An unexpected error occurred";
}

/**
 * Checks if the API response indicates success
 *
 * @param response - The API response object
 * @returns True if the response indicates success
 */
export function isSuccessResponse<T>(response: BaseResponse<T>): boolean {
  return response.code === 200 || response.code === 0;
}

/**
 * Validates contact configuration data structure
 *
 * @param data - The contact configuration data to validate
 * @returns True if the data structure is valid
 */
export function validateContactConfig(data: unknown): data is ContactConfig {
  if (!data || typeof data !== "object") {
    return false;
  }

  const config = data as ContactConfig;
  return (
    Array.isArray(config.contacts) &&
    config.contacts.every(
      (contact) =>
        typeof contact.name === "string" &&
        typeof contact.phone === "string" &&
        typeof contact.email === "string" &&
        typeof contact.address === "string"
    )
  );
}

/**
 * Validates hotline configuration data structure
 *
 * @param data - The hotline configuration data to validate
 * @returns True if the data structure is valid
 */
export function validateHotlineConfig(data: unknown): data is HotlineConfig {
  if (!data || typeof data !== "object") {
    return false;
  }

  const config = data as HotlineConfig;
  return (
    Array.isArray(config) &&
    config.every(
      (hotline) =>
        typeof hotline.name === "string" &&
        typeof hotline.phone === "string" &&
        typeof hotline.email === "string"
    )
  );
}
