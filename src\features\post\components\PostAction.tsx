import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  PenLine,
  Trash2,
  UploadCloud,
  Undo2,
  XCircle,
  CheckCircle,
  RefreshCcw,
} from "lucide-react";
import {
  Tooltip,
  TooltipTrigger,
  TooltipContent,
} from "@/components/ui/tooltip";
import type { Post, PostStatus } from "../states/types";
import { approvePost, rejectPost, updatePostStatus } from "../states/api";
import { useAppDispatch } from "@/store/rootReducer";
import { triggerRefetch } from "../states/slices";
import { toast } from "sonner";
import { ActionConfirmDialog } from "./ActionConfirmDialog";

export type PostAction =
  | "edit"
  | "delete"
  | "request-publish"
  | "publish"
  | "reject"
  | "unpublish"
  | "restore";

const actionMap: Record<
  PostAction,
  { icon: React.ReactNode; label: string; confirm: string }
> = {
  delete: {
    icon: <Trash2 className="w-4 h-4" />,
    label: "Xoá",
    confirm: "Bạn chắc chắn muốn chuyển bài viết vào thùng rác?",
  },
  "request-publish": {
    icon: <UploadCloud className="w-4 h-4" />,
    label: "Gửi xuất bản",
    confirm: "Gửi yêu cầu xuất bản bài viết này?",
  },
  publish: {
    icon: <CheckCircle className="w-4 h-4" />,
    label: "Xuất bản",
    confirm: "Xuất bản bài viết này?",
  },
  reject: {
    icon: <XCircle className="w-4 h-4" />,
    label: "Từ chối",
    confirm: "Từ chối bài viết này?",
  },
  unpublish: {
    icon: <RefreshCcw className="w-4 h-4" />,
    label: "Huỷ xuất bản",
    confirm: "Huỷ xuất bản bài viết này?",
  },
  restore: {
    icon: <Undo2 className="w-4 h-4" />,
    label: "Khôi phục",
    confirm: "Khôi phục bài viết về bản nháp?",
  },
  edit: {
    icon: <PenLine className="w-4 h-4" />,
    label: "Sửa",
    confirm: "Chỉnh sửa bài viết này?",
  },
};

const editAction = { icon: <PenLine className="w-4 h-4" />, label: "Sửa" };

function getActionsByStatus(status: PostStatus): PostAction[] {
  switch (status) {
    case "DRAFT":
      return ["edit", "delete", "request-publish"];
    case "REVIEW":
      return ["publish", "reject"];
    case "PUBLISHED":
      return ["unpublish"];
    case "UNPUBLISHED":
    case "REJECTED":
      return ["restore"];
    case "TRASH":
      return ["restore"];
    default:
      return [];
  }
}

export function PostAction({ row: post }: { row: Post }) {
  const dispatch = useAppDispatch();
  const actions = getActionsByStatus(post.status);
  const [loading, setLoading] = useState<PostAction | null>(null);
  const [dialog, setDialog] = useState<PostAction | null>(null);

  const doAction = async (action: PostAction) => {
    setLoading(action);
    try {
      switch (action) {
        case "delete":
          await updatePostStatus(post.id, "TRASH");
          toast.success("Đã xoá vào thùng rác");
          break;
        case "request-publish":
          await updatePostStatus(post.id, "REVIEW");
          toast.success("Đã gửi yêu cầu xuất bản");
          break;
        case "publish":
          await approvePost(post.id);
          toast.success("Xuất bản thành công");
          break;
        case "reject":
          await rejectPost(post.id);
          toast.success("Đã từ chối bài viết");
          break;
        case "unpublish":
          await updatePostStatus(post.id, "UNPUBLISHED");
          toast.success("Đã huỷ xuất bản");
          break;
        case "restore":
          await updatePostStatus(post.id, "DRAFT");
          toast.success("Đã khôi phục về bản nháp");
          break;
        case "edit":
          onEdit();
          break;
      }
      dispatch(triggerRefetch());
      setDialog(null);
    } catch {
      toast.error("Thao tác thất bại!");
    }
    setLoading(null);
  };

  const onEdit = () => {
    window.open(`/edit/${post.id}`, "_blank");
  };

  const handleClick = (action: PostAction) => {
    if (action === "edit" && post.status === "DRAFT") {
      onEdit();
    } else {
      setDialog(action);
    }
  };

  return (
    <>
      <div className="flex gap-1">
        {actions.map((action) =>
          action === "edit" ? (
            <Tooltip key="edit">
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => handleClick("edit")}
                  disabled={loading !== null}
                >
                  {editAction.icon}
                </Button>
              </TooltipTrigger>
              <TooltipContent side="top">{editAction.label}</TooltipContent>
            </Tooltip>
          ) : (
            <Tooltip key={action}>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => handleClick(action)}
                  disabled={loading !== null}
                >
                  {actionMap[action].icon}
                </Button>
              </TooltipTrigger>
              <TooltipContent side="top">
                {actionMap[action].label}
              </TooltipContent>
            </Tooltip>
          )
        )}
      </div>
      {dialog && (
        <ActionConfirmDialog
          open={!!dialog}
          onOpenChange={(open) => {
            if (!open) setDialog(null);
          }}
          onConfirm={() => doAction(dialog)}
          icon={actionMap[dialog].icon}
          confirmText={actionMap[dialog].confirm}
          loading={loading === dialog}
        />
      )}
    </>
  );
}
