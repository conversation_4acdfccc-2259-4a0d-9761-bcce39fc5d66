import React, { useEffect } from "react";
import { useAppSelector, useAppDispatch } from "@/store/rootReducer";
import {
  selectFilteredActions,
  selectActionLoading,
  selectSearchTerm,
} from "../states/selectors";
import {
  fetchActionsThunk,
  setCurrentAction,
  setSearchTerm,
} from "../states/slices";
import { useSwitchMode } from "../states/hooks";
import { AutoTable } from "@/components/table/AutoTable";
import { Action } from "../states/types";
import { Input } from "@/components/ui/input";
import { Search } from "lucide-react";
import { ACTION_COLUMNS } from "../states/table";

export const ActionTable = () => {
  const dispatch = useAppDispatch();
  const switchMode = useSwitchMode();

  const actions = useAppSelector(selectFilteredActions);
  const loading = useAppSelector(selectActionLoading);
  const searchTerm = useAppSelector(selectSearchTerm);

  useEffect(() => {
    dispatch(fetchActionsThunk());
  }, [dispatch]);

  const handleAction = (action: string, row: Action) => {
    if (action === "edit") {
      dispatch(setCurrentAction(row));
      switchMode("detail", row.id.toString(), "view");
    }
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setSearchTerm(event.target.value));
  };

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Danh sách quyền</h1>
      </div>

      {/* Search */}
      <div className="flex items-center space-x-2">
        <div className="relative">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Tìm kiếm quyền..."
            value={searchTerm}
            onChange={handleSearchChange}
            className="pl-8 max-w-sm"
          />
        </div>
      </div>

      {/* Table */}
      <AutoTable<Action>
        columns={ACTION_COLUMNS}
        data={actions}
        loading={loading}
        onAction={handleAction}
      />
    </div>
  );
};
