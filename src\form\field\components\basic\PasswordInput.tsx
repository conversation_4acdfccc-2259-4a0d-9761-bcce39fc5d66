import React, { useState } from "react";
import { FieldComponentProps } from "../../FieldFactory";
import { Eye, EyeOff } from "lucide-react";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";

export const PasswordInput: React.FC<FieldComponentProps> = ({
  value,
  onChange,
  onBlur,
  disabled,
  placeholder,
  className,
  id,
  ...rest
}) => {
  const [show, setShow] = useState(false);
  return (
    <div className="relative w-full">
      <Input
        type={show ? "text" : "password"}
        value={typeof value === "string" ? value : ""}
        onChange={(e) => onChange(e.target.value)}
        onBlur={onBlur}
        disabled={disabled}
        placeholder={placeholder}
        className={cn("pr-10", className)}
        id={id}
        {...rest}
      />
      {!disabled && (
        <button
          type="button"
          tabIndex={-1}
          className="absolute right-2 top-1/2 -translate-y-1/2 p-1 text-muted-foreground hover:text-foreground"
          onClick={() => setShow((v) => !v)}
        >
          {show ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
        </button>
      )}
    </div>
  );
};
