import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { CategoryDTO, CategoryMode, CategoryType } from "./types";
import {
  createCategory,
  deleteCategory,
  fetchCategories,
  updateCategory,
  fetchCategoryBySlug,
} from "./api";
import { toast } from "sonner";

// Improved state structure with per-type loading and error states
interface CategoryTypeState {
  data: CategoryDTO[];
  loading: boolean;
  error: string | null;
  lastFetched: number | null; // timestamp để cache
}

interface CategoryState {
  types: Record<CategoryType, CategoryTypeState>;
  selectedId: number;
  selectedType: CategoryType;
  mode: CategoryMode;
  createParentId: number; // Parent ID for create mode
  // Global operation states
  creating: boolean;
  updating: boolean;
  deleting: boolean;
  globalError: string | null;
}

const createInitialTypeState = (): CategoryTypeState => ({
  data: [],
  loading: false,
  error: null,
  lastFetched: null,
});

const initialState: CategoryState = {
  types: {
    "admin-menu": createInitialTypeState(),
    "user-menu": createInitialTypeState(),
    "public-menu": createInitialTypeState(),
  },
  selectedId: 0,
  selectedType: "public-menu",
  mode: "view",
  createParentId: 0, // Default parent ID
  creating: false,
  updating: false,
  deleting: false,
  globalError: null,
};

// Enhanced thunk with better caching logic
export const fetchCategoryByType = createAsyncThunk(
  "category/fetchByType",
  async (
    payload: { type: CategoryType; forceRefresh?: boolean },
    { getState, rejectWithValue }
  ) => {
    const { type, forceRefresh = false } = payload;

    const state = getState() as { categoryState: CategoryState };
    const typeState = state.categoryState.types[type];

    // Only skip if we have data AND we're loading (to prevent duplicate calls with data)
    if (typeState.loading && typeState.data.length > 0) {
      return { type, data: typeState.data };
    }

    // Skip if recently fetched (within 5 minutes) and has data, unless forced
    const now = Date.now();
    const fiveMinutes = 5 * 60 * 1000;

    if (
      !forceRefresh &&
      typeState.lastFetched &&
      typeState.data.length > 0 &&
      now - typeState.lastFetched < fiveMinutes
    ) {
      return { type, data: typeState.data };
    }

    try {
      const response = await fetchCategories(type);

      if (response.status !== 200) {
        return rejectWithValue(
          `HTTP ${response.status}: ${response.statusText}`
        );
      }

      if (!response.data || !response.data.data) {
        return rejectWithValue("No data received from API");
      }

      return { type, data: response.data.data };
    } catch (error: any) {
      console.error("API call failed for type:", type, error);
      const errorMessage =
        error.response?.data?.message || error.message || "Unknown error";
      return rejectWithValue(errorMessage);
    }
  }
);

// Thêm mới danh mục
export const createCategoryThunk = createAsyncThunk(
  "category/create",
  async (
    payload: Omit<CategoryDTO, "id" | "createdAt" | "updatedAt">,
    { rejectWithValue }
  ) => {
    try {
      const response = await createCategory(payload);

      if (response.status !== 200 && response.status !== 201) {
        return rejectWithValue(
          `HTTP ${response.status}: ${response.statusText}`
        );
      }

      if (!response.data || !response.data.data) {
        return rejectWithValue("No data received from create API");
      }

      return response.data.data;
    } catch (error: any) {
      console.error("Create category failed:", error);
      const errorMessage =
        error.response?.data?.message ||
        error.message ||
        "Create category failed";
      return rejectWithValue(errorMessage);
    }
  }
);

// Sửa danh mục
export const updateCategoryThunk = createAsyncThunk(
  "category/update",
  async (
    { id, payload }: { id: number | null; payload: Partial<CategoryDTO> },
    { rejectWithValue }
  ) => {
    if (!id) {
      return rejectWithValue("Category ID is required");
    }

    try {
      const response = await updateCategory(id, payload);

      if (response.status !== 200) {
        return rejectWithValue(
          `HTTP ${response.status}: ${response.statusText}`
        );
      }

      if (!response.data || !response.data.data) {
        return rejectWithValue("No data received from update API");
      }

      return response.data.data;
    } catch (error: any) {
      console.error("Update category failed:", error);
      const errorMessage =
        error.response?.data?.message ||
        error.message ||
        "Update category failed";
      return rejectWithValue(errorMessage);
    }
  }
);

// Xóa danh mục
export const deleteCategoryThunk = createAsyncThunk(
  "category/delete",
  async (
    { id, type }: { id: number | null; type: CategoryType },
    { rejectWithValue }
  ) => {
    if (!id) {
      return rejectWithValue("Category ID is required");
    }

    try {
      const response = await deleteCategory(id);

      if (response.status !== 200 && response.status !== 204) {
        return rejectWithValue(
          `HTTP ${response.status}: ${response.statusText}`
        );
      }

      return { id, type };
    } catch (error: any) {
      console.error("Delete category failed:", error);
      const errorMessage =
        error.response?.data?.message ||
        error.message ||
        "Delete category failed";
      return rejectWithValue(errorMessage);
    }
  }
);

export const fetchCategoryBySlugAsync = createAsyncThunk(
  "category/fetchBySlug",
  async (slug: string, { rejectWithValue }) => {
    try {
      const response = await fetchCategoryBySlug(slug);

      if (response.status !== 200) {
        return rejectWithValue(
          `HTTP ${response.status}: ${response.statusText}`
        );
      }

      if (!response.data || !response.data.data) {
        return rejectWithValue("Category not found");
      }

      return response.data.data;
    } catch (error: any) {
      console.error("💥 fetchCategoryBySlugAsync: Exception:", error);
      const errorMessage =
        error.response?.data?.message ||
        error.message ||
        "Fetch category failed";
      return rejectWithValue(errorMessage);
    }
  }
);

const categorySlice = createSlice({
  name: "category",
  initialState,
  reducers: {
    clearCategories(state) {
      // Reset all types
      Object.keys(state.types).forEach((type) => {
        state.types[type as CategoryType] = createInitialTypeState();
      });
      state.globalError = null;
    },
    clearCategoryType(state, action: { payload: CategoryType }) {
      state.types[action.payload] = createInitialTypeState();
    },
    setSelectedId(state, action: { payload: number | null }) {
      if (action.payload === null) {
        action.payload = 0;
      }
      state.selectedId = action.payload;
    },
    setSelectedType(state, action: { payload: CategoryType }) {
      state.selectedType = action.payload;
    },
    setCategoryMode(state, action: { payload: CategoryMode }) {
      state.mode = action.payload;
    },
    setCreateParentId(state, action: { payload: number }) {
      state.createParentId = action.payload;
    },
    clearGlobalError(state) {
      state.globalError = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch by type
      .addCase(fetchCategoryByType.pending, (state, action) => {
        const type = action.meta.arg.type;
        state.types[type].loading = true;
        state.types[type].error = null;
      })
      .addCase(fetchCategoryByType.fulfilled, (state, action) => {
        const { type, data } = action.payload;
        const typeState = state.types[type];
        typeState.data = data;
        typeState.loading = false;
        typeState.error = null;
        typeState.lastFetched = Date.now();
        // Sắp xếp data
        typeState.data.sort((a, b) => a.id - b.id || a.priority - b.priority);
      })
      .addCase(fetchCategoryByType.rejected, (state, action) => {
        const type = action.meta.arg.type;
        const typeState = state.types[type];
        typeState.loading = false;
        // Use rejectWithValue payload or fallback to error message
        typeState.error =
          (action.payload as string) ||
          action.error.message ||
          "Something went wrong";
      })

      // Create category
      .addCase(createCategoryThunk.pending, (state) => {
        state.creating = true;
        state.globalError = null;
      })
      .addCase(createCategoryThunk.fulfilled, (state, action) => {
        const newItem = action.payload;

        const typeState = state.types[newItem.type];
        typeState.data.push(newItem);
        typeState.data.sort((a, b) => a.id - b.id || a.priority - b.priority);
        state.creating = false;

        toast.success("Thêm danh mục thành công");
      })
      .addCase(createCategoryThunk.rejected, (state, action) => {
        state.creating = false;
        state.globalError =
          (action.payload as string) ||
          action.error.message ||
          "Tạo danh mục thất bại";
      })

      // Update category
      .addCase(updateCategoryThunk.pending, (state) => {
        state.updating = true;
        state.globalError = null;
      })
      .addCase(updateCategoryThunk.fulfilled, (state, action) => {
        const updated = action.payload;
        if (!updated) return;

        const typeState = state.types[updated.type];
        const idx = typeState.data.findIndex((cat) => cat.id === updated.id);
        if (idx !== -1) {
          typeState.data[idx] = updated;
        }
        state.updating = false;
        toast.success("Sửa danh mục thành công");
      })
      .addCase(updateCategoryThunk.rejected, (state, action) => {
        state.updating = false;
        state.globalError =
          (action.payload as string) ||
          action.error.message ||
          "Cập nhật danh mục thất bại";
      })

      // Delete category
      .addCase(deleteCategoryThunk.pending, (state) => {
        state.deleting = true;
        state.globalError = null;
      })
      .addCase(deleteCategoryThunk.fulfilled, (state, action) => {
        if (!action.payload) return;
        const { id, type } = action.payload;
        const typeState = state.types[type];
        typeState.data = typeState.data.filter((cat) => cat.id !== id);
        typeState.data.sort((a, b) => a.id - b.id || a.priority - b.priority);
        state.deleting = false;
        toast.success("Xóa danh mục thành công");
      })
      .addCase(deleteCategoryThunk.rejected, (state, action) => {
        state.deleting = false;
        state.globalError =
          (action.payload as string) ||
          action.error.message ||
          "Xóa danh mục thất bại";
      });
  },
});

export const {
  clearCategories,
  clearCategoryType,
  setSelectedId,
  setSelectedType,
  setCategoryMode,
  setCreateParentId,
  clearGlobalError,
} = categorySlice.actions;
export default categorySlice.reducer;
