import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useSelector } from "react-redux";
import { RootState } from "@/store/rootReducer";

const UserPage: React.FC = () => {
  const navigate = useNavigate();
  const { account, is_authenticated } = useSelector(
    (state: RootState) => state.auth
  );

  useEffect(() => {
    if (is_authenticated && account) {
      // Redirect to user profile page (route configured dynamically)
      navigate("/me/thong-tin-ca-nhan", { replace: true });
    }
  }, [navigate, is_authenticated, account]);

  if (!is_authenticated || !account) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-600">
            Vui lòng đăng nhập để truy cập
          </h2>
        </div>
      </div>
    );
  }

  // Show loading while redirecting
  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
        <p className="mt-4 text-gray-600">
          Đang chuyển hướng đến thông tin cá nhân...
        </p>
      </div>
    </div>
  );
};

export default UserPage;
