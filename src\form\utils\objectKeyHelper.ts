/**
 * Object-Key Pattern Utilities
 *
 * These utilities support the object-key pattern used in form configuration
 * where nested object paths are represented as strings with "->" separators.
 *
 * Example: "user->profile->email" represents nested object { user: { profile: { email: value } } }
 */

/**
 * Flatten object using separator for object-key pattern
 * Example: { user: { email: "test" } } → { "user->email": "test" }
 */
export function flattenObjectKeys(
  obj: Record<string, unknown>,
  prefix?: string,
  delimiter: string = "."
): Record<string, unknown> {
  const flattened: Record<string, unknown> = {};

  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      const value = obj[key];
      const newKey = prefix ? `${prefix}${delimiter}${key}` : key;

      if (
        typeof value === "object" &&
        value !== null &&
        !Array.isArray(value)
      ) {
        Object.assign(
          flattened,
          flattenObjectKeys(value as Record<string, unknown>, newKey, delimiter)
        );
      } else {
        flattened[newKey] = value;
      }
    }
  }

  return flattened;
}

/**
 * Unflatten object using separator for object-key pattern
 * Example: { "user->email": "test" } → { user: { email: "test" } }
 */
export function unflattenObjectKeys<T = Record<string, unknown>>(
  obj: Record<string, unknown>,
  delimiter: string = "."
): T {
  const result: Record<string, unknown> = {};

  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      const keys = key.split(delimiter);
      let current = result;

      for (let i = 0; i < keys.length - 1; i++) {
        const k = keys[i];
        if (!(k in current)) {
          current[k] = {};
        }
        current = current[k] as Record<string, unknown>;
      }

      current[keys[keys.length - 1]] = obj[key];
    }
  }

  return result as T;
}

/**
 * Set nested value using object-key pattern
 * Example: setNestedValue(obj, "user->email", "<EMAIL>")
 */
export function setNestedValue(
  obj: Record<string, unknown>,
  key: string,
  value: unknown,
  delimiter: string = "."
): void {
  const keys = key.split(delimiter);
  let current = obj;

  for (let i = 0; i < keys.length - 1; i++) {
    const k = keys[i];
    if (
      !(k in current) ||
      typeof current[k] !== "object" ||
      current[k] === null
    ) {
      current[k] = {};
    }
    current = current[k] as Record<string, unknown>;
  }

  current[keys[keys.length - 1]] = value;
}

/**
 * Get nested value using object-key pattern
 * Example: getNestedValue(obj, "user->email") → "<EMAIL>"
 */
export function getNestedValue(
  obj: Record<string, unknown>,
  key: string,
  delimiter: string = "."
): unknown {
  const keys = key.split(delimiter);
  let current: unknown = obj;

  for (const k of keys) {
    if (
      current &&
      typeof current === "object" &&
      current !== null &&
      k in current
    ) {
      current = (current as Record<string, unknown>)[k];
    } else {
      return undefined;
    }
  }

  return current;
}

/**
 * Check if object has nested property using object-key pattern
 * Example: hasNestedProperty(obj, "user->email") → true/false
 */
export function hasNestedProperty(
  obj: Record<string, unknown>,
  key: string,
  delimiter: string = "."
): boolean {
  const keys = key.split(delimiter);
  let current: unknown = obj;

  for (const k of keys) {
    if (
      current &&
      typeof current === "object" &&
      current !== null &&
      k in current
    ) {
      current = (current as Record<string, unknown>)[k];
    } else {
      return false;
    }
  }

  return true;
}

/**
 * Get all object keys in flattened format
 * Example: { user: { email: "test", name: "John" } } → ["user->email", "user->name"]
 */
export function getAllObjectKeys(
  obj: Record<string, unknown>,
  prefix?: string,
  delimiter: string = "."
): string[] {
  const keys: string[] = [];

  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      const value = obj[key];
      const newKey = prefix ? `${prefix}${delimiter}${key}` : key;

      if (
        typeof value === "object" &&
        value !== null &&
        !Array.isArray(value)
      ) {
        keys.push(
          ...getAllObjectKeys(value as Record<string, unknown>, newKey, delimiter)
        );
      } else {
        keys.push(newKey);
      }
    }
  }

  return keys;
}
