import { Badge } from "@/components/ui/badge";
import {
  FileTex<PERSON>,
  Timer,
  CheckCircle2,
  <PERSON><PERSON><PERSON>,
  Trash2,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "lucide-react";
import type { PostStatus } from "../states/types";
import React from "react";

const STATUS_CONFIG: Record<
  PostStatus,
  {
    label: string;
    color: string;
    icon: React.ReactNode;
    className?: string;
  }
> = {
  DRAFT: {
    label: "Bản nháp",
    color: "bg-muted text-muted-foreground border",
    icon: <FileText className="w-4 h-4 mr-1" />,
  },
  REVIEW: {
    label: "Chờ duyệt",
    color: "bg-yellow-100 text-yellow-800 border-yellow-200",
    icon: <Timer className="w-4 h-4 mr-1" />,
  },
  PUBLISHED: {
    label: "Đã xuất bản",
    color: "bg-green-100 text-green-800 border-green-200",
    icon: <CheckCircle2 className="w-4 h-4 mr-1" />,
  },
  UNPUBLISHED: {
    label: "Đã gỡ",
    color: "bg-gray-200 text-gray-600 border-gray-300",
    icon: <EyeOff className="w-4 h-4 mr-1" />,
  },
  TRASH: {
    label: "Đã xoá",
    color: "bg-rose-100 text-rose-700 border-rose-200",
    icon: <Trash2 className="w-4 h-4 mr-1" />,
  },
  REJECTED: {
    label: "Bị từ chối",
    color: "bg-orange-100 text-orange-700 border-orange-200",
    icon: <AlertTriangle className="w-4 h-4 mr-1" />,
  },
};

export const PostBadge = React.memo(function PostBadge({
  value,
}: {
  value: PostStatus;
}) {
  const config = STATUS_CONFIG[value];

  return (
    <Badge
      className={`inline-flex items-center font-semibold rounded-lg px-2 py-1 text-xs gap-1 border ${config.color} shadow-sm`}
      title={config.label}
    >
      {config.icon}
      {config.label}
    </Badge>
  );
});
