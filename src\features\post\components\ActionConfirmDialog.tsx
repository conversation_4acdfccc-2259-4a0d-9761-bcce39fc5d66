import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  Di<PERSON>Footer as <PERSON><PERSON><PERSON><PERSON>,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";

export function ActionConfirmDialog({
  open,
  onOpenChange,
  onConfirm,
  icon,
  confirmText,
  loading,
}: {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => Promise<void>;
  icon: React.ReactNode;
  confirmText: string;
  loading: boolean;
}) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-xs  bg-white">
        <DialogHeader className="flex flex-col items-center">
          <DialogTitle className="flex items-center mb-2 text-primary">
            <div className="mr-2">{icon}</div><PERSON><PERSON><PERSON> nhận
          </DialogTitle>
          <div className="font-medium text-center">{confirmText}</div>
        </DialogHeader>
        <DialogActions className="flex justify-end gap-2 pt-2">
          <Button
            className="w-24"
            variant="outline"
            size="sm"
            onClick={() => onOpenChange(false)}
            disabled={loading}
          >
            Hủy
          </Button>
          <Button
            className="w-24"
            variant="default"
            size="sm"
            onClick={onConfirm}
            disabled={loading}
          >
            Xác nhận
          </Button>
        </DialogActions>
      </DialogContent>
    </Dialog>
  );
}
