import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { useAppDispatch } from "@/store/rootReducer";
import { selectCategoryData } from "@/features/category/states/selector";
import { selectPageBySlug } from "./selectors";
import { updateCategoryPageMapping, fetchPageByIdAsync } from "./slices";
import { RootState } from "@/store/rootReducer";

// Hook to sync category-page mapping from category data
export const useCategoryPageMapping = () => {
  const dispatch = useAppDispatch();
  const categoryData = useSelector(selectCategoryData);

  useEffect(() => {
    // Extract category-page mapping from all category types
    const mapping: Record<number, number> = {};

    Object.values(categoryData).forEach((categories) => {
      categories.forEach((category) => {
        if (category.postId) {
          mapping[category.id] = category.postId;
        }
      });
    });

    // Only update if mapping has changed
    dispatch(updateCategoryPageMapping(mapping));
  }, [categoryData, dispatch]);
};

// Hook to get page for selected category
export const useSelectedCategoryPage = () => {
  const dispatch = useAppDispatch();
  const selectedCategoryId = useSelector(
    (state: RootState) => state.pageState.selectedCategoryId
  );
  const selectedPage = useSelector(
    (state: RootState) => state.pageState.selectedPageId
  );
  const pages = useSelector((state: RootState) => state.pageState.pages);
  const categoryPageMapping = useSelector(
    (state: RootState) => state.pageState.categoryPageMapping
  );

  // Get linked page for the selected category
  const linkedPageId = selectedCategoryId
    ? categoryPageMapping[selectedCategoryId]
    : null;
  const linkedPage = linkedPageId ? pages[linkedPageId] : null;

  // Debug logging
  console.log("useSelectedCategoryPage Debug:", {
    selectedCategoryId,
    linkedPageId,
    hasLinkedPage: !!linkedPageId,
    hasLinkedPageData: !!linkedPage,
    categoryPageMapping,
    pagesKeys: Object.keys(pages),
  });

  // Auto-fetch linked page if we have ID but no data
  useEffect(() => {
    if (linkedPageId && !linkedPage) {
      console.log(
        `🔄 Auto-fetching linked page ${linkedPageId} for category ${selectedCategoryId}`
      );
      dispatch(fetchPageByIdAsync(linkedPageId));
    }
  }, [linkedPageId, linkedPage, selectedCategoryId, dispatch]);

  return {
    selectedCategoryId,
    selectedPageId: selectedPage,
    linkedPageId,
    linkedPage,
    hasLinkedPage: !!linkedPageId, // Keep this as checking ID existence
  };
};

// Hook specifically for Page component - fetches page content by slug
export const usePageBySlug = (slug: string) => {
  const dispatch = useAppDispatch();
  const [isFirstLoad, setIsFirstLoad] = useState(true);
  const [isFetching, setIsFetching] = useState(false);

  // Get data from selector
  const selectorResult = useSelector((state: RootState) =>
    selectPageBySlug(state, slug)
  );

  const {
    category,
    page,
    loading: storeLoading,
    error: storeError,
    found,
  } = selectorResult;

  // If category found but no page in cache, fetch it
  useEffect(() => {
    if (found && category?.postId && !page && !isFetching && !storeLoading) {
      console.log(
        `🔄 Fetching page for category "${category.name}" (postId: ${category.postId})`
      );

      setIsFetching(true);
      dispatch(fetchPageByIdAsync(category.postId))
        .unwrap()
        .then(() => {
          console.log(`✅ Successfully fetched page ${category.postId}`);
        })
        .catch((error: unknown) => {
          console.error(`❌ Failed to fetch page ${category.postId}:`, error);
        })
        .finally(() => {
          setIsFetching(false);
          setIsFirstLoad(false);
        });
    } else if (isFirstLoad) {
      setIsFirstLoad(false);
    }
  }, [
    found,
    category?.postId,
    page,
    isFetching,
    storeLoading,
    dispatch,
    category?.name,
    isFirstLoad,
  ]);

  // Refetch function
  const refetch = () => {
    if (category?.postId) {
      setIsFetching(true);
      dispatch(fetchPageByIdAsync(category.postId))
        .unwrap()
        .finally(() => setIsFetching(false));
    }
  };

  return {
    category,
    page,
    loading: storeLoading || isFetching,
    error: storeError,
    found,
    refetch,
  };
};
