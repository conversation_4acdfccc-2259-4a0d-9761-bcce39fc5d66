import { useState, useEffect, useMemo } from "react";
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Loader2, Upload, Search, Plus } from "lucide-react";
import { ImageUploader } from "@/components/form/upload/ImageUploader";
import { AlbumSelectionGrid } from "@/features/media/images/components/AlbumSelectionGrid";
import { Album, Media } from "@/features/media/images/states/types";
import { toast } from "sonner";
import type { BannerItem } from "../states/type";
import { MediaBreadcrumb } from "@/components/other/MediaBreadcrumb";
import { fetchAlbumMedia } from "@/features/media/images/states/api";

interface BannerMediaModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectBanner: (banner: BannerItem) => void;
  existingBanners?: BannerItem[];
}

export function BannerMediaModal({
  isOpen,
  onClose,
  onSelectBanner,
  existingBanners = [],
}: BannerMediaModalProps) {
  const [activeTab, setActiveTab] = useState<"upload" | "browse">("upload");

  // Browse tab state
  const [selectedAlbum, setSelectedAlbum] = useState<Album | null>(null);
  const [browseView, setBrowseView] = useState<"albums" | "media">("albums");

  const handleUploadSuccess = (fileUrl: string) => {
    // Immediately create banner and close modal after successful upload
    const bannerData: BannerItem = {
      src: fileUrl,
      alt: "", // Empty alt text as per requirement
    };

    onSelectBanner(bannerData);
    toast.success("Banner đã được thêm thành công");
    handleClose(); // Close modal after selection
  };



  const handleSelectAlbum = (album: Album) => {
    setSelectedAlbum(album);
    setBrowseView("media");
  };

  const handleBackToAlbums = () => {
    setBrowseView("albums");
    setSelectedAlbum(null);
  };

  const handleMediaSelected = (media: Media, removeFromDisplay?: (mediaId: number) => void) => {
    const bannerData: BannerItem = {
      src: media.src,
      alt: "", // Empty alt text as per requirement
    };

    // Remove the selected media from display immediately for visual feedback
    if (removeFromDisplay) {
      removeFromDisplay(media.id);
    }

    onSelectBanner(bannerData);
  };

  const handleClose = () => {
    // Reset all states
    setActiveTab("upload");
    setSelectedAlbum(null);
    setBrowseView("albums");
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[800px] max-h-[80vh] overflow-hidden bg-white">
        <DialogHeader>
          <DialogTitle>Thêm banner mới</DialogTitle>
        </DialogHeader>

        <Tabs
          value={activeTab}
          onValueChange={(value) => setActiveTab(value as "upload" | "browse")}
        >
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="upload" className="flex items-center gap-2">
              <Upload className="w-4 h-4" />
              Tải lên từ máy
            </TabsTrigger>
            <TabsTrigger
              value="browse"
              className="flex items-center gap-2"
            >
              <Search className="w-4 h-4" />
              Chọn từ thư viện
            </TabsTrigger>
          </TabsList>

          {/* Upload Tab */}
          <TabsContent value="upload" className="space-y-4">
            <div className="space-y-4">
              {/* Image Uploader */}
              <div className="space-y-2">
                <Label>Chọn ảnh banner từ máy tính</Label>
                <div className="flex justify-center">
                  <ImageUploader
                    onUploadSuccess={handleUploadSuccess}
                    onUploadError={() => { }}
                    placeholder="Click để chọn và tải ảnh banner lên"
                    className="w-full max-w-xs"
                  />
                </div>
                <p className="text-sm text-muted-foreground text-center">
                  Banner sẽ được thêm tự động sau khi tải lên thành công
                </p>
              </div>
            </div>
          </TabsContent>

          {/* Browse Tab */}
          <TabsContent value="browse" className="space-y-4">
            <div className="h-[60vh] overflow-hidden">
              {browseView === "albums" ? (
                <div className="h-full space-y-4">
                  {/* Breadcrumb for Album Selection */}
                  <div className="space-y-2">
                    <MediaBreadcrumb
                      currentPath="Tất cả thư viện"
                    />
                    <p className="text-sm text-muted-foreground">
                      Chọn thư viện để duyệt ảnh banner
                    </p>
                  </div>

                  <div className="h-full overflow-y-auto">
                    <AlbumSelectionGrid
                      albumType="image"
                      onSelectAlbum={handleSelectAlbum}
                    />
                  </div>
                </div>
              ) : selectedAlbum ? (
                <div className="h-full space-y-4 ">
                  <div className="h-full overflow-y-auto">
                    <BannerMediaGrid
                      album={selectedAlbum}
                      onBack={handleBackToAlbums}
                      onMediaSelected={(media, removeFromDisplay) =>
                        handleMediaSelected(media, removeFromDisplay)
                      }
                      existingBanners={existingBanners}
                    />
                  </div>
                </div>
              ) : null}
            </div>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}

/**
 * Custom media grid for banner selection with pagination support
 */
interface BannerMediaGridProps {
  album: Album;
  onBack: () => void;
  onMediaSelected: (media: Media, removeFromDisplay?: (mediaId: number) => void) => void;
  existingBanners?: BannerItem[];
}

function BannerMediaGrid({ album, onBack, onMediaSelected, existingBanners = [] }: BannerMediaGridProps) {
  const [media, setMedia] = useState<Media[]>([]);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(0);

  const SIZE = 8; // Number of items per page

  /**
   * Creates a Set of existing banner URLs for efficient filtering
   */
  const existingBannerUrls = useMemo(() =>
    new Set(existingBanners.map(banner => banner.src)),
    [existingBanners]
  );

  /**
   * Loads media from album with pagination support and filters out existing banners
   * @param pageNum - Page number to load (0-based)
   * @param reset - Whether to reset the media list or append to it
   * @param size - Number of items per page
   */
  const loadMedia = async (pageNum: number = 0, reset: boolean = false, size: number = SIZE) => {
    if (loading) return;

    setLoading(true);
    try {
      const response = await fetchAlbumMedia(album.id, {
        page: pageNum,
        size: size,
      });

      // Filter out media that already exists in banner list
      const filteredMedia = response.data.filter(mediaItem => !existingBannerUrls.has(mediaItem.src));

      if (reset) {
        setMedia(filteredMedia);
      } else {
        setMedia((prev) => [...prev, ...filteredMedia]);
      }

      setHasMore(response.hasMore || false);
      setPage(pageNum);

      if (response.hasMore && (media.length === 0 && filteredMedia.length === 0)) {
        handleLoadMore(pageNum);
      } else if (response.hasMore && (filteredMedia.length === 0)) {
        handleLoadMore(pageNum);
      }

    } catch {
      toast.error("Không thể tải ảnh từ thư viện");
    } finally {
      setLoading(false);
    }
  };

  // Load initial media when album changes
  useEffect(() => {
    loadMedia(0, true);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [album.id]);

  /**
   * Handles loading more media items
   */
  const handleLoadMore = (pageNum: number) => {
    if (hasMore && !loading) {
      loadMedia(pageNum + 1, false);
    }
  };

  /**
   * Removes a media item from the display after selection
   * @param mediaId - ID of the media item to remove
   */
  const removeMediaFromDisplay = (mediaId: number) => {
    setMedia((prev) => prev.filter(item => item.id !== mediaId));
  };

  /**
   * Handles media selection with immediate visual feedback
   * @param mediaItem - The selected media item
   */
  const handleMediaClick = (mediaItem: Media) => {
    onMediaSelected(mediaItem, removeMediaFromDisplay);
  };

  let contentRender = (<></>);

  if (loading && media.length === 0) {
    contentRender = (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-2" />
          <p className="text-sm text-muted-foreground">Đang tải ảnh...</p>
        </div>
      </div>
    );
  } else if (media.length === 0) {
    contentRender = (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="w-16 h-16 bg-muted rounded-lg flex items-center justify-center mx-auto mb-3">
            <Search className="w-8 h-8 text-muted-foreground" />
          </div>
          <p className="text-sm text-muted-foreground">
            Thư viện này chưa có ảnh nào
          </p>
        </div>
      </div>
    )
  } else {
    contentRender = (
      <div className="space-y-4">
        <div className="grid grid-cols-4 gap-2">
          {media.map((mediaItem) => (
            <div
              key={mediaItem.id}
              className="relative group aspect-square rounded-lg overflow-hidden bg-muted cursor-pointer hover:ring-2 hover:ring-primary transition-all flex items-center justify-center"
              onClick={() => handleMediaClick(mediaItem)}
            >
              <img
                src={mediaItem.src}
                alt={mediaItem.name}
                className="w-full h-full object-contain"
              />

              {/* Overlay */}
              <div className="absolute inset-0 bg-black/0 group-hover:bg-black/50 transition-colors flex items-center justify-center">
                <Plus className="w-6 h-6 text-white opacity-0 group-hover:opacity-100 transition-opacity" />
              </div>
            </div>
          ))}
        </div>

        {/* Load More Button */}
        {hasMore && (
          <div className="flex justify-center">
            <Button
              variant="outline"
              onClick={() => handleLoadMore(page)}
              disabled={loading}
              size="sm"
            >
              {loading && <Loader2 className="w-4 h-4 animate-spin mr-2" />}
              Xem thêm
            </Button>
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Breadcrumb Navigation */}
      <div className="space-y-2">
        {/* Navigation Header with Back Button */}
        <MediaBreadcrumb
          currentPath={album.name}
          onBack={onBack}
          backText="Tất cả thư viện"
        />
        <p className="text-sm text-muted-foreground">
          {media.length} ảnh có sẵn
        </p>
      </div>
      {contentRender}
    </div>
  );
}
