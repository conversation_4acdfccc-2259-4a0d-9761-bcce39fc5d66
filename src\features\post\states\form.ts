import { FormConfig } from "@/components/form/registry";

export const CreatePostFormConfig: FormConfig = {
  code: "create-post-form",
  name: "Tạo bài viết mới",
  note: "<PERSON>ùng để tạo bài viết mới",
  config: {
    id: "root",
    type: "group",
    label: "Thông tin bài viết",
    style: {
      frame: "p-4",
      label: "",
      content: "",
      error: "",
    },
    children: [
      {
        id: "title-field",
        type: "field",
        fieldConfig: {
          id: "title",
          label: "Tiêu đề",
          data_type: "text",
          input_type: "TextInput",
          placeholder: "Nhập tiêu đề bài viết",
          default_value: "",
          validation: {
            required: true,
            maxLength: 200,
          },
        },
        children: [],
      },
      {
        id: "slug-field",
        type: "field",
        fieldConfig: {
          id: "slug",
          label: "Đường dẫn",
          data_type: "text",
          input_type: "TextInput",
          placeholder: "duong-dan-url-bai-viet",
          default_value: "",
          validation: {
            required: true,
            maxLength: 200,
          },
        },
        children: [],
      },
      {
        id: "postType-field",
        type: "field",
        fieldConfig: {
          id: "postType",
          label: "Loại bài viết",
          data_type: "text",
          input_type: "DropdownInput",
          placeholder: "Chọn loại bài viết",
          default_value: "ARTICLE",
          validation: {
            required: true,
          },
          options: ["ARTICLE", "PAGE"],
          labels: ["Bài viết", "Trang"],
        },
        children: [],
      },
      {
        id: "authorId-field",
        type: "field",
        fieldConfig: {
          id: "authorId",
          label: "ID tác giả",
          data_type: "text",
          input_type: "TextInput",
          placeholder: "Nhập ID tác giả",
          default_value: "admin",
          validation: {
            required: true,
            maxLength: 50,
          },
        },
        children: [],
      },
      {
        id: "excerpt-image-field",
        type: "field",
        fieldConfig: {
          id: "excerpt.image",
          label: "Ảnh bìa",
          data_type: "text",
          input_type: "ImagePickerInput",
          placeholder: "Chọn hình ảnh",
          default_value: "",
          validation: {
            required: true,
            maxLength: 200,
          },
        },
        children: [],
      },
      {
        id: "excerpt-description-field",
        type: "field",
        fieldConfig: {
          id: "excerpt.description",
          label: "Tóm tắt",
          data_type: "text",
          input_type: "TextAreaInput",
          placeholder: "Nhập tóm tắt bài viết",
          default_value: "",
          validation: {
            required: true,
            maxLength: 500,
          },
        },
        children: [],
      },
    ],
  },
};
