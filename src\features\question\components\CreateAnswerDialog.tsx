import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  CustomDial<PERSON>,
  Di<PERSON><PERSON>ooter,
  DialogButton,
} from "@/components/ui/CustomDialog";
import { MessageSquare, PlusCircle } from "lucide-react";
import { useQuestion } from "../hooks/useQuestion";
import { CreateAnswerRequest } from "../states/types";
import SimpleUploadInput from "@/components/form/upload/SimpleUploadInput";

interface CreateAnswerDialogProps {
  questionId: number;
  onSuccess?: () => void;
}

export const CreateAnswerDialog: React.FC<CreateAnswerDialogProps> = ({
  questionId,
  onSuccess,
}) => {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const { createAnswer } = useQuestion();

  const [formData, setFormData] = useState<CreateAnswerRequest>({
    questionId,
    answerContent: {
      answer: "",
      fileList: [],
    },
  });

  const handleInputChange = (field: string, value: string) => {
    if (field.startsWith("answerContent.")) {
      const contentField = field.replace("answerContent.", "");
      setFormData((prev) => ({
        ...prev,
        answerContent: {
          ...prev.answerContent,
          [contentField]: value,
        },
      }));
    } else {
      setFormData((prev) => ({
        ...prev,
        [field]: value,
      }));
    }
  };

  const handleFileUploadSuccess = (fileUrl: string) => {
    setFormData((prev) => ({
      ...prev,
      answerContent: {
        ...prev.answerContent,
        fileList: [...prev.answerContent.fileList, fileUrl],
      },
    }));
  };

  const handleFileUploadError = (error: string) => {
    console.error("File upload error:", error);
    // You can add toast notification here
  };

  const handleRemoveAttachment = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      answerContent: {
        ...prev.answerContent,
        fileList: prev.answerContent.fileList.filter(
          (_: string, i: number) => i !== index
        ),
      },
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setLoading(true);
      console.log("Creating answer:", formData);

      await createAnswer(formData);

      console.log("Answer created successfully");
      setOpen(false);

      // Reset form
      setFormData({
        questionId,
        answerContent: {
          answer: "",
          fileList: [],
        },
      });

      onSuccess?.();
    } catch (error) {
      console.error("Error creating answer:", error);
    } finally {
      setLoading(false);
    }
  };

  const isFormValid = () => {
    return formData.answerContent.answer.trim();
  };

  return (
    <>
      <Button onClick={() => setOpen(true)}>
        <PlusCircle className="h-4 w-4 mr-2" />
        Tạo câu trả lời
      </Button>

      <CustomDialog
        open={open}
        onClose={() => setOpen(false)}
        title="Tạo câu trả lời"
        maxWidth="2xl"
      >
        <div className="text-sm text-gray-600 mb-6">
          Nhập nội dung câu trả lời cho câu hỏi này
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Answer Content */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Nội dung câu trả lời <span className="text-red-500">*</span>
            </label>
            <textarea
              value={formData.answerContent.answer}
              onChange={(e) =>
                handleInputChange("answerContent.answer", e.target.value)
              }
              placeholder="Nhập nội dung câu trả lời chi tiết"
              rows={6}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
              disabled={loading}
              required
            />
          </div>

          {/* File Upload */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Tệp đính kèm (tùy chọn)
            </label>
            <SimpleUploadInput
              onUploadSuccess={handleFileUploadSuccess}
              onUploadError={handleFileUploadError}
            />
          </div>

          {/* Uploaded Files List */}
          {formData.answerContent.fileList.length > 0 && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Tệp đã tải lên ({formData.answerContent.fileList.length})
              </label>
              <div className="space-y-2">
                {formData.answerContent.fileList.map((attachment, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-3 bg-gray-50 rounded-md"
                  >
                    <div className="flex items-center gap-2">
                      <MessageSquare className="h-4 w-4 text-gray-400" />
                      <span className="text-sm text-gray-700 truncate">
                        {attachment.split("/").pop()}
                      </span>
                    </div>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRemoveAttachment(index)}
                      className="text-red-500 hover:text-red-700"
                    >
                      Xóa
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          )}

          <DialogFooter>
            <DialogButton
              onClick={() => setOpen(false)}
              variant="secondary"
              disabled={loading}
            >
              Hủy bỏ
            </DialogButton>

            <DialogButton
              type="submit"
              variant="primary"
              disabled={!isFormValid() || loading}
              loading={loading}
            >
              {loading ? "Đang tạo..." : "Tạo câu trả lời"}
            </DialogButton>
          </DialogFooter>
        </form>
      </CustomDialog>
    </>
  );
};
